{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?77cc", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?5e7a", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?6b0c", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?c3e8", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?7430", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue?3f57"], "names": ["name", "components", "uploadImage", "uploadFile", "options", "virtualHost", "emits", "props", "modelValue", "type", "default", "value", "disabled", "disablePreview", "delIcon", "autoUpload", "limit", "mode", "fileMediatype", "fileExtname", "title", "listStyles", "border", "dividline", "borderStyle", "imageStyles", "width", "height", "readonly", "returnType", "sizeType", "sourceType", "provider", "data", "files", "localValue", "watch", "handler", "immediate", "computed", "filesList", "showType", "limitLength", "created", "uniCloud", "methods", "clearFiles", "upload", "setValue", "newData", "reg", "url", "v", "newVal", "i", "filesData", "choose", "uni", "icon", "chooseFiles", "chooseAndUploadFile", "compressed", "extension", "count", "onChooseFile", "onUploadProgress", "then", "catch", "console", "chooseFileCallback", "_extname", "is_one", "filePaths", "currentData", "filedata", "file", "tempFiles", "tempFilePaths", "res", "fileItem", "uploadFiles", "setSuccessAndError", "successData", "errorData", "tempFile<PERSON>ath", "errorTempFilePath", "item", "index", "setProgress", "idx", "progress", "tempFile", "delFile", "getFileExt", "ext", "setEmit", "backObject", "newFilesData", "extname", "fileType", "image", "path", "size", "fileID", "uuid", "status", "cloudPath", "getTempFileURL", "fileList", "urls", "getForm", "parent", "parentName"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAuyB,CAAgB,uzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyB3zB;AAIA;AAMA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxCA,gBAyCA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;QACA;UACA;UACAY;UACA;UACAC;UACA;UACAC;QACA;MACA;IACA;IACAC;MACAhB;MACAC;QACA;UACAgB;UACAC;QACA;MACA;IACA;IACAC;MACAnB;MACAC;IACA;IACAmB;MACApB;MACAC;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACAqB;MACAtB;MACAC;QACA;MACA;IACA;IACAsB;MACAvB;MACAC;IACA;EACA;EACAuB;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAzB;MACA0B;QACA;MACA;MACAC;IACA;IACA9B;MACA6B;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAN;MACA;MACA;IACA;IACAO;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;MACAC;IACA;IACA;IACA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACAb;QACA;MACA;MACA;IACA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBAAA;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BACAC;4BACAC;4BACA;8BACAA;4BACA;8BACAA;4BACA;4BAAA,KACAD;8BAAA;8BAAA;4BAAA;4BACAE;4BAAA;4BAAA,OACA;0BAAA;4BAAAA;0BAAA;4BAEA;4BAAA,iCACAA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;kBAAA,gBAdAH;oBAAA;kBAAA;gBAAA;gBAAA,MAeA;kBAAA;kBAAA;gBAAA;gBAAA,KACAI;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAJ;cAAA;gBAAA;gBAAA;cAAA;gBAEAI;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAF;gBAAA;gBAAA,OACAH;cAAA;gBAFAK;gBAAA;gBAAA;cAAA;gBAKA;gBACA;kBACA;kBACA;gBACA;gBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA,qGACA;QACAC;UACArC;UACAsC;QACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACAf,SACAgB;QACAnD;QACAoD;QACA/B;QACAC;QACA;QACA+B;QACAC;QAAA;QACAC;QACAC;UACA;QACA;MACA,GACAC;QACA;MACA,GACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC,6CACA,yBACA,oBACA,gCACA;gBACA;kBACA;gBACA;gBAAA,wBAKA,iDAFAC,6CACAtC;gBAEA;kBACAsC;kBACAtC;gBACA;gBAEAuC;gBACAnB;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACApB;gBAAA;gBAAA,OACA;cAAA;gBAAAwC;gBACAA;gBACAA;gBACA;gBACAD,iDACAC;kBACAC;gBAAA,GACA;cAAA;gBAVArB;gBAAA;gBAAA;cAAA;gBAYA;kBACAsB;kBACAC;gBACA;gBACAC;gBACA;gBACA;kBACAA;gBACA;gBACAA;kBACA;kBACA;kBACA;kBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA9C;MACA;QACA;MACA,GACAgC;QACA;QACA;MACA,GACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAC;gBACAC;gBAAA,8DACA/B;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAgC;0BACAC;4BAAA;0BAAA;0BAAA,MAEAA;4BAAA;4BAAA;0BAAA;0BAAA;wBAAA;0BAAA,MACAD;4BAAA;4BAAA;0BAAA;0BACA;0BACA;0BACA;0BACA;0BACAH;0BACAE;0BAAA;0BAAA;wBAAA;0BAEA;0BACA;0BACAnC;0BAAA,KACAA;4BAAA;4BAAA;0BAAA;0BAAA;0BAAA,OACA;wBAAA;0BAAA;0BAAA;0BAAA;wBAAA;0BAEA;wBAAA;0BAGA;0BACA;0BACAgC;0BACAE;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAzBA9B;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBA6BA;kBACA;kBACA;kBACA;oBACAsB;oBACAC;kBACA;gBACA;gBAEA;kBACA;oBACAD;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAW;MACA;MACA;MACA;MACA;MACA;QACAC;UAAA;QAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACAF;QACAG;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACAL;QACAI;QACAP;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAS;MACA;MACA;MACA;QACA7F;QACA8F;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA9D;QACA;MACA;QACAA;QACA;UACA;QACA;QACA;MACA;MAKA;IAEA;IAEA;AACA;AACA;AACA;IACA+D;MACA;MACA9D;QACA+D;UACAC;UACAC;UACAC;UACApG;UACAqG;UACAC;UACAC;UACApD;UACA;UACAqD;UACAC;UACAC;QACA;MACA;MACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAA;gBACA;gBAAA;gBAAA,OACAhE;cAAA;gBAAAiE;gBAAA,kCACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxmBA;AAAA;AAAA;AAAA;AAAqoC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAzpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-file-picker.vue?vue&type=template&id=4e9b2668&\"\nvar renderjs\nimport script from \"./uni-file-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-file-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-file-picker.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-file-picker.vue?vue&type=template&id=4e9b2668&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.title ? _vm.filesList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-file-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-file-picker.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-file-picker\">\r\n\t\t<view v-if=\"title\" class=\"uni-file-picker__header\">\r\n\t\t\t<text class=\"file-title\">{{ title }}</text>\r\n\t\t\t<text class=\"file-count\">{{ filesList.length }}/{{ limitLength }}</text>\r\n\t\t</view>\r\n\t\t<upload-image v-if=\"fileMediatype === 'image' && showType === 'grid'\" :readonly=\"readonly\"\r\n\t\t\t:image-styles=\"imageStyles\" :files-list=\"filesList\" :limit=\"limitLength\" :disablePreview=\"disablePreview\"\r\n\t\t\t:delIcon=\"delIcon\" @uploadFiles=\"uploadFiles\" @choose=\"choose\" @delFile=\"delFile\">\r\n\t\t\t<slot>\r\n\t\t\t\t<view class=\"is-add\">\r\n\t\t\t\t\t<view class=\"icon-add\"></view>\r\n\t\t\t\t\t<view class=\"icon-add rotate\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</slot>\r\n\t\t</upload-image>\r\n\t\t<upload-file v-if=\"fileMediatype !== 'image' || showType !== 'grid'\" :readonly=\"readonly\"\r\n\t\t\t:list-styles=\"listStyles\" :files-list=\"filesList\" :showType=\"showType\" :delIcon=\"delIcon\"\r\n\t\t\t@uploadFiles=\"uploadFiles\" @choose=\"choose\" @delFile=\"delFile\">\r\n\t\t\t<slot><button type=\"primary\" size=\"mini\">选择文件</button></slot>\r\n\t\t</upload-file>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tchooseAndUploadFile,\r\n\t\tuploadCloudFiles\r\n\t} from './choose-and-upload-file.js'\r\n\timport {\r\n\t\tget_file_ext,\r\n\t\tget_extname,\r\n\t\tget_files_and_is_max,\r\n\t\tget_file_info,\r\n\t\tget_file_data\r\n\t} from './utils.js'\r\n\timport uploadImage from './upload-image.vue'\r\n\timport uploadFile from './upload-file.vue'\r\n\tlet fileInput = null\r\n\t/**\r\n\t * FilePicker 文件选择上传\r\n\t * @description 文件选择上传组件，可以选择图片、视频等任意文件并上传到当前绑定的服务空间\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=4079\r\n\t * @property {Object|Array}\tvalue\t组件数据，通常用来回显 ,类型由return-type属性决定\r\n\t * @property {Boolean}\tdisabled = [true|false]\t组件禁用\r\n\t * \t@value true \t禁用\r\n\t * \t@value false \t取消禁用\r\n\t * @property {Boolean}\treadonly = [true|false]\t组件只读，不可选择，不显示进度，不显示删除按钮\r\n\t * \t@value true \t只读\r\n\t * \t@value false \t取消只读\r\n\t * @property {String}\treturn-type = [array|object]\t限制 value 格式，当为 object 时 ，组件只能单选，且会覆盖\r\n\t * \t@value array\t规定 value 属性的类型为数组\r\n\t * \t@value object\t规定 value 属性的类型为对象\r\n\t * @property {Boolean}\tdisable-preview = [true|false]\t禁用图片预览，仅 mode:grid 时生效\r\n\t * \t@value true \t禁用图片预览\r\n\t * \t@value false \t取消禁用图片预览\r\n\t * @property {Boolean}\tdel-icon = [true|false]\t是否显示删除按钮\r\n\t * \t@value true \t显示删除按钮\r\n\t * \t@value false \t不显示删除按钮\r\n\t * @property {Boolean}\tauto-upload = [true|false]\t是否自动上传，值为true则只触发@select,可自行上传\r\n\t * \t@value true \t自动上传\r\n\t * \t@value false \t取消自动上传\r\n\t * @property {Number|String}\tlimit\t最大选择个数 ，h5 会自动忽略多选的部分\r\n\t * @property {String}\ttitle\t组件标题，右侧显示上传计数\r\n\t * @property {String}\tmode = [list|grid]\t选择文件后的文件列表样式\r\n\t * \t@value list \t列表显示\r\n\t * \t@value grid \t宫格显示\r\n\t * @property {String}\tfile-mediatype = [image|video|all]\t选择文件类型\r\n\t * \t@value image\t只选择图片\r\n\t * \t@value video\t只选择视频\r\n\t * \t@value all\t\t选择所有文件\r\n\t * @property {Array}\tfile-extname\t选择文件后缀，根据 file-mediatype 属性而不同\r\n\t * @property {Object}\tlist-style\tmode:list 时的样式\r\n\t * @property {Object}\timage-styles\t选择文件后缀，根据 file-mediatype 属性而不同\r\n\t * @event {Function} select \t选择文件后触发\r\n\t * @event {Function} progress 文件上传时触发\r\n\t * @event {Function} success \t上传成功触发\r\n\t * @event {Function} fail \t\t上传失败触发\r\n\t * @event {Function} delete \t文件从列表移除时触发\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniFilePicker',\r\n\t\tcomponents: {\r\n\t\t\tuploadImage,\r\n\t\t\tuploadFile\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\tvirtualHost: true\r\n\t\t},\r\n\t\temits: ['select', 'success', 'fail', 'progress', 'delete', 'update:modelValue', 'input'],\r\n\t\tprops: {\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [Array, Object],\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [Array, Object],\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tdisablePreview: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tdelIcon: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 自动上传\r\n\t\t\tautoUpload: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 最大选择个数 ，h5只能限制单选或是多选\r\n\t\t\tlimit: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 9\r\n\t\t\t},\r\n\t\t\t// 列表样式 grid | list | list-card\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'grid'\r\n\t\t\t},\r\n\t\t\t// 选择文件类型  image/video/all\r\n\t\t\tfileMediatype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'image'\r\n\t\t\t},\r\n\t\t\t// 文件类型筛选\r\n\t\t\tfileExtname: {\r\n\t\t\t\ttype: [Array, String],\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tlistStyles: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t// 是否显示边框\r\n\t\t\t\t\t\tborder: true,\r\n\t\t\t\t\t\t// 是否显示分隔线\r\n\t\t\t\t\t\tdividline: true,\r\n\t\t\t\t\t\t// 线条样式\r\n\t\t\t\t\t\tborderStyle: {}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageStyles: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\twidth: 'auto',\r\n\t\t\t\t\t\theight: 'auto'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treadonly: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\treturnType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'array'\r\n\t\t\t},\r\n\t\t\tsizeType: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ['original', 'compressed']\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsourceType: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn  ['album', 'camera']\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tprovider: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '' // 默认上传到 unicloud 内置存储 extStorage 扩展存储\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfiles: [],\r\n\t\t\t\tlocalValue: []\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue: {\r\n\t\t\t\thandler(newVal, oldVal) {\r\n\t\t\t\t\tthis.setValue(newVal, oldVal)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\thandler(newVal, oldVal) {\r\n\t\t\t\t\tthis.setValue(newVal, oldVal)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tfilesList() {\r\n\t\t\t\tlet files = []\r\n\t\t\t\tthis.files.forEach(v => {\r\n\t\t\t\t\tfiles.push(v)\r\n\t\t\t\t})\r\n\t\t\t\treturn files\r\n\t\t\t},\r\n\t\t\tshowType() {\r\n\t\t\t\tif (this.fileMediatype === 'image') {\r\n\t\t\t\t\treturn this.mode\r\n\t\t\t\t}\r\n\t\t\t\treturn 'list'\r\n\t\t\t},\r\n\t\t\tlimitLength() {\r\n\t\t\t\tif (this.returnType === 'object') {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.limit) {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t\tif (this.limit >= 9) {\r\n\t\t\t\t\treturn 9\r\n\t\t\t\t}\r\n\t\t\t\treturn this.limit\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// TODO 兼容不开通服务空间的情况\r\n\t\t\tif (!(uniCloud.config && uniCloud.config.provider)) {\r\n\t\t\t\tthis.noSpace = true\r\n\t\t\t\tuniCloud.chooseAndUploadFile = chooseAndUploadFile\r\n\t\t\t}\r\n\t\t\tthis.form = this.getForm('uniForms')\r\n\t\t\tthis.formItem = this.getForm('uniFormsItem')\r\n\t\t\tif (this.form && this.formItem) {\r\n\t\t\t\tif (this.formItem.name) {\r\n\t\t\t\t\tthis.rename = this.formItem.name\r\n\t\t\t\t\tthis.form.inputChildrens.push(this)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 公开用户使用，清空文件\r\n\t\t\t * @param {Object} index\r\n\t\t\t */\r\n\t\t\tclearFiles(index) {\r\n\t\t\t\tif (index !== 0 && !index) {\r\n\t\t\t\t\tthis.files = []\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.setEmit()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.files.splice(index, 1)\r\n\t\t\t\t}\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.setEmit()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公开用户使用，继续上传\r\n\t\t\t */\r\n\t\t\tupload() {\r\n\t\t\t\tlet files = []\r\n\t\t\t\tthis.files.forEach((v, index) => {\r\n\t\t\t\t\tif (v.status === 'ready' || v.status === 'error') {\r\n\t\t\t\t\t\tfiles.push(Object.assign({}, v))\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn this.uploadFiles(files)\r\n\t\t\t},\r\n\t\t\tasync setValue(newVal, oldVal) {\r\n\t\t\t\tconst newData =  async (v) => {\r\n\t\t\t\t\tconst reg = /cloud:\\/\\/([\\w.]+\\/?)\\S*/\r\n\t\t\t\t\tlet url = ''\r\n\t\t\t\t\tif(v.fileID){\r\n\t\t\t\t\t\turl = v.fileID\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\turl = v.url\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (reg.test(url)) {\r\n\t\t\t\t\t\tv.fileID = url\r\n\t\t\t\t\t\tv.url = await this.getTempFileURL(url)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(v.url) v.path = v.url\r\n\t\t\t\t\treturn v\r\n\t\t\t\t}\r\n\t\t\t\tif (this.returnType === 'object') {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tawait newData(newVal)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tnewVal = {}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!newVal) newVal = []\r\n\t\t\t\t\tfor(let i =0 ;i < newVal.length ;i++){\r\n\t\t\t\t\t\tlet v = newVal[i]\r\n\t\t\t\t\t\tawait newData(v)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.localValue = newVal\r\n\t\t\t\tif (this.form && this.formItem &&!this.is_reset) {\r\n\t\t\t\t\tthis.is_reset = false\r\n\t\t\t\t\tthis.formItem.setValue(this.localValue)\r\n\t\t\t\t}\r\n\t\t\t\tlet filesData = Object.keys(newVal).length > 0 ? newVal : [];\r\n\t\t\t\tthis.files = [].concat(filesData)\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 选择文件\r\n\t\t\t */\r\n\t\t\tchoose() {\r\n\t\t\t\tif (this.disabled) return\r\n\t\t\t\tif (this.files.length >= Number(this.limitLength) && this.showType !== 'grid' && this.returnType ===\r\n\t\t\t\t\t'array') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `您最多选择 ${this.limitLength} 个文件`,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.chooseFiles()\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 选择文件并上传\r\n\t\t\t */\r\n\t\t\tchooseFiles() {\r\n\t\t\t\tconst _extname = get_extname(this.fileExtname)\r\n\t\t\t\t// 获取后缀\r\n\t\t\t\tuniCloud\r\n\t\t\t\t\t.chooseAndUploadFile({\r\n\t\t\t\t\t\ttype: this.fileMediatype,\r\n\t\t\t\t\t\tcompressed: false,\r\n\t\t\t\t\t\tsizeType: this.sizeType,\r\n\t\t\t\t\t\tsourceType: this.sourceType,\r\n\t\t\t\t\t\t// TODO 如果为空，video 有问题\r\n\t\t\t\t\t\textension: _extname.length > 0 ? _extname : undefined,\r\n\t\t\t\t\t\tcount: this.limitLength - this.files.length, //默认9\r\n\t\t\t\t\t\tonChooseFile: this.chooseFileCallback,\r\n\t\t\t\t\t\tonUploadProgress: progressEvent => {\r\n\t\t\t\t\t\t\tthis.setProgress(progressEvent, progressEvent.index)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(result => {\r\n\t\t\t\t\t\tthis.setSuccessAndError(result.tempFiles)\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.log('选择失败', err)\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 选择文件回调\r\n\t\t\t * @param {Object} res\r\n\t\t\t */\r\n\t\t\tasync chooseFileCallback(res) {\r\n\t\t\t\tconst _extname = get_extname(this.fileExtname)\r\n\t\t\t\tconst is_one = (Number(this.limitLength) === 1 &&\r\n\t\t\t\t\t\tthis.disablePreview &&\r\n\t\t\t\t\t\t!this.disabled) ||\r\n\t\t\t\t\tthis.returnType === 'object'\r\n\t\t\t\t// 如果这有一个文件 ，需要清空本地缓存数据\r\n\t\t\t\tif (is_one) {\r\n\t\t\t\t\tthis.files = []\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet {\r\n\t\t\t\t\tfilePaths,\r\n\t\t\t\t\tfiles\r\n\t\t\t\t} = get_files_and_is_max(res, _extname)\r\n\t\t\t\tif (!(_extname && _extname.length > 0)) {\r\n\t\t\t\t\tfilePaths = res.tempFilePaths\r\n\t\t\t\t\tfiles = res.tempFiles\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet currentData = []\r\n\t\t\t\tfor (let i = 0; i < files.length; i++) {\r\n\t\t\t\t\tif (this.limitLength - this.files.length <= 0) break\r\n\t\t\t\t\tfiles[i].uuid = Date.now()\r\n\t\t\t\t\tlet filedata = await get_file_data(files[i], this.fileMediatype)\r\n\t\t\t\t\tfiledata.progress = 0\r\n\t\t\t\t\tfiledata.status = 'ready'\r\n\t\t\t\t\tthis.files.push(filedata)\r\n\t\t\t\t\tcurrentData.push({\r\n\t\t\t\t\t\t...filedata,\r\n\t\t\t\t\t\tfile: files[i]\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('select', {\r\n\t\t\t\t\ttempFiles: currentData,\r\n\t\t\t\t\ttempFilePaths: filePaths\r\n\t\t\t\t})\r\n\t\t\t\tres.tempFiles = files\r\n\t\t\t\t// 停止自动上传\r\n\t\t\t\tif (!this.autoUpload || this.noSpace) {\r\n\t\t\t\t\tres.tempFiles = []\r\n\t\t\t\t}\r\n\t\t\t\tres.tempFiles.forEach((fileItem, index) => {\r\n\t\t\t\t\tthis.provider && (fileItem.provider = this.provider);\r\n\t\t\t\t\tconst fileNameSplit = fileItem.name.split('.')\r\n\t\t\t\t\tconst ext = fileNameSplit.pop()\r\n\t\t\t\t\tconst fileName = fileNameSplit.join('.').replace(/[\\s\\/\\?<>\\\\:\\*\\|\":]/g, '_')\r\n\t\t\t\t\tfileItem.cloudPath = fileName + '_' + Date.now() + '_' + index + '.' + ext\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 批传\r\n\t\t\t * @param {Object} e\r\n\t\t\t */\r\n\t\t\tuploadFiles(files) {\r\n\t\t\t\tfiles = [].concat(files)\r\n\t\t\t\treturn uploadCloudFiles.call(this, files, 5, res => {\r\n\t\t\t\t\t\tthis.setProgress(res, res.index, true)\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(result => {\r\n\t\t\t\t\t\tthis.setSuccessAndError(result)\r\n\t\t\t\t\t\treturn result;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 成功或失败\r\n\t\t\t */\r\n\t\t\tasync setSuccessAndError(res, fn) {\r\n\t\t\t\tlet successData = []\r\n\t\t\t\tlet errorData = []\r\n\t\t\t\tlet tempFilePath = []\r\n\t\t\t\tlet errorTempFilePath = []\r\n\t\t\t\tfor (let i = 0; i < res.length; i++) {\r\n\t\t\t\t\tconst item = res[i]\r\n\t\t\t\t\tconst index = item.uuid ? this.files.findIndex(p => p.uuid === item.uuid) : item.index\r\n\r\n\t\t\t\t\tif (index === -1 || !this.files) break\r\n\t\t\t\t\tif (item.errMsg === 'request:fail') {\r\n\t\t\t\t\t\tthis.files[index].url = item.path\r\n\t\t\t\t\t\tthis.files[index].status = 'error'\r\n\t\t\t\t\t\tthis.files[index].errMsg = item.errMsg\r\n\t\t\t\t\t\t// this.files[index].progress = -1\r\n\t\t\t\t\t\terrorData.push(this.files[index])\r\n\t\t\t\t\t\terrorTempFilePath.push(this.files[index].url)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.files[index].errMsg = ''\r\n\t\t\t\t\t\tthis.files[index].fileID = item.url\r\n\t\t\t\t\t\tconst reg = /cloud:\\/\\/([\\w.]+\\/?)\\S*/\r\n\t\t\t\t\t\tif (reg.test(item.url)) {\r\n\t\t\t\t\t\t\tthis.files[index].url = await this.getTempFileURL(item.url)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.files[index].url = item.url\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis.files[index].status = 'success'\r\n\t\t\t\t\t\tthis.files[index].progress += 1\r\n\t\t\t\t\t\tsuccessData.push(this.files[index])\r\n\t\t\t\t\t\ttempFilePath.push(this.files[index].fileID)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (successData.length > 0) {\r\n\t\t\t\t\tthis.setEmit()\r\n\t\t\t\t\t// 状态改变返回\r\n\t\t\t\t\tthis.$emit('success', {\r\n\t\t\t\t\t\ttempFiles: this.backObject(successData),\r\n\t\t\t\t\t\ttempFilePaths: tempFilePath\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (errorData.length > 0) {\r\n\t\t\t\t\tthis.$emit('fail', {\r\n\t\t\t\t\t\ttempFiles: this.backObject(errorData),\r\n\t\t\t\t\t\ttempFilePaths: errorTempFilePath\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取进度\r\n\t\t\t * @param {Object} progressEvent\r\n\t\t\t * @param {Object} index\r\n\t\t\t * @param {Object} type\r\n\t\t\t */\r\n\t\t\tsetProgress(progressEvent, index, type) {\r\n\t\t\t\tconst fileLenth = this.files.length\r\n\t\t\t\tconst percentNum = (index / fileLenth) * 100\r\n\t\t\t\tconst percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)\r\n\t\t\t\tlet idx = index\r\n\t\t\t\tif (!type) {\r\n\t\t\t\t\tidx = this.files.findIndex(p => p.uuid === progressEvent.tempFile.uuid)\r\n\t\t\t\t}\r\n\t\t\t\tif (idx === -1 || !this.files[idx]) return\r\n\t\t\t\t// fix by mehaotian 100 就会消失，-1 是为了让进度条消失\r\n\t\t\t\tthis.files[idx].progress = percentCompleted - 1\r\n\t\t\t\t// 上传中\r\n\t\t\t\tthis.$emit('progress', {\r\n\t\t\t\t\tindex: idx,\r\n\t\t\t\t\tprogress: parseInt(percentCompleted),\r\n\t\t\t\t\ttempFile: this.files[idx]\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 删除文件\r\n\t\t\t * @param {Object} index\r\n\t\t\t */\r\n\t\t\tdelFile(index) {\r\n\t\t\t\tthis.$emit('delete', {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\ttempFile: this.files[index],\r\n\t\t\t\t\ttempFilePath: this.files[index].url\r\n\t\t\t\t})\r\n\t\t\t\tthis.files.splice(index, 1)\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.setEmit()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取文件名和后缀\r\n\t\t\t * @param {Object} name\r\n\t\t\t */\r\n\t\t\tgetFileExt(name) {\r\n\t\t\t\tconst last_len = name.lastIndexOf('.')\r\n\t\t\t\tconst len = name.length\r\n\t\t\t\treturn {\r\n\t\t\t\t\tname: name.substring(0, last_len),\r\n\t\t\t\t\text: name.substring(last_len + 1, len)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 处理返回事件\r\n\t\t\t */\r\n\t\t\tsetEmit() {\r\n\t\t\t\tlet data = []\r\n\t\t\t\tif (this.returnType === 'object') {\r\n\t\t\t\t\tdata = this.backObject(this.files)[0]\r\n\t\t\t\t\tthis.localValue = data?data:null\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdata = this.backObject(this.files)\r\n\t\t\t\t\tif (!this.localValue) {\r\n\t\t\t\t\t\tthis.localValue = []\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.localValue = [...data]\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.$emit('update:modelValue', this.localValue)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef VUE3\r\n\t\t\t\tthis.$emit('input', this.localValue)\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 处理返回参数\r\n\t\t\t * @param {Object} files\r\n\t\t\t */\r\n\t\t\tbackObject(files) {\r\n\t\t\t\tlet newFilesData = []\r\n\t\t\t\tfiles.forEach(v => {\r\n\t\t\t\t\tnewFilesData.push({\r\n\t\t\t\t\t\textname: v.extname,\r\n\t\t\t\t\t\tfileType: v.fileType,\r\n\t\t\t\t\t\timage: v.image,\r\n\t\t\t\t\t\tname: v.name,\r\n\t\t\t\t\t\tpath: v.path,\r\n\t\t\t\t\t\tsize: v.size,\r\n\t\t\t\t\t\tfileID:v.fileID,\r\n\t\t\t\t\t\turl: v.url,\r\n\t\t\t\t\t\t// 修改删除一个文件后不能再上传的bug, #694\r\n            uuid: v.uuid,\r\n            status: v.status,\r\n            cloudPath: v.cloudPath\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\treturn newFilesData\r\n\t\t\t},\r\n\t\t\tasync getTempFileURL(fileList) {\r\n\t\t\t\tfileList = {\r\n\t\t\t\t\tfileList: [].concat(fileList)\r\n\t\t\t\t}\r\n\t\t\t\tconst urls = await uniCloud.getTempFileURL(fileList)\r\n\t\t\t\treturn urls.fileList[0].tempFileURL || ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetForm(name = 'uniForms') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false;\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.uni-file-picker {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow: hidden;\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.uni-file-picker__header {\r\n\t\tpadding-top: 5px;\r\n\t\tpadding-bottom: 10px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.file-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.file-count {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.is-add {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.icon-add {\r\n\t\twidth: 50px;\r\n\t\theight: 5px;\r\n\t\tbackground-color: #f1f1f1;\r\n\t\tborder-radius: 2px;\r\n\t}\r\n\r\n\t.rotate {\r\n\t\tposition: absolute;\r\n\t\ttransform: rotate(90deg);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-file-picker.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-file-picker.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650626149\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}