{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue", "mtime": 1752649761445}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnZVRhYmxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9QYWdlVGFibGUudnVlJw0KaW1wb3J0IHsgTG9hZGluZyB9IGZyb20gJ2VsZW1lbnQtdWknDQppbXBvcnQgVHJlZUJveCBmcm9tICdAL3ZpZXdzL2NvbXBvbmVudHMvVHJlZUJveC52dWUnDQppbXBvcnQgRGV0YWlsVmlldyBmcm9tICcuL0RldGFpbFZpZXcudnVlJw0KDQpjb25zdCBzdGF0dXMgPSB7ICcxJzogJ+WcqOiBjCcsICc1JzogJ+emu+iBjCcsICc4JzogJ+emgeeUqCcgfQ0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgUGFnZVRhYmxlLCBUcmVlQm94LCBEZXRhaWxWaWV3IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZvcm1MYWJlbFdpZHRoOiAnMTAwcHgnLA0KICAgICAgcWZvcm06IHsNCiAgICAgICAga2V5d29yZDogbnVsbCwNCiAgICAgICAgdGltZUJlZ2luOiBudWxsLA0KICAgICAgICB0aW1lRW5kOiBudWxsLA0KICAgICAgICBkYXRhVHlwZTogbnVsbA0KICAgICAgfSwNCiAgICAgIGRhdGVSYW5nZTogbnVsbCwNCiAgICAgIGl0ZW1zOiBbXSwNCiAgICAgIGRlcHRUcmVlOiBbXSwNCiAgICAgIHJlZ2lvbk9wdGlvbnM6IFtdLA0KICAgICAgZnVsbHNjcmVlbkxvYWRpbmc6IGZhbHNlDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMubG9hZERlcHRUcmVlKCkNCiAgICB0aGlzLmxvYWRSZWdpb25PcHRpb25zKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnNlYXJjaCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBsb2FkRGVwdFRyZWUoKSB7DQogICAgICB0aGlzLiRodHRwKCcvc3lzL2RlcHQvdHJlZUJ5VHlwZS8xJykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmRlcHRUcmVlID0gcmVzDQogICAgICB9KS5jYXRjaCgoKSA9PiB7IHRoaXMuJGFsZXJ0KCfliqDovb3mnLrmnoTmoJHlh7rplJknKSB9KQ0KICAgIH0sDQogICAgbG9hZFJlZ2lvbk9wdGlvbnMoKSB7DQogICAgICB0aGlzLiRodHRwKCcvYW0vcmVnaW9uL2xpc3QnKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMucmVnaW9uT3B0aW9ucyA9IHJlcyB8fCBbXQ0KICAgICAgfSkuY2F0Y2goKCkgPT4geyB0aGlzLiRhbGVydCgn5Yqg6L295Yy65Z+f5YiX6KGo5Ye66ZSZJykgfSkNCiAgICB9LA0KICAgIGNvbEdlbmRlcihfLCBfXywgdikgew0KICAgICAgcmV0dXJuIHYgPT09ICcxJyA/ICfnlLcnIDogdiA9PT0gJzInID8gJ+WlsycgOiAnJw0KICAgIH0sDQogICAgZm5TdGF0dXMoXywgX18sIHYpIHsNCiAgICAgIHJldHVybiBzdGF0dXNbdl0gfHwgJycNCiAgICB9LA0KICAgIGhhbmRsZURhdGVSYW5nZUNoYW5nZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy5xZm9ybS50aW1lQmVnaW4gPSB2YWxbMF0NCiAgICAgICAgdGhpcy5xZm9ybS50aW1lRW5kID0gdmFsWzFdDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnFmb3JtLnRpbWVCZWdpbiA9IG51bGwNCiAgICAgICAgdGhpcy5xZm9ybS50aW1lRW5kID0gbnVsbA0KICAgICAgfQ0KICAgIH0sDQogICAgZm9ybWF0RGF0ZVRpbWUoZGF0ZVRpbWUpIHsNCiAgICAgIGlmICghZGF0ZVRpbWUpIHJldHVybiAnJw0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVUaW1lKQ0KICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKQ0KICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpDQogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkYXRlLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGF0ZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGF0ZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX0gJHtob3Vyc306JHttaW51dGVzfToke3NlY29uZHN9YA0KICAgIH0sDQogICAgc2VhcmNoKCkgew0KICAgICAgdGhpcy4kcmVmcy5ncmlkLnNlYXJjaCh0aGlzLnFmb3JtKQ0KICAgIH0sDQogICAgc2VsZWN0aW9uQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMuaXRlbXMgPSB2IHx8IFtdDQogICAgfSwNCiAgICBkb3dubG9hZCgpIHsNCiAgICAgIGlmICh0aGlzLml0ZW1zLmxlbmd0aCA9PT0gMCkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Iez5bCR6KaB6YCJ5oup5LiA5p2h6LWE5Lqn6K6w5b2VJykNCiAgICAgIGNvbnN0IGlkcyA9IFtdDQogICAgICB0aGlzLml0ZW1zLmZvckVhY2gociA9PiBpZHMucHVzaChyLmlkKSkNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ato+WcqOWvvOWHuuaWh+S7tu+8jOivt+iAkOW/g+etieW+hS4uLicgfSkNCiAgICAgIHRoaXMuJGphc3Blcih7IHVybDogJy9ycC9leHBvcnRCYXRjaCcsIGRhdGE6IGlkcywgcmVzcG9uc2VUeXBlOiAnYmxvYicgfSkudGhlbihibG9iID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICB0aGlzLiRzYXZlQXMoYmxvYiwgJ+i1hOS6p+e7iOerr+acuue7keWumuaYjue7hi54bHN4JykNCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85Ye655Sf5oiQ5Ye66ZSZOicgKyBlcnIpDQogICAgICB9KQ0KICAgIH0sDQogICAgZXhwb3J0UXVlcnkoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmgqjnoa7lrpropoHmoLnmja7ov5nkupvmnaHku7blr7zlh7rlkJc/JywgJ+aPkOekuicsIHsgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLCBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBjb25zdCBsb2FkSW5zdCA9IExvYWRpbmcuc2VydmljZSh7IGZ1bGxzY3JlZW46IHRydWUsIHRleHQ6ICfmraPlnKjlr7zlh7rmlofku7bvvIzor7fogJDlv4PnrYnlvoUuLi4nIH0pDQogICAgICAgIHRoaXMuJGphc3Blcih7IHVybDogJy9ycC9leHBvcnRBbGwnLCBkYXRhOiB0aGlzLnFmb3JtLCByZXNwb25zZVR5cGU6ICdibG9iJyB9KS50aGVuKGJsb2IgPT4gew0KICAgICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgICAgICB0aGlzLiRzYXZlQXMoYmxvYiwgJ+i1hOS6p+e7iOerr+acuue7keWumuaYjue7hi54bHN4JykNCiAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85Ye655Sf5oiQ5Ye66ZSZOicgKyBlcnIpDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgYmF0Y2hEZWwoKSB7DQogICAgICBpZiAodGhpcy5pdGVtcy5sZW5ndGggPT09IDApIHJldHVybiB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+iHs+WwkeimgemAieaLqeS4gOadoei1hOS6p+iusOW9lScpDQogICAgICB0aGlzLiRjb25maXJtKCfmgqjnoa7lrpropoHmsLjkuYXmibnph4/liKDpmaTov5nkupvotYTkuqflkJc/JywgJ+aPkOekuicsIHsgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLCBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBjb25zdCBpZHMgPSBbXQ0KICAgICAgICB0aGlzLml0ZW1zLmZvckVhY2gociA9PiB7IGlkcy5wdXNoKHIuaWQpIH0pDQogICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvcnAvZGVsZXRlcycsIGRhdGE6IGlkcyB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmibnph4/liKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2goKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pDQogICAgfSwNCiAgICB2aWV3SXRlbShpdGVtKSB7DQogICAgICAvLyDmo4Dmn6UgaWQg5piv5ZCm5pyJ5pWIDQogICAgICBpZiAoIWl0ZW0gfHwgIWl0ZW0uaWQgfHwgaXRlbS5pZCA9PT0gJ3VuZGVmaW5lZCcpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5peg5pWI55qE6LWE5LqnSUTvvIzml6Dms5Xmn6XnnIvor6bmg4UnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuZnVsbHNjcmVlbkxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLiRodHRwKCcvYW0vYXNzZXQvZ2V0LycgKyBpdGVtLmlkKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuZnVsbHNjcmVlbkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICBpZiAocmVzLmNvZGUgPiAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5kZXRhaWxWaWV3LnNob3cocmVzLmRhdGEpDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOi2heaXticpDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["sn.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sn.vue", "sourceRoot": "src/views/rp/asset", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-header\">\r\n      <div class=\"page-tollbar\">\r\n        <div class=\"opt\">\r\n          <el-button-group>\r\n            <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"batchDel\">删除</el-button>\r\n            <el-button type=\"success\" size=\"mini\" icon=\"el-icon-download\" @click=\"download\">选中导出</el-button>\r\n            <el-button type=\"success\" size=\"mini\" icon=\"el-icon-download\" @click=\"exportQuery\">根据条件导出</el-button>\r\n          </el-button-group>\r\n        </div>\r\n        <div class=\"search\">\r\n          <el-button-group>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          </el-button-group>\r\n        </div>\r\n      </div>\r\n      <div class=\"page-filter\">\r\n        <el-form :model=\"qform\" inline size=\"mini\" label-width=\"110px\" @submit.native.prevent=\"search\">\r\n          <el-form-item label=\"检索：\">\r\n            <el-input v-model=\"qform.keyword\" clearable placeholder=\"输入资产编码、名称、型号、时间、姓名等关键字\" autocomplete=\"off\"\r\n              style=\"width:360px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"数据类型：\">\r\n            <el-select v-model=\"qform.dataType\" clearable placeholder=\"请选择\" style=\"width:180px;\">\r\n              <el-option :value=\"1\" label=\"已领用已绑定数据\"></el-option>\r\n              <el-option :value=\"2\" label=\"已领用未绑定数据\"></el-option>\r\n              <el-option :value=\"3\" label=\"没有绑定设备的网点\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"网点名称：\">\r\n            <el-input v-model=\"qform.locationName\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点地址：\">\r\n            <el-input v-model=\"qform.locationAddress\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点区域：\">\r\n            <el-select v-model=\"qform.locationRegion\" clearable placeholder=\"请选择区域\" style=\"width:150px;\">\r\n              <el-option v-for=\"item in regionOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"网点备注：\">\r\n            <el-input v-model=\"qform.locationMemo\" clearable placeholder=\"请输入网点备注\" autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属部门：\">\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点添加时间：\" style=\"margin-left: 50px;\">\r\n            <el-date-picker v-model=\"dateRange\" type=\"daterange\" range-separator=\"至\" start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" clearable editable\r\n              style=\"width: 360px;\" @change=\"handleDateRangeChange\"></el-date-picker>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <div class=\"page-body\">\r\n      <page-table ref=\"grid\" v-table-height size=\"mini\" path=\"/rp/asset/getAssetSnPage\" stripe border\r\n        highlight-current-row @selection-change=\"selectionChange\">\r\n        <el-table-column type=\"selection\" width=\"45\" fixed=\"left\" align=\"center\" />\r\n        <el-table-column label=\"绑定姓名\" prop=\"name\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.name || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"绑定帐号\" prop=\"account\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.account || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n        <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n        <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.deptName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"销售终端编号\" prop=\"nowSn\" width=\"110\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.nowSn || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.typeName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link v-if=\"scope.row.no\" type=\"danger\" size=\"mini\" @click.stop=\"viewItem(scope.row)\">{{ scope.row.no }}</el-link>\r\n            <span v-else></span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"assetName\" min-width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.assetName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"规格型号\" prop=\"spec\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.spec || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点名称\" prop=\"locationName\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点地址\" prop=\"locationAddress\" min-width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationAddress || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点区域\" prop=\"locationRegion\" width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"网点备注\" prop=\"locationMemo\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationMemo || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点添加时间\" prop=\"time\" width=\"140\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"绑定时间\" prop=\"bindTime\" width=\"140\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.bindTime ? formatDateTime(scope.row.bindTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"是否定位\" prop=\"whetherLocation\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.whetherLocation || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"定位地址\" prop=\"locAddr\" min-width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locAddr || '' }}\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n      <detail-view ref=\"detailView\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport { Loading } from 'element-ui'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport DetailView from './DetailView.vue'\r\n\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, DetailView },\r\n  data() {\r\n    return {\r\n      formLabelWidth: '100px',\r\n      qform: {\r\n        keyword: null,\r\n        timeBegin: null,\r\n        timeEnd: null,\r\n        dataType: null\r\n      },\r\n      dateRange: null,\r\n      items: [],\r\n      deptTree: [],\r\n      regionOptions: [],\r\n      fullscreenLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n    this.loadRegionOptions()\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegionOptions() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionOptions = res || []\r\n      }).catch(() => { this.$alert('加载区域列表出错') })\r\n    },\r\n    colGender(_, __, v) {\r\n      return v === '1' ? '男' : v === '2' ? '女' : ''\r\n    },\r\n    fnStatus(_, __, v) {\r\n      return status[v] || ''\r\n    },\r\n    handleDateRangeChange(val) {\r\n      if (val) {\r\n        this.qform.timeBegin = val[0]\r\n        this.qform.timeEnd = val[1]\r\n      } else {\r\n        this.qform.timeBegin = null\r\n        this.qform.timeEnd = null\r\n      }\r\n    },\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    search() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    selectionChange(v) {\r\n      this.items = v || []\r\n    },\r\n    download() {\r\n      if (this.items.length === 0) return this.$message.warning('至少要选择一条资产记录')\r\n      const ids = []\r\n      this.items.forEach(r => ids.push(r.id))\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: '/rp/exportBatch', data: ids, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '资产终端机绑定明细.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    exportQuery() {\r\n      this.$confirm('您确定要根据这些条件导出吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n        this.$jasper({ url: '/rp/exportAll', data: this.qform, responseType: 'blob' }).then(blob => {\r\n          loadInst.close()\r\n          this.$saveAs(blob, '资产终端机绑定明细.xlsx')\r\n        }).catch(err => {\r\n          loadInst.close()\r\n          this.$message.error('导出生成出错:' + err)\r\n        })\r\n      })\r\n    },\r\n    batchDel() {\r\n      if (this.items.length === 0) return this.$message.warning('至少要选择一条资产记录')\r\n      this.$confirm('您确定要永久批量删除这些资产吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        const ids = []\r\n        this.items.forEach(r => { ids.push(r.id) })\r\n        this.$http({ url: '/rp/deletes', data: ids }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('批量删除成功')\r\n            this.search()\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    viewItem(item) {\r\n      // 检查 id 是否有效\r\n      if (!item || !item.id || item.id === 'undefined') {\r\n        this.$message.error('无效的资产ID，无法查看详情')\r\n        return\r\n      }\r\n      this.fullscreenLoading = true\r\n      this.$http('/am/asset/get/' + item.id).then(res => {\r\n        this.fullscreenLoading = false\r\n        if (res.code > 0 && res.data) {\r\n          this.$refs.detailView.show(res.data)\r\n        }\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n        this.$message.error('网络超时')\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}