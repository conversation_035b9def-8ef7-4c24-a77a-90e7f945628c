<view class="container"><block wx:if="{{index==0}}"><view class="list-block"><block wx:if="{{$root.g0}}"><view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">盘点名称：</view><view class="text">{{item.$orig.name}}</view><view class="time">{{item.m0}}</view></view><view class="row"><view class="label">负责人：</view><view class="text">{{item.$orig.userName}}</view><view class="button-sp-area" style="float:right;"><block wx:if="{{item.$orig.status=='1'}}"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['inventoryTask',['$event']]]]]}}" bindtap="__e">待盘点</button></block><block wx:else><block wx:if="{{item.$orig.status=='2'}}"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['inventoryTask',['$event']]]]]}}" bindtap="__e">正在盘点</button></block><block wx:else><block wx:if="{{item.$orig.status=='3'}}"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['inventoryTask',['$event']]]]]}}" bindtap="__e">已盘点</button></block></block></block></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="90a48b02-1" bind:__l="__l" vue-slots="{{['default']}}">近期没有盘点任务</uni-text></block></view></block></view>