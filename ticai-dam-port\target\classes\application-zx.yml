server:
  port: 20005
spring:
  datasource-master:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #    jdbc-url: ***********************************************************************************************************************************************
    #    username: ziyuan
    #    password:  ZiYuan2022
    jdbc-url: *********************************************************************************************************************************************
    username: root
    password: 123456
    initial-size: 10 #初始化连接
    max-idle: 20 #最大空闲连接
    min-idle: 5 #最小空闲连接
    max-active: 200 #最大连接数量
    log-abandoned: true #是否在自动回收超时连接的时候打印连接的超时错误
    remove-abandoned: true #是否自动回收超时连接
    remove-abandoned-timeout: 180 #超时时间(以秒数为单位)
    max-wait: 10000 #超时等待时间以毫秒为单位 6000毫秒/1000等于60秒
    test-while-idle: true
    connection-test-query: select now()  #检测数据库的查询语句
    test-on-borrow: true
    min-evictable-idle-time-millis: 600000 #每隔五分钟检测空闲超过10分钟的连接
    time-between-eviction-runs-millis: 300000
    maximum-pool-size: 50  #池中最大连接数（包括空闲和正在使用的连接）默认值是10
    minimum-idle: 10  #池中最小空闲连接数量。默认值10
    pool-name: zy-ds-pool  #连接池的名字。
    auto-commit: true  #是否自动提交池中返回的连接。默认值为true
    idle-timeout: 10000  #空闲时间，毫秒
    max-lifetime: 500000 #连接池中连接的最大生命周期。
    connection-timeout: 20000  #连接超时时间，毫秒。默认值为30s

  redis:
    database: 0 #数据库索引（默认为0）
    host: 127.0.0.1
    port: 6378
    password: xudejian
    pool:
      max-active: 500 #连接池最大连接数（使用负值表示没有限制）
      max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-idle: 20 #连接池中的最大空闲连接
      min-idle: 10 #连接池中的最小空闲连接
      timeout: 5000 #连接超时时间（毫秒）

file:
  path: /data/attach
  context: /attach

logging:
  level:
    org.springframework.boot.autoconfigure: INFO

# 注册中心
eureka:
  client:
    enabled: false
    serviceUrl:
      defaultZone: **************************************/eureka/
    tls:
      trust-store-password: abc
  instance:
    leaseRenewalIntervalInSeconds: 10
    metadataMap:
      instanceId: ${vcap.application.instance_id:${spring.application.name}:${spring.application.instance_id:${server.port}}}

weixin:
  appid: "wx6656f02e1b896e38"
  secret: "3a82f3ba9391256a7171a3dba2e20929"

amap:
  key: "b4ae4625e7f217801a29af656cbf8ce7"