(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7dd60e4a"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"50c3":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"0 10px"}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.searchAsset}},[t._v("查询资产")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.searchAsset(e)}}},[a("el-form-item",{attrs:{label:"终端号："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入终端号",autocomplete:"off"},model:{value:t.qform.terminalNo,callback:function(e){t.$set(t.qform,"terminalNo",e)},expression:"qform.terminalNo"}})],1),a("el-form-item",{attrs:{label:"资产编号："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入资产编号",autocomplete:"off"},model:{value:t.qform.no,callback:function(e){t.$set(t.qform,"no",e)},expression:"qform.no"}})],1),a("el-form-item",{attrs:{label:"业主名称："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入业主名称",autocomplete:"off"},model:{value:t.qform.locationName,callback:function(e){t.$set(t.qform,"locationName",e)},expression:"qform.locationName"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/trace/page",query:t.qform,stripe:"",border:"","highlight-current-row":""},on:{"row-click":t.viewAssetHistory}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"130","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"130","header-align":"center",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewAssetHistory(e.row)}}},[t._v(t._s(e.row.no))])]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"130",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewAssetHistory(e.row)}}},[t._v(t._s(e.row.name))])]}}])}),a("el-table-column",{attrs:{label:"规格型号",prop:"spec",width:"120","header-align":"center"}}),a("el-table-column",{attrs:{label:"入库日期",prop:"inDate",width:"120","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.inDate,"yyyy-MM-dd"))+" ")]}}])}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"使用人",prop:"useUserName",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"销售终端编号",prop:"terminalNo",width:"110","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用网点",prop:"locationName","min-width":"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"定位地址",prop:"locAddr","min-width":"250","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"small",hit:"",type:t.getAssetStatusType(e.row),"disable-transitions":""}},[t._v(t._s(t.getAssetStatusText(e.row)))])]}}])})],1)],1),a("el-dialog",{attrs:{title:"资产历史记录 - "+(t.currentAsset?t.currentAsset.name:""),visible:t.historyDialogVisible,width:"80%","close-on-click-modal":!1},on:{"update:visible":function(e){t.historyDialogVisible=e}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.historyLoading,expression:"historyLoading"}]},[t.currentAsset?a("div",[a("el-descriptions",{attrs:{column:4,border:""}},[a("el-descriptions-item",{attrs:{label:"资产编码"}},[t._v(t._s(t.currentAsset.no))]),a("el-descriptions-item",{attrs:{label:"资产名称"}},[t._v(t._s(t.currentAsset.name))]),a("el-descriptions-item",{attrs:{label:"资产类型"}},[t._v(t._s(t.currentAsset.typeName))]),a("el-descriptions-item",{attrs:{label:"规格型号"}},[t._v(t._s(t.currentAsset.spec))]),a("el-descriptions-item",{attrs:{label:"终端号"}},[t._v(t._s(t.currentAsset.terminalNo))]),a("el-descriptions-item",{attrs:{label:"使用网点"}},[t._v(t._s(t.currentAsset.locationName))]),a("el-descriptions-item",{attrs:{label:"定位地址"}},[t._v(t._s(t.currentAsset.locAddr))]),a("el-descriptions-item",{attrs:{label:"当前状态"}},[a("el-tag",{attrs:{size:"small",hit:"",type:t.getAssetStatusType(t.currentAsset),"disable-transitions":""}},[t._v(t._s(t.getAssetStatusText(t.currentAsset)))])],1)],1)],1):t._e(),a("el-table",{staticStyle:{width:"100%","margin-top":"15px"},attrs:{data:t.historyList,border:"",stripe:""}},[a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"操作类型",prop:"operationTypeName",width:"100",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"操作单号",prop:"operationNo",width:"150",align:"center"}}),a("el-table-column",{attrs:{label:"操作时间",prop:"operationTime",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.operationTime))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作人",prop:"operatorName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"终端号",prop:"terminalNo",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"业主名称",prop:"locationName",width:"150",align:"center"}}),a("el-table-column",{attrs:{label:"更新信息",prop:"updateInfo","min-width":"250","header-align":"center"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.exportHistory}},[t._v("导出")]),a("el-button",{on:{click:function(e){t.historyDialogVisible=!1}}},[t._v("关闭")])],1)])],1)},n=[],i=(a("99af"),a("fb6a"),a("ac1f"),a("4d90"),a("5319"),a("841c"),a("6ecd")),s=a("d2ed"),o=a("576f"),l={components:{PageTable:i["a"],AssetTypeChosen:s["a"]},data:function(){return{qform:{terminalNo:"",no:"",locationName:""},historyDialogVisible:!1,historyLoading:!1,currentAsset:null,historyList:[]}},mounted:function(){var t=this;this.$nextTick((function(){t.searchAsset()}))},methods:{searchAsset:function(){this.$refs.grid.search(this.qform)},viewAssetHistory:function(t){var e=this;this.currentAsset=t,this.historyDialogVisible=!0,this.historyLoading=!0,this.historyList=[];var a=t.id;if(!a)return this.historyLoading=!1,void this.$message.error("无法获取资产ID，请确保资产有ID");this.$http.post("/am/asset/trace/historyById/".concat(a)).then((function(t){e.historyLoading=!1,t.code>0?e.historyList=t.data||[]:e.$message.error(t.msg||"获取资产历史记录失败")})).catch((function(){e.historyLoading=!1,e.$message.error("网络错误，请稍后重试")}))},getAssetStatusType:function(t){return Object(o["c"])(t.status)},getAssetStatusText:function(t){return Object(o["b"])(t.status)},formatDate:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yyyy-MM-dd HH:mm:ss";if(!t)return"";var a=new Date(t),r=a.getFullYear(),n=String(a.getMonth()+1).padStart(2,"0"),i=String(a.getDate()).padStart(2,"0"),s=String(a.getHours()).padStart(2,"0"),o=String(a.getMinutes()).padStart(2,"0"),l=String(a.getSeconds()).padStart(2,"0");return"yyyy-MM-dd"===e?"".concat(r,"-").concat(n,"-").concat(i):"".concat(r,"-").concat(n,"-").concat(i," ").concat(s,":").concat(o,":").concat(l)},exportHistory:function(){var t=this;this.currentAsset&&this.currentAsset.id?this.$confirm("您确定要导出这个资产的历史记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=t.$loading({fullscreen:!0,text:"正在导出文件，请耐心等待..."});t.$jasper({url:"/am/asset/trace/exportByAssetId/".concat(t.currentAsset.id),responseType:"blob"}).then((function(a){e.close();var r=t.currentAsset.no||"未知编号",n="资产历史记录_".concat(r,"_").concat((new Date).toISOString().slice(0,10).replace(/-/g,""),".xlsx");t.$saveAs(a,n)})).catch((function(a){e.close(),t.$message.error("导出生成出错:"+a)}))})).catch((function(){})):this.$message.error("无法获取资产信息，请重新打开详情")}}},c=l,u=(a("8e79"),a("2877")),p=Object(u["a"])(c,r,n,!1,null,"0f89f8ae",null);e["default"]=p.exports},"576f":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return s}));var r={1:"闲置中",2:"使用中",3:"已借用",5:"维修中",6:"已锁定",7:"已损坏",8:"已报废"};function n(t){var e=[];for(var a in r)e.push({value:a,text:r[a]});return e}function i(t){switch(t){case"1":return"success";case"2":return"primary";case"3":return"warning";case"5":return"danger";case"6":return"secondary";case"7":return"warning";case"8":return"info"}return""}function s(t){return r[t]||""}},"5f26":function(t,e,a){},"6ecd":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[t.total>1?a("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),a("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},n=[],i=a("53ca"),s=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),o={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var a=this;if(this.path){var r={pageNumber:1},n=Object(i["a"])(t);"undefined"===n?r.pageNumber=1:"number"===n?r.pageNumber=t:"object"===n?(this.params=t,"number"===typeof e&&(r.pageNumber=e),"boolean"===typeof e&&this.empty()):r.pageNumber=t.pageNumber,this.pi=r.pageNumber,this.paging&&(this.params.pageNumber=r.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(s["a"])({url:this.path,data:this.params}).then((function(t){a.loading=!1,a.paging?a.renderPage(t):a.renderList(t.rows?t.rows:t),a.$emit("loaded",t)})).catch((function(t){a.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var a=[],r=0;r<e.length;r++)e[r][t]&&a.push(e[r][t]);return a}}},l=o,c=(a("b2d4"),a("2877")),u=Object(c["a"])(l,r,n,!1,null,"bdcc19d8",null);e["a"]=u.exports},"841c":function(t,e,a){"use strict";var r=a("d784"),n=a("825a"),i=a("1d80"),s=a("129f"),o=a("14c3");r("search",1,(function(t,e,a){return[function(e){var a=i(this),r=void 0==e?void 0:e[t];return void 0!==r?r.call(e,a):new RegExp(e)[t](String(a))},function(t){var r=a(e,t,this);if(r.done)return r.value;var i=n(t),l=String(this),c=i.lastIndex;s(c,0)||(i.lastIndex=0);var u=o(i,l);return s(i.lastIndex,c)||(i.lastIndex=c),null===u?-1:u.index}]}))},"8e79":function(t,e,a){"use strict";a("5f26")},ac65:function(t,e,a){},b2d4:function(t,e,a){"use strict";a("ac65")},d2ed:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-cascader",t._g(t._b({attrs:{options:t.options,props:t.props},on:{change:t.changeMe}},"el-cascader",t.$attrs,!1),t.$listeners))},n=[],i=a("b775"),s={name:"AssetTypeChosen",props:{anyNode:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},simple:{type:Boolean,default:!0}},data:function(){return{options:[],props:{checkStrictly:this.anyNode,multiple:this.multiple,value:"id"}}},mounted:function(){this.load()},methods:{load:function(){var t=this;Object(i["a"])("/am/asset/type/cascade").then((function(e){e.length&&(t.options=e)}))},changeMe:function(t){this.simple?this.$emit("input",t&&t.length?t[t.length-1]:null):this.$emit("input",t),this.$emit("selected",t)}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,n,!1,null,null,null);e["a"]=c.exports}}]);