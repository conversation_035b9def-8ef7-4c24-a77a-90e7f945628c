<view class="container"><view class="card"><view class="thumb"><open-data type="userAvatarUrl"></open-data></view><view class="info"><view style="text-align:left;">{{'姓名：'+(user.name||'***')+''}}</view><view class="btn-bar"><block wx:if="{{user.user=='0'}}"><button type="primary" size="mini" data-event-opts="{{[['tap',[['bindUser',['$event']]]]]}}" bindtap="__e">绑定用户</button></block><block wx:else><button type="warn" size="mini" data-event-opts="{{[['tap',[['unbindUser',['$event']]]]]}}" bindtap="__e">取消绑定</button></block></view></view></view><view class="btn-bar"><button type="warn" data-event-opts="{{[['tap',[['toLogout',['$event']]]]]}}" bindtap="__e">退出登录</button></view><uni-popup class="vue-ref" vue-id="24ff2508-1" type="dialog" background-color="#fff" mask-click="{{false}}" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="dialog-head">用户绑定</view><view class="dialog-body"><uni-forms class="vue-ref" vue-id="{{('24ff2508-2')+','+('24ff2508-1')}}" modelValue="{{formData}}" rules="{{formRules}}" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('24ff2508-3')+','+('24ff2508-2')}}" label="帐号" name="account" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('24ff2508-4')+','+('24ff2508-3')}}" type="text" placeholder="请输入登录帐号" value="{{formData.account}}" data-event-opts="{{[['^input',[['__set_model',['$0','account','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('24ff2508-5')+','+('24ff2508-2')}}" label="密码" name="password" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('24ff2508-6')+','+('24ff2508-5')}}" type="password" placeholder="请输入登录密码" value="{{formData.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms></view><view class="dialog-foot"><view class="btn-bar"><button size="mini" data-event-opts="{{[['tap',[['cancelBind',['$event']]]]]}}" bindtap="__e">取 消</button><button type="primary" size="mini" data-event-opts="{{[['tap',[['submitBind',['$event']]]]]}}" bindtap="__e">提 交</button></view></view></uni-popup><watermark vue-id="24ff2508-7" bind:__l="__l"></watermark></view>