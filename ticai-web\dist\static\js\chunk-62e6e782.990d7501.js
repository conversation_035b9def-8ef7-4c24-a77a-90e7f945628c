(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-62e6e782"],{"0900":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"0 0px"}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.download}},[t._v("选中导出")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.exportQuery}},[t._v("根据条件导出")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"资产编号："}},[a("el-input",{staticStyle:{width:"140px"},attrs:{clearable:"",placeholder:"请输入资产编号",autocomplete:"off"},model:{value:t.qform.assetNo,callback:function(e){t.$set(t.qform,"assetNo",e)},expression:"qform.assetNo"}})],1),a("el-form-item",{attrs:{label:"业主姓名："}},[a("el-input",{staticStyle:{width:"140px"},attrs:{clearable:"",placeholder:"请输入业主姓名",autocomplete:"off"},model:{value:t.qform.locationContact,callback:function(e){t.$set(t.qform,"locationContact",e)},expression:"qform.locationContact"}})],1),a("el-form-item",{attrs:{label:"网点名称："}},[a("el-input",{staticStyle:{width:"140px"},attrs:{clearable:"",placeholder:"请输入网点名称",autocomplete:"off"},model:{value:t.qform.locationName,callback:function(e){t.$set(t.qform,"locationName",e)},expression:"qform.locationName"}})],1),a("el-form-item",{attrs:{label:"押金状态："}},[a("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:t.qform.depositStatus,callback:function(e){t.$set(t.qform,"depositStatus",e)},expression:"qform.depositStatus"}},t._l(t.statusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"领用时间："}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.qdate,callback:function(e){t.qdate=e},expression:"qdate"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/consuming/deposit/page",query:t.qform,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),a("el-table-column",{attrs:{label:"资产编号",prop:"assetNo",width:"130",align:"center",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewAsset(e.row.asset)}}},[t._v(t._s(e.row.assetNo))])]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"assetName","min-width":"120","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"押金凭证号",prop:"depositNo",width:"120","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"押金金额",prop:"depositAmount",width:"80","header-align":"center",align:"right"}}),a("el-table-column",{attrs:{label:"领用单号",prop:"no",width:"140",align:"center"}}),a("el-table-column",{attrs:{label:"领用人",prop:"receiverName",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"领用时间",prop:"time",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"网点名称",prop:"locationName","min-width":"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"网点业主",prop:"locationContact",width:"90","header-align":"center"}}),a("el-table-column",{attrs:{label:"联系方式",prop:"locationPhone",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"押金状态",prop:"depositStatus",width:"80",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"small",hit:"",type:t.getStatusType(e.row),"disable-transitions":""}},[t._v(t._s(t.getStatusText(e.row)))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"70",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.viewItem(e.row)}}},[t._v("押金")])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"押金登记",width:"900px",visible:t.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.detailVisible=e}}},[a("el-form",{ref:"dataform",attrs:{size:"small","label-width":"140px",model:t.form}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"资产编号："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.assetNo,callback:function(e){t.$set(t.form,"assetNo",e)},expression:"form.assetNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"资产名称："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.assetName,callback:function(e){t.$set(t.form,"assetName",e)},expression:"form.assetName"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"领用单号："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.no,callback:function(e){t.$set(t.form,"no",e)},expression:"form.no"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"领用人："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.receiverName,callback:function(e){t.$set(t.form,"receiverName",e)},expression:"form.receiverName"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"领用时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.time,callback:function(e){t.$set(t.form,"time",e)},expression:"form.time"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"领用网点："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.locationName,callback:function(e){t.$set(t.form,"locationName",e)},expression:"form.locationName"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"网点业主："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.locationContact,callback:function(e){t.$set(t.form,"locationContact",e)},expression:"form.locationContact"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系方式："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.locationPhone,callback:function(e){t.$set(t.form,"locationPhone",e)},expression:"form.locationPhone"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"押金凭证号：",prop:"depositNo"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{"show-word-limit":"",clearable:"",placeholder:"请输入押金凭证号"},model:{value:t.form.depositNo,callback:function(e){t.$set(t.form,"depositNo",e)},expression:"form.depositNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"押金金额：",prop:"depositAmount"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入押金金额"},model:{value:t.form.depositAmount,callback:function(e){t.$set(t.form,"depositAmount",e)},expression:"form.depositAmount"}},[a("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"押金日期："}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择押金日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.form.depositDate,callback:function(e){t.$set(t.form,"depositDate",e)},expression:"form.depositDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.depositTime,callback:function(e){t.$set(t.form,"depositTime",e)},expression:"form.depositTime"}})],1)],1)],1)],1),a("upload-file",{attrs:{simple:"",multiple:"",type:"YJ"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.detailVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保 存")])],1)],1),a("asset-view",{ref:"assetView"})],1)},s=[],i=(a("b64b"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),o=a("c21a"),r=a("660a"),n=a("5c96"),c={0:"未登记",1:"已登记"},d={components:{PageTable:i["a"],AssetView:o["a"],UploadFile:r["a"]},data:function(){return{fullscreenLoading:!1,formLabelWidth:"110px",statusOptions:[],qform:{keyword:null},qdate:[],items:[],detailVisible:!1,form:{},fileList:[]}},watch:{qdate:function(t){this.qform.begin=t&&2===t.length?t[0]:null,this.qform.end=t&&2===t.length?t[1]:null}},mounted:function(){var t=this;Object.keys(c).forEach((function(e){t.statusOptions.push({value:e,text:c[e]})})),this.search()},methods:{getStatusType:function(t){switch(t.depositStatus){case"1":return"success"}return""},getStatusText:function(t){return c[t.depositStatus]||""},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(t){this.items=t||[]},print:function(){},download:function(){var t=this;if(0===this.items.length)return this.$message.warning("至少要选择一条押金记录");var e=[];this.items.forEach((function(t){return e.push(t.asset)}));var a=n["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/am/asset/consuming/deposit/exportBatch",data:e,responseType:"blob"}).then((function(e){a.close(),t.$saveAs(e,"资产押金明细.xlsx")})).catch((function(e){a.close(),t.$message.error("导出生成出错:"+e)}))},exportQuery:function(){var t=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=n["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});t.$jasper({url:"/am/asset/consuming/deposit/exportAll",data:t.qform,responseType:"blob"}).then((function(a){e.close(),t.$saveAs(a,"资产押金明细.xlsx")})).catch((function(a){e.close(),t.$message.error("导出生成出错:"+a)}))}))},viewAsset:function(t){this.$refs.assetView.show(t)},preview:function(){this.items.length&&this.viewItem(this.items[0])},viewItem:function(t){var e=this;this.fullscreenLoading=!0,this.$http({url:"/am/asset/consuming/deposit/get",data:{id:t.bill,rel:t.asset}}).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&(e.form=t.data,e.fileList=t.data.fileList,e.detailVisible=!0)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")}))},save:function(){var t=this;this.form.fileList=this.fileList,this.$http({url:"/am/asset/consuming/deposit/save",data:this.form}).then((function(e){e.code>0&&(t.visible=!1,t.$message.success("保存押金登记成功"),t.detailVisible=!1,t.search())}))}}},m=d,p=a("2877"),u=Object(p["a"])(m,l,s,!1,null,null,null);e["default"]=u.exports},c21a:function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"},{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"custom-class":"dialog-tab dialog-full",width:"1180px",visible:t.visible,"append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[t._v("资产详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":t.activeIndex,mode:"horizontal"},on:{select:t.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[t._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[t._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[t._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[t._v("附件信息")]),a("el-menu-item",{attrs:{index:"5"}},[t._v("地理位置")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-form",{ref:"baseform",attrs:{model:t.data,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.typeName,callback:function(e){t.$set(t.data,"typeName",e)},expression:"data.typeName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.no,callback:function(e){t.$set(t.data,"no",e)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.name,callback:function(e){t.$set(t.data,"name",e)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.deptName,callback:function(e){t.$set(t.data,"deptName",e)},expression:"data.deptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.regionName,callback:function(e){t.$set(t.data,"regionName",e)},expression:"data.regionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.sn,callback:function(e){t.$set(t.data,"sn",e)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.maDate,callback:function(e){t.$set(t.data,"maDate",e)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.guDate,callback:function(e){t.$set(t.data,"guDate",e)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.productDate,callback:function(e){t.$set(t.data,"productDate",e)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.takeDate,callback:function(e){t.$set(t.data,"takeDate",e)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boDate,callback:function(e){t.$set(t.data,"boDate",e)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boAmount,callback:function(e){t.$set(t.data,"boAmount",e)},expression:"data.boAmount"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.expiryMonth,callback:function(e){t.$set(t.data,"expiryMonth",e)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[t._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.financeDate,callback:function(e){t.$set(t.data,"financeDate",e)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.value,callback:function(e){t.$set(t.data,"value",e)},expression:"data.value"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.spec,callback:function(e){t.$set(t.data,"spec",e)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.brand,callback:function(e){t.$set(t.data,"brand",e)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.manu,callback:function(e){t.$set(t.data,"manu",e)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.invoice,callback:function(e){t.$set(t.data,"invoice",e)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.seller,callback:function(e){t.$set(t.data,"seller",e)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.voucher,callback:function(e){t.$set(t.data,"voucher",e)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.price,callback:function(e){t.$set(t.data,"price",e)},expression:"data.price"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.contract,callback:function(e){t.$set(t.data,"contract",e)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.memo,callback:function(e){t.$set(t.data,"memo",e)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDeptName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useDeptName,callback:function(e){t.$set(t.data,"useDeptName",e)},expression:"data.useDeptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUserName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useUserName,callback:function(e){t.$set(t.data,"useUserName",e)},expression:"data.useUserName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最后打印：",prop:"printTime"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.printTime,callback:function(e){t.$set(t.data,"printTime",e)},expression:"data.printTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原存放地址：",prop:"fromAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromAddr,callback:function(e){t.$set(t.data,"fromAddr",e)},expression:"data.fromAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"定位地址：",prop:"locAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locAddr,callback:function(e){t.$set(t.data,"locAddr",e)},expression:"data.locAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原备注：",prop:"fromMemo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromMemo,callback:function(e){t.$set(t.data,"fromMemo",e)},expression:"data.fromMemo"}})],1)],1)],1),t.data.location?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用网点：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationName,callback:function(e){t.$set(t.data,"locationName",e)},expression:"data.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点联系人：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationContact,callback:function(e){t.$set(t.data,"locationContact",e)},expression:"data.locationContact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点电话：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationPhone,callback:function(e){t.$set(t.data,"locationPhone",e)},expression:"data.locationPhone"}})],1)],1)],1):t._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},t._l(t.fields,(function(t,e){return a("element-view-item",{key:t.renderKey,attrs:{"current-item":t,index:e}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-table",{attrs:{data:t.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:t.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:t.colRelDescr}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===t.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("upload-file",{attrs:{multiple:"",disabled:"",type:"ASSET"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===t.activeIndex,expression:"activeIndex === '5'"}],staticStyle:{"min-height":"500px"}},[a("div",{ref:"map",style:{"min-height":"500px",height:"100%"}},[null==t.lnglat?a("div",{staticStyle:{"text-align":"center","line-height":"50px","font-size":"24px"}},[t._v("该资产还未定位")]):t._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===t.activeIndex,expression:"activeIndex === '6'"}],staticStyle:{"min-height":"500px"}},[a("div",{staticStyle:{width:"400px",height:"400px",margin:"45px auto"}},[a("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.imgQr||t.defaultQr}})])])],2):t._e()],1)},s=[],i=(a("d81d"),a("b0c0"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("1cfe")),o=a("660a"),r={components:{ElementViewItem:i["a"],UploadFile:o["a"]},data:function(){return{visible:!1,loading:!1,activeIndex:"1",data:{},regionText:"",latlng:"",fields:[],relList:[],fileList:[],attachContext:this.$store.getters.attachContext,omap:null,map:{center:null,zoom:15,satellite:!1,markers:[]},currPoint:null,lnglat:null,infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)}),defaultQr:"/images/default_qr.png",imgQr:null,address:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{show:function(t){var e=this;this.visible=!0,"string"===typeof t?(this.loading=!0,this.$http("/am/asset/get/"+t).then((function(t){e.loading=!1,t.code>0&&t.data?e.showItem(t.data):e.visible=!1})).catch((function(){e.loading=!1,e.$message.error("网络超时"),e.visible=!1}))):this.showItem(t)},showItem:function(t){this.regionText=(t.regionName||"")+"/"+(t.locationName||""),this.latlng=t.lat&&t.lng?t.lat+", "+t.lng:"",this.activeIndex="1",this.data=t,this.fields=t.attr?JSON.parse(t.attr):[];var e=[];t.relList&&t.relList.forEach((function(t){switch(t.type){case"1":t.the?e.push({id:t.id,type:"2",rel:t.the,name:t.name}):e.push({id:t.id,type:"1",rel:t.rel,name:t.name});break;case"2":t.the?e.push({id:t.id,type:"4",rel:t.the,name:t.name}):e.push({id:t.id,type:"3",rel:t.rel,name:t.name})}})),this.relList=e,this.fileList=t.fileList||[],this.lnglat=t.lng&&t.lng?new window.AMap.LngLat(t.lng,t.lat):null,this.lnglat&&null==this.omap&&this.initMap()},handleSelectMenu:function(t){this.activeIndex=t,"6"===t&&this.data&&this.data.id&&(this.imgQr=this.defaultQr,this.imgQr="/api/am/asset/qrcode/"+this.data.id)},colRelType:function(t){switch(t.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(t){switch(t.type){case"1":return"当前资产包含【"+t.name+"】";case"2":return"当前资产属于【"+t.name+"】";case"3":return"当前资产安装了【"+t.name+"】";case"4":return"当前资产运行与【"+t.name+"】"}return""},initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete)}))},mapComplete:function(t){var e=this;this.currPoint=new window.AMap.Marker({position:this.lnglat,label:{direction:"top",content:this.data.name},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.omap.setCenter(this.lnglat),window.AMap.plugin("AMap.Geocoder",(function(){var t=new window.AMap.Geocoder({city:"全国"});t.getAddress([e.data.lng,e.data.lat],(function(t,a){"complete"===t&&"OK"===a.info&&a.regeocode&&(e.address=a.regeocode.formattedAddress)}))})),this.currPoint.on("click",(function(t){var a='<div style="padding:20px 15px;">';a+="<div>经度："+e.data.lng+"</div>",a+="<div>纬度："+e.data.lat+"</div>",a+="<div>定位地址："+e.address+"</div>",a+="</div>",e.infoWindow.setContent(a),e.infoWindow.open(e.omap,e.lnglat)}))}}},n=r,c=a("2877"),d=Object(c["a"])(n,l,s,!1,null,"4dd0b724",null);e["a"]=d.exports}}]);