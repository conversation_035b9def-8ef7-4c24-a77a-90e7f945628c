(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c3066be4"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"23cf":function(t,e,i){"use strict";i("d61f")},"575b":function(t,e,i){"use strict";i("67d1")},"58db":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.visible?i("div",[i("el-dialog",{attrs:{fullscreen:"",visible:t.visible,"custom-class":"dialog-tab dialog-full","append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[i("template",{slot:"title"},[i("div",{staticClass:"header-bar"},[i("div",{staticClass:"caption"},[t._v("地图定位")]),i("div",{staticClass:"tabbar"},[i("el-checkbox",{model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},[t._v("启动地图定位")]),i("span",{staticStyle:{"margin-left":"60px"}}),i("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-s-promotion"},on:{click:t.confirmPin}},[t._v("提交本次定位")]),null!=t.lastPoint?i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:t.removePin}},[t._v("取消定位")]):t._e()],1)])]),i("div",{ref:"map",style:{height:t.bodyHeight+"px"}})],2)],1):t._e()},n=[],s=(i("d81d"),i("d3b7"),i("0643"),i("a573"),{data:function(){return{visible:!1,bodyHeight:document.documentElement.clientHeight-60,checked:!0,omap:null,map:{center:null,zoom:13,satellite:!1,markers:[]},lastPoint:null,currPoint:null,lastLngLat:null,currData:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete),t.omap.on("click",t.mapClick),t.showLast()}))},mapComplete:function(t){},show:function(t){this.visible=!0,this.lastLngLat=t&&t.lng&&t.lat?t:null,null==this.omap?this.initMap():this.showLast()},showLast:function(){if(this.lastLngLat){var t=new window.AMap.LngLat(this.lastLngLat.lng,this.lastLngLat.lat);this.lastPoint=new window.AMap.Marker({position:t,label:{direction:"top",content:"原位置"},icon:"/icons/pin-blue.png"}),this.omap.add(this.lastPoint),this.omap.setZoomAndCenter(16,t)}},mapClick:function(t){if(this.checked){var e=t.lnglat;this.currPoint&&this.omap.remove(this.currPoint),this.currPoint=new window.AMap.Marker({position:new window.AMap.LngLat(e.lng,e.lat),label:{direction:"top",content:"新位置"},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.currData={lnglat:e,address:null},this.getMapAddress()}},getMapAddress:function(){if(null!=this.currData&&null!=this.currData.lnglat){var t=[this.currData.lnglat.lng,this.currData.lnglat.lat],e=this;window.AMap.plugin("AMap.Geocoder",(function(){var i=new window.AMap.Geocoder({city:"全国"});i.getAddress(t,(function(t,i){"complete"===t&&"OK"===i.info&&i.regeocode&&(e.currData.address=i.regeocode.formattedAddress)}))}))}},confirmPin:function(){if(null==this.currData)return this.$message.warning("您还未点击地图定位");this.$emit("success",this.currData),this.visible=!1},removePin:function(){var t=this;this.$confirm("您确定要删除之前的定位吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$emit("success",{lnglat:null,address:null}),t.visible=!1})).catch((function(){}))}}}),o=s,l=(i("e9cc"),i("2877")),r=Object(l["a"])(o,a,n,!1,null,"236dde37",null);e["a"]=r.exports},"67d1":function(t,e,i){},"6ecd":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-container",{staticClass:"page-table-ctn"},[i("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?i("el-footer",{staticClass:"footer"},[i("div",{staticClass:"size-info"},[t.total>1?i("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),i("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},n=[],s=i("53ca"),o=(i("a9e3"),i("d3b7"),i("ac1f"),i("841c"),i("0643"),i("4e3e"),i("159b"),i("b775")),l={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var i=this;if(this.path){var a={pageNumber:1},n=Object(s["a"])(t);"undefined"===n?a.pageNumber=1:"number"===n?a.pageNumber=t:"object"===n?(this.params=t,"number"===typeof e&&(a.pageNumber=e),"boolean"===typeof e&&this.empty()):a.pageNumber=t.pageNumber,this.pi=a.pageNumber,this.paging&&(this.params.pageNumber=a.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(o["a"])({url:this.path,data:this.params}).then((function(t){i.loading=!1,i.paging?i.renderPage(t):i.renderList(t.rows?t.rows:t),i.$emit("loaded",t)})).catch((function(t){i.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var i=[],a=0;a<e.length;a++)e[a][t]&&i.push(e[a][t]);return i}}},r=l,c=(i("b2d4"),i("2877")),u=Object(c["a"])(r,a,n,!1,null,"bdcc19d8",null);e["a"]=u.exports},"841c":function(t,e,i){"use strict";var a=i("d784"),n=i("825a"),s=i("1d80"),o=i("129f"),l=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=s(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var s=n(t),r=String(this),c=s.lastIndex;o(c,0)||(s.lastIndex=0);var u=l(s,r);return o(s.lastIndex,c)||(s.lastIndex=c),null===u?-1:u.index}]}))},"94d2":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"layout-lr"},[i("div",{staticClass:"left"},[i("div",{staticClass:"head",staticStyle:{"padding-left":"30px"}},[t._v(" 点位分组 "),i("el-button",{staticStyle:{float:"right","margin-top":"10px","margin-right":"4px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:t.addGroup}})],1),i("div",{staticClass:"body"},[i("ul",[i("li",{staticClass:"all",on:{click:function(e){return t.showGroup()}}},[t._v("所有分组")]),t._l(t.groupList,(function(e){return i("li",{key:e.id,class:{act:t.activeItem&&t.activeItem.id==e.id},on:{click:function(i){return t.showGroup(e)}}},[t._v(t._s(e.name))])}))],2)])]),i("div",{staticClass:"center"},[i("div",{staticClass:"page-header"},[i("div",{staticClass:"page-tollbar"},[i("div",{staticClass:"opt"},[i("span",{staticClass:"point-group"},[t._v("当前分组："),i("b",[t._v(t._s(t.activeItem.name))])]),i("el-button-group",[t.activeItem&&t.activeItem.id&&"0"!=t.activeItem.id?[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:t.editGroup}},[t._v("编辑分组")]),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:t.removeGroup}},[t._v("删除分组")])]:t._e()],2)],1),i("div",{staticClass:"search",staticStyle:{width:"350px"}},[t.activeItem&&t.activeItem.id?[i("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:t.addPoint}},[t._v("新增点位")])]:t._e(),i("el-button-group",{staticStyle:{"margin-left":"30px"}},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.searchPoint}},[t._v("查询")])],1)],2)]),i("div",{staticClass:"page-filter"},[i("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[i("el-form-item",{attrs:{label:"快捷检索："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"请输入点位编码、名称、地址关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1),i("el-form-item",{attrs:{label:"状态："}},[i("el-select",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.qform.status,callback:function(e){t.$set(t.qform,"status",e)},expression:"qform.status"}},t._l(t.statusOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1)]),i("div",{staticClass:"page-body"},[i("page-table",{ref:"grid",attrs:{size:"mini",path:"/am/patrol/point/page",query:t.qform,stripe:"",border:"","max-height":t.tableHeight}},[i("el-table-column",{attrs:{label:"编码",prop:"no",width:"80",align:"center"}}),i("el-table-column",{attrs:{label:"名称",prop:"name",width:"130"}}),i("el-table-column",{attrs:{label:"地址",prop:"address","min-width":"200"}}),i("el-table-column",{attrs:{label:"状态",prop:"status",align:"center",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[t._v(" "+t._s("1"===i.status?"启用":"禁用")+" ")]}}])}),i("el-table-column",{attrs:{label:"操作",width:"220",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[i("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.appendItem1(a)}}},[t._v("指标")]),i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return e.stopPropagation(),t.editPoint(a)}}},[t._v("编辑")]),"1"==a.status?i("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(e){return e.stopPropagation(),t.shiftPoint(a,"2")}}},[t._v("禁用")]):t._e(),"2"==a.status?i("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return e.stopPropagation(),t.shiftPoint(a,"1")}}},[t._v("启用")]):t._e(),i("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return e.stopPropagation(),t.removePoint(a)}}},[t._v("删除")])]}}])})],1)],1)]),i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"分组信息",width:"600px",visible:t.groupVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.groupVisible=e}}},[i("el-form",{ref:"groupform",attrs:{model:t.groupData,rules:t.groupRules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"分组名称：",prop:"name"}},[i("el-input",{attrs:{maxlength:"20",autocomplete:"off"},model:{value:t.groupData.name,callback:function(e){t.$set(t.groupData,"name",e)},expression:"groupData.name"}})],1),i("el-form-item",{attrs:{label:"分组排序："}},[i("el-input-number",{attrs:{autocomplete:"off",min:1,max:999},model:{value:t.groupData.ord,callback:function(e){t.$set(t.groupData,"ord",e)},expression:"groupData.ord"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.groupVisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.saveGroup}},[t._v("确 定")])],1)],1),i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"点位信息",width:"700px",visible:t.pointVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.pointVisible=e}}},[i("el-form",{ref:"pointform",attrs:{model:t.pointData,rules:t.pointRules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"分组名称："}},[i("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.pointData.groupName,callback:function(e){t.$set(t.pointData,"groupName",e)},expression:"pointData.groupName"}})],1),i("el-form-item",{attrs:{label:"点位编码：",prop:"no"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{autocomplete:"off"},model:{value:t.pointData.no,callback:function(e){t.$set(t.pointData,"no",e)},expression:"pointData.no"}})],1),i("el-form-item",{attrs:{label:"点位名称：",prop:"name"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{autocomplete:"off"},model:{value:t.pointData.name,callback:function(e){t.$set(t.pointData,"name",e)},expression:"pointData.name"}})],1),i("el-form-item",{attrs:{label:"点位地址：",prop:"address"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{autocomplete:"off"},model:{value:t.pointData.address,callback:function(e){t.$set(t.pointData,"address",e)},expression:"pointData.address"}},[i("template",{slot:"append"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-map-location"},on:{click:t.mapPin}},[t._v("地图定位")])],1)],2)],1),i("el-form-item",{attrs:{label:"点位描述：",prop:"connent"}},[i("el-input",{attrs:{type:"textarea",rows:5,maxlength:"250","show-word-limit":"",placeholder:"请输入点位描述",autocomplete:"off"},model:{value:t.pointData.memo,callback:function(e){t.$set(t.pointData,"memo",e)},expression:"pointData.memo"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.pointVisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.savePoint}},[t._v("确 定")])],1)],1),i("map-location",{ref:"mapLocation",on:{success:t.pined}}),i("target",{ref:"target",on:{confirm:t.confirmKpi}})],1)},n=[],s=(i("b0c0"),i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("841c"),i("0643"),i("4e3e"),i("159b"),i("6ecd")),o=i("58db"),l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{visible:t.visible,title:"指标配置",width:"1100px",top:"10px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[t.names&&t.names.length>0?i("div",[t._l(t.names,(function(e,a){return i("el-tag",{key:a,staticClass:"name-item",attrs:{size:"small"}},[t._v(t._s(e))])})),i("el-divider")],2):t._e(),i("div",{staticClass:"tree-block"},[i("div",{staticClass:"tree-left"},[i("el-tree",{ref:"tree",attrs:{"default-expand-all":"","show-checkbox":"","node-key":"id",data:t.treeData},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.data;return i("span",{staticClass:"tree-node-line"},[i("span",[t._v(t._s(a.label))]),a.leaf?i("span",[i("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return e.stopPropagation(),function(){return t.showKpi(a)}()}}},[t._v("详情")])],1):t._e()])}}])})],1),i("div",{staticClass:"tree-right"},[i("div",{directives:[{name:"show",rawName:"v-show",value:null!=t.kpi.id,expression:"kpi.id != null"}]},[i("el-form",{attrs:{"label-width":"100px",size:"small"}},[i("el-row",[i("el-col",{attrs:{span:14}},[i("el-form-item",{attrs:{label:"指标编码："}},[i("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.kpi.no,callback:function(e){t.$set(t.kpi,"no",e)},expression:"kpi.no"}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"指标类型："}},[i("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.modeText,callback:function(e){t.modeText=e},expression:"modeText"}})],1)],1)],1),i("el-form-item",{attrs:{label:"指标名称："}},[i("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.kpi.name,callback:function(e){t.$set(t.kpi,"name",e)},expression:"kpi.name"}})],1),i("el-form-item",{attrs:{label:"指导意见："}},[i("el-input",{staticClass:"form-static",staticStyle:{},attrs:{type:"textarea",rows:5,readonly:""},model:{value:t.kpi.content,callback:function(e){t.$set(t.kpi,"content",e)},expression:"kpi.content"}})],1)],1),t.kpi.itemList&&t.kpi.itemList.length>0?i("div",[i("el-divider"),i("div",{staticStyle:{padding:"0 20px"}},t._l(t.kpi.itemList,(function(e){return i("div",{key:e.id,staticClass:"kpi-item"},[i("div",{class:t.itemCls}),i("div",{staticClass:"kpi-content"},[t._v(t._s(e.no)+"、 "+t._s(e.content))])])})),0)],1):t._e()],1)])]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确 定")])],1)])],1)},r=[],c={data:function(){return{visible:!1,treeData:[],names:[],kpis:[],extra:null,nodes:{},kpi:{},modeText:"",itemCls:""}},methods:{show:function(t){t||(t={}),this.names=t.names||[],this.kpis=t.kpis||[],this.extra=t,this.visible=!0,0===this.treeData.length?this.loadTree():this.loadCheck()},loadTree:function(){var t=this;this.$http("/am/patrol/kpi/tree").then((function(e){t.treeData=e,t.loadCheck()}))},confirm:function(){var t=[];this.$refs.tree.getCheckedNodes().forEach((function(e){e.leaf&&t.push(e.id)})),this.$emit("confirm",t,this.extra),this.visible=!1},loadCheck:function(){var t=this;this.$nextTick((function(){t.$refs.tree.setCheckedKeys(t.kpis)}))},showKpi:function(t){var e=this;this.kpi={},this.modeText="",this.$http("/am/patrol/kpi/get/"+t.id).then((function(t){t.code&&t.data&&(e.modeText={1:"单选",2:"多选",3:"判断",4:"填空"}[t.data.mode],e.itemCls="1"===t.data.mode?"kpi-item-radiobox":"2"===t.data.mode?"kpi-item-checkbox":"",e.kpi=t.data)}))}}},u=c,p=(i("23cf"),i("2877")),d=Object(p["a"])(u,l,r,!1,null,"51cf670d",null),m=d.exports,h={components:{PageTable:s["a"],MapLocation:o["a"],Target:m},data:function(){return{groupList:[],activeItem:{},tableHeight:Math.max(document.documentElement.clientHeight-300,200),groupVisible:!1,groupData:{},groupRules:{name:[{required:!0,message:"请输入分组名称",trigger:"blur"}]},targeVisible:!1,pointVisible:!1,pointData:{group:""},pointRules:{no:[{required:!0,message:"请输入点位编码",trigger:"blur"}],name:[{required:!0,message:"请输入点位名称",trigger:"blur"}],address:[{required:!0,message:"请输入点位地址",trigger:"blur"}]},qform:{keyword:""},statusOptions:[{value:"1",label:"启用"},{value:"2",label:"禁用"}]}},created:function(){this.loadGroup()},methods:{loadGroup:function(){var t=this;this.$http("/am/patrol/point/group/list").then((function(e){if(t.groupList=e||[],t.activeItem&&t.activeItem.id)for(var i=0;i<e.length;i++)if(e[i].id===t.activeItem.id){t.activeItem=e[i];break}}))},showGroup:function(t){this.activeItem=t||{},this.qform.group=t?t.id:null,this.qform.keyword="",this.searchPoint()},addGroup:function(){this.groupVisible=!0,this.groupData={ord:1}},editGroup:function(){if(this.activeItem.id){this.groupVisible=!0;var t=JSON.stringify(this.activeItem);this.groupData=JSON.parse(t)}},saveGroup:function(){var t=this;this.$refs.groupform.validate((function(e){e&&t.$http({url:"/am/patrol/point/group/save",data:t.groupData}).then((function(e){e.code>0&&(t.$message.success("保存分组信息成功"),t.groupVisible=!1,t.loadGroup())}))}))},removeGroup:function(){var t=this;this.$confirm("此操作将永久删除该分组, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/am/patrol/point/group/delete/"+t.activeItem.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.activeItem={},t.loadGroup())}))})).catch((function(){}))},searchPoint:function(){this.$refs.grid.search(this.qform)},addPoint:function(){this.pointVisible=!0,this.pointData={group:this.activeItem.id,groupName:this.activeItem.name}},editPoint:function(t){this.pointVisible=!0;var e=JSON.stringify(t);this.pointData=JSON.parse(e),this.$refs.pointform&&this.$refs.pointform.clearValidate()},savePoint:function(){var t=this;this.$refs.pointform.validate((function(e){if(e){if(!t.pointData.lat)return t.$message.warning("请点击地图定位");t.$http({url:"/am/patrol/point/save",data:t.pointData}).then((function(e){e.code>0&&(t.$message.success("保存分组信息成功"),t.pointVisible=!1,t.searchPoint())}))}}))},removePoint:function(t){var e=this;this.$confirm("此操作将永久删除该点位, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:"/am/patrol/point/delete/"+t.id}).then((function(t){t.code>0&&(e.$message.success("删除成功"),e.searchPoint())}))})).catch((function(){}))},shiftPoint:function(t,e){var i=this;this.$http({url:"/am/patrol/point/"+("2"===e?"disable":"enable")+"/"+t.id}).then((function(t){t.code>0&&(i.$message.success("操作成功"),i.searchPoint())}))},mapPin:function(){var t=this.pointData.lat?{lng:this.pointData.lng,lat:this.pointData.lat}:null;this.$refs.mapLocation.show(t)},pined:function(t){this.$set(this.pointData,"address",t.address),this.$set(this.pointData,"lng",t.lnglat?t.lnglat.lng:null),this.$set(this.pointData,"lat",t.lnglat?t.lnglat.lat:null)},appendItem1:function(t){var e=this;this.kpiIndex=1,this.$http("/am/patrol/kpi/asset/kpi/"+t.id).then((function(i){e.$refs.target.show({names:[t.name],kpis:i||[],items:[t],index:1})}))},confirmKpi:function(t,e){var i=this;if(1===e.index){var a={assetList:[],kpiList:[]};e.items.forEach((function(t){return a.assetList.push(t.id)})),t&&t.forEach((function(t){return a.kpiList.push(t)})),this.$http({url:"/am/patrol/kpi/asset/append",data:a}).then((function(t){t.code>0&&(i.$message.success("保存资产指标成功"),i.searchPoint())}))}}}},f=h,g=(i("575b"),Object(p["a"])(f,a,n,!1,null,null,null));e["default"]=g.exports},ac65:function(t,e,i){},b2d4:function(t,e,i){"use strict";i("ac65")},d61f:function(t,e,i){},dcb5:function(t,e,i){},e9cc:function(t,e,i){"use strict";i("dcb5")}}]);