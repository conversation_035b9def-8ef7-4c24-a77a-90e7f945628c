(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-62749a62"],{"06c5":function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));a("a630"),a("fb6a"),a("b0c0"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("3ca3");var i=a("6b75");function l(e,t){if(e){if("string"==typeof e)return Object(i["a"])(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?Object(i["a"])(e,t):void 0}}},"13d5":function(e,t,a){"use strict";var i=a("23e7"),l=a("d58f").left,o=a("a640"),n=a("ae40"),c=o("reduce"),r=n("reduce",{1:0});i({target:"Array",proto:!0,forced:!c||!r},{reduce:function(e){return l(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"20f9":function(e,t,a){"use strict";a("8a67")},2909:function(e,t,a){"use strict";a.d(t,"a",(function(){return r}));var i=a("6b75");function l(e){if(Array.isArray(e))return Object(i["a"])(e)}a("a4d3"),a("e01a"),a("d28b"),a("a630"),a("d3b7"),a("3ca3"),a("ddb0");function o(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}var n=a("06c5");function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function r(e){return l(e)||o(e)||Object(n["a"])(e)||c()}},"36ae":function(e,t,a){"use strict";a("6236")},4836:function(e,t,a){},"4df4":function(e,t,a){"use strict";var i=a("0366"),l=a("7b0b"),o=a("9bdd"),n=a("e95a"),c=a("50c4"),r=a("8418"),s=a("35a1");e.exports=function(e){var t,a,u,d,_,v,p=l(e),f="function"==typeof this?this:Array,m=arguments.length,b=m>1?arguments[1]:void 0,h=void 0!==b,g=s(p),D=0;if(h&&(b=i(b,m>2?arguments[2]:void 0,2)),void 0==g||f==Array&&n(g))for(t=c(p.length),a=new f(t);t>D;D++)v=h?b(p[D],D):p[D],r(a,D,v);else for(d=g.call(p),_=d.next,a=new f;!(u=_.call(d)).done;D++)v=h?o(d,b,[u.value,D],!0):u.value,r(a,D,v);return a.length=D,a}},"4e82":function(e,t,a){"use strict";var i=a("23e7"),l=a("1c0b"),o=a("7b0b"),n=a("d039"),c=a("a640"),r=[],s=r.sort,u=n((function(){r.sort(void 0)})),d=n((function(){r.sort(null)})),_=c("sort"),v=u||!d||!_;i({target:"Array",proto:!0,forced:v},{sort:function(e){return void 0===e?s.call(o(this)):s.call(o(this),l(e))}})},6236:function(e,t,a){},"6b75":function(e,t,a){"use strict";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}a.d(t,"a",(function(){return i}))},"6dfe":function(e,t,a){"use strict";a.r(t);var i,l,o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("div",{staticClass:"left-board"},[a("div",{staticClass:"logo-wrapper"},[a("div",{staticClass:"logo"},[a("img",{attrs:{src:"logo0.png",alt:"logo"}}),e._v("表单设计器："),a("span",[e._v(" "+e._s(e.formName))])])]),a("el-scrollbar",{staticClass:"left-scrollbar"},[a("div",{staticClass:"components-list"},e._l(e.leftComponents,(function(t,i){return a("div",{key:i},[a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v(e._s(t.title))],1),a("draggable",{staticClass:"components-draggable",attrs:{list:t.list,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(t.list,(function(t,i){return a("div",{key:i,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.__config__.tagIcon}}),e._v(e._s(t.__config__.label))],1)])})),0)],1)})),0)])],1),a("div",{staticClass:"center-board"},[a("div",{staticClass:"action-bar"},[a("el-button",{attrs:{icon:"el-icon-video-play",size:"mini",type:"primary"},on:{click:e.previewForm}},[e._v("预 览")]),a("el-button",{attrs:{icon:"el-icon-upload",size:"mini",type:"success"},on:{click:e.saveForm}},[e._v("保 存")]),a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:e.emptyForm}},[e._v("清空")])],1),a("el-scrollbar",{staticClass:"center-scrollbar"},[a("el-row",{staticClass:"center-board-row",attrs:{gutter:e.formConf.gutter}},[a("el-form",{attrs:{size:e.formConf.size,"label-position":e.formConf.labelPosition,disabled:e.formConf.disabled,"label-width":e.formConf.labelWidth+"px"}},[a("draggable",{staticClass:"drawing-board",attrs:{list:e.drawingList,animation:340,group:"componentsGroup"}},e._l(e.drawingList,(function(t,i){return a("draggable-item",{key:t.renderKey,attrs:{"drawing-list":e.drawingList,"current-item":t,index:i,"active-id":e.activeId,"form-conf":e.formConf},on:{activeItem:e.activeFormItem,copyItem:e.drawingItemCopy,deleteItem:e.drawingItemDelete}})})),1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.drawingList.length,expression:"!drawingList.length"}],staticClass:"empty-info"},[e._v("从左侧拖入或点选组件进行表单设计")])],1)],1)],1)],1),a("simple-right-panel",{attrs:{"active-data":e.activeData,"form-conf":e.formConf,"show-field":!!e.drawingList.length},on:{"fetch-data":e.fetchData}}),a("PreviewDialog",{attrs:{title:"表单预览",top:"10px",width:"1000px",visible:e.previewVisible,fields:e.previewFields},on:{"update:visible":function(t){e.previewVisible=t}}})],1)},n=[],c=(a("7db0"),a("c740"),a("a15b"),a("d81d"),a("13d5"),a("4e82"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("0643"),a("fffc"),a("4e3e"),a("a573"),a("9d4a"),a("159b"),a("b76a")),r=a.n(c),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-board"},[a("el-tabs",{staticClass:"center-tabs",model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"组件属性",name:"field"}})],1),a("div",{staticClass:"field-box"},[a("el-scrollbar",{staticClass:"right-scrollbar"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showField,expression:"showField"}],attrs:{size:"small","label-width":"90px"}},[void 0!==e.activeData.__config__.componentName?a("el-form-item",{attrs:{label:"组件名"}},[e._v(" "+e._s(e.activeData.__config__.componentName)+" ")]):e._e(),void 0!==e.activeData.__config__.label?a("el-form-item",{attrs:{label:"属性名称"}},[a("el-input",{attrs:{placeholder:"请输入属性名称"},on:{input:e.changeRenderKey},model:{value:e.activeData.__config__.label,callback:function(t){e.$set(e.activeData.__config__,"label",t)},expression:"activeData.__config__.label"}})],1):e._e(),void 0!==e.activeData.placeholder?a("el-form-item",{attrs:{label:"属性提示"}},[a("el-input",{attrs:{placeholder:"请输入属性提示"},on:{input:e.changeRenderKey},model:{value:e.activeData.placeholder,callback:function(t){e.$set(e.activeData,"placeholder",t)},expression:"activeData.placeholder"}})],1):e._e(),void 0!==e.activeData["start-placeholder"]?a("el-form-item",{attrs:{label:"开始占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["start-placeholder"],callback:function(t){e.$set(e.activeData,"start-placeholder",t)},expression:"activeData['start-placeholder']"}})],1):e._e(),void 0!==e.activeData["end-placeholder"]?a("el-form-item",{attrs:{label:"结束占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["end-placeholder"],callback:function(t){e.$set(e.activeData,"end-placeholder",t)},expression:"activeData['end-placeholder']"}})],1):e._e(),void 0!==e.activeData.__config__.span?a("el-form-item",{attrs:{label:"表单栅格"}},[a("el-slider",{attrs:{max:24,min:1,marks:{12:""}},on:{change:e.spanChange},model:{value:e.activeData.__config__.span,callback:function(t){e.$set(e.activeData.__config__,"span",t)},expression:"activeData.__config__.span"}})],1):e._e(),"rowFormItem"===e.activeData.__config__.layout&&void 0!==e.activeData.gutter?a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.activeData.gutter,callback:function(t){e.$set(e.activeData,"gutter",t)},expression:"activeData.gutter"}})],1):e._e(),"rowFormItem"===e.activeData.__config__.layout&&void 0!==e.activeData.type?a("el-form-item",{attrs:{label:"布局模式"}},[a("el-radio-group",{model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-radio-button",{attrs:{label:"default"}}),a("el-radio-button",{attrs:{label:"flex"}})],1)],1):e._e(),void 0!==e.activeData.justify&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"水平排列"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择水平排列"},model:{value:e.activeData.justify,callback:function(t){e.$set(e.activeData,"justify",t)},expression:"activeData.justify"}},e._l(e.justifyOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.align&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"垂直排列"}},[a("el-radio-group",{model:{value:e.activeData.align,callback:function(t){e.$set(e.activeData,"align",t)},expression:"activeData.align"}},[a("el-radio-button",{attrs:{label:"top"}}),a("el-radio-button",{attrs:{label:"middle"}}),a("el-radio-button",{attrs:{label:"bottom"}})],1)],1):e._e(),void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.activeData.__config__.labelWidth,callback:function(t){e.$set(e.activeData.__config__,"labelWidth",e._n(t))},expression:"activeData.__config__.labelWidth"}})],1):e._e(),e.activeData.style&&void 0!==e.activeData.style.width?a("el-form-item",{attrs:{label:"组件宽度"}},[a("el-input",{attrs:{placeholder:"请输入组件宽度",clearable:""},model:{value:e.activeData.style.width,callback:function(t){e.$set(e.activeData.style,"width",t)},expression:"activeData.style.width"}})],1):e._e(),void 0!==e.activeData.__vModel__?a("el-form-item",{attrs:{label:"默认值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData.__config__.defaultValue),placeholder:"请输入默认值"},on:{input:e.onDefaultValueInput}})],1):e._e(),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"至少应选"}},[a("el-input-number",{attrs:{value:e.activeData.min,min:0,placeholder:"至少应选"},on:{input:function(t){return e.$set(e.activeData,"min",t||void 0)}}})],1):e._e(),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"最多可选"}},[a("el-input-number",{attrs:{value:e.activeData.max,min:0,placeholder:"最多可选"},on:{input:function(t){return e.$set(e.activeData,"max",t||void 0)}}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.prepend?a("el-form-item",{attrs:{label:"前缀"}},[a("el-input",{attrs:{placeholder:"请输入前缀"},model:{value:e.activeData.__slot__.prepend,callback:function(t){e.$set(e.activeData.__slot__,"prepend",t)},expression:"activeData.__slot__.prepend"}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.append?a("el-form-item",{attrs:{label:"后缀"}},[a("el-input",{attrs:{placeholder:"请输入后缀"},model:{value:e.activeData.__slot__.append,callback:function(t){e.$set(e.activeData.__slot__,"append",t)},expression:"activeData.__slot__.append"}})],1):e._e(),void 0!==e.activeData["prefix-icon"]?a("el-form-item",{attrs:{label:"前图标"}},[a("el-input",{attrs:{placeholder:"请输入前图标名称"},model:{value:e.activeData["prefix-icon"],callback:function(t){e.$set(e.activeData,"prefix-icon",t)},expression:"activeData['prefix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("prefix-icon")}},slot:"append"},[e._v("选择")])],1)],1):e._e(),void 0!==e.activeData["suffix-icon"]?a("el-form-item",{attrs:{label:"后图标"}},[a("el-input",{attrs:{placeholder:"请输入后图标名称"},model:{value:e.activeData["suffix-icon"],callback:function(t){e.$set(e.activeData,"suffix-icon",t)},expression:"activeData['suffix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("suffix-icon")}},slot:"append"},[e._v("选择")])],1)],1):e._e(),void 0!==e.activeData["icon"]&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮图标"}},[a("el-input",{attrs:{placeholder:"请输入按钮图标名称"},model:{value:e.activeData["icon"],callback:function(t){e.$set(e.activeData,"icon",t)},expression:"activeData['icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("icon")}},slot:"append"},[e._v("选择")])],1)],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"选项分隔符"}},[a("el-input",{attrs:{placeholder:"请输入选项分隔符"},model:{value:e.activeData.separator,callback:function(t){e.$set(e.activeData,"separator",t)},expression:"activeData.separator"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最小行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最小行数"},model:{value:e.activeData.autosize.minRows,callback:function(t){e.$set(e.activeData.autosize,"minRows",t)},expression:"activeData.autosize.minRows"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最大行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最大行数"},model:{value:e.activeData.autosize.maxRows,callback:function(t){e.$set(e.activeData.autosize,"maxRows",t)},expression:"activeData.autosize.maxRows"}})],1):e._e(),e.isShowMin?a("el-form-item",{attrs:{label:"最小值"}},[a("el-input-number",{attrs:{placeholder:"最小值"},model:{value:e.activeData.min,callback:function(t){e.$set(e.activeData,"min",t)},expression:"activeData.min"}})],1):e._e(),e.isShowMax?a("el-form-item",{attrs:{label:"最大值"}},[a("el-input-number",{attrs:{placeholder:"最大值"},model:{value:e.activeData.max,callback:function(t){e.$set(e.activeData,"max",t)},expression:"activeData.max"}})],1):e._e(),void 0!==e.activeData.height?a("el-form-item",{attrs:{label:"组件高度"}},[a("el-input-number",{attrs:{placeholder:"高度"},on:{input:e.changeRenderKey},model:{value:e.activeData.height,callback:function(t){e.$set(e.activeData,"height",t)},expression:"activeData.height"}})],1):e._e(),e.isShowStep?a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{attrs:{placeholder:"步数"},model:{value:e.activeData.step,callback:function(t){e.$set(e.activeData,"step",t)},expression:"activeData.step"}})],1):e._e(),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"精度"}},[a("el-input-number",{attrs:{min:0,placeholder:"精度"},model:{value:e.activeData.precision,callback:function(t){e.$set(e.activeData,"precision",t)},expression:"activeData.precision"}})],1):e._e(),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮位置"}},[a("el-radio-group",{model:{value:e.activeData["controls-position"],callback:function(t){e.$set(e.activeData,"controls-position",t)},expression:"activeData['controls-position']"}},[a("el-radio-button",{attrs:{label:""}},[e._v("默认")]),a("el-radio-button",{attrs:{label:"right"}},[e._v("右侧")])],1)],1):e._e(),void 0!==e.activeData.maxlength?a("el-form-item",{attrs:{label:"最多输入"}},[a("el-input",{attrs:{placeholder:"请输入字符长度"},model:{value:e.activeData.maxlength,callback:function(t){e.$set(e.activeData,"maxlength",t)},expression:"activeData.maxlength"}},[a("template",{slot:"append"},[e._v("个字符")])],2)],1):e._e(),void 0!==e.activeData["active-text"]?a("el-form-item",{attrs:{label:"开启提示"}},[a("el-input",{attrs:{placeholder:"请输入开启提示"},model:{value:e.activeData["active-text"],callback:function(t){e.$set(e.activeData,"active-text",t)},expression:"activeData['active-text']"}})],1):e._e(),void 0!==e.activeData["inactive-text"]?a("el-form-item",{attrs:{label:"关闭提示"}},[a("el-input",{attrs:{placeholder:"请输入关闭提示"},model:{value:e.activeData["inactive-text"],callback:function(t){e.$set(e.activeData,"inactive-text",t)},expression:"activeData['inactive-text']"}})],1):e._e(),void 0!==e.activeData["active-value"]?a("el-form-item",{attrs:{label:"开启值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["active-value"]),placeholder:"请输入开启值"},on:{input:function(t){return e.onSwitchValueInput(t,"active-value")}}})],1):e._e(),void 0!==e.activeData["inactive-value"]?a("el-form-item",{attrs:{label:"关闭值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["inactive-value"]),placeholder:"请输入关闭值"},on:{input:function(t){return e.onSwitchValueInput(t,"inactive-value")}}})],1):e._e(),void 0!==e.activeData.type&&"el-date-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"时间类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择时间类型"},on:{change:e.dateTypeChange},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},e._l(e.dateOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.name?a("el-form-item",{attrs:{label:"文件字段名"}},[a("el-input",{attrs:{placeholder:"请输入上传文件字段名"},model:{value:e.activeData.name,callback:function(t){e.$set(e.activeData,"name",t)},expression:"activeData.name"}})],1):e._e(),void 0!==e.activeData.accept?a("el-form-item",{attrs:{label:"文件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择文件类型",clearable:""},model:{value:e.activeData.accept,callback:function(t){e.$set(e.activeData,"accept",t)},expression:"activeData.accept"}},[a("el-option",{attrs:{label:"图片",value:"image/*"}}),a("el-option",{attrs:{label:"视频",value:"video/*"}}),a("el-option",{attrs:{label:"音频",value:"audio/*"}}),a("el-option",{attrs:{label:"excel",value:".xls,.xlsx"}}),a("el-option",{attrs:{label:"word",value:".doc,.docx"}}),a("el-option",{attrs:{label:"pdf",value:".pdf"}}),a("el-option",{attrs:{label:"txt",value:".txt"}})],1)],1):e._e(),void 0!==e.activeData.__config__.fileSize?a("el-form-item",{attrs:{label:"文件大小"}},[a("el-input",{attrs:{placeholder:"请输入文件大小"},model:{value:e.activeData.__config__.fileSize,callback:function(t){e.$set(e.activeData.__config__,"fileSize",e._n(t))},expression:"activeData.__config__.fileSize"}},[a("el-select",{style:{width:"66px"},attrs:{slot:"append"},slot:"append",model:{value:e.activeData.__config__.sizeUnit,callback:function(t){e.$set(e.activeData.__config__,"sizeUnit",t)},expression:"activeData.__config__.sizeUnit"}},[a("el-option",{attrs:{label:"KB",value:"KB"}}),a("el-option",{attrs:{label:"MB",value:"MB"}}),a("el-option",{attrs:{label:"GB",value:"GB"}})],1)],1)],1):e._e(),void 0!==e.activeData.action?a("el-form-item",{attrs:{label:"上传地址"}},[a("el-input",{attrs:{placeholder:"请输入上传地址",clearable:""},model:{value:e.activeData.action,callback:function(t){e.$set(e.activeData,"action",t)},expression:"activeData.action"}})],1):e._e(),void 0!==e.activeData["list-type"]?a("el-form-item",{attrs:{label:"列表类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData["list-type"],callback:function(t){e.$set(e.activeData,"list-type",t)},expression:"activeData['list-type']"}},[a("el-radio-button",{attrs:{label:"text"}},[e._v("text")]),a("el-radio-button",{attrs:{label:"picture"}},[e._v("picture")]),a("el-radio-button",{attrs:{label:"picture-card"}},[e._v("picture-card")])],1)],1):e._e(),void 0!==e.activeData.type&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮类型"}},[a("el-select",{style:{width:"100%"},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-option",{attrs:{label:"primary",value:"primary"}}),a("el-option",{attrs:{label:"success",value:"success"}}),a("el-option",{attrs:{label:"warning",value:"warning"}}),a("el-option",{attrs:{label:"danger",value:"danger"}}),a("el-option",{attrs:{label:"info",value:"info"}}),a("el-option",{attrs:{label:"text",value:"text"}})],1)],1):e._e(),void 0!==e.activeData.__config__.buttonText?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"picture-card"!==e.activeData["list-type"],expression:"'picture-card' !== activeData['list-type']"}],attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.__config__.buttonText,callback:function(t){e.$set(e.activeData.__config__,"buttonText",t)},expression:"activeData.__config__.buttonText"}})],1):e._e(),"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.__slot__.default,callback:function(t){e.$set(e.activeData.__slot__,"default",t)},expression:"activeData.__slot__.default"}})],1):e._e(),void 0!==e.activeData["range-separator"]?a("el-form-item",{attrs:{label:"分隔符"}},[a("el-input",{attrs:{placeholder:"请输入分隔符"},model:{value:e.activeData["range-separator"],callback:function(t){e.$set(e.activeData,"range-separator",t)},expression:"activeData['range-separator']"}})],1):e._e(),void 0!==e.activeData["picker-options"]?a("el-form-item",{attrs:{label:"时间段"}},[a("el-input",{attrs:{placeholder:"请输入时间段"},model:{value:e.activeData["picker-options"].selectableRange,callback:function(t){e.$set(e.activeData["picker-options"],"selectableRange",t)},expression:"activeData['picker-options'].selectableRange"}})],1):e._e(),void 0!==e.activeData.format?a("el-form-item",{attrs:{label:"时间格式"}},[a("el-input",{attrs:{value:e.activeData.format,placeholder:"请输入时间格式"},on:{input:function(t){return e.setTimeValue(t)}}})],1):e._e(),["el-checkbox-group","el-radio-group","el-select"].indexOf(e.activeData.__config__.tag)>-1?[a("el-divider",[e._v("选项")]),a("draggable",{attrs:{list:e.activeData.__slot__.options,animation:340,group:"selectItem",handle:".option-drag"}},e._l(e.activeData.__slot__.options,(function(t,i){return a("div",{key:i,staticClass:"select-item"},[a("div",{staticClass:"select-line-icon option-drag"},[a("i",{staticClass:"el-icon-s-operation"})]),a("el-input",{attrs:{placeholder:"选项名",size:"small"},model:{value:t.label,callback:function(a){e.$set(t,"label",a)},expression:"item.label"}}),a("el-input",{attrs:{placeholder:"选项值",size:"small",value:t.value},on:{input:function(a){return e.setOptionValue(t,a)}}}),a("div",{staticClass:"close-btn select-line-icon",on:{click:function(t){return e.activeData.__slot__.options.splice(i,1)}}},[a("i",{staticClass:"el-icon-remove-outline"})])],1)})),0),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addSelectItem}},[e._v(" 添加选项 ")])],1),a("el-divider")]:e._e(),["el-cascader","el-table"].includes(e.activeData.__config__.tag)?[a("el-divider",[e._v("选项")]),e.activeData.__config__.dataType?a("el-form-item",{attrs:{label:"数据类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData.__config__.dataType,callback:function(t){e.$set(e.activeData.__config__,"dataType",t)},expression:"activeData.__config__.dataType"}},[a("el-radio-button",{attrs:{label:"dynamic"}},[e._v(" 动态数据 ")]),a("el-radio-button",{attrs:{label:"static"}},[e._v(" 静态数据 ")])],1)],1):e._e(),"dynamic"===e.activeData.__config__.dataType?[a("el-form-item",{attrs:{label:"接口地址"}},[a("el-input",{attrs:{title:e.activeData.__config__.url,placeholder:"请输入接口地址",clearable:""},on:{blur:function(t){return e.$emit("fetch-data",e.activeData)}},model:{value:e.activeData.__config__.url,callback:function(t){e.$set(e.activeData.__config__,"url",t)},expression:"activeData.__config__.url"}},[a("el-select",{style:{width:"85px"},attrs:{slot:"prepend"},on:{change:function(t){return e.$emit("fetch-data",e.activeData)}},slot:"prepend",model:{value:e.activeData.__config__.method,callback:function(t){e.$set(e.activeData.__config__,"method",t)},expression:"activeData.__config__.method"}},[a("el-option",{attrs:{label:"get",value:"get"}}),a("el-option",{attrs:{label:"post",value:"post"}}),a("el-option",{attrs:{label:"put",value:"put"}}),a("el-option",{attrs:{label:"delete",value:"delete"}})],1)],1)],1),a("el-form-item",{attrs:{label:"数据位置"}},[a("el-input",{attrs:{placeholder:"请输入数据位置"},on:{blur:function(t){return e.$emit("fetch-data",e.activeData)}},model:{value:e.activeData.__config__.dataPath,callback:function(t){e.$set(e.activeData.__config__,"dataPath",t)},expression:"activeData.__config__.dataPath"}})],1),e.activeData.props&&e.activeData.props.props?[a("el-form-item",{attrs:{label:"标签键名"}},[a("el-input",{attrs:{placeholder:"请输入标签键名"},model:{value:e.activeData.props.props.label,callback:function(t){e.$set(e.activeData.props.props,"label",t)},expression:"activeData.props.props.label"}})],1),a("el-form-item",{attrs:{label:"值键名"}},[a("el-input",{attrs:{placeholder:"请输入值键名"},model:{value:e.activeData.props.props.value,callback:function(t){e.$set(e.activeData.props.props,"value",t)},expression:"activeData.props.props.value"}})],1),a("el-form-item",{attrs:{label:"子级键名"}},[a("el-input",{attrs:{placeholder:"请输入子级键名"},model:{value:e.activeData.props.props.children,callback:function(t){e.$set(e.activeData.props.props,"children",t)},expression:"activeData.props.props.children"}})],1)]:e._e()]:e._e(),"static"===e.activeData.__config__.dataType?a("el-tree",{attrs:{draggable:"",data:e.activeData.options,"node-key":"id","expand-on-click-node":!1,"render-content":e.renderContent}}):e._e(),"static"===e.activeData.__config__.dataType?a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addTreeItem}},[e._v(" 添加父级 ")])],1):e._e(),a("el-divider")]:e._e(),void 0!==e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"选项样式"}},[a("el-radio-group",{model:{value:e.activeData.__config__.optionType,callback:function(t){e.$set(e.activeData.__config__,"optionType",t)},expression:"activeData.__config__.optionType"}},[a("el-radio-button",{attrs:{label:"default"}},[e._v(" 默认 ")]),a("el-radio-button",{attrs:{label:"button"}},[e._v(" 按钮 ")])],1)],1):e._e(),void 0!==e.activeData["active-color"]?a("el-form-item",{attrs:{label:"开启颜色"}},[a("el-color-picker",{model:{value:e.activeData["active-color"],callback:function(t){e.$set(e.activeData,"active-color",t)},expression:"activeData['active-color']"}})],1):e._e(),void 0!==e.activeData["inactive-color"]?a("el-form-item",{attrs:{label:"关闭颜色"}},[a("el-color-picker",{model:{value:e.activeData["inactive-color"],callback:function(t){e.$set(e.activeData,"inactive-color",t)},expression:"activeData['inactive-color']"}})],1):e._e(),void 0!==e.activeData.__config__.showLabel&&void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"显示标签"}},[a("el-switch",{model:{value:e.activeData.__config__.showLabel,callback:function(t){e.$set(e.activeData.__config__,"showLabel",t)},expression:"activeData.__config__.showLabel"}})],1):e._e(),void 0!==e.activeData.branding?a("el-form-item",{attrs:{label:"品牌烙印"}},[a("el-switch",{on:{input:e.changeRenderKey},model:{value:e.activeData.branding,callback:function(t){e.$set(e.activeData,"branding",t)},expression:"activeData.branding"}})],1):e._e(),void 0!==e.activeData["allow-half"]?a("el-form-item",{attrs:{label:"允许半选"}},[a("el-switch",{model:{value:e.activeData["allow-half"],callback:function(t){e.$set(e.activeData,"allow-half",t)},expression:"activeData['allow-half']"}})],1):e._e(),void 0!==e.activeData["show-text"]?a("el-form-item",{attrs:{label:"辅助文字"}},[a("el-switch",{on:{change:e.rateTextChange},model:{value:e.activeData["show-text"],callback:function(t){e.$set(e.activeData,"show-text",t)},expression:"activeData['show-text']"}})],1):e._e(),void 0!==e.activeData["show-score"]?a("el-form-item",{attrs:{label:"显示分数"}},[a("el-switch",{on:{change:e.rateScoreChange},model:{value:e.activeData["show-score"],callback:function(t){e.$set(e.activeData,"show-score",t)},expression:"activeData['show-score']"}})],1):e._e(),void 0!==e.activeData["show-stops"]?a("el-form-item",{attrs:{label:"显示间断点"}},[a("el-switch",{model:{value:e.activeData["show-stops"],callback:function(t){e.$set(e.activeData,"show-stops",t)},expression:"activeData['show-stops']"}})],1):e._e(),void 0!==e.activeData.range?a("el-form-item",{attrs:{label:"范围选择"}},[a("el-switch",{on:{change:e.rangeChange},model:{value:e.activeData.range,callback:function(t){e.$set(e.activeData,"range",t)},expression:"activeData.range"}})],1):e._e(),void 0!==e.activeData.__config__.border&&"default"===e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"是否带边框"}},[a("el-switch",{model:{value:e.activeData.__config__.border,callback:function(t){e.$set(e.activeData.__config__,"border",t)},expression:"activeData.__config__.border"}})],1):e._e(),"el-color-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"颜色格式"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择颜色格式",clearable:""},on:{change:e.colorFormatChange},model:{value:e.activeData["color-format"],callback:function(t){e.$set(e.activeData,"color-format",t)},expression:"activeData['color-format']"}},e._l(e.colorFormatOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0===e.activeData.size||"button"!==e.activeData.__config__.optionType&&!e.activeData.__config__.border&&"el-color-picker"!==e.activeData.__config__.tag&&"el-button"!==e.activeData.__config__.tag?e._e():a("el-form-item",{attrs:{label:"组件尺寸"}},[a("el-radio-group",{model:{value:e.activeData.size,callback:function(t){e.$set(e.activeData,"size",t)},expression:"activeData.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v(" 中等 ")]),a("el-radio-button",{attrs:{label:"small"}},[e._v(" 较小 ")]),a("el-radio-button",{attrs:{label:"mini"}},[e._v(" 迷你 ")])],1)],1),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"严格步数"}},[a("el-switch",{model:{value:e.activeData["step-strictly"],callback:function(t){e.$set(e.activeData,"step-strictly",t)},expression:"activeData['step-strictly']"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"任选层级"}},[a("el-switch",{model:{value:e.activeData.props.props.checkStrictly,callback:function(t){e.$set(e.activeData.props.props,"checkStrictly",t)},expression:"activeData.props.props.checkStrictly"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{model:{value:e.activeData.props.props.multiple,callback:function(t){e.$set(e.activeData.props.props,"multiple",t)},expression:"activeData.props.props.multiple"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"展示全路径"}},[a("el-switch",{model:{value:e.activeData["show-all-levels"],callback:function(t){e.$set(e.activeData,"show-all-levels",t)},expression:"activeData['show-all-levels']"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"可否筛选"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),void 0!==e.activeData.clearable?a("el-form-item",{attrs:{label:"显示清空"}},[a("el-switch",{model:{value:e.activeData.clearable,callback:function(t){e.$set(e.activeData,"clearable",t)},expression:"activeData.clearable"}})],1):e._e(),void 0!==e.activeData.__config__.showTip?a("el-form-item",{attrs:{label:"显示提示"}},[a("el-switch",{model:{value:e.activeData.__config__.showTip,callback:function(t){e.$set(e.activeData.__config__,"showTip",t)},expression:"activeData.__config__.showTip"}})],1):e._e(),"el-upload"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"多选文件"}},[a("el-switch",{model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData["auto-upload"]?a("el-form-item",{attrs:{label:"自动上传"}},[a("el-switch",{model:{value:e.activeData["auto-upload"],callback:function(t){e.$set(e.activeData,"auto-upload",t)},expression:"activeData['auto-upload']"}})],1):e._e(),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"能否搜索"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{on:{change:e.multipleChange},model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData.__config__.required?a("el-form-item",{attrs:{label:"是否必填"}},[a("el-switch",{model:{value:e.activeData.__config__.required,callback:function(t){e.$set(e.activeData.__config__,"required",t)},expression:"activeData.__config__.required"}})],1):e._e()],2)],1)],1),a("treeNode-dialog",{attrs:{visible:e.dialogVisible,title:"添加选项"},on:{"update:visible":function(t){e.dialogVisible=t},commit:e.addNode}}),a("icons-dialog",{attrs:{visible:e.iconsVisible,current:e.activeData[e.currentIconModel]},on:{"update:visible":function(t){e.iconsVisible=t},select:e.setIcon}})],1)},u=[],d=(a("99af"),a("caad"),a("b648")),_=a("ed08"),v=a("4df4e"),p={date:"yyyy-MM-dd",week:"yyyy 第 WW 周",month:"yyyy-MM",year:"yyyy",datetime:"yyyy-MM-dd HH:mm:ss",daterange:"yyyy-MM-dd",monthrange:"yyyy-MM",datetimerange:"yyyy-MM-dd HH:mm:ss"},f=["tinymce"],m={components:{draggable:r.a,TreeNodeDialog:d["a"],IconsDialog:v["a"]},props:{showField:{type:Boolean,default:!1},activeData:{type:Object,default:function(){}},formConf:{type:Object,default:function(){}}},data:function(){return{currentTab:"field",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:"日(date)",value:"date"},{label:"周(week)",value:"week"},{label:"月(month)",value:"month"},{label:"年(year)",value:"year"},{label:"日期时间(datetime)",value:"datetime"}],dateRangeTypeOptions:[{label:"日期范围(daterange)",value:"daterange"},{label:"月范围(monthrange)",value:"monthrange"},{label:"日期时间范围(datetimerange)",value:"datetimerange"}],colorFormatOptions:[{label:"hex",value:"hex"},{label:"rgb",value:"rgb"},{label:"rgba",value:"rgba"},{label:"hsv",value:"hsv"},{label:"hsl",value:"hsl"}],justifyOptions:[{label:"start",value:"start"},{label:"end",value:"end"},{label:"center",value:"center"},{label:"space-around",value:"space-around"},{label:"space-between",value:"space-between"}],layoutTreeProps:{label:function(e,t){var a=e.__config__;return e.componentName||"".concat(a.label,": ").concat(e.__vModel__)}}}},computed:{dateOptions:function(){return void 0!==this.activeData.type&&"el-date-picker"===this.activeData.__config__.tag?void 0===this.activeData["start-placeholder"]?this.dateTypeOptions:this.dateRangeTypeOptions:[]},activeTag:function(){return this.activeData.__config__.tag},isShowMin:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1},isShowMax:function(){return["el-input-number","el-slider","el-rate"].indexOf(this.activeTag)>-1},isShowStep:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1}},methods:{addReg:function(){this.activeData.__config__.regList.push({pattern:"",message:""})},addSelectItem:function(){this.activeData.__slot__.options.push({label:"",value:""})},addTreeItem:function(){++this.idGlobal,this.dialogVisible=!0,this.currentNode=this.activeData.options},renderContent:function(e,t){var a=this,i=t.node,l=t.data;t.store;return e("div",{class:"custom-tree-node"},[e("span",[i.label]),e("span",{class:"node-operation"},[e("i",{on:{click:function(){return a.append(l)}},class:"el-icon-plus",attrs:{title:"添加"}}),e("i",{on:{click:function(){return a.remove(i,l)}},class:"el-icon-delete",attrs:{title:"删除"}})])])},append:function(e){e.children||this.$set(e,"children",[]),this.dialogVisible=!0,this.currentNode=e.children},remove:function(e,t){this.activeData.__config__.defaultValue=[];var a=e.parent,i=a.data.children||a.data,l=i.findIndex((function(e){return e.id===t.id}));i.splice(l,1)},addNode:function(e){this.currentNode.push(e)},setOptionValue:function(e,t){e.value=Object(_["f"])(t)?+t:t},setDefaultValue:function(e){return Array.isArray(e)?e.join(","):"boolean"===typeof e?"".concat(e):e},onDefaultValueInput:function(e){Array.isArray(this.activeData.__config__.defaultValue)?this.$set(this.activeData.__config__,"defaultValue",e.split(",").map((function(e){return Object(_["f"])(e)?+e:e}))):["true","false"].indexOf(e)>-1?this.$set(this.activeData.__config__,"defaultValue",JSON.parse(e)):this.$set(this.activeData.__config__,"defaultValue",Object(_["f"])(e)?+e:e)},onSwitchValueInput:function(e,t){["true","false"].indexOf(e)>-1?this.$set(this.activeData,t,JSON.parse(e)):this.$set(this.activeData,t,Object(_["f"])(e)?+e:e)},setTimeValue:function(e,t){var a="week"===t?p.date:e;this.$set(this.activeData.__config__,"defaultValue",null),this.$set(this.activeData,"value-format",a),this.$set(this.activeData,"format",e)},spanChange:function(e){this.formConf.span=e},multipleChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[]:"")},dateTypeChange:function(e){this.setTimeValue(p[e],e)},rangeChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[this.activeData.min,this.activeData.max]:this.activeData.min)},rateTextChange:function(e){e&&(this.activeData["show-score"]=!1)},rateScoreChange:function(e){e&&(this.activeData["show-text"]=!1)},colorFormatChange:function(e){this.activeData.__config__.defaultValue=null,this.activeData["show-alpha"]=e.indexOf("a")>-1,this.activeData.__config__.renderKey=+new Date},openIconsDialog:function(e){this.iconsVisible=!0,this.currentIconModel=e},setIcon:function(e){this.activeData[this.currentIconModel]=e},changeRenderKey:function(){f.includes(this.activeData.__config__.tag)&&(this.activeData.__config__.renderKey=+new Date)}}},b=m,h=(a("7498"),a("2877")),g=Object(h["a"])(b,s,u,!1,null,"2a40cede",null),D=g.exports,y={formRef:"elForm",formModel:"formData",size:"medium",labelPosition:"right",labelWidth:100,formRules:"rules",gutter:15,disabled:!1,span:24,formBtns:!0},w=[{__config__:{label:"单行文本",labelWidth:null,showLabel:!0,tag:"el-input",tagIcon:"input",defaultValue:void 0,required:!0,layout:"colFormItem",span:24},__slot__:{prepend:"",append:""},__placeholder__:"请输入",style:{width:"100%"},clearable:!0,"prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"多行文本",labelWidth:null,showLabel:!0,tag:"el-input",tagIcon:"textarea",defaultValue:void 0,required:!0,layout:"colFormItem",span:24},type:"textarea",__placeholder__:"请输入",autosize:{minRows:4,maxRows:4},style:{width:"100%"},maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"下拉选择",showLabel:!0,labelWidth:null,tag:"el-select",tagIcon:"select",layout:"colFormItem",span:24,required:!0},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},__placeholder__:"请选择",style:{width:"100%"},clearable:!0,disabled:!1,filterable:!1,multiple:!1},{__config__:{label:"级联选择",dataPath:"list",dataConsumer:"options",showLabel:!0,labelWidth:null,tag:"el-cascader",tagIcon:"cascader",layout:"colFormItem",defaultValue:[],dataType:"static",span:24,required:!0},options:[{value:1,label:"选项1",children:[{value:2,label:"选项1-1"}]}],__placeholder__:"请选择",style:{width:"100%"},props:{props:{multiple:!1,label:"label",value:"value",children:"children"}},"show-all-levels":!0,disabled:!1,clearable:!0,filterable:!1,separator:"/"},{__config__:{label:"单选框组",labelWidth:null,showLabel:!0,tag:"el-radio-group",tagIcon:"radio",changeTag:!0,defaultValue:void 0,layout:"colFormItem",span:24,optionType:"default",required:!0,border:!1},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",disabled:!1},{__config__:{label:"多选框组",tag:"el-checkbox-group",tagIcon:"checkbox",defaultValue:[],span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",optionType:"default",required:!0,border:!1},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",min:null,max:null,disabled:!1},{__config__:{label:"开关",tag:"el-switch",tagIcon:"switch",defaultValue:!1,span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0},style:{},disabled:!1,"active-text":"","inactive-text":"","active-color":null,"inactive-color":null,"active-value":!0,"inactive-value":!1},{__config__:{label:"时间选择",tag:"el-time-picker",tagIcon:"time",defaultValue:null,span:24,showLabel:!0,layout:"colFormItem",labelWidth:null,required:!0},__placeholder__:"请选择",style:{width:"100%"},disabled:!1,clearable:!0,"picker-options":{selectableRange:"00:00:00-23:59:59"},format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"时间范围",tag:"el-time-picker",tagIcon:"time-range",span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",defaultValue:null,required:!0},style:{width:"100%"},disabled:!1,clearable:!0,"is-range":!0,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"日期选择",tag:"el-date-picker",tagIcon:"date",defaultValue:null,showLabel:!0,labelWidth:null,span:24,layout:"colFormItem",required:!0},__placeholder__:"请选择",type:"date",style:{width:"100%"},disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1},{__config__:{label:"日期范围",tag:"el-date-picker",tagIcon:"date-range",defaultValue:null,span:24,showLabel:!0,labelWidth:null,required:!0,layout:"colFormItem"},style:{width:"100%"},type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1}],x=a("f1e9"),k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1}},"el-dialog",e.$attrs,!1),e.$listeners),[a("div",{staticClass:"preview-dialog-height"},[a("el-row",{attrs:{gutter:15}},[a("el-form",{attrs:{size:"small","label-width":"120px"}},e._l(e.fields,(function(e,t){return a("element-item",{key:e.renderKey,attrs:{"current-item":e,index:t}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.showValue}},[e._v("确 定")])],1)])],1)},$=[],I=a("b32f"),C={components:{ElementItem:I["a"]},props:{fields:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{showValue:function(){console.log(this.fields)}}},T=C,V=(a("36ae"),Object(h["a"])(T,k,$,!1,null,null,null)),F=V.exports,O={components:{draggable:r.a,SimpleRightPanel:D,DraggableItem:x["a"],PreviewDialog:F},data:function(){return{formData:null,lastFormId:0,formName:"",formConf:y,inputComponents:w,labelWidth:100,drawingList:[],activeId:null,showFileName:!1,activeData:{__config__:{tag:"none"}},leftComponents:[{title:"组件列表",list:w}],previewVisible:!1,previewFields:[]}},watch:{"activeData.__config__.label":function(e,t){void 0!==this.activeData.placeholder&&this.activeData.__config__.tag&&i===this.activeId&&(this.activeData.placeholder=this.activeData.__placeholder__+e)},activeId:{handler:function(e){i=e},immediate:!0}},mounted:function(){var e=this,t=this.$route.query;t&&t.code&&(this.formName=t.name||"",this.$http("/bd/form/getByCode/"+t.code).then((function(a){if(a.code>0&&(e.formData=a.data||{code:t.code,name:t.name},a.data&&a.data.data&&a.data.data.length>0)){var i=JSON.parse(a.data.data),l=[];if(i.length){var o=0;i.forEach((function(e){if(e.__config__&&e.__config__.formId){var t=parseInt(e.__config__.formId.replace(/^form_/,""));isNaN(t)||(t>o&&(o=t),l.push(e))}}))}e.drawingList=l}})))},methods:{setObjectValueReduce:function(e,t,a){var i=t.split(".");i.reduce((function(e,t,l){return i.length===l+1?e[t]=a:Object(_["g"])(e[t])||(e[t]={}),e[t]}),e)},setRespData:function(e,t){var a=e.__config__,i=a.dataPath,l=a.renderKey,o=a.dataConsumer;if(i&&o){var n=i.split(".").reduce((function(e,t){return e[t]}),t);this.setObjectValueReduce(e,o,n);var c=this.drawingList.findIndex((function(e){return e.__config__.renderKey===l}));c>-1&&this.$set(this.drawingList,c,e)}},fetchData:function(e){var t=this,a=e.__config__,i=a.dataType,l=a.method,o=a.url;"dynamic"===i&&l&&o&&(this.setLoading(e,!0),this.$axios({method:l,url:o}).then((function(a){t.setLoading(e,!1),t.setRespData(e,a.data)})))},setLoading:function(e,t){var a=e.directives;if(Array.isArray(a)){var i=a.find((function(e){return"loading"===e.name}));i&&(i.value=t)}},activeFormItem:function(e){this.activeData=e,this.activeId=e.__config__.formId},onEnd:function(e){e.from!==e.to&&(this.fetchData(l),this.activeData=l,this.activeId="form"+this.lastFormId)},addComponent:function(e){var t=this.cloneComponent(e);this.fetchData(t),this.drawingList.push(t),this.activeFormItem(t)},cloneComponent:function(e){var t=Object(_["c"])(e),a=t.__config__;return a.span=this.formConf.span,this.createIdAndKey(t),t.__placeholder__&&(t.placeholder=t.__placeholder__+a.label),l=t,l},getNextFormId:function(){return++this.lastFormId},getNextFieldName:function(e){var t="F",a=30;switch(e){case"checkbox":case"textarea":t="T",a=20;break;case"input":case"cascader":t="S",a=20;break}var i=[];this.drawingList.forEach((function(e){if(e.__vModel__&&e.__vModel__.substring(0,1)===t){var a=parseInt(e.__vModel__.replace(/.0*/,""));isNaN(a)||i.push(a)}})),i.sort((function(e,t){return e-t}));for(var l=!1,o=1;o<=a;o++){l=!1;for(var n=0;n<i.length;n++){if(i[n]===o){l=!0;break}if(i[n]>o)break}if(!l)return t+(o>9?o:"0"+o)}return t+"01"},createIdAndKey:function(e){var t=this,a=e.__config__;a.formId="form_"+this.getNextFormId(),a.renderKey="".concat(+new Date);var i=this.getNextFieldName(a.tagIcon);return"colFormItem"===a.layout?e.__vModel__=i:"rowFormItem"===a.layout&&(a.componentName="row_".concat(a.formId),!Array.isArray(a.children)&&(a.children=[]),delete a.label),Array.isArray(a.children)&&(a.children=a.children.map((function(e){return t.createIdAndKey(e)}))),e},emptyForm:function(){var e=this;this.$confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then((function(){e.drawingList=[],e.lastFormId=0})).catch((function(){}))},drawingItemCopy:function(e,t){var a=Object(_["c"])(e);a=this.createIdAndKey(a),t.push(a),this.activeFormItem(a)},drawingItemDelete:function(e,t){var a=this;t.splice(e,1),this.$nextTick((function(){var e=a.drawingList.length;e&&a.activeFormItem(a.drawingList[e-1])}))},previewForm:function(){var e=JSON.stringify(this.drawingList);this.previewFields=JSON.parse(e),this.previewVisible=!0},saveForm:function(){var e=this;null!==this.formData&&null!==this.formData.code?this.$confirm("确定要提交该表单设计吗？","提示",{type:"warning"}).then((function(){var t={id:e.formData.id,code:e.formData.code,name:e.formData.name},a=[];e.drawingList.forEach((function(e){e.__vModel__&&a.push(e.__vModel__)})),t.field=a.join(","),t.data=JSON.stringify(e.drawingList),e.$http({url:"/bd/form/save",data:t}).then((function(t){t.code>0&&e.$message.success("保存成功")}))})).catch((function(){})):this.$message.warning("缺少参数，表单编码")}}},M=O,L=(a("20f9"),Object(h["a"])(M,o,n,!1,null,null,null));t["default"]=L.exports},7498:function(e,t,a){"use strict";a("4836")},"8a67":function(e,t,a){},"9d4a":function(e,t,a){"use strict";var i=a("23e7"),l=a("2266"),o=a("1c0b"),n=a("825a");i({target:"Iterator",proto:!0,real:!0},{reduce:function(e){n(this),o(e);var t=arguments.length<2,a=t?void 0:arguments[1];if(l(this,(function(i){t?(t=!1,a=i):a=e(a,i)}),void 0,!1,!0),t)throw TypeError("Reduce of empty iterator with no initial value");return a}})},a630:function(e,t,a){var i=a("23e7"),l=a("4df4"),o=a("1c7e"),n=!o((function(e){Array.from(e)}));i({target:"Array",stat:!0,forced:n},{from:l})},b32f:function(e,t,a){"use strict";a("d81d"),a("a9e3"),a("d3b7"),a("0643"),a("a573");var i=a("4758");function l(e,t){var a=this,l=t.__config__,n=!1===l.showLabel?"0":l.labelWidth?"".concat(l.labelWidth,"px"):null,c=l.showLabel?l.label+"：":"",r=o.apply(this,arguments);return e("el-col",{attrs:{span:l.span}},[e("el-form-item",{attrs:{"label-width":n,label:c,required:l.required}},[e(i["a"],{key:l.renderKey,attrs:{conf:t},on:{input:function(e){a.$set(l,"defaultValue",e)}}},[r])])])}function o(e,t){var a=this,i=t.__config__;return Array.isArray(i.children)?i.children.map((function(t,o){return l.call(a,e,t,o,i.children)})):null}var n,c,r={components:{render:i["a"]},props:{currentItem:{type:Object,default:function(){}},index:{type:Number,default:0}},render:function(e){return"colFormItem"===this.currentItem.__config__.layout?l.call(this,e,this.currentItem):""}},s=r,u=a("2877"),d=Object(u["a"])(s,n,c,!1,null,null,null);t["a"]=d.exports},c740:function(e,t,a){"use strict";var i=a("23e7"),l=a("b727").findIndex,o=a("44d2"),n=a("ae40"),c="findIndex",r=!0,s=n(c);c in[]&&Array(1)[c]((function(){r=!1})),i({target:"Array",proto:!0,forced:r||!s},{findIndex:function(e){return l(this,e,arguments.length>1?arguments[1]:void 0)}}),o(c)},d58f:function(e,t,a){var i=a("1c0b"),l=a("7b0b"),o=a("44ad"),n=a("50c4"),c=function(e){return function(t,a,c,r){i(a);var s=l(t),u=o(s),d=n(s.length),_=e?d-1:0,v=e?-1:1;if(c<2)while(1){if(_ in u){r=u[_],_+=v;break}if(_+=v,e?_<0:d<=_)throw TypeError("Reduce of empty array with no initial value")}for(;e?_>=0:d>_;_+=v)_ in u&&(r=a(r,u[_],_,s));return r}};e.exports={left:c(!1),right:c(!0)}}}]);