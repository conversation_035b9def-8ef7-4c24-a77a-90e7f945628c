@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-file-picker__files {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.uni-file-picker__lists {
  position: relative;
  margin-top: 5px;
  overflow: hidden;
}
.file-picker__mask {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  color: #fff;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-file-picker__lists-box {
  position: relative;
}
.uni-file-picker__item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  padding-right: 5px;
  padding-left: 10px;
}
.files-border {
  border-top: 1px #eee solid;
}
.files__name {
  flex: 1;
  font-size: 14px;
  color: #666;
  margin-right: 25px;
  word-break: break-all;
  word-wrap: break-word;
}
.icon-files {
  position: static;
  background-color: initial;
}
.is-list-card {
  border: 1px #eee solid;
  margin-bottom: 5px;
  border-radius: 5px;
  box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.1);
  padding: 5px;
}
.files__image {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}
.header-image {
  width: 100%;
  height: 100%;
}
.is-text-box {
  border: 1px #eee solid;
  border-radius: 5px;
}
.is-text-image {
  width: 25px;
  height: 25px;
  margin-left: 5px;
}
.rotate {
  position: absolute;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.icon-del-box {
  display: flex;
  margin: auto 0;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0px;
  bottom: 0;
  right: 5px;
  height: 26px;
  width: 26px;
  z-index: 2;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.icon-del {
  width: 15px;
  height: 1px;
  background-color: #333;
}

