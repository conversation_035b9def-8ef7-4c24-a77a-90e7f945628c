<view class="container"><view class="tab-host"><uni-grid vue-id="b0e7b522-1" column="{{3}}" showBorder="{{false}}" square="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-grid-item vue-id="{{('b0e7b522-2')+','+('b0e7b522-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTab',[0]]]]]}}" class="{{['tool-item',(index==0)?'act':'']}}" bindtap="__e"><uni-badge vue-id="{{('b0e7b522-3')+','+('b0e7b522-2')}}" size="small" text="{{taskCount.doing}}" absolute="rightTop" type="error" bind:__l="__l" vue-slots="{{['default']}}"><uni-icons vue-id="{{('b0e7b522-4')+','+('b0e7b522-3')}}" custom-prefix="zi" type="zi-task-doing" size="30" color="{{tabColor0}}" bind:__l="__l"></uni-icons></uni-badge><text class="icon-text">待巡检</text></view></uni-grid-item><uni-grid-item vue-id="{{('b0e7b522-5')+','+('b0e7b522-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTab',[1]]]]]}}" class="{{['tool-item',(index==1)?'act':'']}}" bindtap="__e"><uni-badge vue-id="{{('b0e7b522-6')+','+('b0e7b522-5')}}" size="small" text="{{taskCount.done}}" absolute="rightTop" type="error" bind:__l="__l" vue-slots="{{['default']}}"><uni-icons vue-id="{{('b0e7b522-7')+','+('b0e7b522-6')}}" custom-prefix="zi" type="zi-task-done" size="30" color="{{tabColor1}}" bind:__l="__l"></uni-icons></uni-badge><text class="icon-text">已巡检</text></view></uni-grid-item><uni-grid-item vue-id="{{('b0e7b522-8')+','+('b0e7b522-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTab',[2]]]]]}}" class="{{['tool-item',(index==2)?'act':'']}}" bindtap="__e"><uni-badge vue-id="{{('b0e7b522-9')+','+('b0e7b522-8')}}" size="small" text="{{taskCount.abnormal}}" absolute="rightTop" type="error" bind:__l="__l" vue-slots="{{['default']}}"><uni-icons vue-id="{{('b0e7b522-10')+','+('b0e7b522-9')}}" custom-prefix="zi" type="zi-task-warning" size="30" color="{{tabColor2}}" bind:__l="__l"></uni-icons></uni-badge><text class="icon-text">异常项</text></view></uni-grid-item></uni-grid></view><view class="tab-content"><block wx:if="{{index==0}}"><view class="list-block"><block wx:if="{{$root.g0}}"><view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item"><view class="row"><view class="caption">{{item.$orig.planName}}</view></view><view class="row"><view class="label">任务单号：</view><view class="text">{{item.$orig.no}}</view><view class="time"><block wx:if="{{item.$orig.willSecond>=172800}}"><uni-tag vue-id="{{'b0e7b522-11-'+__i0__}}" circle="{{true}}" size="small" text="2天以后" bind:__l="__l"></uni-tag></block><block wx:else><block wx:if="{{item.$orig.willSecond>=86400}}"><uni-tag vue-id="{{'b0e7b522-12-'+__i0__}}" circle="{{true}}" size="small" text="1天以后" bind:__l="__l"></uni-tag></block><block wx:else><block wx:if="{{item.$orig.willSecond>0}}"><uni-countdown vue-id="{{'b0e7b522-13-'+__i0__}}" color="#FFFFFF" background-color="#CC0000" border-color="#CC0000" show-day="{{false}}" hour="{{item.m0}}" minute="{{item.m1}}" second="{{item.$orig.willSecond%60}}" bind:__l="__l"></uni-countdown></block><block wx:else><block wx:if="{{item.$orig.willSecond>-86000}}"><uni-tag vue-id="{{'b0e7b522-14-'+__i0__}}" type="warning" circle="{{true}}" size="small" text="待执行" bind:__l="__l"></uni-tag></block><block wx:else><uni-tag vue-id="{{'b0e7b522-15-'+__i0__}}" type="royal" mark="{{true}}" size="small" text="已到期" bind:__l="__l"></uni-tag></block></block></block></block></view></view><view class="row"><view class="label">计划时间：</view><view class="text">{{item.$orig.timeText}}</view><view class="button"><block wx:if="{{item.$orig.willSecond<300}}"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['startPatrol',['$event']]]]]}}" bindtap="__e">开始巡检</button></block><block wx:else><button size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['viewTask',['$event']]]]]}}" bindtap="__e">任务详情</button></block></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="b0e7b522-16" bind:__l="__l" vue-slots="{{['default']}}">近期未有巡检单</uni-text></block></view></block><block wx:else><block wx:if="{{index==1}}"><view class="list-block"><block wx:if="{{$root.g1}}"><view><block wx:for="{{doneList}}" wx:for-item="item" wx:for-index="__i1__" wx:key="*this"><view class="list-item"><view class="row"><view class="caption">{{item.planName}}</view></view><view class="row"><view class="label">任务单号：</view><view class="text">{{item.no}}</view><view class="time"><block wx:if="{{item.result=='2'}}"><uni-tag vue-id="{{'b0e7b522-17-'+__i1__}}" type="error" size="small" text="异常" bind:__l="__l"></uni-tag></block><block wx:else><uni-tag vue-id="{{'b0e7b522-18-'+__i1__}}" type="success" mark="{{true}}" size="small" text="正常" bind:__l="__l"></uni-tag></block></view></view><view class="row"><view class="label">执行时间：</view><view class="text">{{item.execStart}}</view><view class="button"><block wx:if="{{item.willSecond<300}}"><button size="mini" data-id="{{item.id}}" data-event-opts="{{[['tap',[['viewPatrol',['$event']]]]]}}" bindtap="__e">巡检详情</button></block></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="b0e7b522-19" bind:__l="__l" vue-slots="{{['default']}}">没有巡检单</uni-text></block></view></block><block wx:else><view><block wx:if="{{$root.g2}}"><view class="list-block"><block wx:for="{{abnormalList}}" wx:for-item="item" wx:for-index="__i2__" wx:key="*this"><view class="list-item"><view class="row"><view class="caption">{{item.planName}}</view></view><view class="row"><view class="label">任务单号：</view><view class="text">{{item.no}}</view><view class="time"><uni-tag vue-id="{{'b0e7b522-20-'+__i2__}}" type="error" size="small" text="异常" bind:__l="__l"></uni-tag></view></view><view class="row"><view class="label">执行时间：</view><view class="text">{{item.execStart}}</view><view class="button"><block wx:if="{{item.willSecond<300}}"><button size="mini" data-id="{{item.id}}" data-event-opts="{{[['tap',[['viewPatrol',['$event']]]]]}}" bindtap="__e">巡检详情</button></block></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="b0e7b522-21" bind:__l="__l" vue-slots="{{['default']}}">未有异常事项</uni-text></block></view></block></block></view><watermark vue-id="b0e7b522-22" bind:__l="__l"></watermark></view>