{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-4154f276\"],{\"0213\":function(t,e,a){\"use strict\";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"layout-lr\"},[a(\"div\",{staticClass:\"left\"},[a(\"div\",{staticClass:\"head\"},[t._v(\"区域列表\")]),a(\"div\",{staticClass:\"body\"},[a(\"ul\",[a(\"li\",{class:{act:null==t.activeItem||null==t.activeItem.id},on:{click:function(e){return t.showRegion({})}}},[t._v(\"所有区域\")]),t._l(t.regionList,(function(e){return a(\"li\",{key:e.id,class:{act:t.activeItem&&t.activeItem.id==e.id},on:{click:function(a){return t.showRegion(e)}}},[t._v(t._s(e.name))])}))],2)])]),a(\"div\",{staticClass:\"center\"},[a(\"el-descriptions\",{staticClass:\"descr-3\",attrs:{title:\"区域信息\",column:3,border:\"\"}},[a(\"template\",{slot:\"extra\"},[a(\"div\",{staticClass:\"button-bar\"},[a(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-plus\"},on:{click:t.addRegion}},[t._v(\"新增区域\")]),a(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-upload\"},on:{click:t.upload}},[t._v(\"网点导入\")]),t.activeItem&&t.activeItem.id?[a(\"el-button\",{attrs:{type:\"primary\",size:\"mini\",icon:\"el-icon-edit\"},on:{click:t.editRegion}},[t._v(\"编辑区域\")]),a(\"el-button\",{attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-remove\"},on:{click:t.removeRegion}},[t._v(\"删除区域\")]),a(\"el-button\",{attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-plus\"},on:{click:t.addLocation}},[t._v(\"新增网点\")]),a(\"el-dropdown\",{staticStyle:{\"margin-left\":\"10px\"},on:{command:t.handleBatchCommand}},[a(\"el-button\",{attrs:{type:\"warning\",size:\"mini\"}},[t._v(\" 批量修改\"),a(\"i\",{staticClass:\"el-icon-arrow-down el-icon--right\"})]),a(\"el-dropdown-menu\",{attrs:{slot:\"dropdown\"},slot:\"dropdown\"},[a(\"el-dropdown-item\",{attrs:{command:\"export\"}},[t._v(\"导出\")]),a(\"el-dropdown-item\",{attrs:{command:\"import\"}},[t._v(\"导入\")])],1)],1)]:t._e()],2)]),t.activeItem&&t.activeItem.id?[a(\"el-descriptions-item\",{attrs:{label:\"所属部门\"}},[t._v(t._s(t.activeItem.deptName))]),a(\"el-descriptions-item\",{attrs:{label:\"区域代码\"}},[t._v(t._s(t.activeItem.code))]),a(\"el-descriptions-item\",{attrs:{label:\"区域名称\"}},[t._v(t._s(t.activeItem.name))]),a(\"el-descriptions-item\",{attrs:{label:\"所在区划\"}},[t._v(t._s(t.activeItem.regionName))]),a(\"el-descriptions-item\",{attrs:{label:\"负责人\"}},[t._v(t._s(t.activeItem.userName))]),a(\"el-descriptions-item\",{attrs:{label:\"联系电话\"}},[t._v(t._s(t.activeItem.userPhone))]),a(\"el-descriptions-item\",{attrs:{span:3,label:\"区域范围\"}},[t._v(t._s(t.activeItem.scope))])]:t._e()],2),a(\"el-divider\"),a(\"div\",{staticClass:\"location-head\"},[a(\"span\",{staticClass:\"location-title\"},[t._v(\"区域网点\")]),a(\"div\",{staticClass:\"location-search\"},[a(\"el-input\",{attrs:{clearable:\"\",size:\"mini\",placeholder:\"输入关键字\",autocomplete:\"off\"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,\"keyword\",e)},expression:\"qform.keyword\"}},[a(\"template\",{slot:\"prepend\"},[t._v(\"检索:\")]),a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-search\"},on:{click:t.searchLocation},slot:\"append\"})],2)],1)]),a(\"page-table\",{ref:\"grid\",attrs:{size:\"mini\",path:\"/am/location/page\",query:t.qform,stripe:\"\",border:\"\"}},[null==t.activeItem||null==t.activeItem.id?a(\"el-table-column\",{attrs:{label:\"所属区域\",prop:\"regionName\",width:\"100\",align:\"center\"}}):t._e(),a(\"el-table-column\",{attrs:{label:\"网点编码\",prop:\"code\",width:\"110\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"网点名称\",prop:\"name\",width:\"300\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"负责人\",prop:\"contact\",width:\"300\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"联系方式\",prop:\"phone\",width:\"150\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"地址\",prop:\"address\",width:\"800\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"网点时间\",prop:\"createTime\",align:\"center\",width:\"150\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(\" \"+t._s(e.row.createTime?t.formatDateTime(e.row.createTime):\"\")+\" \")]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"120\",align:\"center\",fixed:\"right\"},scopedSlots:t._u([{key:\"default\",fn:function(e){var i=e.row;return[\"admin\"!=i.id?a(\"div\",[a(\"el-button\",{attrs:{type:\"primary\",size:\"mini\"},on:{click:function(e){return e.stopPropagation(),t.editLocation(i)}}},[t._v(\"编辑\")]),a(\"el-button\",{attrs:{type:\"danger\",size:\"mini\"},on:{click:function(e){return e.stopPropagation(),t.removeLocation(i)}}},[t._v(\"删除\")])],1):t._e()]}}])})],1)],1),a(\"el-dialog\",{directives:[{name:\"dialog-drag\",rawName:\"v-dialog-drag\"}],attrs:{title:\"区域信息\",width:\"800px\",visible:t.regionVisible,\"close-on-press-escape\":!1,\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.regionVisible=e}}},[a(\"el-form\",{ref:\"regionform\",attrs:{model:t.regionData,rules:t.regionRules,\"label-width\":\"110px\"}},[a(\"el-row\",{attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"区域编码：\",prop:\"code\"}},[a(\"el-input\",{attrs:{maxlength:\"20\",autocomplete:\"off\"},model:{value:t.regionData.code,callback:function(e){t.$set(t.regionData,\"code\",e)},expression:\"regionData.code\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"区域名称：\",prop:\"name\"}},[a(\"el-input\",{attrs:{maxlength:\"20\",autocomplete:\"off\"},model:{value:t.regionData.name,callback:function(e){t.$set(t.regionData,\"name\",e)},expression:\"regionData.name\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"所属机构：\",prop:\"dept\"}},[a(\"tree-box\",{attrs:{data:t.deptTree,\"expand-all\":!0,clearable:!1},model:{value:t.regionData.dept,callback:function(e){t.$set(t.regionData,\"dept\",e)},expression:\"regionData.dept\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"所在区划：\",prop:\"region\"}},[a(\"region\",{staticStyle:{width:\"100%\"},attrs:{root:\"460000\",\"start-level\":1,\"with-root\":\"\",\"any-node\":\"\"},model:{value:t.regionData.region,callback:function(e){t.$set(t.regionData,\"region\",e)},expression:\"regionData.region\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"负责人：\",prop:\"user\"}},[a(\"user-chosen\",{staticStyle:{width:\"100%\"},attrs:{type:\"1\"},model:{value:t.regionData.user,callback:function(e){t.$set(t.regionData,\"user\",e)},expression:\"regionData.user\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"区域排序：\",prop:\"ord\"}},[a(\"el-input-number\",{attrs:{autocomplete:\"off\",min:1,max:999},model:{value:t.regionData.ord,callback:function(e){t.$set(t.regionData,\"ord\",e)},expression:\"regionData.ord\"}})],1)],1),a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"区域范围：\",prop:\"scope\"}},[a(\"el-input\",{attrs:{maxlength:\"32\",autocomplete:\"off\"},model:{value:t.regionData.scope,callback:function(e){t.$set(t.regionData,\"scope\",e)},expression:\"regionData.scope\"}})],1)],1)],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(e){t.regionVisible=!1}}},[t._v(\"取 消\")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.saveRegion}},[t._v(\"确 定\")])],1)],1),a(\"el-dialog\",{directives:[{name:\"dialog-drag\",rawName:\"v-dialog-drag\"}],attrs:{title:\"区域地点\",width:\"800px\",visible:t.locationVisible,\"close-on-press-escape\":!1,\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.locationVisible=e}}},[a(\"el-form\",{ref:\"locationform\",attrs:{model:t.locationData,rules:t.locationRules,\"label-width\":\"110px\"}},[a(\"el-row\",{attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"区域编码：\"}},[a(\"el-input\",{staticClass:\"form-static\",attrs:{readonly:\"\"},model:{value:t.activeItem.code,callback:function(e){t.$set(t.activeItem,\"code\",e)},expression:\"activeItem.code\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"区域名称：\"}},[a(\"el-input\",{staticClass:\"form-static\",attrs:{readonly:\"\"},model:{value:t.activeItem.name,callback:function(e){t.$set(t.activeItem,\"name\",e)},expression:\"activeItem.name\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"网点编码：\",prop:\"code\"}},[a(\"el-input\",{attrs:{maxlength:\"20\",autocomplete:\"off\"},model:{value:t.locationData.code,callback:function(e){t.$set(t.locationData,\"code\",e)},expression:\"locationData.code\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"网点名称：\",prop:\"name\"}},[a(\"el-input\",{attrs:{maxlength:\"64\",autocomplete:\"off\"},model:{value:t.locationData.name,callback:function(e){t.$set(t.locationData,\"name\",e)},expression:\"locationData.name\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"负责人：\",prop:\"contact\"}},[a(\"el-input\",{attrs:{maxlength:\"20\",autocomplete:\"off\"},model:{value:t.locationData.contact,callback:function(e){t.$set(t.locationData,\"contact\",e)},expression:\"locationData.contact\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"联系电话：\",prop:\"phone\"}},[a(\"el-input\",{attrs:{maxlength:\"20\",autocomplete:\"off\"},model:{value:t.locationData.phone,callback:function(e){t.$set(t.locationData,\"phone\",e)},expression:\"locationData.phone\"}})],1)],1),a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"网点地址：\",prop:\"address\"}},[a(\"el-input\",{attrs:{maxlength:\"128\",autocomplete:\"off\"},model:{value:t.locationData.address,callback:function(e){t.$set(t.locationData,\"address\",e)},expression:\"locationData.address\"}},[a(\"template\",{slot:\"append\"},[a(\"el-button\",{attrs:{size:\"mini\",icon:\"el-icon-map-location\"},on:{click:t.mapPin}},[t._v(\"地图定位\")])],1)],2)],1)],1),a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"网点备注：\",prop:\"memo\"}},[a(\"el-input\",{attrs:{maxlength:\"128\",autocomplete:\"off\"},model:{value:t.locationData.memo,callback:function(e){t.$set(t.locationData,\"memo\",e)},expression:\"locationData.memo\"}})],1)],1)],1)],1),a(\"el-table\",{ref:\"optionGrid\",attrs:{data:t.amLocationAsset,size:\"mini\",stripe:!0,border:!0}},[a(\"el-table-column\",{attrs:{type:\"index\",width:\"50\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"销售终端编号\",prop:\"sn\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-input\",{attrs:{size:\"mini\",autocomplete:\"off\"},model:{value:e.row.sn,callback:function(a){t.$set(e.row,\"sn\",a)},expression:\"scope.row.sn\"}})]}}])}),a(\"el-table-column\",{attrs:{width:\"70\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{attrs:{size:\"mini\",type:\"danger\"},on:{click:function(a){return a.stopPropagation(),t.removeAsset(e.$index)}}},[t._v(\"删除\")])]}}])},[a(\"template\",{slot:\"header\"},[a(\"el-button\",{attrs:{type:\"success\",size:\"mini\"},on:{click:t.addAsset}},[t._v(\"新增\")])],1)],2)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(e){t.locationVisible=!1}}},[t._v(\"取 消\")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.saveLocation}},[t._v(\"确 定\")])],1)],1),a(\"el-dialog\",{ref:\"uploadDlg\",staticClass:\"dialog-full\",attrs:{title:\"批量导入\",fullscreen:\"\",visible:t.uploadVisible,\"close-on-press-escape\":!1,\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.uploadVisible=e}}},[a(\"div\",{staticStyle:{margin:\"10px 20px\"}},[a(\"upload-file\",{attrs:{type:\"ASSET_UPLOAD\",simple:\"\",multiple:!1,limit:1,accept:\".xls,.xlsx\"},on:{success:t.uploaded,removeFile:t.uploadRemove},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:\"fileList\"}})],1),a(\"div\",{staticClass:\"upload-block\"},[a(\"table\",[a(\"thead\",[a(\"tr\",[a(\"th\",[t._v(\"行号\")]),a(\"th\",[t._v(\"结果提示\")]),a(\"th\",[t._v(\"所属市县编号\")]),a(\"th\",[t._v(\"销售终端编号\")]),a(\"th\",[t._v(\"门店编号\")]),a(\"th\",[t._v(\"业主姓名\")]),a(\"th\",[t._v(\"负责人\")]),a(\"th\",[t._v(\"联系方式\")]),a(\"th\",[t._v(\"门店地址\")])])]),a(\"tbody\",[t._l(t.uploadList,(function(e){return[t.showUploadAll||null!=e.rowMsg?a(\"tr\",{key:e.rowNum,class:{err:null!=e.rowMsg}},[a(\"td\",[t._v(t._s(e.rowNum))]),a(\"td\",{staticClass:\"upload-msg\"},[t._v(t._s(e.rowMsg))]),a(\"td\",[t._v(t._s(e.region))]),a(\"td\",[t._v(t._s(e.sn))]),a(\"td\",[t._v(t._s(e.code))]),a(\"td\",[t._v(t._s(e.name))]),a(\"td\",[t._v(t._s(e.contact))]),a(\"td\",[t._v(t._s(e.phone))]),a(\"td\",[t._v(t._s(e.address))])]):t._e()]}))],2)])]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"div\",{staticStyle:{left:\"30px\",float:\"left\"}},[a(\"el-button\",{attrs:{size:\"small\",type:\"warning\"},on:{click:t.toggleErr}},[t._v(t._s(t.showUploadAll?\"查看错误\":\"查看全部\"))])],1),a(\"el-button\",{attrs:{size:\"small\"},on:{click:function(e){t.uploadVisible=!1}}},[t._v(\"取 消\")]),a(\"el-button\",{attrs:{size:\"small\",type:\"primary\"},on:{click:t.submitUpload}},[t._v(\"确定上传\")])],1)]),a(\"el-dialog\",{ref:\"batchUploadDlg\",staticClass:\"dialog-full\",attrs:{title:\"网点批量修改\",fullscreen:\"\",visible:t.batchUploadVisible,\"close-on-press-escape\":!1,\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.batchUploadVisible=e}}},[a(\"div\",{staticStyle:{margin:\"10px 20px\"}},[a(\"upload-file\",{attrs:{type:\"ASSET_UPLOAD\",simple:\"\",multiple:!1,limit:1,accept:\".xls,.xlsx\"},on:{success:t.batchUploaded,removeFile:t.batchUploadRemove},model:{value:t.batchFileList,callback:function(e){t.batchFileList=e},expression:\"batchFileList\"}})],1),a(\"div\",{staticClass:\"upload-block\"},[a(\"table\",[a(\"thead\",[a(\"tr\",[a(\"th\",[t._v(\"行号\")]),t.getBatchErrorCount()>0?a(\"th\",[t._v(\"结果提示\")]):t._e(),a(\"th\",[t._v(\"网点编码\")]),a(\"th\",[t._v(\"网点名称\")]),a(\"th\",[t._v(\"网点备注\")]),a(\"th\",[t._v(\"操作\")])])]),a(\"tbody\",[t._l(t.batchUploadList,(function(e,i){return[a(\"tr\",{key:i,class:{err:null!=e.rowMsg}},[a(\"td\",[t._v(t._s(i+1))]),t.getBatchErrorCount()>0?a(\"td\",{staticClass:\"upload-msg\"},[t._v(t._s(e.rowMsg))]):t._e(),a(\"td\",[t._v(t._s(e.code))]),a(\"td\",[t._v(t._s(e.name))]),a(\"td\",[a(\"el-input\",{staticStyle:{width:\"100%\"},attrs:{size:\"mini\",placeholder:\"请输入网点备注\",maxlength:\"200\",\"show-word-limit\":\"\",clearable:\"\"},model:{value:e.memo,callback:function(a){t.$set(e,\"memo\",a)},expression:\"item.memo\"}})],1),a(\"td\",[a(\"el-button\",{attrs:{type:\"danger\",size:\"mini\"},on:{click:function(e){return t.removeBatchRow(i)}}},[t._v(\"删除\")])],1)])]}))],2)])]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"div\",{staticStyle:{left:\"30px\",float:\"left\"}},[a(\"span\",{staticStyle:{color:\"#909399\",\"font-size\":\"12px\"}},[t._v(\" 共 \"+t._s(t.batchUploadList.length)+\" 条数据，其中 \"+t._s(t.getBatchErrorCount())+\" 条错误 \")])]),a(\"el-button\",{attrs:{size:\"small\"},on:{click:function(e){t.batchUploadVisible=!1}}},[t._v(\"取 消\")]),a(\"el-button\",{attrs:{size:\"small\",type:\"primary\"},on:{click:t.submitBatchUpdate}},[t._v(\"确定更新\")])],1)]),a(\"map-location\",{ref:\"mapLocation\",on:{success:t.pined}})],1)},o=[],n=(a(\"99af\"),a(\"4de4\"),a(\"a15b\"),a(\"a434\"),a(\"e9c4\"),a(\"b64b\"),a(\"d3b7\"),a(\"ac1f\"),a(\"4d90\"),a(\"841c\"),a(\"0643\"),a(\"2382\"),a(\"4e3e\"),a(\"159b\"),a(\"5c96\")),l=a(\"6ecd\"),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.visible?a(\"el-cascader\",{attrs:{props:t.props,options:t.options},on:{change:t.changeMe},model:{value:t.values,callback:function(e){t.values=e},expression:\"values\"}}):t._e()},r=[],c=(a(\"a9e3\"),a(\"ddb0\"),a(\"b775\")),u={name:\"Region\",props:{root:{type:String,default:\"0\"},anyNode:{type:Boolean,default:!1},withRoot:{type:Boolean,default:!1},startLevel:{type:Number,default:0},value:{type:String,default:null}},data:function(){var t=this;return{visible:!1,props:{lazy:!0,checkStrictly:this.anyNode,expandTrigger:\"click\",lazyLoad:function(e,a){t.lazyLoadHandle(e,a)}},values:[],options:[]}},watch:{value:function(t){var e=this;this.$nextTick((function(){e.load(t)}))}},mounted:function(){this.load(this.value)},methods:{lazyLoadHandle:function(t,e){t.root||(t.data&&t.data.children?e([]):Object(c[\"a\"])(\"/region/\"+t.value).then((function(a){t.value&&t.value.length>=9&&a.forEach((function(t){t.leaf=!0})),e(a)})))},load:function(t){var e=this,a=[],i=t?t.length:0;i>=6&&(0===this.startLevel&&a.push(t.substring(0,2)+\"0000\"),this.startLevel<2&&a.push(t.substring(0,4)+\"00\"),this.startLevel<3&&a.push(t.substring(0,6)),i>=9&&a.push(t.substring(0,9)),i>=12&&a.push(t.substring(0,12))),this.options=[],this.values=a,Object(c[\"a\"])((this.withRoot?\"/regionWith/\":\"/regionTo/\")+(this.root||\"0\")+\"_\"+(t||\"\")).then((function(t){e.options=t,e.visible=!0}))},update:function(t){this.visible=!1,this.load(t||this.value)},changeMe:function(t){this.$emit(\"input\",t&&t.length?t[t.length-1]:null),this.$emit(\"selected\",t)}}},d=u,p=a(\"2877\"),h=Object(p[\"a\"])(d,s,r,!1,null,null,null),f=h.exports,m=a(\"ee5a\"),g=a(\"ad10\"),v=a(\"58db\"),b=a(\"660a\"),_={components:{PageTable:l[\"a\"],Region:f,TreeBox:m[\"a\"],UserChosen:g[\"a\"],MapLocation:v[\"a\"],UploadFile:b[\"a\"]},data:function(){return{regionList:[],deptTree:[],activeItem:{},tableHeight:300,regionVisible:!1,regionData:{region:\"\"},regionRules:{code:[{required:!0,message:\"请输入区域编码\",trigger:\"blur\"}],name:[{required:!0,message:\"请输入区域名称\",trigger:\"blur\"}],dept:[{required:!0,message:\"请选择所属机构\",trigger:\"blur\"}],region:[{required:!0,message:\"请选择所在区划\",trigger:\"blur\"}]},qform:{keyword:\"\"},locationVisible:!1,locationData:{region:\"\"},locationRules:{code:[{required:!0,message:\"请输入地点编码\",trigger:\"blur\"}],name:[{required:!0,message:\"请输入地点名称\",trigger:\"blur\"}]},fileList:[],uploadList:[],uploadVisible:!1,showUploadAll:!0,amLocationAsset:[{}],batchFileList:[],batchUploadList:[],batchUploadVisible:!1}},mounted:function(){this.loadDeptTree(),this.loadRegion(),this.searchLocation(),this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight-380,200))},methods:{formatDateTime:function(t){if(!t)return\"\";var e=new Date(t),a=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,\"0\"),o=String(e.getDate()).padStart(2,\"0\"),n=String(e.getHours()).padStart(2,\"0\"),l=String(e.getMinutes()).padStart(2,\"0\"),s=String(e.getSeconds()).padStart(2,\"0\");return\"\".concat(a,\"-\").concat(i,\"-\").concat(o,\" \").concat(n,\":\").concat(l,\":\").concat(s)},loadDeptTree:function(){var t=this;this.$http(\"/sys/dept/treeByType/1\").then((function(e){t.deptTree=e})).catch((function(){t.$alert(\"加载机构树出错\")}))},loadRegion:function(){var t=this;this.$http(\"/am/region/list\").then((function(e){if(t.regionList=e||[],t.activeItem&&t.activeItem.id)for(var a=0;a<e.length;a++)if(e[a].id===t.activeItem.id){t.activeItem=e[a];break}}))},showRegion:function(t){this.activeItem=t,this.qform.region=t.id,this.qform.keyword=\"\",this.searchLocation()},addRegion:function(){this.regionVisible=!0,this.regionData={ord:1}},editRegion:function(){if(this.activeItem.id){this.regionVisible=!0;var t=JSON.stringify(this.activeItem);this.regionData=JSON.parse(t)}},saveRegion:function(){var t=this;this.$refs.regionform.validate((function(e){e&&t.$http({url:\"/am/region/save\",data:t.regionData}).then((function(e){e.code>0&&(t.$message.success(\"保存区域信息成功\"),t.regionVisible=!1,t.loadRegion())}))}))},removeRegion:function(){var t=this;this.$confirm(\"此操作将永久删除该区域, 是否继续?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.$http({url:\"/am/region/delete/\"+t.activeItem.id}).then((function(e){e.code>0&&(t.$message.success(\"删除成功\"),t.activeItem={},t.loadRegion())}))})).catch((function(){}))},searchLocation:function(){console.log(\"搜索参数:\",JSON.stringify(this.qform)),this.$refs.grid.search(this.qform)},addLocation:function(){this.locationVisible=!0,this.locationData={region:this.activeItem.id},this.amLocationAsset=[]},editLocation:function(t){var e=this;this.$http(\"/am/location/get/\"+t.id).then((function(t){t.code>0&&t.data&&(e.locationVisible=!0,e.locationData=t.data,e.amLocationAsset=t.data.amLocationAsset||[])}))},saveLocation:function(){var t=this;this.$refs.locationform.validate((function(e){if(e){var a=[];if(t.amLocationAsset.forEach((function(t){return a.push({sn:t.sn})})),!a.length)return t.$message.warning(\"请录入终端信息\");t.locationData.amLocationAsset=a,t.$http({url:\"/am/location/saveDevice\",data:t.locationData}).then((function(e){e.code>0&&(t.$message.success(\"保存区域信息成功\"),t.locationVisible=!1,t.searchLocation())}))}}))},removeLocation:function(t){var e=this;this.$confirm(\"此操作将永久删除该地点, 是否继续?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){e.$http({url:\"/am/location/delete/\"+t.id}).then((function(t){t.code>0&&(e.$message.success(\"删除成功\"),e.searchLocation())}))})).catch((function(){}))},mapPin:function(){var t=this.locationData.lat?{lng:this.locationData.lng,lat:this.locationData.lat}:null;this.$refs.mapLocation.show(t)},pined:function(t){this.$set(this.locationData,\"address\",t.address),this.$set(this.locationData,\"lng\",t.lnglat?t.lnglat.lng:null),this.$set(this.locationData,\"lat\",t.lnglat?t.lnglat.lat:null)},upload:function(){this.fileList=[],this.uploadList=[],this.uploadVisible=!0},uploadRemove:function(){this.uploadList=[]},toggleErr:function(){this.showUploadAll=!this.showUploadAll},uploaded:function(t){var e=this;if(t&&t.length){var a=n[\"Loading\"].service({fullscreen:!0,text:\"解析文件中...\"});this.$http({url:\"/am/location/uploadFile\",data:t[0]}).then((function(t){t.code>0&&(e.showUploadAll=!0,e.uploadList=t.data),a.close()}))}},submitUpload:function(){var t=this;if(0===this.uploadList.length)return this.$message.warning(\"没有可提交的数据\");var e=n[\"Loading\"].service({fullscreen:!0,text:\"数据上传中...\"});this.$http({url:\"/am/location/uploadData\",data:this.uploadList}).then((function(a){1===a.code?(t.$message.success(\"上传成功\"),t.$emit(\"success\"),t.uploadVisible=!1,t.search()):2===a.code&&(t.uploadList=a.data,t.$message.error(\"存在错误的数据行\")),e.close()})).catch((function(){e.close()}))},addAsset:function(){this.amLocationAsset.push({})},removeAsset:function(t){this.amLocationAsset.splice(t,1)},handleBatchCommand:function(t){\"export\"===t?this.exportBatch():\"import\"===t&&this.importBatch()},exportBatch:function(){var t=this;if(!this.activeItem||!this.activeItem.id)return this.$message.warning(\"请先选择一个区域\");var e=n[\"Loading\"].service({fullscreen:!0,text:\"正在导出文件，请耐心等待...\"});this.$jasper({url:\"/am/location/exportBatch/\".concat(this.activeItem.id),responseType:\"blob\"}).then((function(a){e.close(),t.$saveAs(a,\"网点批量修改模板.xlsx\")})).catch((function(a){e.close(),t.$message.error(\"导出生成出错:\"+a)}))},importBatch:function(){if(!this.activeItem||!this.activeItem.id)return this.$message.warning(\"请先选择一个区域\");this.batchFileList=[],this.batchUploadList=[],this.batchUploadVisible=!0},batchUploadRemove:function(){this.batchUploadList=[]},removeBatchRow:function(t){var e=this;this.$confirm(\"确定要删除这条数据吗？\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){e.batchUploadList.splice(t,1),e.$message.success(\"删除成功\")})).catch((function(){}))},getBatchErrorCount:function(){return this.batchUploadList.filter((function(t){return null!=t.rowMsg})).length},batchUploaded:function(t){var e=this;if(t&&t.length){var a=n[\"Loading\"].service({fullscreen:!0,text:\"解析文件中...\"});this.$http({url:\"/am/location/uploadBatchFile\",data:t[0]}).then((function(t){t.code>0&&(e.batchUploadList=t.data),a.close()}))}},submitBatchUpdate:function(){var t=this;if(0===this.batchUploadList.length)return this.$message.warning(\"没有可提交的数据\");var e=this.batchUploadList.filter((function(t){return!t.rowMsg}));if(0===e.length)return this.$message.warning(\"没有有效的数据可以提交，请先删除错误行或修正数据\");var a=this.getBatchErrorCount();a>0?this.$confirm(\"当前有 \".concat(a,\" 条错误数据将被忽略，只提交 \").concat(e.length,\" 条有效数据，是否继续？\"),\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.checkDuplicatesAndUpdate(e)})).catch((function(){})):this.checkAndConfirmDuplicates(e)},checkAndConfirmDuplicates:function(t){var e=this;this.$http({url:\"/am/location/checkBatchDuplicates\",data:t}).then((function(a){if(1===a.code&&a.data){var i=a.data;if(i.hasDuplicates){var o=\"\",n=i.importDuplicates||[],l=i.dbDuplicates||[];n.length>0&&(o+=\"检测到以下网点编码在导入数据中重复出现：\".concat(n.join(\", \"),\"。\\n\")),l.length>0&&(o+=\"检测到以下网点编码在数据库中存在多条记录：\".concat(l.join(\", \"),\"。\\n\")),o+=\"重复编码将以最后一条数据为准\\n\",o+=\"是否继续执行批量更新？\",e.$confirm(o,\"发现重复编码\",{confirmButtonText:\"确定更新\",cancelButtonText:\"取消\",type:\"warning\",dangerouslyUseHTMLString:!1}).then((function(){e.doBatchUpdate(t)})).catch((function(){}))}else e.doBatchUpdate(t)}else e.doBatchUpdate(t)})).catch((function(){e.doBatchUpdate(t)}))},doBatchUpdate:function(t){var e=this,a=n[\"Loading\"].service({fullscreen:!0,text:\"数据上传中...\"});this.$http({url:\"/am/location/batchUpdate\",data:t}).then((function(t){1===t.code?(e.$message.success(\"批量更新成功\"),e.batchUploadVisible=!1,e.searchLocation()):2===t.code&&(e.batchUploadList=t.data,e.$message.error(\"部分数据更新失败，请查看错误信息\")),a.close()})).catch((function(){a.close()}))}}},w=_,y=(a(\"c75a\"),Object(p[\"a\"])(w,i,o,!1,null,null,null));e[\"default\"]=y.exports},\"129f\":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},4736:function(t,e,a){},\"4f8b\":function(t,e,a){\"use strict\";a(\"4736\")},\"58db\":function(t,e,a){\"use strict\";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.visible?a(\"div\",[a(\"el-dialog\",{attrs:{fullscreen:\"\",visible:t.visible,\"custom-class\":\"dialog-tab dialog-full\",\"append-to-body\":\"\",\"close-on-press-escape\":!1,\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.visible=e}}},[a(\"template\",{slot:\"title\"},[a(\"div\",{staticClass:\"header-bar\"},[a(\"div\",{staticClass:\"caption\"},[t._v(\"地图定位\")]),a(\"div\",{staticClass:\"tabbar\"},[a(\"el-checkbox\",{model:{value:t.checked,callback:function(e){t.checked=e},expression:\"checked\"}},[t._v(\"启动地图定位\")]),a(\"span\",{staticStyle:{\"margin-left\":\"60px\"}}),a(\"el-button\",{attrs:{type:\"success\",size:\"mini\",icon:\"el-icon-s-promotion\"},on:{click:t.confirmPin}},[t._v(\"提交本次定位\")]),null!=t.lastPoint?a(\"el-button\",{attrs:{type:\"danger\",size:\"mini\",icon:\"el-icon-remove\"},on:{click:t.removePin}},[t._v(\"取消定位\")]):t._e()],1)])]),a(\"div\",{ref:\"map\",style:{height:t.bodyHeight+\"px\"}})],2)],1):t._e()},o=[],n=(a(\"d81d\"),a(\"d3b7\"),a(\"0643\"),a(\"a573\"),{data:function(){return{visible:!1,bodyHeight:document.documentElement.clientHeight-60,checked:!0,omap:null,map:{center:null,zoom:13,satellite:!1,markers:[]},lastPoint:null,currPoint:null,lastLngLat:null,currData:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on(\"complete\",t.mapComplete),t.omap.on(\"click\",t.mapClick),t.showLast()}))},mapComplete:function(t){},show:function(t){this.visible=!0,this.lastLngLat=t&&t.lng&&t.lat?t:null,null==this.omap?this.initMap():this.showLast()},showLast:function(){if(this.lastLngLat){var t=new window.AMap.LngLat(this.lastLngLat.lng,this.lastLngLat.lat);this.lastPoint=new window.AMap.Marker({position:t,label:{direction:\"top\",content:\"原位置\"},icon:\"/icons/pin-blue.png\"}),this.omap.add(this.lastPoint),this.omap.setZoomAndCenter(16,t)}},mapClick:function(t){if(this.checked){var e=t.lnglat;this.currPoint&&this.omap.remove(this.currPoint),this.currPoint=new window.AMap.Marker({position:new window.AMap.LngLat(e.lng,e.lat),label:{direction:\"top\",content:\"新位置\"},icon:\"/icons/pin-red.png\"}),this.omap.add(this.currPoint),this.currData={lnglat:e,address:null},this.getMapAddress()}},getMapAddress:function(){if(null!=this.currData&&null!=this.currData.lnglat){var t=[this.currData.lnglat.lng,this.currData.lnglat.lat],e=this;window.AMap.plugin(\"AMap.Geocoder\",(function(){var a=new window.AMap.Geocoder({city:\"全国\"});a.getAddress(t,(function(t,a){\"complete\"===t&&\"OK\"===a.info&&a.regeocode&&(e.currData.address=a.regeocode.formattedAddress)}))}))}},confirmPin:function(){if(null==this.currData)return this.$message.warning(\"您还未点击地图定位\");this.$emit(\"success\",this.currData),this.visible=!1},removePin:function(){var t=this;this.$confirm(\"您确定要删除之前的定位吗?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.$emit(\"success\",{lnglat:null,address:null}),t.visible=!1})).catch((function(){}))}}}),l=n,s=(a(\"e9cc\"),a(\"2877\")),r=Object(s[\"a\"])(l,i,o,!1,null,\"236dde37\",null);e[\"a\"]=r.exports},\"660a\":function(t,e,a){\"use strict\";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"el-upload\",t._g(t._b({attrs:{action:t.action,\"file-list\":t.fileList,limit:t.limit,disabled:t.disabled,\"before-upload\":t.handleUpload,\"before-remove\":t.handleRemove,\"on-exceed\":t.uploadExceed,\"on-error\":t.uploadError,\"on-preview\":t.previewAttach}},\"el-upload\",t.$attrs,!1),t.$listeners),[t.disabled?[a(\"div\",{staticClass:\"el-upload__text\"},[t._v(\"附件列表\")])]:[t.simple?a(\"el-button\",{attrs:{size:t.btnSize,type:\"primary\"}},[t._v(\"点击上传\")]):[a(\"i\",{staticClass:\"el-icon-upload\"}),a(\"div\",{staticClass:\"el-upload__text\"},[t._v(\"将文件拖到此处，或\"),a(\"em\",[t._v(\"点击上传\")])])],a(\"div\",{staticClass:\"el-upload__tip\",attrs:{slot:\"tip\"},slot:\"tip\"},[t._v(t._s(t.showTip()))])]],2)},o=[],n=(a(\"a434\"),a(\"b0c0\"),a(\"a9e3\"),a(\"5c96\")),l=a(\"b775\"),s=a(\"4360\"),r={name:\"Upload\",props:{type:{type:String,default:\"GB\"},limit:{type:Number,default:8},tip:{type:String,default:null},disabled:{type:Boolean,default:!1},simple:{type:Boolean,default:!1},value:{type:Array,default:function(){return[]}},btnSize:{type:String,default:\"small\"}},data:function(){return{action:\"/api/file/upload/\"+this.type,fileList:this.value}},watch:{value:function(t){this.fileList=t}},methods:{showTip:function(){return this.tip?this.tip:\"文件个数不超过\"+this.limit+\"个，且大小不超过20MB\"},handleUpload:function(t){var e=this,a=new FormData;return a.append(\"file\",t,t.name),Object(l[\"a\"])({url:\"/file/upload/\"+this.type,data:a}).then((function(t){var a=t.data;a&&(e.fileList.push({id:a.id,name:a.name,ext:a.ext,url:\"/attach\"+a.path}),e.$emit(\"input\",e.fileList),e.$emit(\"success\",e.fileList))})),!1},handleRemove:function(t,e){var a=this;if(t.id){for(var i=0;i<this.fileList.length;i++)if(this.fileList[i].id===t.id){this.fileList.splice(i,1),this.$emit(\"input\",this.fileList);break}Object(l[\"a\"])(\"/file/remove/\"+t.id).then((function(e){e.code>0&&(n[\"Message\"].success(\"删除成功\"),a.$emit(\"removeFile\",t))}))}},uploadExceed:function(t,e){n[\"Message\"].warning(\"当前限制选择 \"+this.limit+\" 个文件，本次选择了 \"+t.length+\" 个文件，已上传了 \"+e.length+\" 个文件\")},uploadError:function(t,e,a){n[\"Message\"].error(t||\"上传出错\")},previewAttach:function(t){var e=this;if(t.response&&t.response.data)window.open(t.response.data.url,\"_blank\");else if(t.url)window.open(t.url,\"_blank\");else{var a=s[\"a\"].getters.attachContext||\"\",i=a+t.path;this.$axios({url:i,method:\"get\",responseType:\"blob\"}).then((function(a){200===a.status&&e.$saveAs(a.data,t.name)})).catch((function(t){e.$message.error(\"下载出错:\"+t)}))}}}},c=r,u=a(\"2877\"),d=Object(u[\"a\"])(c,i,o,!1,null,null,null);e[\"a\"]=d.exports},\"6ecd\":function(t,e,a){\"use strict\";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"el-container\",{staticClass:\"page-table-ctn\"},[a(\"el-table\",t._g(t._b({directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],ref:\"grid\",attrs:{\"max-height\":t.maxHeight,data:t.rows}},\"el-table\",t.$attrs,!1),t.$listeners),[t._t(\"default\")],2),t.paging?a(\"el-footer\",{staticClass:\"footer\"},[a(\"div\",{staticClass:\"size-info\"},[t.total>1?a(\"span\",[t._v(\"显示第 \"+t._s(t.from)+\" 条到第 \"+t._s(t.to)+\" 条的数据，\")]):t._e(),t._v(\" 共\"+t._s(t.total)+\" 条数据 \")]),a(\"el-pagination\",t._b({staticStyle:{float:\"right\"},attrs:{layout:t.layout,\"page-sizes\":t.pageSizes,\"current-page\":t.pi,\"page-size\":t.pz,total:t.total},on:{\"current-change\":t.handleNumberChange,\"size-change\":t.handleSizeChange}},\"el-pagination\",t.$attrs,!1))],1):t._e()],1)},o=[],n=a(\"53ca\"),l=(a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"841c\"),a(\"0643\"),a(\"4e3e\"),a(\"159b\"),a(\"b775\")),s={name:\"PageTable\",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:\"sizes, prev, pager, next, jumper\"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var a=this;if(this.path){var i={pageNumber:1},o=Object(n[\"a\"])(t);\"undefined\"===o?i.pageNumber=1:\"number\"===o?i.pageNumber=t:\"object\"===o?(this.params=t,\"number\"===typeof e&&(i.pageNumber=e),\"boolean\"===typeof e&&this.empty()):i.pageNumber=t.pageNumber,this.pi=i.pageNumber,this.paging&&(this.params.pageNumber=i.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(l[\"a\"])({url:this.path,data:this.params}).then((function(t){a.loading=!1,a.paging?a.renderPage(t):a.renderList(t.rows?t.rows:t),a.$emit(\"loaded\",t)})).catch((function(t){a.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t=\"id\");for(var a=[],i=0;i<e.length;i++)e[i][t]&&a.push(e[i][t]);return a}}},r=s,c=(a(\"b2d4\"),a(\"2877\")),u=Object(c[\"a\"])(r,i,o,!1,null,\"bdcc19d8\",null);e[\"a\"]=u.exports},\"7a94\":function(t,e,a){},\"841c\":function(t,e,a){\"use strict\";var i=a(\"d784\"),o=a(\"825a\"),n=a(\"1d80\"),l=a(\"129f\"),s=a(\"14c3\");i(\"search\",1,(function(t,e,a){return[function(e){var a=n(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var n=o(t),r=String(this),c=n.lastIndex;l(c,0)||(n.lastIndex=0);var u=s(n,r);return l(n.lastIndex,c)||(n.lastIndex=c),null===u?-1:u.index}]}))},a434:function(t,e,a){\"use strict\";var i=a(\"23e7\"),o=a(\"23cb\"),n=a(\"a691\"),l=a(\"50c4\"),s=a(\"7b0b\"),r=a(\"65f0\"),c=a(\"8418\"),u=a(\"1dde\"),d=a(\"ae40\"),p=u(\"splice\"),h=d(\"splice\",{ACCESSORS:!0,0:0,1:2}),f=Math.max,m=Math.min,g=9007199254740991,v=\"Maximum allowed length exceeded\";i({target:\"Array\",proto:!0,forced:!p||!h},{splice:function(t,e){var a,i,u,d,p,h,b=s(this),_=l(b.length),w=o(t,_),y=arguments.length;if(0===y?a=i=0:1===y?(a=0,i=_-w):(a=y-2,i=m(f(n(e),0),_-w)),_+a-i>g)throw TypeError(v);for(u=r(b,i),d=0;d<i;d++)p=w+d,p in b&&c(u,d,b[p]);if(u.length=i,a<i){for(d=w;d<_-i;d++)p=d+i,h=d+a,p in b?b[h]=b[p]:delete b[h];for(d=_;d>_-i+a;d--)delete b[d-1]}else if(a>i)for(d=_-i;d>w;d--)p=d+i-1,h=d+a-1,p in b?b[h]=b[p]:delete b[h];for(d=0;d<a;d++)b[d+w]=arguments[d+2];return b.length=_-i+a,u}})},ac65:function(t,e,a){},ad10:function(t,e,a){\"use strict\";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"el-select\",t._g(t._b({attrs:{loading:t.loading,filterable:\"\",\"filter-method\":t.filter}},\"el-select\",t.$attrs,!1),t.$listeners),t._l(t.options,(function(e){return a(\"el-option\",{key:e.id,attrs:{label:e.name,value:e.id}},[a(\"span\",{staticStyle:{float:\"left\"}},[t._v(t._s(e.name))]),a(\"span\",{staticStyle:{float:\"right\",color:\"#8492a6\",\"font-size\":\"13px\"}},[t._v(t._s(e.no||e.account))])])})),1)},o=[],n=(a(\"4de4\"),a(\"b0c0\"),a(\"d3b7\"),a(\"0643\"),a(\"2382\"),a(\"4e3e\"),a(\"159b\"),a(\"b775\")),l={name:\"UserChosen\",props:{type:{type:String,default:null}},data:function(){return{loading:!1,options:[],list:[]}},mounted:function(){this.load()},methods:{load:function(){var t=this;this.loading=!0,Object(n[\"a\"])(\"/sys/user/list/\"+(this.type||\"all\")).then((function(e){t.loading=!1,e&&e.length&&(t.list=e,t.filter())})).catch((function(e){t.loading=!1,console.log(e)}))},getData:function(){return this.options},filter:function(t){if(t){var e=[];this.options.forEach((function(a){if(a.name&&-1!==a.name.indexOf(t))e.push(a);else if(a.no){if(-1!==a.no.indexOf(t))return void e.push(a)}else if(a.account&&-1!==a.account.indexOf(t))return void e.push(a)})),this.options=e}else this.options=this.list}}},s=l,r=a(\"2877\"),c=Object(r[\"a\"])(s,i,o,!1,null,null,null);e[\"a\"]=c.exports},b2d4:function(t,e,a){\"use strict\";a(\"ac65\")},c75a:function(t,e,a){\"use strict\";a(\"7a94\")},dcb5:function(t,e,a){},e9cc:function(t,e,a){\"use strict\";a(\"dcb5\")},ee5a:function(t,e,a){\"use strict\";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"el-popover\",{ref:\"popover\",attrs:{placement:\"bottom-start\",trigger:\"click\"},on:{show:t.onShowPopover,hide:t.onHidePopover}},[a(\"el-tree\",{ref:\"tree\",staticClass:\"select-tree\",style:\"min-width: \"+t.treeWidth,attrs:{\"highlight-current\":\"\",data:t.data,props:t.props,\"expand-on-click-node\":!1,\"filter-node-method\":t.filterNode,\"default-expand-all\":t.expandAll},on:{\"node-click\":t.onClickNode}}),a(\"el-input\",{ref:\"input\",class:{rotate:t.showStatus},style:\"width: \"+t.width+\"px\",attrs:{slot:\"reference\",clearable:t.clearable,\"suffix-icon\":\"el-icon-arrow-down\",placeholder:t.placeholder},slot:\"reference\",model:{value:t.labelModel,callback:function(e){t.labelModel=e},expression:\"labelModel\"}})],1)},o=[],n=(a(\"99af\"),a(\"4de4\"),a(\"d3b7\"),a(\"0643\"),a(\"2382\"),a(\"b775\")),l={name:\"TreeBox\",model:{prop:\"value\",event:\"selected\"},props:{value:{type:String,default:null},width:{type:String,default:null},expandAll:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},data:{type:Array,default:null},path:{type:String,default:null},params:{type:Object,default:null},placeholder:{type:String,required:!1,default:\"请选择\"},labelField:{type:String,default:null},props:{type:Object,required:!1,default:function(){return{parent:\"pid\",value:\"id\",label:\"label\",children:\"children\"}}}},data:function(){return{showStatus:!1,treeWidth:\"auto\",labelModel:\"\",valueModel:\"0\"}},watch:{labelModel:function(t){t||(this.valueModel=\"\"),this.$refs.tree.filter(t)},value:function(t){this.labelModel=this.queryTree(this.data,t)}},created:function(){var t=this;null!=this.path?Object(n[\"a\"])({url:this.path,data:this.params}).then((function(e){t.init(e),t.$emit(\"loaded\",e)})).catch((function(t){console.log(t)})):this.init(this.data)},methods:{init:function(t){var e=this;this.data=t||[],this.labelField&&(this.props.label=this.labelField),this.value&&(this.labelModel=this.queryTree(this.data,this.value)),this.$nextTick((function(){e.treeWidth=\"\".concat((e.width||e.$refs.input.$refs.input.clientWidth)-24,\"px\")}))},onClickNode:function(t){this.labelModel=t[this.props.label],this.valueModel=t[this.props.value],this.onCloseTree()},onCloseTree:function(){this.$refs.popover.showPopper=!1},onShowPopover:function(){this.showStatus=!0,this.$refs.tree.filter(!1)},onHidePopover:function(){this.showStatus=!1,this.$emit(\"selected\",this.valueModel)},filterNode:function(t,e){return!t||-1!==e[this.props.label].indexOf(t)},queryTree:function(t,e){var a=[];a=a.concat(t);while(a.length){var i=a.shift();if(i[this.props.children]&&(a=a.concat(i[this.props.children])),i[this.props.value]===e)return i[this.props.label]}return\"\"}}},s=l,r=(a(\"4f8b\"),a(\"2877\")),c=Object(r[\"a\"])(s,i,o,!1,null,null,null);e[\"a\"]=c.exports}}]);", "extractedComments": []}