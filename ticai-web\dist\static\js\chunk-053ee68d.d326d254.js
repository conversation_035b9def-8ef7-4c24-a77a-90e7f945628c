(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-053ee68d"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"2fd4":function(e,t,a){},"6ecd":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},n=[],s=a("53ca"),o=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),r={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var i={pageNumber:1},n=Object(s["a"])(e);"undefined"===n?i.pageNumber=1:"number"===n?i.pageNumber=e:"object"===n?(this.params=e,"number"===typeof t&&(i.pageNumber=t),"boolean"===typeof t&&this.empty()):i.pageNumber=e.pageNumber,this.pi=i.pageNumber,this.paging&&(this.params.pageNumber=i.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(o["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],i=0;i<t.length;i++)t[i][e]&&a.push(t[i][e]);return a}}},c=r,l=(a("b2d4"),a("2877")),u=Object(l["a"])(c,i,n,!1,null,"bdcc19d8",null);t["a"]=u.exports},"841c":function(e,t,a){"use strict";var i=a("d784"),n=a("825a"),s=a("1d80"),o=a("129f"),r=a("14c3");i("search",1,(function(e,t,a){return[function(t){var a=s(this),i=void 0==t?void 0:t[e];return void 0!==i?i.call(t,a):new RegExp(t)[e](String(a))},function(e){var i=a(t,e,this);if(i.done)return i.value;var s=n(e),c=String(this),l=s.lastIndex;o(l,0)||(s.lastIndex=0);var u=r(s,c);return o(s.lastIndex,l)||(s.lastIndex=l),null===u?-1:u.index}]}))},ac65:function(e,t,a){},adad:function(e,t,a){"use strict";a("2fd4")},b2d4:function(e,t,a){"use strict";a("ac65")},cdfb:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter"},[a("el-form",{attrs:{model:e.qform},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"检索：","label-width":e.formLabelWidth}},[a("el-input",{attrs:{clearable:"",placeholder:"输入关键字",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{staticClass:"filter-item",attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)],1)],1)],1),a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{path:"/sys/role/page",query:e.qform,stripe:!0,border:!0}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"名称",prop:"name",width:"250"}}),a("el-table-column",{attrs:{label:"说明",prop:"memo"}}),a("el-table-column",{attrs:{label:"操作",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return a.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return a.stopPropagation(),e.assign(t.row)}}},[e._v("权限")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return a.stopPropagation(),e.remove(t.row)}}},[e._v("删除")])]}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"角色信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules}},[a("el-form-item",{attrs:{label:"名称：","label-width":e.formLabelWidth,prop:"name"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"说明：","label-width":e.formLabelWidth}},[a("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.memo,callback:function(t){e.$set(e.form,"memo",t)},expression:"form.memo"}})],1),a("el-form-item",{attrs:{label:"排序：","label-width":e.formLabelWidth,prop:"ord"}},[a("el-input-number",{attrs:{min:0,max:9999,autocomplete:"off"},model:{value:e.form.ord,callback:function(t){e.$set(e.form,"ord",t)},expression:"form.ord"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),a("menus",{ref:"menus",on:{success:e.assigned}})],1)},n=[],s=(a("ac1f"),a("841c"),a("6ecd")),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],ref:"dialog",attrs:{title:"权限菜单分配",top:"50px",width:"80%",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("div",{style:{overflow:"auto",height:e.height+"px"}},[a("el-tree",{ref:"tree",staticClass:"filter-tree",attrs:{"default-expand-all":"","show-checkbox":"","node-key":"code",data:e.treeData},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",[e._v(e._s(i.name))]),null!=i.actions?a("span",{staticStyle:{"margin-left":"20px"}},[a("el-checkbox",{key:i.code,staticClass:"check-all",attrs:{indeterminate:e.indeterminates[i.code],label:i.code},on:{"update:indeterminate":function(t){return e.$set(e.indeterminates,i.code,t)},change:function(t){return e.checkChangeAll(i)}},model:{value:i.checked,callback:function(t){e.$set(i,"checked",t)},expression:"data.checked"}},[e._v("全选")]),e._l(i.actions,(function(t){return a("el-checkbox",{key:t.code,attrs:{label:t.code},on:{change:function(a){return e.checkChange(t)}},model:{value:t.checked,callback:function(a){e.$set(t,"checked",a)},expression:"act.checked"}},[e._v(e._s(t.name))])}))],2):e._e()])}}])})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)])],1)},r=[],c=(a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),{data:function(){return{visible:!1,treeData:null,role:null,height:document.documentElement.clientHeight-250,actions:{},nodes:{},indeterminates:{}}},created:function(){},methods:{checkChangeAll:function(e){var t=this;this.indeterminates[e.code]=!1,Object.keys(this.actions).forEach((function(a){0===a.indexOf(e.code)&&(t.actions[a].checked=e.checked)}))},checkChange:function(e){var t=this,a=0;Object.keys(this.actions).forEach((function(i){0===i.indexOf(e.pcode)&&(t.actions[i].checked?a|=1:a|=2)}));var i=this.nodes[e.pcode];0===a||2===a?(i.checked=!1,this.indeterminates[e.pcode]=!1):1===a?(i.checked=!0,this.indeterminates[e.pcode]=!1):(i.checked=!1,this.indeterminates[e.pcode]=!0)},show:function(e){var t=this;this.visible=!0,this.role=e,null!=this.treeData?this.loadCheck(!0):this.$http({url:"/sys/menu/tree"}).then((function(e){t.recurseAction(e),t.treeData=e,t.loadCheck()})).catch((function(e){t.$message.error("无法加载到菜单树，错误："+e)}))},recurseAction:function(e,t){if(e&&e.length)for(var a,i=this,n=0;n<e.length;n++)a=e[n],a.actions?(a.actions.forEach((function(e){e.checked=!1,i.actions[e.code]=e})),a.checked=!1,i.nodes[a.code]=a):this.recurseAction(a.children)},loadCheck:function(e){var t=this;this.$refs.tree?this.loadRole(e):this.$nextTick((function(){t.loadCheck(e)}))},loadRole:function(e){var t=this,a=this.$refs;e&&a.tree.setCheckedKeys([]),this.$http("/sys/role/menuCodes/"+this.role).then((function(e){a.tree.setCheckedKeys(e.data),e.data&&e.data.length&&e.data.forEach((function(e){var a=t.actions[e];a&&(a.checked=!0)}))})).catch((function(e){t.$message.error("无法加载授权菜单信息，错误："+e)}))},close:function(){this.visible=!1},confirm:function(){var e=this,t={key:this.role,values:[]};this.$refs.tree.getCheckedKeys([]).forEach((function(e){t.values.push(e)})),Object.keys(this.actions).forEach((function(a){e.actions[a].checked&&t.values.push(a)})),this.$http({url:"/sys/role/assignMenu",data:t}).then((function(t){e.visible=!1,e.$message.success("保存成功"),e.$emit("success")})).catch((function(){e.$message.error("无法加载授权菜单信息")}))}}}),l=c,u=(a("adad"),a("2877")),d=Object(u["a"])(l,o,r,!1,null,null,null),h=d.exports,f={components:{PageTable:s["a"],Menus:h},data:function(){return{formLabelWidth:"100px",detailVisible:!1,menuVisible:!1,qform:{keyword:null},form:{},rules:{name:[{required:!0,message:"请输入角色名称",trigger:"blur"}]}}},mounted:function(){this.search()},methods:{search:function(){this.$refs.grid.search(this.qform)},add:function(){this.detailVisible=!0,this.form={ord:1},this.clearValidate()},detail:function(e){this.detailVisible=!0,this.form=Object.assign({},e),this.clearValidate()},remove:function(e){var t=this;this.$confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/role/delete/"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},clearValidate:function(){this.$refs.dataform&&this.$refs.dataform.clearValidate()},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&e.$http({url:"/sys/role/save",data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.search()),e.detailVisible=!1})).catch((function(){}))}))},assign:function(e){this.$refs.menus.show(e.id)},assigned:function(e){console.log(e)}}},m=f,p=Object(u["a"])(m,i,n,!1,null,null,null);t["default"]=p.exports}}]);