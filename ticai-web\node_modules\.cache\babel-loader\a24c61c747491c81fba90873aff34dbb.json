{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js!F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue", "mtime": 1752649761442}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}