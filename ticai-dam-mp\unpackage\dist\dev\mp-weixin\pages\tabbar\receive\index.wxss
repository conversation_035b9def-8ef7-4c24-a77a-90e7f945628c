
.container {
	margin: 10rpx;
}
.list-block {
	background-color: #EEE;
	padding: 0;
}
.rec {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
.list-item {
	margin-top: 1px;
	padding: 8px;
	background-color: #FFF;
	border-bottom: 1px solid #ccc;
}
.list-item .row {
	margin: 4px 0;
	display: flex;
	flex-direction: row;
	align-items: center;
}
.list-item .row .label {
	font-size: 12px;
	min-width: 60px;
	white-space: nowrap;
	line-height: 24px;
}
.list-item .row .text {
	flex: 1;
	font-size: 12px;
	color: #666;
	line-height: 24px;
}
.ceshi {
	height: 70rpx;
	line-height: 70rpx;
}
.but {
	min-height: 82rpx;
	line-height: 82rpx;
}
.test {
	height: 82rpx;
	background-color: #fff;
}

