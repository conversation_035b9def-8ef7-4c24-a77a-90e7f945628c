@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-calendar-item__weeks-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 1px 0;
  position: relative;
}
.uni-calendar-item__weeks-box-text {
  font-size: 14px;
  font-weight: bold;
  color: #001833;
}
.uni-calendar-item__weeks-box-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}
.uni-calendar-item__weeks-box-circle {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #dd524d;
}
.uni-calendar-item__weeks-box .uni-calendar-item--disable {
  cursor: default;
}
.uni-calendar-item--disable .uni-calendar-item__weeks-box-text-disable {
  color: #D1D1D1;
}
.uni-calendar-item--today {
  position: absolute;
  top: 10px;
  right: 17%;
  background-color: #dd524d;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.uni-calendar-item--extra {
  color: #dd524d;
  opacity: 0.8;
}
.uni-calendar-item__weeks-box .uni-calendar-item--checked {
  background-color: #007aff;
  border-radius: 50%;
  box-sizing: border-box;
  border: 3px solid #fff;
}
.uni-calendar-item--checked .uni-calendar-item--checked-text {
  color: #fff;
}
.uni-calendar-item--multiple .uni-calendar-item--checked-range-text {
  color: #333;
}
.uni-calendar-item--multiple {
  background-color: #F6F7FC;
}
.uni-calendar-item--multiple .uni-calendar-item--before-checked,
.uni-calendar-item--multiple .uni-calendar-item--after-checked {
  background-color: #007aff;
  border-radius: 50%;
  box-sizing: border-box;
  border: 3px solid #F6F7FC;
}
.uni-calendar-item--before-checked .uni-calendar-item--checked-text,
.uni-calendar-item--after-checked .uni-calendar-item--checked-text {
  color: #fff;
}
.uni-calendar-item--before-checked-x {
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  box-sizing: border-box;
  background-color: #F6F7FC;
}
.uni-calendar-item--after-checked-x {
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
  background-color: #F6F7FC;
}

