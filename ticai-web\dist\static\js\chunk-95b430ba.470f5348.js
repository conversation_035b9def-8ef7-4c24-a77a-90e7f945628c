(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-95b430ba"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},4736:function(e,t,a){},"4f8b":function(e,t,a){"use strict";a("4736")},"5edd":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tree-box",e._g(e._b({ref:"treeBox",attrs:{data:e.deptTree,loading:e.loading},on:{selected:e.selected}},"tree-box",e.$attrs,!1),e.$listeners))},o=[],s=a("b775"),i=a("ee5a"),r={name:"DeptTreeBox",components:{TreeBox:i["a"]},data:function(){return{loading:!1,deptTree:[]}},mounted:function(){this.load()},methods:{load:function(){var e=this;this.loading=!0,Object(s["a"])("/sys/dept/tree").then((function(t){e.loading=!1,e.deptTree=t,e.$nextTick((function(){e.$refs.treeBox.init(t)}))})).catch((function(){e.loading=!1,e.$alert("加载机构树出错")}))},selected:function(e){this.$emit("input",e)},getData:function(){return this.deptTree}}},n=r,d=a("2877"),c=Object(d["a"])(n,l,o,!1,null,null,null);t["a"]=c.exports},"660a":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-upload",e._g(e._b({attrs:{action:e.action,"file-list":e.fileList,limit:e.limit,disabled:e.disabled,"before-upload":e.handleUpload,"before-remove":e.handleRemove,"on-exceed":e.uploadExceed,"on-error":e.uploadError,"on-preview":e.previewAttach}},"el-upload",e.$attrs,!1),e.$listeners),[e.disabled?[a("div",{staticClass:"el-upload__text"},[e._v("附件列表")])]:[e.simple?a("el-button",{attrs:{size:e.btnSize,type:"primary"}},[e._v("点击上传")]):[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])],a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(e._s(e.showTip()))])]],2)},o=[],s=(a("a434"),a("b0c0"),a("a9e3"),a("5c96")),i=a("b775"),r=a("4360"),n={name:"Upload",props:{type:{type:String,default:"GB"},limit:{type:Number,default:8},tip:{type:String,default:null},disabled:{type:Boolean,default:!1},simple:{type:Boolean,default:!1},value:{type:Array,default:function(){return[]}},btnSize:{type:String,default:"small"}},data:function(){return{action:"/api/file/upload/"+this.type,fileList:this.value}},watch:{value:function(e){this.fileList=e}},methods:{showTip:function(){return this.tip?this.tip:"文件个数不超过"+this.limit+"个，且大小不超过20MB"},handleUpload:function(e){var t=this,a=new FormData;return a.append("file",e,e.name),Object(i["a"])({url:"/file/upload/"+this.type,data:a}).then((function(e){var a=e.data;a&&(t.fileList.push({id:a.id,name:a.name,ext:a.ext,url:"/attach"+a.path}),t.$emit("input",t.fileList),t.$emit("success",t.fileList))})),!1},handleRemove:function(e,t){var a=this;if(e.id){for(var l=0;l<this.fileList.length;l++)if(this.fileList[l].id===e.id){this.fileList.splice(l,1),this.$emit("input",this.fileList);break}Object(i["a"])("/file/remove/"+e.id).then((function(t){t.code>0&&(s["Message"].success("删除成功"),a.$emit("removeFile",e))}))}},uploadExceed:function(e,t){s["Message"].warning("当前限制选择 "+this.limit+" 个文件，本次选择了 "+e.length+" 个文件，已上传了 "+t.length+" 个文件")},uploadError:function(e,t,a){s["Message"].error(e||"上传出错")},previewAttach:function(e){var t=this;if(e.response&&e.response.data)window.open(e.response.data.url,"_blank");else if(e.url)window.open(e.url,"_blank");else{var a=r["a"].getters.attachContext||"",l=a+e.path;this.$axios({url:l,method:"get",responseType:"blob"}).then((function(a){200===a.status&&t.$saveAs(a.data,e.name)})).catch((function(e){t.$message.error("下载出错:"+e)}))}}}},d=n,c=a("2877"),p=Object(c["a"])(d,l,o,!1,null,null,null);t["a"]=p.exports},"6ecd":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},o=[],s=a("53ca"),i=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),r={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var l={pageNumber:1},o=Object(s["a"])(e);"undefined"===o?l.pageNumber=1:"number"===o?l.pageNumber=e:"object"===o?(this.params=e,"number"===typeof t&&(l.pageNumber=t),"boolean"===typeof t&&this.empty()):l.pageNumber=e.pageNumber,this.pi=l.pageNumber,this.paging&&(this.params.pageNumber=l.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(i["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],l=0;l<t.length;l++)t[l][e]&&a.push(t[l][e]);return a}}},n=r,d=(a("b2d4"),a("2877")),c=Object(d["a"])(n,l,o,!1,null,"bdcc19d8",null);t["a"]=c.exports},"841c":function(e,t,a){"use strict";var l=a("d784"),o=a("825a"),s=a("1d80"),i=a("129f"),r=a("14c3");l("search",1,(function(e,t,a){return[function(t){var a=s(this),l=void 0==t?void 0:t[e];return void 0!==l?l.call(t,a):new RegExp(t)[e](String(a))},function(e){var l=a(t,e,this);if(l.done)return l.value;var s=o(e),n=String(this),d=s.lastIndex;i(d,0)||(s.lastIndex=0);var c=r(s,n);return i(s.lastIndex,d)||(s.lastIndex=d),null===c?-1:c.index}]}))},a434:function(e,t,a){"use strict";var l=a("23e7"),o=a("23cb"),s=a("a691"),i=a("50c4"),r=a("7b0b"),n=a("65f0"),d=a("8418"),c=a("1dde"),p=a("ae40"),u=c("splice"),m=p("splice",{ACCESSORS:!0,0:0,1:2}),f=Math.max,h=Math.min,b=9007199254740991,v="Maximum allowed length exceeded";l({target:"Array",proto:!0,forced:!u||!m},{splice:function(e,t){var a,l,c,p,u,m,M=r(this),y=i(M.length),g=o(e,y),x=arguments.length;if(0===x?a=l=0:1===x?(a=0,l=y-g):(a=x-2,l=h(f(s(t),0),y-g)),y+a-l>b)throw TypeError(v);for(c=n(M,l),p=0;p<l;p++)u=g+p,u in M&&d(c,p,M[u]);if(c.length=l,a<l){for(p=g;p<y-l;p++)u=p+l,m=p+a,u in M?M[m]=M[u]:delete M[m];for(p=y;p>y-l+a;p--)delete M[p-1]}else if(a>l)for(p=y-l;p>g;p--)u=p+l-1,m=p+a-1,u in M?M[m]=M[u]:delete M[m];for(p=0;p<a;p++)M[p+g]=arguments[p+2];return M.length=y-l+a,c}})},ac65:function(e,t,a){},b2d4:function(e,t,a){"use strict";a("ac65")},c9ab:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"工单信息",width:"1200px",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.dataModel,size:"mini","label-suffix":":","label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"申请单号"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.no,callback:function(t){e.$set(e.dataModel,"no",t)},expression:"dataModel.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务类型",prop:"type"}},[a("el-input",{staticClass:"readonly",attrs:{value:"1"==e.dataModel.type?"上门服务":"远程指导",readonly:""}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维保单位",prop:"maintain"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.maintainName,callback:function(t){e.$set(e.dataModel,"maintainName",t)},expression:"dataModel.maintainName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"登记日期",prop:"registerTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.registerTime,callback:function(t){e.$set(e.dataModel,"registerTime",t)},expression:"dataModel.registerTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点用户",prop:"locationName"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationName,callback:function(t){e.$set(e.dataModel,"locationName",t)},expression:"dataModel.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点地址",prop:"locationAddress"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationAddress,callback:function(t){e.$set(e.dataModel,"locationAddress",t)},expression:"dataModel.locationAddress"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.contact,callback:function(t){e.$set(e.dataModel,"contact",t)},expression:"dataModel.contact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.phone,callback:function(t){e.$set(e.dataModel,"phone",t)},expression:"dataModel.phone"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备用电话",prop:"phoneRes"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.phoneRes,callback:function(t){e.$set(e.dataModel,"phoneRes",t)},expression:"dataModel.phoneRes"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"用户反映故障",prop:"faultReport"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.faultReport,callback:function(t){e.$set(e.dataModel,"faultReport",t)},expression:"dataModel.faultReport"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"现场检查故障",prop:"faultCheck"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.faultCheck,callback:function(t){e.$set(e.dataModel,"faultCheck",t)},expression:"dataModel.faultCheck"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"报障时间",prop:"reportTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.reportTime,callback:function(t){e.$set(e.dataModel,"reportTime",t)},expression:"dataModel.reportTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修时长",prop:"takeMinute"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.takeMinute,callback:function(t){e.$set(e.dataModel,"takeMinute",t)},expression:"dataModel.takeMinute"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("分钟")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"收费项目",prop:"feeFlag"}},[a("el-input",{staticClass:"readonly",attrs:{value:"1"==e.dataModel.feeFlag?"是":"否",readonly:""}})],1)],1)],1),"1"==e.dataModel.feeFlag?[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"材料费",prop:"matCost"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.matCost,callback:function(t){e.$set(e.dataModel,"matCost",t)},expression:"dataModel.matCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修费",prop:"maiCost"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.maiCost,callback:function(t){e.$set(e.dataModel,"maiCost",t)},expression:"dataModel.maiCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"费用合计",prop:"amount"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.amount,callback:function(t){e.$set(e.dataModel,"amount",t)},expression:"dataModel.amount"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1)],1)]:e._e(),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"排障时间",prop:"solveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.solveTime,callback:function(t){e.$set(e.dataModel,"solveTime",t)},expression:"dataModel.solveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"到达时间",prop:"arriveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.arriveTime,callback:function(t){e.$set(e.dataModel,"arriveTime",t)},expression:"dataModel.arriveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离开时间",prop:"leaveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.leaveTime,callback:function(t){e.$set(e.dataModel,"leaveTime",t)},expression:"dataModel.leaveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门",prop:"dept"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.deptName,callback:function(t){e.$set(e.dataModel,"deptName",t)},expression:"dataModel.deptName"}})],1)],1)],1),a("el-divider"),a("div",{staticStyle:{margin:"10px 0"}},[e._v("【服务项目】")]),a("el-table",{ref:"detail",attrs:{data:e.detailList,size:"mini",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"序列号",prop:"sn","min-width":"100px"}}),a("el-table-column",{attrs:{label:"设备名称",prop:"name",width:"200px"}}),a("el-table-column",{attrs:{label:"故障现象",prop:"fault","min-width":"120px"}}),a("el-table-column",{attrs:{label:"是否保修",prop:"flag",width:"85px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s("1"==l.flag?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"维修情况",prop:"solve","min-width":"120px"}})],1),e.dataModel.servicePoint?[a("div",{staticStyle:{margin:"10px 0",color:"#C00"}},[e._v("【服务评价】")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务态度",prop:"servicePoint"}},[a("el-rate",{staticStyle:{"margin-top":"5px"},attrs:{size:"mini",disabled:"","show-text":"",texts:["非常不满意","不满意","一般","满意","非常满意"]},model:{value:e.dataModel.servicePoint,callback:function(t){e.$set(e.dataModel,"servicePoint",t)},expression:"dataModel.servicePoint"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"确认手机",prop:"confirmMobile"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.confirmMobile,callback:function(t){e.$set(e.dataModel,"confirmMobile",t)},expression:"dataModel.confirmMobile"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"短信确认码",prop:"confirmCode"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.confirmCode,callback:function(t){e.$set(e.dataModel,"confirmCode",t)},expression:"dataModel.confirmCode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务人员",prop:"serviceUser"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.serviceUser,callback:function(t){e.$set(e.dataModel,"serviceUser",t)},expression:"dataModel.serviceUser"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"服务评价",prop:"serviceMemo"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.serviceMemo,callback:function(t){e.$set(e.dataModel,"serviceMemo",t)},expression:"dataModel.serviceMemo"}})],1)],1)],1)]:e._e()],2),e.fileList.length?[a("el-divider"),a("upload-file",{attrs:{simple:"",multiple:"",disabled:"","btn-size":"mini",type:"GD"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})]:e._e()],2)],1)},o=[],s=a("660a"),i={name:"ViewWo",components:{UploadFile:s["a"]},data:function(){return{fullscreenLoading:!1,visible:!1,dataModel:{},detailList:[],fileList:[]}},methods:{show:function(e){this.dataModel=e||{},this.detailList=this.dataModel.detailList||[],this.fileList=this.dataModel.attachList||[],this.visible=!0}}},r=i,n=a("2877"),d=Object(n["a"])(r,l,o,!1,null,null,null);t["a"]=d.exports},ee5a:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-popover",{ref:"popover",attrs:{placement:"bottom-start",trigger:"click"},on:{show:e.onShowPopover,hide:e.onHidePopover}},[a("el-tree",{ref:"tree",staticClass:"select-tree",style:"min-width: "+e.treeWidth,attrs:{"highlight-current":"",data:e.data,props:e.props,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"default-expand-all":e.expandAll},on:{"node-click":e.onClickNode}}),a("el-input",{ref:"input",class:{rotate:e.showStatus},style:"width: "+e.width+"px",attrs:{slot:"reference",clearable:e.clearable,"suffix-icon":"el-icon-arrow-down",placeholder:e.placeholder},slot:"reference",model:{value:e.labelModel,callback:function(t){e.labelModel=t},expression:"labelModel"}})],1)},o=[],s=(a("99af"),a("4de4"),a("d3b7"),a("0643"),a("2382"),a("b775")),i={name:"TreeBox",model:{prop:"value",event:"selected"},props:{value:{type:String,default:null},width:{type:String,default:null},expandAll:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},data:{type:Array,default:null},path:{type:String,default:null},params:{type:Object,default:null},placeholder:{type:String,required:!1,default:"请选择"},labelField:{type:String,default:null},props:{type:Object,required:!1,default:function(){return{parent:"pid",value:"id",label:"label",children:"children"}}}},data:function(){return{showStatus:!1,treeWidth:"auto",labelModel:"",valueModel:"0"}},watch:{labelModel:function(e){e||(this.valueModel=""),this.$refs.tree.filter(e)},value:function(e){this.labelModel=this.queryTree(this.data,e)}},created:function(){var e=this;null!=this.path?Object(s["a"])({url:this.path,data:this.params}).then((function(t){e.init(t),e.$emit("loaded",t)})).catch((function(e){console.log(e)})):this.init(this.data)},methods:{init:function(e){var t=this;this.data=e||[],this.labelField&&(this.props.label=this.labelField),this.value&&(this.labelModel=this.queryTree(this.data,this.value)),this.$nextTick((function(){t.treeWidth="".concat((t.width||t.$refs.input.$refs.input.clientWidth)-24,"px")}))},onClickNode:function(e){this.labelModel=e[this.props.label],this.valueModel=e[this.props.value],this.onCloseTree()},onCloseTree:function(){this.$refs.popover.showPopper=!1},onShowPopover:function(){this.showStatus=!0,this.$refs.tree.filter(!1)},onHidePopover:function(){this.showStatus=!1,this.$emit("selected",this.valueModel)},filterNode:function(e,t){return!e||-1!==t[this.props.label].indexOf(e)},queryTree:function(e,t){var a=[];a=a.concat(e);while(a.length){var l=a.shift();if(l[this.props.children]&&(a=a.concat(l[this.props.children])),l[this.props.value]===t)return l[this.props.label]}return""}}},r=i,n=(a("4f8b"),a("2877")),d=Object(n["a"])(r,l,o,!1,null,null,null);t["a"]=d.exports},f5d2:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"完成工单信息",width:"1200px",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.dataModel,size:"mini","label-suffix":":","label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"申请单号"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.no,callback:function(t){e.$set(e.dataModel,"no",t)},expression:"dataModel.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务类型",prop:"type"}},[a("el-input",{staticClass:"readonly",attrs:{value:"1"==e.dataModel.type?"上门服务":"远程指导",readonly:""}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维保单位",prop:"maintain"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.maintainName,callback:function(t){e.$set(e.dataModel,"maintainName",t)},expression:"dataModel.maintainName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"登记日期",prop:"registerTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.registerTime,callback:function(t){e.$set(e.dataModel,"registerTime",t)},expression:"dataModel.registerTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点用户",prop:"locationName"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationName,callback:function(t){e.$set(e.dataModel,"locationName",t)},expression:"dataModel.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点地址",prop:"locationAddress"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationAddress,callback:function(t){e.$set(e.dataModel,"locationAddress",t)},expression:"dataModel.locationAddress"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.contact,callback:function(t){e.$set(e.dataModel,"contact",t)},expression:"dataModel.contact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.phone,callback:function(t){e.$set(e.dataModel,"phone",t)},expression:"dataModel.phone"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备用电话",prop:"phoneRes"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.phoneRes,callback:function(t){e.$set(e.dataModel,"phoneRes",t)},expression:"dataModel.phoneRes"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"用户反映故障",prop:"faultReport"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.faultReport,callback:function(t){e.$set(e.dataModel,"faultReport",t)},expression:"dataModel.faultReport"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"现场检查故障",prop:"faultCheck"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.faultCheck,callback:function(t){e.$set(e.dataModel,"faultCheck",t)},expression:"dataModel.faultCheck"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"报障时间",prop:"reportTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.reportTime,callback:function(t){e.$set(e.dataModel,"reportTime",t)},expression:"dataModel.reportTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修时长",prop:"takeMinute"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.takeMinute,callback:function(t){e.$set(e.dataModel,"takeMinute",t)},expression:"dataModel.takeMinute"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("分钟")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"收费项目",prop:"feeFlag"}},[a("el-input",{staticClass:"readonly",attrs:{value:"1"==e.dataModel.feeFlag?"是":"否",readonly:""}})],1)],1)],1),"1"==e.dataModel.feeFlag?[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"材料费",prop:"matCost"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.matCost,callback:function(t){e.$set(e.dataModel,"matCost",t)},expression:"dataModel.matCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修费",prop:"maiCost"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.maiCost,callback:function(t){e.$set(e.dataModel,"maiCost",t)},expression:"dataModel.maiCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"费用合计",prop:"amount"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{readonly:""},model:{value:e.dataModel.amount,callback:function(t){e.$set(e.dataModel,"amount",t)},expression:"dataModel.amount"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1)],1)]:e._e(),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"排障时间",prop:"solveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.solveTime,callback:function(t){e.$set(e.dataModel,"solveTime",t)},expression:"dataModel.solveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"到达时间",prop:"arriveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.arriveTime,callback:function(t){e.$set(e.dataModel,"arriveTime",t)},expression:"dataModel.arriveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离开时间",prop:"leaveTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.leaveTime,callback:function(t){e.$set(e.dataModel,"leaveTime",t)},expression:"dataModel.leaveTime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门",prop:"dept"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.deptName,callback:function(t){e.$set(e.dataModel,"deptName",t)},expression:"dataModel.deptName"}})],1)],1)],1)],2),a("el-divider"),a("div",{staticStyle:{margin:"10px 0"}},[e._v("【服务项目】")]),a("el-table",{ref:"detail",attrs:{data:e.detailList,size:"mini",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"序列号",prop:"sn","min-width":"100px"}}),a("el-table-column",{attrs:{label:"设备名称",prop:"name",width:"200px"}}),a("el-table-column",{attrs:{label:"故障现象",prop:"fault","min-width":"120px"}}),a("el-table-column",{attrs:{label:"是否保修",prop:"flag",width:"85px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s("1"==l.flag?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"维修情况",prop:"solve","min-width":"120px"}})],1),a("div",{staticStyle:{margin:"10px 0",color:"#C00"}},[e._v("【服务评价】")]),a("el-form",{ref:"serviceform",attrs:{model:e.serviceModel,rules:e.rules,size:"mini","label-suffix":":","label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务态度",prop:"servicePoint"}},[a("el-rate",{staticStyle:{"margin-top":"5px"},attrs:{size:"mini","show-text":"",texts:["非常不满意","不满意","一般","满意","非常满意"]},model:{value:e.serviceModel.servicePoint,callback:function(t){e.$set(e.serviceModel,"servicePoint",t)},expression:"serviceModel.servicePoint"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"确认手机",prop:"confirmMobile"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{maxlength:"11",autocomplete:"off"},model:{value:e.serviceModel.confirmMobile,callback:function(t){e.$set(e.serviceModel,"confirmMobile",t)},expression:"serviceModel.confirmMobile"}},[a("template",{slot:"append"},[e.timeCount<=0?a("el-button",{staticStyle:{width:"120px",color:"#00C"},attrs:{type:"primary",icon:"el-icon-message"},on:{click:function(t){return t.stopPropagation(),e.sendSms(t)}}},[e._v("发送短信")]):a("el-button",{staticStyle:{width:"120px"},attrs:{disabled:"",icon:"el-icon-time"}},[e._v(e._s(e.timeCount)+"秒可重发")])],1)],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"短信确认码",prop:"confirmCode"}},[a("el-input",{staticStyle:{width:"100px"},attrs:{maxlength:"6",autocomplete:"off"},model:{value:e.serviceModel.confirmCode,callback:function(t){e.$set(e.serviceModel,"confirmCode",t)},expression:"serviceModel.confirmCode"}}),a("span",{staticStyle:{"margin-left":"6px",color:"#999"}},[e._v("手机收到的确认码")])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务人员",prop:"serviceUser"}},[a("el-input",{model:{value:e.serviceModel.serviceUser,callback:function(t){e.$set(e.serviceModel,"serviceUser",t)},expression:"serviceModel.serviceUser"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"服务评价",prop:"serviceMemo"}},[a("el-input",{model:{value:e.serviceModel.serviceMemo,callback:function(t){e.$set(e.serviceModel,"serviceMemo",t)},expression:"serviceModel.serviceMemo"}})],1)],1)],1)],1),a("el-divider"),a("div",{staticStyle:{margin:"10px 0"}},[e._v("【附件】")]),a("upload-file",{attrs:{simple:"",multiple:"","btn-size":"mini",type:"GD"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},o=[],s=(a("ac1f"),a("00b4"),a("660a")),i={name:"CloseWo",components:{UploadFile:s["a"]},data:function(){return{fullscreenLoading:!1,visible:!1,dataModel:{},serviceModel:{},rules:{servicePoint:[{required:!0,message:"请选择服务态度",trigger:"blur"}],confirmMobile:[{required:!0,message:"请输入确认手机号",trigger:"blur"}],confirmCode:[{required:!0,message:"请输入短信确认码",trigger:"blur"}]},detailList:[],fileList:[],timeCount:0,timer:null}},beforeDestroy:function(){this.timer&&clearInterval(this.timer)},methods:{show:function(e){this.dataModel=e||{},this.detailList=this.dataModel.detailList||[],this.fileList=this.dataModel.attachList||[],this.serviceModel={id:this.dataModel.id,confirmMobile:this.dataModel.phone||this.dataModel.phoneRes},this.visible=!0},sendSms:function(){var e=this;if(!this.serviceModel.confirmMobile||!/^1[3-9]\d{9}$/.test(this.serviceModel.confirmMobile))return this.$message.warning("请输入有效的手机号码");this.timeCount=60,this.timer&&clearInterval(this.timer),this.timer=setInterval((function(){e.timeCount--,e.timeCount<=0&&e.timer&&clearInterval(e.timer)}),1e3),this.$http({url:"/wo/bill/sendSms",data:this.serviceModel}).then((function(t){t.code>0&&t.data?(e.serviceModel.smsId=t.data,e.$message.success("发送成功")):(e.timer&&clearInterval(e.timer),e.timeCount=0,e.$message.error(t.msg||"发送失败"))})).catch((function(){e.timer&&clearInterval(e.timer),e.timeCount=0,e.$message.error("网络异常")}))},save:function(){var e=this;if(!this.serviceModel.smsId)return this.$message.warning("请发送短信获取确认码");this.$refs.serviceform.validate((function(t){t&&e.$http({url:"/wo/bill/close",data:e.serviceModel}).then((function(t){t.code>0&&(e.visible=!1,e.$message.success("关闭工单成功"),e.$emit("success"))}))}))}}},r=i,n=a("2877"),d=Object(n["a"])(r,l,o,!1,null,null,null);t["a"]=d.exports},f71b:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"解决工单信息",width:"1200px",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.dataModel,rules:e.rules,size:"mini","label-suffix":":","label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"申请单号"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.no,callback:function(t){e.$set(e.dataModel,"no",t)},expression:"dataModel.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"服务类型",prop:"type"}},[a("el-radio-group",{model:{value:e.dataModel.type,callback:function(t){e.$set(e.dataModel,"type",t)},expression:"dataModel.type"}},[a("el-radio",{attrs:{label:"1"}},[e._v("上门服务")]),a("el-radio",{attrs:{label:"2"}},[e._v("远程指导")])],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维保单位",prop:"maintain"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.maintainName,callback:function(t){e.$set(e.dataModel,"maintainName",t)},expression:"dataModel.maintainName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"登记日期",prop:"registerTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.registerTime,callback:function(t){e.$set(e.dataModel,"registerTime",t)},expression:"dataModel.registerTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点用户",prop:"locationName"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationName,callback:function(t){e.$set(e.dataModel,"locationName",t)},expression:"dataModel.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点地址",prop:"locationAddress"}},[a("el-input",{model:{value:e.dataModel.locationAddress,callback:function(t){e.$set(e.dataModel,"locationAddress",t)},expression:"dataModel.locationAddress"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[a("el-input",{model:{value:e.dataModel.contact,callback:function(t){e.$set(e.dataModel,"contact",t)},expression:"dataModel.contact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{model:{value:e.dataModel.phone,callback:function(t){e.$set(e.dataModel,"phone",t)},expression:"dataModel.phone"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备用电话",prop:"phoneRes"}},[a("el-input",{model:{value:e.dataModel.phoneRes,callback:function(t){e.$set(e.dataModel,"phoneRes",t)},expression:"dataModel.phoneRes"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"用户反映故障",prop:"faultReport"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.faultReport,callback:function(t){e.$set(e.dataModel,"faultReport",t)},expression:"dataModel.faultReport"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"现场检查故障",prop:"faultCheck"}},[a("el-input",{model:{value:e.dataModel.faultCheck,callback:function(t){e.$set(e.dataModel,"faultCheck",t)},expression:"dataModel.faultCheck"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"报障时间",prop:"reportTime"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.reportTime,callback:function(t){e.$set(e.dataModel,"reportTime",t)},expression:"dataModel.reportTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修时长",prop:"takeMinute"}},[a("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.takeMinute,callback:function(t){e.$set(e.dataModel,"takeMinute",t)},expression:"dataModel.takeMinute"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("分钟")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"收费项目",prop:"feeFlag"}},[a("el-radio-group",{model:{value:e.dataModel.feeFlag,callback:function(t){e.$set(e.dataModel,"feeFlag",t)},expression:"dataModel.feeFlag"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),a("el-radio",{attrs:{label:"2"}},[e._v("否")])],1)],1)],1)],1),"1"==e.dataModel.feeFlag?[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"材料费",prop:"matCost"}},[a("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.matCost,callback:function(t){e.$set(e.dataModel,"matCost",t)},expression:"dataModel.matCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"维修费",prop:"maiCost"}},[a("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.maiCost,callback:function(t){e.$set(e.dataModel,"maiCost",t)},expression:"dataModel.maiCost"}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"费用合计",prop:"amount"}},[a("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{value:parseInt(e.dataModel.matCost||0)+parseInt(e.dataModel.maiCost||0),type:"number",autocomplete:"off",readonly:""}},[a("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1)],1)]:e._e(),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"排障时间",prop:"solveTime"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.solveTime,callback:function(t){e.$set(e.dataModel,"solveTime",t)},expression:"dataModel.solveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"到达时间",prop:"arriveTime"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.arriveTime,callback:function(t){e.$set(e.dataModel,"arriveTime",t)},expression:"dataModel.arriveTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离开时间",prop:"leaveTime"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.leaveTime,callback:function(t){e.$set(e.dataModel,"leaveTime",t)},expression:"dataModel.leaveTime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门",prop:"dept"}},[a("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.dataModel.dept,callback:function(t){e.$set(e.dataModel,"dept",t)},expression:"dataModel.dept"}})],1)],1)],1)],2),a("el-divider"),a("div",{staticStyle:{margin:"10px 0"}},[e._v("【服务项目】")]),a("el-table",{ref:"detail",attrs:{data:e.detailList,size:"mini",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"序列号",prop:"sn","min-width":"100px"}}),a("el-table-column",{attrs:{label:"设备名称",prop:"name",width:"200px"}}),a("el-table-column",{attrs:{label:"故障现象",prop:"fault","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.edit?a("el-select",{staticStyle:{width:"100%"},attrs:{size:"mini","allow-create":"",filterable:""},model:{value:l.fault,callback:function(t){e.$set(l,"fault",t)},expression:"row.fault"}},e._l(e.faultTypeOptions,(function(e){return a("el-option",{key:e,attrs:{value:e,label:e}})})),1):a("span",[e._v(e._s(l.fault))])]}}])}),a("el-table-column",{attrs:{label:"是否保修",prop:"flag",width:"85px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.edit?a("el-select",{attrs:{size:"mini",placeholder:""},model:{value:l.flag,callback:function(t){e.$set(l,"flag",t)},expression:"row.flag"}},[a("el-option",{attrs:{value:"1",label:"是"}}),a("el-option",{attrs:{value:"2",label:"否"}})],1):a("span",[e._v(e._s("1"==l.flag?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"维修情况",prop:"solve","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[l.edit?a("el-select",{staticStyle:{width:"100%"},attrs:{size:"mini","allow-create":"",filterable:"","default-first-option":""},model:{value:l.solve,callback:function(t){e.$set(l,"solve",t)},expression:"row.solve"}},e._l(e.faultDealOptions,(function(e){return a("el-option",{key:e,attrs:{value:e,label:e}})})),1):a("span",[e._v(e._s(l.solve))])]}}])}),a("el-table-column",{attrs:{width:"60",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,o=t.$index;return[l.edit?a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-circle-check"},on:{click:function(t){return e.handleRowView(l,o)}}}):a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.handleRowEdit(l,o)}}})]}}])})],1),a("div",{staticStyle:{margin:"10px 0"}},[e._v("【附件】")]),a("upload-file",{attrs:{simple:"",multiple:"","btn-size":"mini",type:"GD"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},o=[],s=(a("b0c0"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("660a")),i=a("5edd"),r={name:"SolveWo",components:{UploadFile:s["a"],DeptTreeBox:i["a"]},data:function(){return{fullscreenLoading:!1,visible:!1,dataModel:{},faultTypeOptions:[],faultDealOptions:[],rules:{type:[{required:!0,message:"请选择服务类型",trigger:"blur"}],faultCheck:[{required:!0,message:"请输入现场检查故障",trigger:"blur"}],solveTime:[{required:!0,message:"请选择排障时间",trigger:"blur"}],takeMinute:[{required:!0,message:"请填写维修时长",trigger:"blur"}]},detailList:[],fileList:[]}},mounted:function(){var e=this;this.$http("/sys/dict/code/WO_FAULT_TYPE").then((function(t){t&&t.length&&t.forEach((function(t){t&&e.faultTypeOptions.push(t.name)}))})),this.$http("/sys/dict/code/WO_FAULT_DEAL").then((function(t){t&&t.length&&t.forEach((function(t){t&&e.faultDealOptions.push(t.name)}))}))},methods:{show:function(e){this.dataModel=e||{},this.detailList=this.dataModel.detailList||[],this.fileList=this.dataModel.attachList||[],this.visible=!0},handleRowEdit:function(e,t){e.edit=!0,this.$set(this.detailList,t,e)},handleRowView:function(e,t){e.edit=!1,this.$set(this.detailList,t,e)},save:function(){var e=this;if(0===this.detailList.length)return this.$message.warning("至少提供一个服务项目");this.$refs.dataform.validate((function(t){t&&(e.dataModel.detailList=e.detailList,e.dataModel.attachList=e.fileList,e.$http({url:"/wo/bill/solve",data:e.dataModel}).then((function(t){t.code>0&&(e.visible=!1,e.$message.success("解决工单成功"),e.$emit("success"))})))}))}}},n=r,d=a("2877"),c=Object(d["a"])(n,l,o,!1,null,null,null);t["a"]=c.exports}}]);