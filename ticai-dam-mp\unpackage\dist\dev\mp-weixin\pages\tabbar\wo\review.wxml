<view class="container"><view class="form-view"><view class="form-view-item"><label class="label">网点用户</label><text class="text">{{dataModel.locationName}}</text></view><view class="form-view-item"><label class="label">网点地址</label><text class="text">{{dataModel.locationAddress}}</text></view><view class="form-view-item"><label class="label">联系人</label><text class="text">{{dataModel.contact}}</text></view><view class="form-view-item"><label class="label">联系电话</label><text class="text">{{dataModel.phone}}</text></view><view class="form-view-item"><label class="label">故障反映</label><text class="text">{{dataModel.faultReport}}</text></view><view class="form-view-item"><label class="label">现场检查</label><text class="text">{{dataModel.faultCheck}}</text></view><view class="form-view-item"><label class="label">材料费</label><text class="text">{{dataModel.matCost}}</text></view><view class="form-view-item"><label class="label">维修费</label><text class="text">{{dataModel.maiCost}}</text></view><view class="form-view-item"><label class="label">合计金额</label><text class="text">{{dataModel.amount}}</text></view></view><block wx:for="{{dataModel.detailList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><uni-card vue-id="{{'d34a8722-1-'+index}}" title="{{item.name}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-view"><view class="form-view-item"><label class="label">规格型号</label><text class="text">{{item.spec}}</text></view><view class="form-view-item"><label class="label">序列号</label><text class="text">{{item.sn}}</text></view><view class="form-view-item"><label class="label">故障类型</label><text class="text">{{item.fault}}</text></view><view class="form-view-item"><label class="label">是否保修</label><text class="text">{{item.flag=='1'?'是':'否'}}</text></view><view class="form-view-item"><label class="label">维修情况</label><text class="text">{{item.solve}}</text></view></view></uni-card></block><uni-forms class="vue-ref" vue-id="d34a8722-2" modelValue="{{dataModel}}" rules="{{formRules}}" border="{{true}}" label-align="right" label-width="80" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('d34a8722-3')+','+('d34a8722-2')}}" label="服务评价:" required="{{true}}" name="servicePoint" bind:__l="__l" vue-slots="{{['default']}}"><uni-rate bind:input="__e" vue-id="{{('d34a8722-4')+','+('d34a8722-3')}}" value="{{dataModel.servicePoint}}" data-event-opts="{{[['^input',[['__set_model',['$0','servicePoint','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-rate></uni-forms-item><uni-forms-item vue-id="{{('d34a8722-5')+','+('d34a8722-2')}}" label="服务意见:" name="serviceMemo" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('d34a8722-6')+','+('d34a8722-5')}}" type="text" placeholder="请输入服务意见" value="{{dataModel.serviceMemo}}" data-event-opts="{{[['^input',[['__set_model',['$0','serviceMemo','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('d34a8722-7')+','+('d34a8722-2')}}" label="确认手机:" required="{{true}}" name="confirmMobile" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('d34a8722-8')+','+('d34a8722-7')}}" type="text" placeholder="请输入手机号码" value="{{dataModel.confirmMobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','confirmMobile','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('d34a8722-9')+','+('d34a8722-2')}}" label="确认码:" required="{{true}}" name="confirmCode" bind:__l="__l" vue-slots="{{['default']}}"><uni-row vue-id="{{('d34a8722-10')+','+('d34a8722-9')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('d34a8722-11')+','+('d34a8722-10')}}" span="{{12}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('d34a8722-12')+','+('d34a8722-11')}}" type="text" size="mini" placeholder="请输入确认码" value="{{dataModel.confirmCode}}" data-event-opts="{{[['^input',[['__set_model',['$0','confirmCode','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('d34a8722-13')+','+('d34a8722-10')}}" span="{{12}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{smsTimeCount>0}}"><text class="form-text">{{smsTimeCount+"秒可重发"}}</text></block><block wx:else><button type="primary" size="mini" plain="true" data-event-opts="{{[['tap',[['sendSms',['$event']]]]]}}" bindtap="__e">获取确认码</button></block></uni-col></uni-row></uni-forms-item></uni-forms><uni-file-picker bind:input="__e" vue-id="d34a8722-14" title="附件" limit="{{6}}" fileMediatype="image" mode="grid" readonly="{{true}}" value="{{imageValue}}" data-event-opts="{{[['^input',[['__set_model',['','imageValue','$event',[]]]]]]}}" bind:__l="__l"></uni-file-picker><view style="margin-top:20rpx;"><button type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提 交</button></view></view>