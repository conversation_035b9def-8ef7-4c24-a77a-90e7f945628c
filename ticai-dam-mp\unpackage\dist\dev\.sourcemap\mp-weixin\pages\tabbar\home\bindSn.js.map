{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>", "uni-app:///pages/tabbar/home/<USER>", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "asset", "sn", "info", "onLoad", "methods", "loadData", "ctx", "that", "searchSn", "saveBind", "value", "text"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACuDlzB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACA;UACAC;QACA;MACA;QACAD;MACA;IACA;IACAE;MACA,yCACA;QACA;QACAF;UACA;UACA;UACAC;QACA;MACA;IACA;IACAE;MACA;MACA;MACAH;QACAI;QACAC;MACA;QACA;QACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAA4nC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACAhpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bindSn.vue?vue&type=template&id=699739a0&\"\nvar renderjs\nimport script from \"./bindSn.vue?vue&type=script&lang=js&\"\nexport * from \"./bindSn.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bindSn.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/home/<USER>\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bindSn.vue?vue&type=template&id=699739a0&\"", "var components\ntry {\n  components = {\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-row/uni-row\" */ \"@dcloudio/uni-ui/lib/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-col/uni-col\" */ \"@dcloudio/uni-ui/lib/uni-col/uni-col.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bindSn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bindSn.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<text class=\"caption\">资产标签信息</text>\r\n\t\t<view class=\"form-view\">\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">资产编码</label>\r\n\t\t\t\t<text class=\"text\">{{ asset.no }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">资产名称</label>\r\n\t\t\t\t<text class=\"text\">{{ asset.name }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点用户</label>\r\n\t\t\t\t<text class=\"text\">{{ asset.locationName }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">原终端号</label>\r\n\t\t\t\t<text class=\"text\">{{ asset.sn }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<text class=\"caption\">绑定信息</text>\r\n\t\t<uni-row>\r\n\t\t\t<uni-col :span=\"18\">\r\n\t\t\t\t<uni-easyinput type=\"text\" size=\"mini\" v-model=\"sn\" placeholder=\"请输入终端号\" style=\"height: 60rpx;\" />\r\n\t\t\t</uni-col>\r\n\t\t\t<uni-col :span=\"6\">\r\n\t\t\t\t<button type=\"primary\" class=\"ceshi\" plain=\"true\" @click=\"searchSn\">核查</button>\r\n\t\t\t</uni-col>\r\n\t\t</uni-row>\r\n\t\t<view class=\"form-view\">\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点名称</label>\r\n\t\t\t\t<text class=\"text\">{{ info.locationName }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点地址</label>\r\n\t\t\t\t<text class=\"text\">{{ info.locationAddress }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点电话</label>\r\n\t\t\t\t<text class=\"text\">{{ info.locationPhone }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">原资产编号</label>\r\n\t\t\t\t<view class=\"text\">{{ info.assetNo }}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"margin-top: 100rpx;\">\r\n\t\t\t<button type=\"primary\" @click=\"saveBind\">提交绑定</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport settings from '../../../utils/settings.js'\r\n\timport * as ctx from '../../../utils/context.js'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tasset: {},\r\n\t\t\t\tsn: null,\r\n\t\t\t\tinfo: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.loadData(option.id);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadData(id) {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\tif (id) {\r\n\t\t\t\t\tctx.post('/am/asset/getWithLocation/' + id, function(r) {\r\n\t\t\t\t\t\tif (r.code < 0 || r.data == null) return ctx.error('无法获取资产信息', 'back')\r\n\t\t\t\t\t\tthat.asset = r.data\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tctx.error('无效参数', 'back')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsearchSn() {\r\n\t\t\t\tif (this.sn == null) this.info = {}\r\n\t\t\t\telse {\r\n\t\t\t\t\tconst that = this;\r\n\t\t\t\t\tctx.post('/wx/findSn/' + this.sn, function(r) {\r\n\t\t\t\t\t\tif (r.code < 0) return ctx.error(r.msg)\r\n\t\t\t\t\t\tif (r.data == null) return ctx.error('该终端号不存在')\r\n\t\t\t\t\t\tthat.info = r.data\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsaveBind() {\r\n\t\t\t\tconst that = this\r\n                  if (!this.sn) return ctx.error('请输入终端号')\r\n\t\t\t\t    ctx.post('/wx/bindSn', {\r\n\t\t\t\t\tvalue: this.asset.id,\r\n\t\t\t\t\ttext: this.sn\r\n\t\t\t\t}, function(r) {\r\n\t\t\t\t\tif (r.code < 0 ) return ctx.error(r.msg)\r\n\t\t\t\t\tctx.ok('绑定成功', 'back')\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tmargin: 12rpx;\r\n\t}\r\n\r\n\t.radio {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\t.ceshi {\r\n\t\theight: 70rpx;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bindSn.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./bindSn.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623935\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}