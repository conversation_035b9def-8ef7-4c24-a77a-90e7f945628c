{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?108c", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?b4a0", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?619f", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?91c8", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?01b1", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue?3dc3"], "names": ["name", "mixins", "emits", "props", "mode", "type", "default", "multiple", "value", "modelValue", "localdata", "min", "max", "wrap", "icon", "selectedColor", "selectedTextColor", "emptyText", "disabled", "map", "text", "watch", "handler", "deep", "mixinDatacomResData", "data", "dataList", "range", "contentText", "contentdown", "contentrefresh", "contentnomore", "isLocal", "styles", "isTop", "computed", "dataValue", "created", "methods", "loadData", "getForm", "parent", "parentName", "change", "detail", "getDataList", "item", "list", "setRang<PERSON>", "setStyles", "getSelectedValue", "<PERSON><PERSON><PERSON>", "setStyleBackgroud", "classles", "setStyleIcon", "setStyleIconText", "setStyleRightIcon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACc;;;AAG9E;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyyB,CAAgB,yzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgD7zB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,gBAyBA;EACAA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IAEAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;QACA;UACAc;UACAZ;QACA;MACA;IACA;EACA;EACAa;IACAX;MACAY;QACA;QACA;MACA;MACAC;IACA;IACAC;MACA;MACA;IACA;IACAhB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAgB;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAlB;QACAC;MACA;MACAkB;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;MACA;MACA;MACA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACAnC;QACAiB;MACA;MAEA;QACA;UAEA;YACAmB;YACAA;UACA;QACA;MACA;QACA;UAAA;QAAA;QACA;UACAA;YACApC;YACAiB;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAmB;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;UACArC;QACA;MACA;MACAkB;QACAoB;QACA;UACA;YACA;cAAA;YAAA;YACAA;UACA;YACAA;UACA;QACA;UACAA;QACA;QAEAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;MACAD;QACA;UACA;YACA;cAAA;YAAA;YACA;cACAD;YACA;UACA;UAEA;YACA;cAAA;YAAA;YACA;cACAA;YACA;UACA;QACA;QACA;QACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAE;MACA;MACAH;MACAA;MACAA;MACAA;IACA;IAEA;AACA;AACA;AACA;IACAI;MAAA;MACA;MACA;MACAvB;QACA;UACAwB;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAnB;QACA;QACA;UACAA;QACA;MACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACArB;QACAA;QAEA;UACAA;UACAA;QACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;QACA;QACA;UACAtB;QACA;UACAA;QACA;QACA;UACAA;QACA;MACA;MACA;QACAoB;MACA;MACA;IACA;IACAG;MACA;MACA;MACA;QACAvB;MACA;MACA;QACAoB;MACA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxcA;AAAA;AAAA;AAAA;AAA4+C,CAAgB,g8CAAG,EAAC,C;;;;;;;;;;;ACAhgD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-data-checkbox.vue?vue&type=template&id=57ccb15a&\"\nvar renderjs\nimport script from \"./uni-data-checkbox.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-data-checkbox.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=template&id=57ccb15a&\"", "var components\ntry {\n  components = {\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more\" */ \"@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-data-checklist\" :style=\"{'margin-top':isTop+'px'}\">\r\n\t\t<template v-if=\"!isLocal\">\r\n\t\t\t<view class=\"uni-data-loading\">\r\n\t\t\t\t<uni-load-more v-if=\"!mixinDatacomErrorMessage\" status=\"loading\" iconType=\"snow\" :iconSize=\"18\"\r\n\t\t\t\t\t:content-text=\"contentText\"></uni-load-more>\r\n\t\t\t\t<text v-else>{{mixinDatacomErrorMessage}}</text>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t\t<template v-else>\r\n\t\t\t<checkbox-group v-if=\"multiple\" class=\"checklist-group\" :class=\"{'is-list':mode==='list' || wrap}\"\r\n\t\t\t\t@change=\"change\">\r\n\t\t\t\t<label class=\"checklist-box\"\r\n\t\t\t\t\t:class=\"['is--'+mode,item.selected?'is-checked':'',(disabled || !!item.disabled)?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']\"\r\n\t\t\t\t\t:style=\"item.styleBackgroud\" v-for=\"(item,index) in dataList\" :key=\"index\">\r\n\t\t\t\t\t<checkbox class=\"hidden\" hidden :disabled=\"disabled || !!item.disabled\" :value=\"item[map.value]+''\"\r\n\t\t\t\t\t\t:checked=\"item.selected\" />\r\n\t\t\t\t\t<view v-if=\"(mode !=='tag' && mode !== 'list') || ( mode === 'list' && icon === 'left')\"\r\n\t\t\t\t\t\tclass=\"checkbox__inner\" :style=\"item.styleIcon\">\r\n\t\t\t\t\t\t<view class=\"checkbox__inner-icon\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"checklist-content\" :class=\"{'list-content':mode === 'list' && icon ==='left'}\">\r\n\t\t\t\t\t\t<text class=\"checklist-text\" :style=\"item.styleIconText\">{{item[map.text]}}</text>\r\n\t\t\t\t\t\t<view v-if=\"mode === 'list' && icon === 'right'\" class=\"checkobx__list\" :style=\"item.styleBackgroud\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</label>\r\n\t\t\t</checkbox-group>\r\n\t\t\t<radio-group v-else class=\"checklist-group\" :class=\"{'is-list':mode==='list','is-wrap':wrap}\" @change=\"change\">\r\n\t\t\t\t<label class=\"checklist-box\"\r\n\t\t\t\t\t:class=\"['is--'+mode,item.selected?'is-checked':'',(disabled || !!item.disabled)?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']\"\r\n\t\t\t\t\t:style=\"item.styleBackgroud\" v-for=\"(item,index) in dataList\" :key=\"index\">\r\n\t\t\t\t\t<radio class=\"hidden\" hidden :disabled=\"disabled || item.disabled\" :value=\"item[map.value]+''\"\r\n\t\t\t\t\t\t:checked=\"item.selected\" />\r\n\t\t\t\t\t<view v-if=\"(mode !=='tag' && mode !== 'list') || ( mode === 'list' && icon === 'left')\" class=\"radio__inner\"\r\n\t\t\t\t\t\t:style=\"item.styleBackgroud\">\r\n\t\t\t\t\t\t<view class=\"radio__inner-icon\" :style=\"item.styleIcon\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"checklist-content\" :class=\"{'list-content':mode === 'list' && icon ==='left'}\">\r\n\t\t\t\t\t\t<text class=\"checklist-text\" :style=\"item.styleIconText\">{{item[map.text]}}</text>\r\n\t\t\t\t\t\t<view v-if=\"mode === 'list' && icon === 'right'\" :style=\"item.styleRightIcon\" class=\"checkobx__list\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</label>\r\n\t\t\t</radio-group>\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * DataChecklist 数据选择器\r\n\t * @description 通过数据渲染 checkbox 和 radio\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=xxx\r\n\t * @property {String} mode = [default| list | button | tag] 显示模式\r\n\t * @value default  \t默认横排模式\r\n\t * @value list\t\t列表模式\r\n\t * @value button\t按钮模式\r\n\t * @value tag \t\t标签模式\r\n\t * @property {Boolean} multiple = [true|false] 是否多选\r\n\t * @property {Array|String|Number} value 默认值\r\n\t * @property {Array} localdata 本地数据 ，格式 [{text:'',value:''}]\r\n\t * @property {Number|String} min 最小选择个数 ，multiple为true时生效\r\n\t * @property {Number|String} max 最大选择个数 ，multiple为true时生效\r\n\t * @property {Boolean} wrap 是否换行显示\r\n\t * @property {String} icon = [left|right]  list 列表模式下icon显示位置\r\n\t * @property {Boolean} selectedColor 选中颜色\r\n\t * @property {Boolean} emptyText 没有数据时显示的文字 ，本地数据无效\r\n\t * @property {Boolean} selectedTextColor 选中文本颜色，如不填写则自动显示\r\n\t * @property {Object} map 字段映射， 默认 map={text:'text',value:'value'}\r\n\t * @value left 左侧显示\r\n\t * @value right 右侧显示\r\n\t * @event {Function} change  选中发生变化触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniDataChecklist',\r\n\t\tmixins: [uniCloud.mixinDatacom || {}],\r\n\t\temits: ['input', 'update:modelValue', 'change'],\r\n\t\tprops: {\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\r\n\t\t\tmultiple: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [Array, String, Number],\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// TODO vue3\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [Array, String, Number],\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlocaldata: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmin: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmax: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\twrap: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\ticon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'left'\r\n\t\t\t},\r\n\t\t\tselectedColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tselectedTextColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\temptyText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '暂无数据'\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tmap: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\ttext: 'text',\r\n\t\t\t\t\t\tvalue: 'value'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tlocaldata: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.range = newVal\r\n\t\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(newVal))\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tmixinDatacomResData(newVal) {\r\n\t\t\t\tthis.range = newVal\r\n\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(newVal))\r\n\t\t\t},\r\n\t\t\tvalue(newVal) {\r\n\t\t\t\tthis.dataList = this.getDataList(newVal)\r\n\t\t\t\t// fix by mehaotian is_reset 在 uni-forms 中定义\r\n\t\t\t\t// if(!this.is_reset){\r\n\t\t\t\t// \tthis.is_reset = false\r\n\t\t\t\t// \tthis.formItem && this.formItem.setValue(newVal)\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tmodelValue(newVal) {\r\n\t\t\t\tthis.dataList = this.getDataList(newVal);\r\n\t\t\t\t// if(!this.is_reset){\r\n\t\t\t\t// \tthis.is_reset = false\r\n\t\t\t\t// \tthis.formItem && this.formItem.setValue(newVal)\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdataList: [],\r\n\t\t\t\trange: [],\r\n\t\t\t\tcontentText: {\r\n\t\t\t\t\tcontentdown: '查看更多',\r\n\t\t\t\t\tcontentrefresh: '加载中',\r\n\t\t\t\t\tcontentnomore: '没有更多'\r\n\t\t\t\t},\r\n\t\t\t\tisLocal: true,\r\n\t\t\t\tstyles: {\r\n\t\t\t\t\tselectedColor: '#2979ff',\r\n\t\t\t\t\tselectedTextColor: '#666',\r\n\t\t\t\t},\r\n\t\t\t\tisTop: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tdataValue() {\r\n\t\t\t\tif (this.value === '') return this.modelValue\r\n\t\t\t\tif (this.modelValue === '') return this.value\r\n\t\t\t\treturn this.value\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// this.form = this.getForm('uniForms')\r\n\t\t\t// this.formItem = this.getForm('uniFormsItem')\r\n\t\t\t// this.formItem && this.formItem.setValue(this.value)\r\n\r\n\t\t\t// if (this.formItem) {\r\n\t\t\t// \tthis.isTop = 6\r\n\t\t\t// \tif (this.formItem.name) {\r\n\t\t\t// \t\t// 如果存在name添加默认值,否则formData 中不存在这个字段不校验\r\n\t\t\t// \t\tif(!this.is_reset){\r\n\t\t\t// \t\t\tthis.is_reset = false\r\n\t\t\t// \t\t\tthis.formItem.setValue(this.dataValue)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t\tthis.rename = this.formItem.name\r\n\t\t\t// \t\tthis.form.inputChildrens.push(this)\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\r\n\t\t\tif (this.localdata && this.localdata.length !== 0) {\r\n\t\t\t\tthis.isLocal = true\r\n\t\t\t\tthis.range = this.localdata\r\n\t\t\t\tthis.dataList = this.getDataList(this.getSelectedValue(this.range))\r\n\t\t\t} else {\r\n\t\t\t\tif (this.collection) {\r\n\t\t\t\t\tthis.isLocal = false\r\n\t\t\t\t\tthis.loadData()\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadData() {\r\n\t\t\t\tthis.mixinDatacomGet().then(res => {\r\n\t\t\t\t\tthis.mixinDatacomResData = res.result.data\r\n\t\t\t\t\tif (this.mixinDatacomResData.length === 0) {\r\n\t\t\t\t\t\tthis.isLocal = false\r\n\t\t\t\t\t\tthis.mixinDatacomErrorMessage = this.emptyText\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.isLocal = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthis.mixinDatacomErrorMessage = err.message\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetForm(name = 'uniForms') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tconst values = e.detail.value\r\n\r\n\t\t\t\tlet detail = {\r\n\t\t\t\t\tvalue: [],\r\n\t\t\t\t\tdata: []\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.multiple) {\r\n\t\t\t\t\tthis.range.forEach(item => {\r\n\r\n\t\t\t\t\t\tif (values.includes(item[this.map.value] + '')) {\r\n\t\t\t\t\t\t\tdetail.value.push(item[this.map.value])\r\n\t\t\t\t\t\t\tdetail.data.push(item)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconst range = this.range.find(item => (item[this.map.value] + '') === values)\r\n\t\t\t\t\tif (range) {\r\n\t\t\t\t\t\tdetail = {\r\n\t\t\t\t\t\t\tvalue: range[this.map.value],\r\n\t\t\t\t\t\t\tdata: range\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// this.formItem && this.formItem.setValue(detail.value)\r\n\t\t\t\t// TODO 兼容 vue2\r\n\t\t\t\tthis.$emit('input', detail.value);\r\n\t\t\t\t// // TOTO 兼容 vue3\r\n\t\t\t\tthis.$emit('update:modelValue', detail.value);\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tdetail\r\n\t\t\t\t})\r\n\t\t\t\tif (this.multiple) {\r\n\t\t\t\t\t// 如果 v-model 没有绑定 ，则走内部逻辑\r\n\t\t\t\t\t// if (this.value.length === 0) {\r\n\t\t\t\t\tthis.dataList = this.getDataList(detail.value, true)\r\n\t\t\t\t\t// }\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.dataList = this.getDataList(detail.value)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取渲染的新数组\r\n\t\t\t * @param {Object} value 选中内容\r\n\t\t\t */\r\n\t\t\tgetDataList(value) {\r\n\t\t\t\t// 解除引用关系，破坏原引用关系，避免污染源数据\r\n\t\t\t\tlet dataList = JSON.parse(JSON.stringify(this.range))\r\n\t\t\t\tlet list = []\r\n\t\t\t\tif (this.multiple) {\r\n\t\t\t\t\tif (!Array.isArray(value)) {\r\n\t\t\t\t\t\tvalue = []\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tdataList.forEach((item, index) => {\r\n\t\t\t\t\titem.disabled = item.disable || item.disabled || false\r\n\t\t\t\t\tif (this.multiple) {\r\n\t\t\t\t\t\tif (value.length > 0) {\r\n\t\t\t\t\t\t\tlet have = value.find(val => val === item[this.map.value])\r\n\t\t\t\t\t\t\titem.selected = have !== undefined\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.selected = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\titem.selected = value === item[this.map.value]\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlist.push(item)\r\n\t\t\t\t})\r\n\t\t\t\treturn this.setRange(list)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 处理最大最小值\r\n\t\t\t * @param {Object} list\r\n\t\t\t */\r\n\t\t\tsetRange(list) {\r\n\t\t\t\tlet selectList = list.filter(item => item.selected)\r\n\t\t\t\tlet min = Number(this.min) || 0\r\n\t\t\t\tlet max = Number(this.max) || ''\r\n\t\t\t\tlist.forEach((item, index) => {\r\n\t\t\t\t\tif (this.multiple) {\r\n\t\t\t\t\t\tif (selectList.length <= min) {\r\n\t\t\t\t\t\t\tlet have = selectList.find(val => val[this.map.value] === item[this.map.value])\r\n\t\t\t\t\t\t\tif (have !== undefined) {\r\n\t\t\t\t\t\t\t\titem.disabled = true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (selectList.length >= max && max !== '') {\r\n\t\t\t\t\t\t\tlet have = selectList.find(val => val[this.map.value] === item[this.map.value])\r\n\t\t\t\t\t\t\tif (have === undefined) {\r\n\t\t\t\t\t\t\t\titem.disabled = true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.setStyles(item, index)\r\n\t\t\t\t\tlist[index] = item\r\n\t\t\t\t})\r\n\t\t\t\treturn list\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置 class\r\n\t\t\t * @param {Object} item\r\n\t\t\t * @param {Object} index\r\n\t\t\t */\r\n\t\t\tsetStyles(item, index) {\r\n\t\t\t\t//  设置自定义样式\r\n\t\t\t\titem.styleBackgroud = this.setStyleBackgroud(item)\r\n\t\t\t\titem.styleIcon = this.setStyleIcon(item)\r\n\t\t\t\titem.styleIconText = this.setStyleIconText(item)\r\n\t\t\t\titem.styleRightIcon = this.setStyleRightIcon(item)\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取选中值\r\n\t\t\t * @param {Object} range\r\n\t\t\t */\r\n\t\t\tgetSelectedValue(range) {\r\n\t\t\t\tif (!this.multiple) return this.dataValue\r\n\t\t\t\tlet selectedArr = []\r\n\t\t\t\trange.forEach((item) => {\r\n\t\t\t\t\tif (item.selected) {\r\n\t\t\t\t\t\tselectedArr.push(item[this.map.value])\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn this.dataValue.length > 0 ? this.dataValue : selectedArr\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 设置背景样式\r\n\t\t\t */\r\n\t\t\tsetStyleBackgroud(item) {\r\n\t\t\t\tlet styles = {}\r\n\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\r\n\t\t\t\tif (this.selectedColor) {\r\n\t\t\t\t\tif (this.mode !== 'list') {\r\n\t\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.mode === 'tag') {\r\n\t\t\t\t\t\tstyles['background-color'] = item.selected ? selectedColor : '#f5f5f5'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlet classles = ''\r\n\t\t\t\tfor (let i in styles) {\r\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\r\n\t\t\t\t}\r\n\t\t\t\treturn classles\r\n\t\t\t},\r\n\t\t\tsetStyleIcon(item) {\r\n\t\t\t\tlet styles = {}\r\n\t\t\t\tlet classles = ''\r\n\t\t\t\tif (this.selectedColor) {\r\n\t\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\r\n\t\t\t\t\tstyles['background-color'] = item.selected ? selectedColor : '#fff'\r\n\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\r\n\r\n\t\t\t\t\tif (!item.selected && item.disabled) {\r\n\t\t\t\t\t\tstyles['background-color'] = '#F2F6FC'\r\n\t\t\t\t\t\tstyles['border-color'] = item.selected ? selectedColor : '#DCDFE6'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i in styles) {\r\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\r\n\t\t\t\t}\r\n\t\t\t\treturn classles\r\n\t\t\t},\r\n\t\t\tsetStyleIconText(item) {\r\n\t\t\t\tlet styles = {}\r\n\t\t\t\tlet classles = ''\r\n\t\t\t\tif (this.selectedColor) {\r\n\t\t\t\t\tlet selectedColor = this.selectedColor ? this.selectedColor : '#2979ff'\r\n\t\t\t\t\tif (this.mode === 'tag') {\r\n\t\t\t\t\t\tstyles.color = item.selected ? (this.selectedTextColor ? this.selectedTextColor : '#fff') : '#666'\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyles.color = item.selected ? (this.selectedTextColor ? this.selectedTextColor : selectedColor) : '#666'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!item.selected && item.disabled) {\r\n\t\t\t\t\t\tstyles.color = '#999'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i in styles) {\r\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\r\n\t\t\t\t}\r\n\t\t\t\treturn classles\r\n\t\t\t},\r\n\t\t\tsetStyleRightIcon(item) {\r\n\t\t\t\tlet styles = {}\r\n\t\t\t\tlet classles = ''\r\n\t\t\t\tif (this.mode === 'list') {\r\n\t\t\t\t\tstyles['border-color'] = item.selected ? this.styles.selectedColor : '#DCDFE6'\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i in styles) {\r\n\t\t\t\t\tclassles += `${i}:${styles[i]};`\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn classles\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-primary: #2979ff !default;\r\n\t$border-color: #DCDFE6;\r\n\t$disable: 0.4;\r\n\r\n\t@mixin flex {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-data-loading {\r\n\t\t@include flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 36px;\r\n\t\tpadding-left: 10px;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.uni-data-checklist {\r\n\t\tposition: relative;\r\n\t\tz-index: 0;\r\n\t\tflex: 1;\r\n\r\n\t\t// 多选样式\r\n\t\t.checklist-group {\r\n\t\t\t@include flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t&.is-list {\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t}\r\n\r\n\t\t\t.checklist-box {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 5px 0;\r\n\t\t\t\tmargin-right: 25px;\r\n\r\n\t\t\t\t.hidden {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\topacity: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 文字样式\r\n\t\t\t\t.checklist-content {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tmargin-left: 5px;\r\n\t\t\t\t\t\tline-height: 14px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.checkobx__list {\r\n\t\t\t\t\t\tborder-right-width: 1px;\r\n\t\t\t\t\t\tborder-right-color: #007aff;\r\n\t\t\t\t\t\tborder-right-style: solid;\r\n\t\t\t\t\t\tborder-bottom-width: 1px;\r\n\t\t\t\t\t\tborder-bottom-color: #007aff;\r\n\t\t\t\t\t\tborder-bottom-style: solid;\r\n\t\t\t\t\t\theight: 12px;\r\n\t\t\t\t\t\twidth: 6px;\r\n\t\t\t\t\t\tleft: -5px;\r\n\t\t\t\t\t\ttransform-origin: center;\r\n\t\t\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\t\t\topacity: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 多选样式\r\n\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 16px;\r\n\t\t\t\t\theight: 16px;\r\n\t\t\t\t\tborder: 1px solid $border-color;\r\n\t\t\t\t\tborder-radius: 4px;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\tz-index: 1;\r\n\r\n\t\t\t\t\t.checkbox__inner-icon {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\t\t\t\ttop: 2px;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\t\ttop: 1px;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\tleft: 5px;\r\n\t\t\t\t\t\theight: 8px;\r\n\t\t\t\t\t\twidth: 4px;\r\n\t\t\t\t\t\tborder-right-width: 1px;\r\n\t\t\t\t\t\tborder-right-color: #fff;\r\n\t\t\t\t\t\tborder-right-style: solid;\r\n\t\t\t\t\t\tborder-bottom-width: 1px;\r\n\t\t\t\t\t\tborder-bottom-color: #fff;\r\n\t\t\t\t\t\tborder-bottom-style: solid;\r\n\t\t\t\t\t\topacity: 0;\r\n\t\t\t\t\t\ttransform-origin: center;\r\n\t\t\t\t\t\ttransform: rotate(40deg);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 单选样式\r\n\t\t\t\t.radio__inner {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 16px;\r\n\t\t\t\t\theight: 16px;\r\n\t\t\t\t\tborder: 1px solid $border-color;\r\n\t\t\t\t\tborder-radius: 16px;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\tz-index: 1;\r\n\r\n\t\t\t\t\t.radio__inner-icon {\r\n\t\t\t\t\t\twidth: 8px;\r\n\t\t\t\t\t\theight: 8px;\r\n\t\t\t\t\t\tborder-radius: 10px;\r\n\t\t\t\t\t\topacity: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 默认样式\r\n\t\t\t\t&.is--default {\r\n\r\n\t\t\t\t\t// 禁用\r\n\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\r\n\t\t\t\t\t\t\tborder-color: $border-color;\r\n\t\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.radio__inner {\r\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\r\n\t\t\t\t\t\t\tborder-color: $border-color;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 选中\r\n\t\t\t\t\t&.is-checked {\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t\t.checkbox__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.radio__inner {\r\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t\t.radio__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: $uni-primary;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 选中禁用\r\n\t\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.radio__inner {\r\n\t\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 按钮样式\r\n\t\t\t\t&.is--button {\r\n\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t\tpadding: 5px 10px;\r\n\t\t\t\t\tborder: 1px $border-color solid;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t\ttransition: border-color 0.2s;\r\n\r\n\t\t\t\t\t// 禁用\r\n\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\tborder: 1px #eee solid;\r\n\t\t\t\t\t\topacity: $disable;\r\n\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\r\n\t\t\t\t\t\t\tborder-color: $border-color;\r\n\t\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.radio__inner {\r\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\r\n\t\t\t\t\t\t\tborder-color: $border-color;\r\n\t\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.is-checked {\r\n\t\t\t\t\t\tborder-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t\t.checkbox__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.radio__inner {\r\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t\t.radio__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: $uni-primary;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 选中禁用\r\n\t\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 标签样式\r\n\t\t\t\t&.is--tag {\r\n\t\t\t\t\tmargin-right: 10px;\r\n\t\t\t\t\tpadding: 5px 10px;\r\n\t\t\t\t\tborder: 1px $border-color solid;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t\tbackground-color: #f5f5f5;\r\n\r\n\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 禁用\r\n\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.is-checked {\r\n\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\t\t\t\t\t\tborder-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 列表样式\r\n\t\t\t\t&.is--list {\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tpadding: 10px 15px;\r\n\t\t\t\t\tpadding-left: 0;\r\n\t\t\t\t\tmargin: 0;\r\n\r\n\t\t\t\t\t&.is-list-border {\r\n\t\t\t\t\t\tborder-top: 1px #eee solid;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 禁用\r\n\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tbackground-color: #F2F6FC;\r\n\t\t\t\t\t\t\tborder-color: $border-color;\r\n\t\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.is-checked {\r\n\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\r\n\t\t\t\t\t\t\t.checkbox__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.radio__inner {\n\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\t\t\t\t\t\t\t.radio__inner-icon {\r\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\tbackground-color: $uni-primary;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\tcolor: $uni-primary;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.checklist-content {\r\n\t\t\t\t\t\t\t.checkobx__list {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t\tborder-color: $uni-primary;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 选中禁用\r\n\t\t\t\t\t\t&.is-disable {\r\n\t\t\t\t\t\t\t.checkbox__inner {\r\n\t\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.checklist-text {\r\n\t\t\t\t\t\t\t\topacity: $disable;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-data-checkbox.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650629473\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}