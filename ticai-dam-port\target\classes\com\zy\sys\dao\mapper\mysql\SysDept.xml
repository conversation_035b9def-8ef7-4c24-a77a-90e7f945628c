<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysDeptDAO">

    <select id="findByType" resultType="com.zy.model.TreeNode">
        select a.ID_, a.PID_, a.NAME_
        from SYS_DEPT a
        where a.FLAG_='1' and (a.PID_='0' or a.TYPE_=#{0})
        order by a.ORD_
    </select>

</mapper>
