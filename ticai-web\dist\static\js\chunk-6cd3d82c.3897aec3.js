(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6cd3d82c"],{2893:function(t,e,a){},"4abb":function(t,e,a){"use strict";a("2893")},"5e10":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"编码规则",width:"500px",visible:t.noVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.noVisible=e}}},[a("el-form",{ref:"noform",attrs:{"label-width":"120px",model:t.nodata,rules:t.noRules}},[t.tag&&t.tag.name?a("el-form-item",{attrs:{label:"业务名称："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"300px"},attrs:{value:t.tag.name,readonly:""}})],1):t._e(),a("el-form-item",{attrs:{label:"编码前缀："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{maxlength:"6",autocomplete:"off"},model:{value:t.nodata.prefix,callback:function(e){t.$set(t.nodata,"prefix",e)},expression:"nodata.prefix"}})],1),a("el-form-item",{attrs:{label:"编码后缀："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{maxlength:"6",autocomplete:"off"},model:{value:t.nodata.suffix,callback:function(e){t.$set(t.nodata,"suffix",e)},expression:"nodata.suffix"}})],1),a("el-form-item",{attrs:{label:"日期格式："}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:t.nodata.dateFormat,callback:function(e){t.$set(t.nodata,"dateFormat",e)},expression:"nodata.dateFormat"}},t._l(t.dateFormatList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"流水号长度：",prop:"len"}},[a("el-input-number",{staticStyle:{width:"300px"},attrs:{maxlength:"1",autocomplete:"off",min:3,max:9},model:{value:t.nodata.len,callback:function(e){t.$set(t.nodata,"len",e)},expression:"nodata.len"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.removeable&&t.existed?a("el-button",{attrs:{type:"danger"},on:{click:t.remove}},[t._v("取消编码规则")]):t._e(),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)],1)},n=[],i={data:function(){return{dateFormatList:[{value:"",label:"不需要"},{value:"yyyyMMdd",label:"年月日(yyyyMMdd)"},{value:"yyMMdd",label:"短年月日(yyMMdd)"},{value:"yyyyMM",label:"年月(yyyyMM)"},{value:"yyMM",label:"短年月(yyMM)"},{value:"yyyy",label:"年(yyyy)"},{value:"yy",label:"短年(yy)"}],noVisible:!1,nodata:{code:null,prefix:null,dateFormat:null,len:5,suffix:null},noRules:{len:[{required:!0,message:"请输入流水号",trigger:"blur"}]},type:null,tag:null,removeable:!1,existed:!1}},methods:{show:function(t,e,a){var o=this;this.type=t,this.tag=e,this.removeable=a,this.existed=!1,this.$http("/sys/no/get/"+t).then((function(e){e.code>0&&(o.existed=null!=e.data,o.nodata=e.data&&"1"===e.data.status?e.data:{dataFormat:"",len:5},o.nodata.type=t,o.noVisible=!0)}))},remove:function(){var t=this;this.$confirm("是否要取消编码规则吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http("/sys/no/disable/"+t.type).then((function(e){e.code>0&&(t.noVisible=!1,t.$emit("remove",t.tag))}))})).catch((function(){}))},save:function(){var t=this;this.$refs.noform.validate((function(e){e&&t.$http({url:"/sys/no/save",data:t.nodata}).then((function(e){e.code>0&&(t.noVisible=!1,t.$message.success("保存编码规则成功"),t.$emit("success",t.tag))}))}))}}},l=i,s=a("2877"),r=Object(s["a"])(l,o,n,!1,null,null,null);e["a"]=r.exports},"6f69":function(t,e,a){"use strict";a("a12e")},"7fb8":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{clearable:"",size:"mini",placeholder:"输入类型名称关键字"},model:{value:t.filterText,callback:function(e){t.filterText=e},expression:"filterText"}},[a("template",{slot:"prepend"},[t._v("快速检索")])],2)],1),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:12}},[a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"mini",icon:"el-icon-refresh"},on:{click:t.loadData}},[t._v("刷新")]),a("el-button",{staticClass:"filter-item",attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:t.addRoot}},[t._v("创建根级类型")])],1)],1),a("el-divider"),a("div",{staticClass:"tree-block"},[a("el-tree",{ref:"tree",staticClass:"filter-tree",attrs:{"default-expand-all":"",data:t.treeData,"highlight-current":"","filter-node-method":t.filterNode,"expand-on-click-node":""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.node,n=e.data;return a("span",{staticClass:"tree-node-line"},[a("span",{staticClass:"t"},[o.expanded?a("svg-icon",{attrs:{"icon-class":"key"}}):a("svg-icon",{attrs:{"icon-class":"lock"}}),t._v(" "+t._s(n.name)+" ")],1),a("span",{staticClass:"c"},[t._v(" "+t._s(n.id)+" ")]),a("span",{staticClass:"b"},[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-document-add"},on:{click:function(e){return e.stopPropagation(),function(){return t.add(n)}()}}},[t._v("新增子类")]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit-outline"},on:{click:function(e){return e.stopPropagation(),function(){return t.detail(n.tag)}()}}},[t._v("编辑")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-tickets"},on:{click:function(e){return e.stopPropagation(),t.editForm(n.tag)}}},[t._v("扩展表单")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(e){return e.stopPropagation(),function(){return t.remove(n.tag)}()}}},[t._v("删除")])],1)])}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"类型设置",visible:t.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.detailVisible=e}}},[a("el-form",{ref:"dataform",attrs:{"label-width":"100px",model:t.form,rules:t.rules}},[t.form.pcode&&"0"!=t.form.pcode?a("el-form-item",{attrs:{label:"上级编号："}},[a("el-input",{staticClass:"form-static",attrs:{value:t.form.pcode,readonly:""}})],1):t._e(),a("el-form-item",{attrs:{label:"上级名称："}},[a("el-input",{staticClass:"form-static",attrs:{value:t.form.pname||"根级",readonly:""}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"类型编号：",prop:"code"}},[t.readOnlyCode?a("el-input",{staticClass:"form-static",attrs:{readonly:""},model:{value:t.form.code,callback:function(e){t.$set(t.form,"code",e)},expression:"form.code"}}):a("el-input",{attrs:{maxlength:"16",autocomplete:"off"},model:{value:t.form.code,callback:function(e){t.$set(t.form,"code",e)},expression:"form.code"}})],1)],1),a("el-col",{attrs:{span:12}},[null==t.form.pcode||"0"===t.form.pcode?a("span",{staticClass:"form-memo"},[t._v("顶级编号必须为2位数字")]):a("span",{staticClass:"form-memo"},[t._v("编号规则：上级编号+2位数字")])])],1),a("el-form-item",{attrs:{label:"类型名称：",prop:"name"}},[a("el-input",{attrs:{name:"name",maxlength:"32",autocomplete:"off"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.detailVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)],1),a("no-detail",{ref:"noDetail",on:{success:t.noSuccess,remove:t.noRemove}})],1)},n=[],i=(a("4de4"),a("b0c0"),a("d3b7"),a("ac1f"),a("00b4"),a("5319"),a("0643"),a("2382"),a("5e10")),l={components:{NoDetail:i["a"]},data:function(){var t=this,e=function(e,a,o){var n=!t.form.pcode||"0"===t.form.pcode;a?!/\d*/.test(a)||!0&a.length?o(new Error("编码不符合规则，必须为"+(a.length+2)+"位")):"add"!==t.formAction||n||a.replace(/\d{2}$/,"")===t.form.pcode?o():o(new Error("编码不符合规则")):o(new Error("必须填写编码"))};return{detailVisible:!1,filterText:"",treeData:null,formAction:null,form:{code:"",pcode:"",name:"",ord:1},rules:{code:[{required:!0,trigger:"blur",validator:e}],name:[{required:!0,message:"请输入名称",trigger:"blur"}],ord:[{required:!0,message:"请输入排序",trigger:"blur"}]},readOnlyCode:!1}},watch:{filterText:function(t){this.$refs.tree.filter(t)}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.$http({url:"/am/asset/type/tree"}).then((function(e){t.treeData=e})).catch((function(){}))},filterNode:function(t,e){return!t||-1!==e.name.indexOf(t)},clearValidate:function(){var t=this;this.$refs.dataform?this.$refs.dataform.clearValidate():this.$nextTick((function(){t.$refs.dataform.clearValidate()}))},addRoot:function(){this.formAction="add",this.detailVisible=!0,this.readOnlyCode=!1,this.clearValidate(),this.form={pcode:"0",ord:1}},add:function(t){this.formAction="add",this.detailVisible=!0,this.readOnlyCode=!1;var e=null;if(t.children&&t.children.length)for(var a=0;a<t.children.length;a++)(!e||t.children[a].tag.code>e)&&(e=t.children[a].tag.code);var o=t.tag.code;if(e){var n=e.substring(e.length-2),i=parseInt(n.replace(/^0+/,""))+1;e=e.substring(0,e.length-2)+(i>9?i:"0"+i)}else e=o+"01";this.clearValidate(),this.form={pcode:o,pname:t.tag.name,code:e,ord:1}},detail:function(t){this.formAction="edit",this.detailVisible=!0,this.readOnlyCode=!0,this.clearValidate(),this.form=Object.assign({},t)},save:function(){var t=this;this.$refs.dataform.validate((function(e){e&&(null==t.form.pcode&&(t.form.pcode="0"),t.$http({url:"/am/asset/type/"+t.formAction,data:t.form}).then((function(e){e.code>0&&(t.$message.success("提交成功"),t.detailVisible=!1,t.loadData())})).catch((function(){})))}))},remove:function(t){var e=this;this.$confirm("此操作将永久删除该资产类型, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:"/am/asset/type/delete/"+t.code}).then((function(t){t.code>0&&(e.$message.success("删除成功"),e.loadData()),e.detailVisible=!1})).catch((function(){}))})).catch((function(){}))},editNo:function(t){var e=this;null==this.noKey?this.$http("/am/asset/type/getNoKey").then((function(a){if(a.code>0){if(!a.data)return e.$message.error("系统未配置编码标识");e.noKey=a.data,e.showNoDetail(t)}})):this.showNoDetail(t)},showNoDetail:function(t){this.$refs.noDetail.show(this.noKey+(t?"_"+t.code:""),t,null!=t&&"0"!==t.noType)},noSuccess:function(t){var e=this;t&&this.$http({url:"/am/asset/type/updateNo",data:{code:t.code,noType:"1"}}).then((function(t){t.code>0&&(e.$message.success("设置自定义编码规则成功"),e.loadData())}))},noRemove:function(t){var e=this;t&&this.$http({url:"/am/asset/type/updateNo",data:{code:t.code,noType:"0"}}).then((function(t){t.code>0&&(e.$message.success("取消自定义编码规则成功"),e.loadData())}))},editForm:function(t){var e="ASSET_"+t.code,a=this.$router.resolve({path:"/formSimple",query:{code:e,name:t.name+"(扩展信息)"}});window.open(a.href,e)}}},s=l,r=(a("6f69"),a("4abb"),a("2877")),c=Object(r["a"])(s,o,n,!1,null,null,null);e["default"]=c.exports},a12e:function(t,e,a){}}]);