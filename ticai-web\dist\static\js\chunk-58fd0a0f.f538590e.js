(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58fd0a0f"],{"01b2":function(t,e,i){},"053f":function(t,e,i){"use strict";i("01b2")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"3ca32":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"page-header"},[i("div",{staticClass:"page-tollbar"},[i("div",{staticClass:"opt"},[i("el-button-group",[i("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:t.add}},[t._v("新增路线")])],1)],1),i("div",{staticClass:"search",staticStyle:{width:"350px"}},[i("el-button-group",{staticStyle:{"margin-left":"30px"}},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)]),i("div",{staticClass:"page-filter"},[i("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[i("el-form-item",{attrs:{label:"快捷检索："}},[i("el-input",{staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"请输入路线编码、名称关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1)],1)],1)]),i("div",{staticClass:"page-body"},[i("page-table",{ref:"grid",attrs:{size:"mini",path:"/am/patrol/path/page",query:t.qform,stripe:"",border:"","max-height":t.tableHeight}},[i("el-table-column",{attrs:{type:"index",width:"50"}}),i("el-table-column",{attrs:{label:"编码",prop:"no",width:"80",align:"center"}}),i("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"200"}}),i("el-table-column",{attrs:{label:"途径点位数",prop:"pointCount",width:"100",align:"center"}}),i("el-table-column",{attrs:{label:"直线距离(米)",prop:"distance",width:"100","header-align":"center",align:"right"}}),i("el-table-column",{attrs:{label:"操作",width:"150",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return e.stopPropagation(),t.edit(a)}}},[t._v("编辑")]),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(e){return e.stopPropagation(),t.remove(a)}}},[t._v("删除")])]}}])})],1)],1),i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],staticClass:"path"},[i("div",{ref:"map",staticClass:"map-container"}),i("div",{ref:"info",staticClass:"info"},[i("div",{staticClass:"header"},[t._v("路线信息")]),i("div",{staticClass:"body"},[i("el-form",{ref:"form",attrs:{model:t.form,size:"small","label-width":"70px",rules:t.rules}},[i("el-form-item",{attrs:{label:"编码：",prop:"no"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{"auto-complete":"off",maxlength:"10"},model:{value:t.form.no,callback:function(e){t.$set(t.form,"no",e)},expression:"form.no"}})],1),i("el-form-item",{attrs:{label:"名称：",prop:"name"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{"auto-complete":"off",maxlength:"10"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),i("el-row",[i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"排序：",prop:"ord"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"number","auto-complete":"off",maxlength:5,max:99999},model:{value:t.form.ord,callback:function(e){t.$set(t.form,"ord",e)},expression:"form.ord"}})],1)],1),i("el-col",{attrs:{span:14}},[i("el-form-item",{attrs:{label:"覆盖半径：",prop:"radius","label-width":"100px"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"number","auto-complete":"off",maxlength:5,max:99999},model:{value:t.form.radius,callback:function(e){t.$set(t.form,"radius",e)},expression:"form.radius"}},[i("span",{attrs:{slot:"append"},slot:"append"},[t._v("米")])])],1)],1)],1),i("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea","auto-complete":"off",maxlength:"10"},model:{value:t.form.memo,callback:function(e){t.$set(t.form,"memo",e)},expression:"form.memo"}})],1)],1),i("el-divider"),i("div",{staticClass:"point-tool"},[i("span",[t._v("点位列表")]),i("div",{staticClass:"point-button"},[i("el-checkbox",{model:{value:t.mapDblChecked,callback:function(e){t.mapDblChecked=e},expression:"mapDblChecked"}},[t._v("双击选择点位")]),i("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:function(e){return t.addPoint(-1)}}},[t._v("新增点位")])],1)]),i("div",{staticClass:"point-list"},[i("draggable",{attrs:{draggable:".point-item"},on:{end:t.onDragEnd},model:{value:t.pointList,callback:function(e){t.pointList=e},expression:"pointList"}},t._l(t.pointList,(function(e,a){return i("div",{key:e.id,staticClass:"point-item"},[i("div",{staticClass:"point-no"},[i("b",[t._v(t._s(a+1))])]),i("div",{staticClass:"point-name",on:{dblclick:function(i){return t.toPoint(e)}}},[t._v(t._s(e.name))]),i("div",{staticClass:"point-btn"},[i("el-link",{attrs:{type:"success",underline:!1,icon:"el-icon-add-location"},on:{click:function(e){return t.addPoint(a)}}},[t._v("插入")]),i("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",underline:!1,icon:"el-icon-remove"},on:{click:function(e){return t.removePoint(a)}}},[t._v("移除")])],1)])})),0)],1),i("div",{staticClass:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.savePath}},[t._v("保 存")])],1)],1)]),i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"点位选择",width:"700px","append-to-body":"",visible:t.pointVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.pointVisible=e}}},[i("el-form",{attrs:{size:"mini","label-width":"60px"}},[i("el-form-item",{attrs:{label:"检索："}},[i("el-input",{attrs:{size:"mini",placeholder:"输入关键字搜索"},model:{value:t.pointKeyword,callback:function(e){t.pointKeyword=e},expression:"pointKeyword"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.filterPoint},slot:"append"})],1)],1)],1),i("el-table",{ref:"pointTable",staticStyle:{width:"100%"},attrs:{size:"mini",stripe:"",border:"",data:t.tableData,"max-height":"400px"},on:{"selection-change":t.selectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),i("el-table-column",{attrs:{label:"编号",prop:"no",width:"80"}}),i("el-table-column",{attrs:{label:"名称",prop:"name",width:"160"}}),i("el-table-column",{attrs:{label:"地址",prop:"address"}})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.confirmTable}},[t._v("确 定")])],1)],1)],1)])},n=[],o=(i("99af"),i("4de4"),i("caad"),i("d81d"),i("a434"),i("b0c0"),i("d3b7"),i("ac1f"),i("2532"),i("841c"),i("0643"),i("2382"),i("4e3e"),i("a573"),i("159b"),i("b76a")),s=i.n(o),r=i("6ecd"),l={components:{PageTable:r["a"],draggable:s.a},data:function(){return{tableHeight:Math.max(document.documentElement.clientHeight-300,200),qform:{},pointData:[],pointDict:{},visible:!1,mapDblChecked:!1,omap:null,map:{center:null,zoom:13,satellite:!1,markers:[]},mapRender:!1,form:{no:null,name:null},rules:{no:[{required:!0,message:"请输入路线编码",trigger:"blur"}],name:[{required:!0,message:"请输入路线名称",trigger:"blur"}]},pointList:[],pointVisible:!1,pointIndex:0,pointKeyword:null,tableData:[],tableCheckList:[],polyline:null,textMarkers:null}},mounted:function(){this.search(),this.loadAllPoint()},destroyed:function(){this.omap&&this.omap.destroy()},methods:{search:function(){this.$refs.grid.search(this.qform)},loadAllPoint:function(){var t=this;this.$http("/am/patrol/point/list").then((function(e){t.pointData=e||[],t.pointData.forEach((function(e){t.pointDict[e.id]=e})),t.omap&&t.renderMap()}))},filterPoint:function(){var t=this;this.pointKeyword?this.tableData=this.pointData.filter((function(e){return e.name.includes(t.pointKeyword)})):this.tableData=this.pointData,this.$nextTick((function(){t.$refs.pointTable.clearSelection()}))},add:function(){this.visible=!0,this.form={},this.pointList=[],this.omap?this.drawPoint():this.initMap()},edit:function(t){var e=this;if(null==this.pointData||0===this.pointData.length)return this.$message.warning("点位还未加载完，请等待片刻再重试");this.$http("/am/patrol/path/get/"+t.id).then((function(t){if(t.code>0&&t.data){e.form=t.data;var i=[];t.data.pointList.forEach((function(t){var a=e.pointDict[t.point];a&&i.push(a)})),e.pointList=i,e.visible=!0,e.omap?e.drawPoint():e.initMap()}}))},remove:function(t){var e=this;this.$confirm("此操作将永久删除该巡检路线, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:"/am/patrol/path/delete/"+t.id}).then((function(t){t.code>0&&(e.$message.success("删除成功"),e.search())}))})).catch((function(){}))},savePath:function(){var t=this;this.$refs.form.validate((function(e){if(e){if(null===t.pointList||0===t.pointList.length)return t.$message.warning("请选择点位");var i=0,a=[],n=t.pointList[0],o={point:n.id,prevDist:0};a.push(o);for(var s=new window.AMap.LngLat(n.lng,n.lat),r=1;r<t.pointList.length;r++){var l=t.pointList[r],c=new window.AMap.LngLat(l.lng,l.lat),p=s.distance(c);i+=p,o.nextDist=p,o.nextPoint=l.id,o={point:l.id,prevPoint:o.point,prevDist:p},a.push(o),s=c}t.form.distance=i,t.form.pointList=a,t.$http({url:"/am/patrol/path/save",data:t.form}).then((function(e){e.code>0&&(t.$message.success("保存路线成功"),t.visible=!1,t.search())}))}}))},initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete),t.omap.on("click",t.mapClick)}))},mapComplete:function(){this.renderMap()},mapClick:function(t){},renderMap:function(){var t=this;!this.mapRender&&this.omap&&0!==this.pointData.length&&(this.mapRender=!0,this.pointData&&(this.pointData.forEach((function(e){var i=new window.AMap.Marker({extData:e,position:new window.AMap.LngLat(e.lng,e.lat),title:e.name,icon:"/icons/pin-blue.png",label:{direction:"top",content:e.name}});i.on("dblclick",t.markerDblclick),i.on("click",t.markerClick),t.omap.add(i)})),this.drawPoint()))},markerClick:function(t){if(!this.mapDblChecked&&t.target){var e=t.target.getExtData();e&&e.id&&(this.pointIndex=-1,this.appendPoint([e]))}},markerDblclick:function(t){if(this.mapDblChecked&&t.target){var e=t.target.getExtData();e&&e.id&&(this.pointIndex=-1,this.appendPoint([e]))}},appendPoint:function(t){var e=this;if(0!==t.length){var i=[],a="";if(t.forEach((function(t){for(var n=0;n<e.pointList.length;n++)if(e.pointList[n].id===t.id)return void(a+="<li>点位【"+t.name+"】已在路线中</li>");i.push(t)})),a&&this.$message({message:'<ul class="error-list">'+a+"</ul>",type:"warning",duration:5e3,dangerouslyUseHTMLString:!0}),0!==i.length){var n,o;if(this.pointIndex<0||this.pointIndex>this.pointList.length-2)(n=this.pointList).push.apply(n,i);else(o=this.pointList).splice.apply(o,[this.pointIndex,0].concat(i));this.drawPoint()}}},toPoint:function(t){this.omap.setCenter(new window.AMap.LngLat(t.lng,t.lat))},onDragEnd:function(){this.drawPoint()},clearDraw:function(){var t=this;this.omap&&(this.polyline&&this.omap.remove(this.polyline),this.textMarkers&&this.textMarkers.forEach((function(e){return t.omap.remove(e)}))),this.polyline=null,this.textMarkers=null},drawPoint:function(){var t=this;if(this.clearDraw(),this.pointList.length<2){if(1===this.pointList.length){var e=this.pointList[0];this.textMarkers=[],this.textMarkers.push(new window.AMap.Text({text:"1",position:new window.AMap.LngLat(e.lng,e.lat),style:{"border-radius":"24px",width:"24px",height:"24px","text-align":"center","font-size":"12px","line-height":"18px",background:"#C00",color:"#FFF"}})),this.omap.add(this.textMarkers[0])}}else{var i=[];this.textMarkers=[],this.pointList.forEach((function(e,a){var n=new window.AMap.LngLat(e.lng,e.lat);i.push(n),t.textMarkers.push(new window.AMap.Text({text:""+(a+1),position:n,style:{"border-radius":"24px",width:"24px",height:"24px","text-align":"center","font-size":"12px","line-height":"18px",background:"#C00",color:"#FFF"}}))})),this.polyline=new window.AMap.Polyline({path:i,strokeColor:"#f16a28",strokeWeight:4,strokeStyle:"solid",lineJoin:"round"}),this.omap.add(this.polyline),this.textMarkers.forEach((function(e){return t.omap.add(e)}))}},addPoint:function(t){this.pointIndex=t,this.pointVisible=!0,this.pointKeyword=null,this.tableCheckList=[],this.filterPoint()},removePoint:function(t){this.pointList.length>t&&this.pointList.splice(t,1)},selectionChange:function(t){this.tableCheckList=t},confirmTable:function(){this.tableCheckList.length&&this.appendPoint(this.tableCheckList),this.pointVisible=!1}}},c=l,p=(i("053f"),i("2877")),h=Object(p["a"])(c,a,n,!1,null,"1590dfd1",null);e["default"]=h.exports},"6ecd":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-container",{staticClass:"page-table-ctn"},[i("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?i("el-footer",{staticClass:"footer"},[i("div",{staticClass:"size-info"},[t.total>1?i("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),i("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},n=[],o=i("53ca"),s=(i("a9e3"),i("d3b7"),i("ac1f"),i("841c"),i("0643"),i("4e3e"),i("159b"),i("b775")),r={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var i=this;if(this.path){var a={pageNumber:1},n=Object(o["a"])(t);"undefined"===n?a.pageNumber=1:"number"===n?a.pageNumber=t:"object"===n?(this.params=t,"number"===typeof e&&(a.pageNumber=e),"boolean"===typeof e&&this.empty()):a.pageNumber=t.pageNumber,this.pi=a.pageNumber,this.paging&&(this.params.pageNumber=a.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(s["a"])({url:this.path,data:this.params}).then((function(t){i.loading=!1,i.paging?i.renderPage(t):i.renderList(t.rows?t.rows:t),i.$emit("loaded",t)})).catch((function(t){i.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var i=[],a=0;a<e.length;a++)e[a][t]&&i.push(e[a][t]);return i}}},l=r,c=(i("b2d4"),i("2877")),p=Object(c["a"])(l,a,n,!1,null,"bdcc19d8",null);e["a"]=p.exports},"841c":function(t,e,i){"use strict";var a=i("d784"),n=i("825a"),o=i("1d80"),s=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=o(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var o=n(t),l=String(this),c=o.lastIndex;s(c,0)||(o.lastIndex=0);var p=r(o,l);return s(o.lastIndex,c)||(o.lastIndex=c),null===p?-1:p.index}]}))},ac65:function(t,e,i){},b2d4:function(t,e,i){"use strict";i("ac65")}}]);