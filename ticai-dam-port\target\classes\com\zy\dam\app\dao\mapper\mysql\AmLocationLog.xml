<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.app.dao.AmLocationLogDAO">

    <sql id="meta">
        a.ID_
        ,a.TYPE_
        ,a.REF_
        ,a.LNG_
        ,a.LAT_
        ,a.USER_
        ,a.TIME_
    </sql>
    <insert id="insert" parameterType="com.zy.dam.app.orm.AmLocationLog">
        insert into AM_LOCATION_LOG(ID_,TYPE_,REF_,LNG_,LAT_,USER_,TIME_)
        values(uuid(),#{type},#{ref},#{lng},#{lat},#{user},#{time})
    </insert>

</mapper>
