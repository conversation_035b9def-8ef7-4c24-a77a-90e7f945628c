{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js!F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\src\\router\\index.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\router\\index.js", "mtime": 1752649821933}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}