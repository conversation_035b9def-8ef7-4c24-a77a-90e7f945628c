(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2bd559b3"],{4736:function(t,e,a){},"4f8b":function(t,e,a){"use strict";a("4736")},d1f2:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:t.batchDel}},[t._v("删除")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.download}},[t._v("选中导出")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.exportQuery}},[t._v("根据条件导出")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"检索："}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:"",placeholder:"输入资产编码、名称、型号、时间、姓名等关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"数据类型："}},[a("el-select",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.qform.dataType,callback:function(e){t.$set(t.qform,"dataType",e)},expression:"qform.dataType"}},[a("el-option",{attrs:{value:1,label:"已领用已绑定数据"}}),a("el-option",{attrs:{value:2,label:"已领用未绑定数据"}}),a("el-option",{attrs:{value:3,label:"没有绑定设备的网点"}})],1)],1),a("el-form-item",{attrs:{label:"网点名称："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:t.qform.locationName,callback:function(e){t.$set(t.qform,"locationName",e)},expression:"qform.locationName"}})],1),a("el-form-item",{attrs:{label:"网点地址："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:t.qform.locationAddress,callback:function(e){t.$set(t.qform,"locationAddress",e)},expression:"qform.locationAddress"}})],1),a("el-form-item",{attrs:{label:"网点区域："}},[a("el-select",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请选择区域"},model:{value:t.qform.locationRegion,callback:function(e){t.$set(t.qform,"locationRegion",e)},expression:"qform.locationRegion"}},t._l(t.regionOptions,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"网点备注："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入网点备注",autocomplete:"off"},model:{value:t.qform.locationMemo,callback:function(e){t.$set(t.qform,"locationMemo",e)},expression:"qform.locationMemo"}})],1),a("el-form-item",{attrs:{label:"所属部门："}},[a("tree-box",{staticStyle:{width:"180px"},attrs:{data:t.deptTree,"expand-all":"",clearable:""},model:{value:t.qform.dept,callback:function(e){t.$set(t.qform,"dept",e)},expression:"qform.dept"}})],1),a("el-form-item",{staticStyle:{"margin-left":"50px"},attrs:{label:"网点添加时间："}},[a("el-date-picker",{staticStyle:{width:"360px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},on:{change:t.handleDateRangeChange},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/rp/asset/getAssetSnPage",stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),a("el-table-column",{attrs:{label:"绑定姓名",prop:"name",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.name||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"绑定帐号",prop:"account",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.account||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"性别",prop:"gender",width:"50",align:"center",formatter:t.colGender}}),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"60",align:"center",formatter:t.fnStatus}}),a("el-table-column",{attrs:{label:"所属机构",prop:"deptName",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.deptName||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"销售终端编号",prop:"nowSn",width:"110",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.nowSn||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.typeName||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.no?a("el-link",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewItem(e.row)}}},[t._v(t._s(e.row.no))]):a("span")]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"assetName","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.assetName||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"规格型号",prop:"spec",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.spec||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"网点名称",prop:"locationName",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locationName||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"网点地址",prop:"locationAddress","min-width":"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locationAddress||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"网点区域",prop:"locationRegion",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"网点备注",prop:"locationMemo",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locationMemo||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"网点添加时间",prop:"time",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDateTime(e.row.time))+" ")]}}])}),a("el-table-column",{attrs:{label:"绑定时间",prop:"bindTime",width:"140",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.bindTime?t.formatDateTime(e.row.bindTime):"")+" ")]}}])}),a("el-table-column",{attrs:{label:"是否定位",prop:"whetherLocation",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.whetherLocation||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"定位地址",prop:"locAddr","min-width":"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locAddr||"")+" ")]}}])})],1),a("detail-view",{ref:"detailView"})],1)])},i=[],o=(a("99af"),a("d3b7"),a("ac1f"),a("4d90"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),n=a("5c96"),s=a("ee5a"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"},{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"custom-class":"dialog-tab dialog-full",width:"1180px",visible:t.visible,"append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[t._v("资产详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":t.activeIndex,mode:"horizontal"},on:{select:t.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[t._v("基本信息")]),a("el-menu-item",{attrs:{index:"5"}},[t._v("地理位置")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-form",{ref:"baseform",attrs:{model:t.data,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.typeName,callback:function(e){t.$set(t.data,"typeName",e)},expression:"data.typeName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.no,callback:function(e){t.$set(t.data,"no",e)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.name,callback:function(e){t.$set(t.data,"name",e)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.deptName,callback:function(e){t.$set(t.data,"deptName",e)},expression:"data.deptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.regionName,callback:function(e){t.$set(t.data,"regionName",e)},expression:"data.regionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.sn,callback:function(e){t.$set(t.data,"sn",e)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.maDate,callback:function(e){t.$set(t.data,"maDate",e)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.guDate,callback:function(e){t.$set(t.data,"guDate",e)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.productDate,callback:function(e){t.$set(t.data,"productDate",e)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.takeDate,callback:function(e){t.$set(t.data,"takeDate",e)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boDate,callback:function(e){t.$set(t.data,"boDate",e)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boAmount,callback:function(e){t.$set(t.data,"boAmount",e)},expression:"data.boAmount"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.expiryMonth,callback:function(e){t.$set(t.data,"expiryMonth",e)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[t._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.financeDate,callback:function(e){t.$set(t.data,"financeDate",e)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.value,callback:function(e){t.$set(t.data,"value",e)},expression:"data.value"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.spec,callback:function(e){t.$set(t.data,"spec",e)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.brand,callback:function(e){t.$set(t.data,"brand",e)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.manu,callback:function(e){t.$set(t.data,"manu",e)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.invoice,callback:function(e){t.$set(t.data,"invoice",e)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.seller,callback:function(e){t.$set(t.data,"seller",e)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.voucher,callback:function(e){t.$set(t.data,"voucher",e)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.price,callback:function(e){t.$set(t.data,"price",e)},expression:"data.price"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.contract,callback:function(e){t.$set(t.data,"contract",e)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.memo,callback:function(e){t.$set(t.data,"memo",e)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDeptName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useDeptName,callback:function(e){t.$set(t.data,"useDeptName",e)},expression:"data.useDeptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUserName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useUserName,callback:function(e){t.$set(t.data,"useUserName",e)},expression:"data.useUserName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最后打印：",prop:"printTime"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.printTime,callback:function(e){t.$set(t.data,"printTime",e)},expression:"data.printTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原存放地址：",prop:"fromAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromAddr,callback:function(e){t.$set(t.data,"fromAddr",e)},expression:"data.fromAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"定位地址：",prop:"locAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locAddr,callback:function(e){t.$set(t.data,"locAddr",e)},expression:"data.locAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原备注：",prop:"fromMemo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromMemo,callback:function(e){t.$set(t.data,"fromMemo",e)},expression:"data.fromMemo"}})],1)],1)],1),t.data.location?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用网点：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationName,callback:function(e){t.$set(t.data,"locationName",e)},expression:"data.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点联系人：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationContact,callback:function(e){t.$set(t.data,"locationContact",e)},expression:"data.locationContact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点电话：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationPhone,callback:function(e){t.$set(t.data,"locationPhone",e)},expression:"data.locationPhone"}})],1)],1)],1):t._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},t._l(t.fields,(function(t,e){return a("element-view-item",{key:t.renderKey,attrs:{"current-item":t,index:e}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-table",{attrs:{data:t.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:t.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:t.colRelDescr}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===t.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("upload-file",{attrs:{multiple:"",disabled:"",type:"ASSET"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===t.activeIndex,expression:"activeIndex === '5'"}],staticStyle:{"min-height":"500px"}},[a("div",{ref:"map",style:{"min-height":"500px",height:"100%"}},[null==t.lnglat?a("div",{staticStyle:{"text-align":"center","line-height":"50px","font-size":"24px"}},[t._v("该资产还未定位")]):t._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===t.activeIndex,expression:"activeIndex === '6'"}],staticStyle:{"min-height":"500px"}},[a("div",{staticStyle:{width:"400px",height:"400px",margin:"45px auto"}},[a("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.imgQr||t.defaultQr}})])])],2):t._e()],1)},c=[],d=(a("d81d"),a("b0c0"),a("b64b"),a("a573"),a("1cfe")),p=a("660a"),u={components:{ElementViewItem:d["a"],UploadFile:p["a"]},data:function(){return{visible:!1,loading:!1,activeIndex:"1",data:{},regionText:"",latlng:"",fields:[],relList:[],fileList:[],attachContext:this.$store.getters.attachContext,omap:null,map:{center:null,zoom:15,satellite:!1,markers:[]},currPoint:null,lnglat:null,infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)}),defaultQr:"/images/default_qr.png",imgQr:null,address:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{show:function(t){var e=this;this.visible=!0,"string"===typeof t?(this.loading=!0,this.$http("/am/asset/get/"+t).then((function(t){e.loading=!1,t.code>0&&t.data?e.showItem(t.data):e.visible=!1})).catch((function(){e.loading=!1,e.$message.error("网络超时"),e.visible=!1}))):this.showItem(t)},showItem:function(t){this.regionText=(t.regionName||"")+"/"+(t.locationName||""),this.latlng=t.lat&&t.lng?t.lat+", "+t.lng:"",this.activeIndex="1",this.data=t,this.fields=t.attr?JSON.parse(t.attr):[];var e=[];t.relList&&t.relList.forEach((function(t){switch(t.type){case"1":t.the?e.push({id:t.id,type:"2",rel:t.the,name:t.name}):e.push({id:t.id,type:"1",rel:t.rel,name:t.name});break;case"2":t.the?e.push({id:t.id,type:"4",rel:t.the,name:t.name}):e.push({id:t.id,type:"3",rel:t.rel,name:t.name})}})),this.relList=e,this.fileList=t.fileList||[],this.lnglat=t.lng&&t.lng?new window.AMap.LngLat(t.lng,t.lat):null,this.lnglat&&null==this.omap&&this.initMap()},handleSelectMenu:function(t){this.activeIndex=t,"6"===t&&this.data&&this.data.id&&(this.imgQr=this.defaultQr,this.imgQr="/api/am/asset/qrcode/"+this.data.id)},colRelType:function(t){switch(t.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(t){switch(t.type){case"1":return"当前资产包含【"+t.name+"】";case"2":return"当前资产属于【"+t.name+"】";case"3":return"当前资产安装了【"+t.name+"】";case"4":return"当前资产运行与【"+t.name+"】"}return""},initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete)}))},mapComplete:function(t){var e=this;this.currPoint=new window.AMap.Marker({position:this.lnglat,label:{direction:"top",content:this.data.name},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.omap.setCenter(this.lnglat),window.AMap.plugin("AMap.Geocoder",(function(){var t=new window.AMap.Geocoder({city:"全国"});t.getAddress([e.data.lng,e.data.lat],(function(t,a){"complete"===t&&"OK"===a.info&&a.regeocode&&(e.address=a.regeocode.formattedAddress)}))})),this.currPoint.on("click",(function(t){var a='<div style="padding:20px 15px;">';a+="<div>经度："+e.data.lng+"</div>",a+="<div>纬度："+e.data.lat+"</div>",a+="<div>定位地址："+e.address+"</div>",a+="</div>",e.infoWindow.setContent(a),e.infoWindow.open(e.omap,e.lnglat)}))}}},m=u,f=a("2877"),h=Object(f["a"])(m,r,c,!1,null,"ee1081c6",null),b=h.exports,v={1:"在职",5:"离职",8:"禁用"},g={components:{PageTable:o["a"],TreeBox:s["a"],DetailView:b},data:function(){return{formLabelWidth:"100px",qform:{keyword:null,timeBegin:null,timeEnd:null,dataType:null},dateRange:null,items:[],deptTree:[],regionOptions:[],fullscreenLoading:!1}},created:function(){this.loadDeptTree(),this.loadRegionOptions()},mounted:function(){this.search()},methods:{loadDeptTree:function(){var t=this;this.$http("/sys/dept/treeByType/1").then((function(e){t.deptTree=e})).catch((function(){t.$alert("加载机构树出错")}))},loadRegionOptions:function(){var t=this;this.$http("/am/region/list").then((function(e){t.regionOptions=e||[]})).catch((function(){t.$alert("加载区域列表出错")}))},colGender:function(t,e,a){return"1"===a?"男":"2"===a?"女":""},fnStatus:function(t,e,a){return v[a]||""},handleDateRangeChange:function(t){t?(this.qform.timeBegin=t[0],this.qform.timeEnd=t[1]):(this.qform.timeBegin=null,this.qform.timeEnd=null)},formatDateTime:function(t){if(!t)return"";var e=new Date(t),a=e.getFullYear(),l=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),s=String(e.getSeconds()).padStart(2,"0");return"".concat(a,"-").concat(l,"-").concat(i," ").concat(o,":").concat(n,":").concat(s)},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(t){this.items=t||[]},download:function(){var t=this;if(0===this.items.length)return this.$message.warning("至少要选择一条资产记录");var e=[];this.items.forEach((function(t){return e.push(t.id)}));var a=n["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/rp/exportBatch",data:e,responseType:"blob"}).then((function(e){a.close(),t.$saveAs(e,"资产终端机绑定明细.xlsx")})).catch((function(e){a.close(),t.$message.error("导出生成出错:"+e)}))},exportQuery:function(){var t=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=n["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});t.$jasper({url:"/rp/exportAll",data:t.qform,responseType:"blob"}).then((function(a){e.close(),t.$saveAs(a,"资产终端机绑定明细.xlsx")})).catch((function(a){e.close(),t.$message.error("导出生成出错:"+a)}))}))},batchDel:function(){var t=this;if(0===this.items.length)return this.$message.warning("至少要选择一条资产记录");this.$confirm("您确定要永久批量删除这些资产吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=[];t.items.forEach((function(t){e.push(t.id)})),t.$http({url:"/rp/deletes",data:e}).then((function(e){e.code>0&&(t.$message.success("批量删除成功"),t.search())}))})).catch((function(){}))},viewItem:function(t){var e=this;t&&t.id&&"undefined"!==t.id?(this.fullscreenLoading=!0,this.$http("/am/asset/get/"+t.id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&e.$refs.detailView.show(t.data)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")}))):this.$message.error("无效的资产ID，无法查看详情")}}},w=g,x=Object(f["a"])(w,l,i,!1,null,null,null);e["default"]=x.exports},ee5a:function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-popover",{ref:"popover",attrs:{placement:"bottom-start",trigger:"click"},on:{show:t.onShowPopover,hide:t.onHidePopover}},[a("el-tree",{ref:"tree",staticClass:"select-tree",style:"min-width: "+t.treeWidth,attrs:{"highlight-current":"",data:t.data,props:t.props,"expand-on-click-node":!1,"filter-node-method":t.filterNode,"default-expand-all":t.expandAll},on:{"node-click":t.onClickNode}}),a("el-input",{ref:"input",class:{rotate:t.showStatus},style:"width: "+t.width+"px",attrs:{slot:"reference",clearable:t.clearable,"suffix-icon":"el-icon-arrow-down",placeholder:t.placeholder},slot:"reference",model:{value:t.labelModel,callback:function(e){t.labelModel=e},expression:"labelModel"}})],1)},i=[],o=(a("99af"),a("4de4"),a("d3b7"),a("0643"),a("2382"),a("b775")),n={name:"TreeBox",model:{prop:"value",event:"selected"},props:{value:{type:String,default:null},width:{type:String,default:null},expandAll:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},data:{type:Array,default:null},path:{type:String,default:null},params:{type:Object,default:null},placeholder:{type:String,required:!1,default:"请选择"},labelField:{type:String,default:null},props:{type:Object,required:!1,default:function(){return{parent:"pid",value:"id",label:"label",children:"children"}}}},data:function(){return{showStatus:!1,treeWidth:"auto",labelModel:"",valueModel:"0"}},watch:{labelModel:function(t){t||(this.valueModel=""),this.$refs.tree.filter(t)},value:function(t){this.labelModel=this.queryTree(this.data,t)}},created:function(){var t=this;null!=this.path?Object(o["a"])({url:this.path,data:this.params}).then((function(e){t.init(e),t.$emit("loaded",e)})).catch((function(t){console.log(t)})):this.init(this.data)},methods:{init:function(t){var e=this;this.data=t||[],this.labelField&&(this.props.label=this.labelField),this.value&&(this.labelModel=this.queryTree(this.data,this.value)),this.$nextTick((function(){e.treeWidth="".concat((e.width||e.$refs.input.$refs.input.clientWidth)-24,"px")}))},onClickNode:function(t){this.labelModel=t[this.props.label],this.valueModel=t[this.props.value],this.onCloseTree()},onCloseTree:function(){this.$refs.popover.showPopper=!1},onShowPopover:function(){this.showStatus=!0,this.$refs.tree.filter(!1)},onHidePopover:function(){this.showStatus=!1,this.$emit("selected",this.valueModel)},filterNode:function(t,e){return!t||-1!==e[this.props.label].indexOf(t)},queryTree:function(t,e){var a=[];a=a.concat(t);while(a.length){var l=a.shift();if(l[this.props.children]&&(a=a.concat(l[this.props.children])),l[this.props.value]===e)return l[this.props.label]}return""}}},s=n,r=(a("4f8b"),a("2877")),c=Object(r["a"])(s,l,i,!1,null,null,null);e["a"]=c.exports}}]);