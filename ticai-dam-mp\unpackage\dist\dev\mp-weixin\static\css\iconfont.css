@font-face {
  font-family: "zi"; /* Project id 2651017 */
  src: url('/static/css/iconfont.ttf?t=1642429809952') format('truetype');
}

.zi {
  font-family: "zi" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.zi-task-done:before {
  content: "\e621";
}

.zi-task-doing:before {
  content: "\e6b9";
}

.zi-task-warning:before {
  content: "\e618";
}

.zi-banner:before {
  content: "\e62b";
}

.zi-chart:before {
  content: "\e66f";
}

.zi-analyse:before {
  content: "\e601";
}

.zi-area:before {
  content: "\e602";
}

.zi-base:before {
  content: "\e603";
}

.zi-balance:before {
  content: "\e604";
}

.zi-breadcrumb-fill:before {
  content: "\e605";
}

.zi-breadcrumb-line:before {
  content: "\e606";
}

.zi-building:before {
  content: "\e607";
}

.zi-chart-bar:before {
  content: "\e608";
}

.zi-cert:before {
  content: "\e60a";
}

.zi-chart-pie:before {
  content: "\e60b";
}

.zi-checkbox-checked-fill:before {
  content: "\e60c";
}

.zi-checkbox-unchecked-line:before {
  content: "\e60e";
}

.zi-circle-close-line:before {
  content: "\e60f";
}

.zi-check:before {
  content: "\e610";
}

.zi-circle-plus-line:before {
  content: "\e611";
}

.zi-circle-plus-fill:before {
  content: "\e612";
}

.zi-clock-fill:before {
  content: "\e613";
}

.zi-comment:before {
  content: "\e614";
}

.zi-code:before {
  content: "\e615";
}

.zi-checkbox-checked-line:before {
  content: "\e616";
}

.zi-conf:before {
  content: "\e617";
}

.zi-clock-line:before {
  content: "\e619";
}

.zi-delete-fill:before {
  content: "\e61a";
}

.zi-data:before {
  content: "\e61b";
}

.zi-dashboard:before {
  content: "\e61c";
}

.zi-dept:before {
  content: "\e61d";
}

.zi-delete-line:before {
  content: "\e61e";
}

.zi-edit-fill:before {
  content: "\e61f";
}

.zi-dashboard-fill:before {
  content: "\e620";
}

.zi-distri:before {
  content: "\e623";
}

.zi-edit-line:before {
  content: "\e625";
}

.zi-fabulous-fill:before {
  content: "\e626";
}

.zi-eye:before {
  content: "\e627";
}

.zi-express:before {
  content: "\e628";
}

.zi-eye-open:before {
  content: "\e629";
}

.zi-exit-full-line:before {
  content: "\e62a";
}

.zi-facility:before {
  content: "\e62c";
}

.zi-font-line:before {
  content: "\e62d";
}

.zi-fill:before {
  content: "\e62e";
}

.zi-fabulous-line:before {
  content: "\e62f";
}

.zi-exit-full-fill:before {
  content: "\e630";
}

.zi-fullscreen:before {
  content: "\e632";
}

.zi-friend-line:before {
  content: "\e633";
}

.zi-friend-fill:before {
  content: "\e634";
}

.zi-fund:before {
  content: "\e635";
}

.zi-goods:before {
  content: "\e636";
}

.zi-home-fill:before {
  content: "\e638";
}

.zi-home-line:before {
  content: "\e639";
}

.zi-icon-line:before {
  content: "\e63a";
}

.zi-I-line:before {
  content: "\e63b";
}

.zi-info-line:before {
  content: "\e63c";
}

.zi-inspection:before {
  content: "\e63d";
}

.zi-inventory:before {
  content: "\e63f";
}

.zi-like-line:before {
  content: "\e641";
}

.zi-live:before {
  content: "\e642";
}

.zi-lang-fill:before {
  content: "\e643";
}

.zi-land:before {
  content: "\e644";
}

.zi-lock:before {
  content: "\e645";
}

.zi-loading:before {
  content: "\e646";
}

.zi-location-fill:before {
  content: "\e647";
}

.zi-link:before {
  content: "\e648";
}

.zi-insp:before {
  content: "\e649";
}

.zi-menu-fill:before {
  content: "\e64a";
}

.zi-message-fill:before {
  content: "\e64d";
}

.zi-member:before {
  content: "\e64e";
}

.zi-minus-line:before {
  content: "\e64f";
}

.zi-message-line:before {
  content: "\e650";
}

.zi-more-fill:before {
  content: "\e651";
}

.zi-my-fill:before {
  content: "\e652";
}

.zi-more-line:before {
  content: "\e653";
}

.zi-nested:before {
  content: "\e654";
}

.zi-money:before {
  content: "\e655";
}

.zi-notice:before {
  content: "\e656";
}

.zi-noncash:before {
  content: "\e657";
}

.zi-notice-fill:before {
  content: "\e658";
}

.zi-password:before {
  content: "\e659";
}

.zi-notice-line:before {
  content: "\e65a";
}

.zi-my-line:before {
  content: "\e65b";
}

.zi-plus-line:before {
  content: "\e65c";
}

.zi-order:before {
  content: "\e65d";
}

.zi-page:before {
  content: "\e65e";
}

.zi-param:before {
  content: "\e65f";
}

.zi-office:before {
  content: "\e660";
}

.zi-question-line:before {
  content: "\e661";
}

.zi-question-fill:before {
  content: "\e662";
}

.zi-radio-checked-line:before {
  content: "\e663";
}

.zi-radio-unchecked-line:before {
  content: "\e664";
}

.zi-like-fill:before {
  content: "\e666";
}

.zi-privilege:before {
  content: "\e667";
}

.zi-refund:before {
  content: "\e668";
}

.zi-region:before {
  content: "\e669";
}

.zi-refresh-line:before {
  content: "\e66a";
}

.zi-radio-checked-fill:before {
  content: "\e66b";
}

.zi-return:before {
  content: "\e66c";
}

.zi-right-line:before {
  content: "\e66d";
}

.zi-sale:before {
  content: "\e66e";
}

.zi-search1-fill:before {
  content: "\e670";
}

.zi-search-fill:before {
  content: "\e671";
}

.zi-price:before {
  content: "\e672";
}

.zi-question:before {
  content: "\e673";
}

.zi-role:before {
  content: "\e674";
}

.zi-settings:before {
  content: "\e675";
}

.zi-org:before {
  content: "\e676";
}

.zi-star-fill:before {
  content: "\e677";
}

.zi-storage:before {
  content: "\e67a";
}

.zi-store:before {
  content: "\e67b";
}

.zi-storage_gas:before {
  content: "\e67c";
}

.zi-style-line:before {
  content: "\e67d";
}

.zi-table:before {
  content: "\e67f";
}

.zi-table-fill:before {
  content: "\e681";
}

.zi-tag:before {
  content: "\e682";
}

.zi-trade:before {
  content: "\e684";
}

.zi-user:before {
  content: "\e685";
}

.zi-users:before {
  content: "\e687";
}

.zi-warning-fill:before {
  content: "\e688";
}

.zi-vehicle:before {
  content: "\e689";
}

.zi-weight1:before {
  content: "\e68a";
}

.zi-write-line:before {
  content: "\e68b";
}

.zi-user-fill:before {
  content: "\e68c";
}

.zi-warning-line:before {
  content: "\e68e";
}

.zi-weight0:before {
  content: "\e68f";
}

.zi-style-fill:before {
  content: "\e690";
}

