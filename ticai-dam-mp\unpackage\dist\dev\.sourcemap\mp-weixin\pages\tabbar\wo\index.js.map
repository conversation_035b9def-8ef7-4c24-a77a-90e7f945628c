{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?0a15", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?0c69", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?0b7a", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?aca8", "uni-app:///pages/tabbar/wo/index.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?59ee", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/index.vue?b398"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "index", "formData", "billCount", "doing", "done", "close", "doingList", "doneList", "closeList", "computed", "tabColor0", "tabColor1", "tabColor2", "onLoad", "onShow", "ctx", "methods", "init", "title", "content", "showCancel", "success", "url", "showTab", "formatTime", "formatPoint", "loadCount", "that", "loadDoing", "status", "offset", "limit", "loadDone", "loadClose", "solveBill", "reviewBill", "viewBill", "detailBill"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgJjzB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;QACAvB;UACAwB;UACAC;UACAC;UACAC;YACA3B;cACA4B;YACA;UACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAX;QACA;UACAY;QACA;MACA;IACA;IACAC;MACA;MACAb;QACAc;QACAC;QACAC;MACA;QACA;UACAJ;QACA;MACA;IACA;IACAK;MACA;MACAjB;QACAc;QACAC;QACAC;MACA;QACA;UACAJ;QACA;MACA;IACA;IACAM;MACA;MACAlB;QACAc;QACAC;QACAC;MACA;QACA;UACAJ;QACA;MACA;IACA;IACAO;MACAxC;QAAA4B;MAAA;IACA;IACAa;MACAzC;QAAA4B;MAAA;IACA;IACAc;MACA1C;QAAA4B;MAAA;IACA;IACAe;MACA3C;QAAA4B;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/wo/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/wo/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=437a3ffa&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/wo/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=437a3ffa&\"", "var components\ntry {\n  components = {\n    uniGrid: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid\" */ \"@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue\"\n      )\n    },\n    uniGridItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-grid-item/uni-grid-item\" */ \"@dcloudio/uni-ui/lib/uni-grid-item/uni-grid-item.vue\"\n      )\n    },\n    uniBadge: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-badge/uni-badge\" */ \"@dcloudio/uni-ui/lib/uni-badge/uni-badge.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    watermark: function () {\n      return import(\n        /* webpackChunkName: \"components/watermark/watermark\" */ \"@/components/watermark/watermark.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.index == 0 ? _vm.doingList && _vm.doingList.length : null\n  var l0 =\n    _vm.index == 0 && g0\n      ? _vm.__map(_vm.doingList, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.formatTime(item.reportTime)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g1 =\n    !(_vm.index == 0) && _vm.index == 1\n      ? _vm.doneList && _vm.doneList.length\n      : null\n  var l1 =\n    !(_vm.index == 0) && _vm.index == 1 && g1\n      ? _vm.__map(_vm.doneList, function (item, __i1__) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.formatTime(item.reportTime)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var g2 =\n    !(_vm.index == 0) && !(_vm.index == 1) && _vm.index == 2\n      ? _vm.closeList && _vm.closeList.length\n      : null\n  var l2 =\n    !(_vm.index == 0) && !(_vm.index == 1) && _vm.index == 2 && g2\n      ? _vm.__map(_vm.closeList, function (item, __i2__) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.formatTime(item.reportTime)\n          var m3 = _vm.formatPoint(item.servicePoint)\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        g2: g2,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"tab-host\">\r\n\t\t\t<uni-grid :column=\"2\" :showBorder=\"false\" :square=\"false\">\r\n\t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 0 }\" @click=\"showTab(0)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"billCount.doing\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-doing\" size=\"30\" :color=\"tabColor0\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">未完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item>\r\n\t\t\t\t<!-- \t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 1 }\" @click=\"showTab(1)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"billCount.done\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-done\" size=\"30\" :color=\"tabColor1\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">已完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item> -->\r\n\t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 2 }\" @click=\"showTab(2)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"billCount.close\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-warning\" size=\"30\"\r\n\t\t\t\t\t\t\t\t:color=\"tabColor2\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">已完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item>\r\n\t\t\t</uni-grid>\r\n\t\t</view>\r\n\t\t<view class=\"tab-content\">\r\n\t\t\t<view v-if=\"index == 0\" class=\"list-block\">\r\n\t\t\t\t<view v-if=\"doingList && doingList.length\">\r\n\t\t\t\t\t<view v-for=\"item in doingList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">工单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">{{ formatTime(item.reportTime) }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点用户：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点地址：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationAddress }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">反映故障：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultReport }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">现场检查：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultCheck }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view style=\"float:right\" class=\"button-sp-area\">\r\n\t\t\t\t\t\t\t\t<button type=\"primary\" size=\"mini\" :data-id=\"item.id\" @click=\"solveBill\">完成</button>\r\n\t\t\t\t\t\t\t\t<button size=\"mini\" :data-id=\"item.id\" @click=\"viewBill\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"margin-left: 40rpx;\">详情</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">沒有未完成工单</uni-text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else-if=\"index == 1\" class=\"list-block\">\r\n\t\t\t\t<view v-if=\"doneList && doneList.length\">\r\n\t\t\t\t\t<view v-for=\"item in doneList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">工单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">{{ formatTime(item.reportTime) }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点用户：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点地址：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationAddress }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">反映故障：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultReport }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">现场检查：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultCheck }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view style=\"float:right\" class=\"button-sp-area\">\r\n\t\t\t\t\t\t\t\t<button type=\"primary\" size=\"mini\" :data-id=\"item.id\" @click=\"reviewBill\">评价</button>\r\n\t\t\t\t\t\t\t\t<button size=\"mini\" :data-id=\"item.id\" @click=\"detailBill\">详情</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">没有已完成工单</uni-text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else-if=\"index == 2\" class=\"list-block\">\r\n\t\t\t\t<view v-if=\"closeList && closeList.length\">\r\n\t\t\t\t\t<view v-for=\"item in closeList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">工单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">{{ formatTime(item.reportTime) }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点用户：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">网点地址：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.locationAddress }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">反映故障：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultReport }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">现场检查：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.faultCheck }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">服务评价：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ formatPoint(item.servicePoint) }}</view>\r\n\t\t\t\t\t\t\t<view style=\"float:right\" class=\"button-sp-area\">\r\n\t\t\t\t\t\t\t\t<button size=\"mini\" :data-id=\"item.id\" @click=\"detailBill\">详情</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">没有已完成工单</uni-text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 水印组件 -->\r\n\t\t<watermark />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport * as ctx from '../../../utils/context.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tindex: 0,\r\n\t\t\tformData: {},\r\n\t\t\tbillCount: {\r\n\t\t\t\tdoing: 0,\r\n\t\t\t\tdone: 0,\r\n\t\t\t\tclose: 0\r\n\t\t\t},\r\n\t\t\tdoingList: [],\r\n\t\t\tdoneList: [],\r\n\t\t\tcloseList: [],\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\ttabColor0: function () {\r\n\t\t\treturn this.index == 0 ? '#007AFF' : '#888888'\r\n\t\t},\r\n\t\ttabColor1: function () {\r\n\t\t\treturn this.index == 1 ? '#007AFF' : '#888888'\r\n\t\t},\r\n\t\ttabColor2: function () {\r\n\t\t\treturn this.index == 2 ? '#007AFF' : '#888888'\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\r\n\t},\r\n\tonShow: function () {\r\n\t\tctx.checkLogin(this.init, false, true);\r\n\t},\r\n\tmethods: {\r\n\t\tinit(user) {\r\n\t\t\tif (user == null || user.user == '0') {\r\n\t\t\t\twx.showModal({\r\n\t\t\t\t\ttitle: '系统提示',\r\n\t\t\t\t\tcontent: '您还未绑定，请先进行绑定',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\twx.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/index',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.loadCount()\r\n\t\t\tthis.loadDoing()\r\n\t\t\tthis.loadDone()\r\n\t\t\tthis.loadClose()\r\n\t\t},\r\n\t\tshowTab(index) {\r\n\t\t\tthis.index = index\r\n\t\t},\r\n\t\tformatTime(time) {\r\n\t\t\tif (time) return time.replace(/:00$/, '')\r\n\t\t},\r\n\t\tformatPoint(point) {\r\n\t\t\treturn ctx.getPointText(point)\r\n\t\t},\r\n\t\tloadCount() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/wo/count', function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.billCount = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadDoing() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/wo/query', {\r\n\t\t\t\tstatus: '1',\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tlimit: 50\r\n\t\t\t}, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.doingList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadDone() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/wo/query', {\r\n\t\t\t\tstatus: '2',\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tlimit: 50\r\n\t\t\t}, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.doneList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadClose() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/wo/query', {\r\n\t\t\t\tstatus: '3',\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tlimit: 50\r\n\t\t\t}, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.closeList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsolveBill(evt) {\r\n\t\t\twx.navigateTo({ url: 'solve?id=' + evt.currentTarget.dataset.id })\r\n\t\t},\r\n\t\treviewBill(evt) {\r\n\t\t\twx.navigateTo({ url: 'review?id=' + evt.currentTarget.dataset.id })\r\n\t\t},\r\n\t\tviewBill(evt) {\r\n\t\t\twx.navigateTo({ url: 'view?id=' + evt.currentTarget.dataset.id })\r\n\t\t},\r\n\t\tdetailBill(evt) {\r\n\t\t\twx.navigateTo({ url: 'detail?id=' + evt.currentTarget.dataset.id })\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {}\r\n\r\n.tab-host {\r\n\ttext-align: center;\r\n\tborder-bottom: 1px solid #ccc;\r\n\tpadding-bottom: 10rpx;\r\n\tpadding-top: 20rpx;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tz-index: 100;\r\n\twidth: 100%;\r\n\tbackground-color: #FFFFFF;\r\n}\r\n\r\n.tab-content {\r\n\tmargin-top: 150rpx;\r\n}\r\n\r\n.tool-item uni-icons {\r\n\tdisplay: block;\r\n}\r\n\r\n.tool-item .icon-text {\r\n\tdisplay: block;\r\n\tline-height: 60rpx;\r\n}\r\n\r\n.tool-item.act .icon-text {\r\n\tcolor: #007AFF;\r\n}\r\n\r\n.list-block {\r\n\tbackground-color: #EEE;\r\n\tpadding: 0;\r\n}\r\n\r\n.list-item {\r\n\tmargin-top: 1px;\r\n\tpadding: 8px;\r\n\tbackground-color: #FFF;\r\n\tborder-bottom: 1px dotted #EEE;\r\n}\r\n\r\n.list-item .row {\r\n\tmargin: 4px 0;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n}\r\n\r\n.list-item .row .caption {\r\n\tflex: 1;\r\n\tfont-size: 14px;\r\n\tfont-weight: bold;\r\n\tline-height: 40px;\r\n\toverflow: hidden;\r\n\ttext-align: left;\r\n}\r\n\r\n.list-item .row .button {\r\n\tmin-width: 80px;\r\n\twhite-space: nowrap;\r\n\ttext-align: right;\r\n}\r\n\r\n.list-item .row .label {\r\n\tfont-size: 12px;\r\n\tmin-width: 60px;\r\n\twhite-space: nowrap;\r\n\tline-height: 24px;\r\n}\r\n\r\n.list-item .row .text {\r\n\tflex: 1;\r\n\tfont-size: 12px;\r\n\tcolor: #666;\r\n\tline-height: 24px;\r\n}\r\n\r\n.list-item .row .time {\r\n\tfont-size: 12px;\r\n\tmin-width: 80px;\r\n\twhite-space: nowrap;\r\n\ttext-align: right;\r\n}\r\n\r\n.time-item .van-count-down {\r\n\tmargin: 2px 4px;\r\n\tcolor: #FFF;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623958\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}