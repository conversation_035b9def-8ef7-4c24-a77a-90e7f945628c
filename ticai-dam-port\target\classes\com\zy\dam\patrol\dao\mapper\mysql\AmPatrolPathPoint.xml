<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPathPointDAO">

    <sql id="meta">
			a.ID_
			,a.PATH_
			,a.POINT_
			,a.PREV_POINT_
			,a.NEXT_POINT_
			,a.PREV_DIST_
			,a.NEXT_DIST_
			,a.ORD_
	</sql>

    <select id="findByPath" resultType="com.zy.dam.patrol.vo.PatrolPathPointVo">
        select
        <include refid="meta"/>
        from AM_PATROL_PATH_POINT a
        where a.PATH_=#{0}
        order by a.ORD_
    </select>

</mapper>
