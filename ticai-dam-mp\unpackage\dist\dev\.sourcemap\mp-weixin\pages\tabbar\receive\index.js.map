{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?7d97", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?a3d2", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?b67f", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?ecce", "uni-app:///pages/tabbar/receive/index.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?8788", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/receive/index.vue?25aa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "dataModel", "asset", "location", "locationCode", "locationName", "locationAddress", "contact", "phone", "sn", "formRules", "rules", "required", "errorMessage", "imageValue", "formData", "doneList", "no", "onShow", "ctx", "methods", "init", "title", "content", "showCancel", "success", "url", "searchSn", "that", "receiveScan", "onlyFromCamera", "scanType", "r", "saveReceive", "assetList", "searchNo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoEjzB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;QACAD;UACAE;YACAC;YACAC;UACA;QACA;QACAT;UACAO;YACAC;YACAC;UACA;QACA;QACAR;UACAM;YACAC;YACAC;UACA;QACA;QACAP;UACAK;YACAC;YACAC;UACA;QACA;QACAN;UACAI;YACAC;YACAC;UACA;QACA;QACAL;UACAG;YACAC;YACAC;UACA;QACA;MACA;MACAC;MACAC;MACAC;MACAP;MACAQ;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;QACA1B;UACA2B;UACAC;UACAC;UACAC;YACA9B;cACA+B;YACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAR;QACA;QACA;QACAS;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAC;MACA;MACAlC;QACAmC;QACAC;QACAN;UACA;UACA;YACA;YACA;YACA;cACAN;gBACA;gBACAa;kBACAJ;gBACA;cACA;YACA;cACAT;YACA;UACA;QACA;MACA;IACA;IACAc;MACA;MACA;QACA;UAAAC;QAAA;QACAN;UACA5B;QACA;QACAmB;UACA;UACAA;QACA;UAAAA;QAAA;MACA;IACA;IACAgB;MACA;MACAhB;QACA;QACAa;UACAJ;QACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1MA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/receive/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/receive/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=064167a4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/receive/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=064167a4&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms\" */ \"@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item\" */ \"@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-row/uni-row\" */ \"@dcloudio/uni-ui/lib/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-col/uni-col\" */ \"@dcloudio/uni-ui/lib/uni-col/uni-col.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.doneList && _vm.doneList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view @click=\"receiveScan\" style=\"text-align: center;\">\r\n\t\t\t<uni-icons type=\"scan\" size=\"80\"></uni-icons>\r\n\t\t</view>\r\n\t\t<uni-forms ref=\"form\" :modelValue=\"dataModel\" :rules=\"formRules\" border label-align=\"right\" label-width=\"80\">\r\n\t\t\t<uni-forms-item label=\"终端号\" required name=\"sn\">\r\n\t\t\t\t<uni-row style=\"font-size：0；\">\r\n\t\t\t\t\t<uni-col :span=\"17\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"text\" size=\"mini\" v-model=\"sn\" placeholder=\"请输入终端号\"></uni-easyinput>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t\t<uni-col :span=\"7\">\r\n\t\t\t\t\t\t<button type=\"primary\" class=\"ceshi\" plain=\"true\" @click=\"searchSn\">核查</button>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t</uni-row>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"资产编码\" name=\"no\">\r\n\t\t\t\t<uni-row style=\"font-size：0；\">\r\n\t\t\t\t\t<uni-col :span=\"17\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"text\" size=\"mini\" v-model=\"no\" placeholder=\"请输入资产编码搜索资产信息\"></uni-easyinput>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t\t<uni-col :span=\"7\">\r\n\t\t\t\t\t\t<button type=\"primary\" class=\"ceshi\" plain=\"true\" @click=\"searchNo\">搜索</button>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t</uni-row>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点编码\" required name=\"locationCode\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.locationCode\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点Id\" name=\"location\" style=\"display:none\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.location\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点用户\" required name=\"locationName\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.locationName\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点地址\" required name=\"locationAddress\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.locationAddress\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系人\" required name=\"contact\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.contact\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系电话\" required name=\"phone\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.phone\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t</uni-forms>\r\n\t\t<text class=\"caption\">资产领用列表：</text>\r\n\t\t<view class=\"list-block\">\r\n\t\t\t<view v-if=\"doneList && doneList.length\">\r\n\t\t\t\t<view v-for=\"item in doneList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t<view class=\"label\">资产编码：</view>\r\n\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t<view class=\"label\">资产名称：</view>\r\n\t\t\t\t\t\t<view class=\"text\">{{ item.name }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"test\" />\r\n\t\t\t<view class=\"rec\">\r\n\t\t\t\t<button type=\"primary\" class=\"but\" @click=\"saveReceive\">提交领用</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport settings from '../../../utils/settings.js'\r\nimport * as ctx from '../../../utils/context.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdataModel: { asset: '', location: '', locationCode: '', locationName: '', locationAddress: '', contact: '', phone: '', sn: '' },\r\n\t\t\tformRules: {\r\n\t\t\t\tsn: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入终端号'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tlocationCode: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入网点编码'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tlocationName: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入网点用户'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tlocationAddress: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入网点地址'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tcontact: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入联系人'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tphone: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请输入电话'\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageValue: [],\r\n\t\t\tformData: {},\r\n\t\t\tdoneList: [],\r\n\t\t\tsn: null,\r\n\t\t\tno: null\r\n\t\t}\r\n\t},\r\n\tonShow: function () {\r\n\t\tctx.checkLogin(this.init, true, true);\r\n\t},\r\n\tmethods: {\r\n\t\tinit(user) {\r\n\t\t\tif (user == null || user.user == '0') {\r\n\t\t\t\twx.showModal({\r\n\t\t\t\t\ttitle: '系统提示',\r\n\t\t\t\t\tcontent: '您还未绑定，请先进行绑定',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\twx.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/index',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t},\r\n\t\tsearchSn() {\r\n\t\t\tconst that = this;\r\n\t\t\tctx.post('/wx/getBySn/' + this.sn, function (r) {\r\n\t\t\t\tif (r.code < 0) return ctx.error(r.msg)\r\n\t\t\t\tif (r.data == null) return ctx.error('该终端号不存在')\r\n\t\t\t\tthat.dataModel.asset = r.data.id;\r\n\t\t\t\tthat.dataModel.location = r.data.location;\r\n\t\t\t\tthat.dataModel.locationCode = r.data.locationCode;\r\n\t\t\t\tthat.dataModel.locationName = r.data.locationName;\r\n\t\t\t\tthat.dataModel.locationAddress = r.data.locationAddress;\r\n\t\t\t\tthat.dataModel.contact = r.data.locationContact;\r\n\t\t\t\tthat.dataModel.phone = r.data.locationPhone;\r\n\t\t\t\tthat.dataModel.sn = r.data.sn;\r\n\t\t\t})\r\n\t\t},\r\n\t\treceiveScan() {\r\n\t\t\tconst that = this\r\n\t\t\twx.scanCode({\r\n\t\t\t\tonlyFromCamera: true,\r\n\t\t\t\tscanType: 'qrCode',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tconst qr = res.result;\r\n\t\t\t\t\tif (qr) {\r\n\t\t\t\t\t\tif (qr.substring(0, 2) != '1:') return ctx.error('无效的二维码')\r\n\t\t\t\t\t\tlet id = qr.substr(2).split('\\n')[0]\r\n\t\t\t\t\t\tif (id) {\r\n\t\t\t\t\t\t\tctx.post('/wx/findAssetConsumingScan/' + id, function (r) {\r\n\t\t\t\t\t\t\t\tif (r.code < 0 || r.data.assetList == 0) return ctx.error('该资产在使用中', 'back')\r\n\t\t\t\t\t\t\t\tr.data.assetList.forEach(r => {\r\n\t\t\t\t\t\t\t\t\tthat.doneList.push(r)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tctx.error('无效参数', 'back')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsaveReceive() {\r\n\t\t\tconst that = this\r\n\t\t\tthis.$refs.form.validate().then(_ => {\r\n\t\t\t\tconst data = Object.assign(that.dataModel, { assetList: [] })\r\n\t\t\t\tthat.doneList.forEach(r => {\r\n\t\t\t\t\tdata.assetList.push(r)\r\n\t\t\t\t})\r\n\t\t\t\tctx.post('/wx/consuming/apply', data, function (res) {\r\n\t\t\t\t\tif (res.code < 0) return ctx.error(res.msg)\r\n\t\t\t\t\tctx.ok('领用成功', 'back')\r\n\t\t\t\t}).catch(() => { ctx.error('网络异常') })\r\n\t\t\t}).catch(() => { })\r\n\t\t},\r\n\t\tsearchNo() {\r\n\t\t\tconst that = this;\r\n\t\t\tctx.post('/wx/findAssetConsumingNo/' + that.no, function (r) {\r\n\t\t\t\tif (r.code < 0 || r.data.assetList == 0) return ctx.error('该资产在使用中', 'back')\r\n\t\t\t\tr.data.assetList.forEach(r => {\r\n\t\t\t\t\tthat.doneList.push(r)\r\n\t\t\t\t})\r\n\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tmargin: 10rpx;\r\n}\r\n\r\n.list-block {\r\n\tbackground-color: #EEE;\r\n\tpadding: 0;\r\n}\r\n\r\n.rec {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n}\r\n\r\n.list-item {\r\n\tmargin-top: 1px;\r\n\tpadding: 8px;\r\n\tbackground-color: #FFF;\r\n\tborder-bottom: 1px solid #ccc;\r\n}\r\n\r\n.list-item .row {\r\n\tmargin: 4px 0;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n}\r\n\r\n.list-item .row .label {\r\n\tfont-size: 12px;\r\n\tmin-width: 60px;\r\n\twhite-space: nowrap;\r\n\tline-height: 24px;\r\n}\r\n\r\n.list-item .row .text {\r\n\tflex: 1;\r\n\tfont-size: 12px;\r\n\tcolor: #666;\r\n\tline-height: 24px;\r\n}\r\n\r\n.ceshi {\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n}\r\n\r\n.but {\r\n\tmin-height: 82rpx;\r\n\tline-height: 82rpx;\r\n}\r\n\r\n.test {\r\n\theight: 82rpx;\r\n\tbackground-color: #fff;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623957\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}