<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetConsumingDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.BACK_TIME_
			,a.DEPT_
			,a.RECEIVER_
			,a.REGION_
			,a.LOCATION_
			,a.MEMO_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CTIME_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetConsuming">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET_CONSUMING(ID_,NO_,USER_,TIME_,BACK_TIME_,DEPT_,RECEIVER_,REGION_,LOCATION_,MEMO_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_,FLAG_)
        values(#{id},#{no},#{user},timestamp(date_add(curdate(), interval - 0
        day)),#{backTime},#{dept},#{receiver},#{region},#{location},#{memo},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetConsuming">
		update AM_ASSET_CONSUMING
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=now(),CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 获取唯一的资管-资产领用退库数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetConsuming">
        select
        <include refid="meta"/>
        from AM_ASSET_CONSUMING a where a.ID_=#{0}
    </select>

    <insert id="insertDetail">
		insert into AM_ASSET_CONSUMING_DETAIL(BILL_, ASSET_, ORD_, DEPT_, REGION_, LOCATION_, USE_DEPT_, USE_USER_, ASSET_STATUS_, DEPOSIT_STATUS_,FLAG_)
		values(#{id}, #{asset}, #{ord}, #{dept}, #{region}, #{location}, #{useDept}, #{useUser}, #{assetStatus}, '0','1')
	</insert>

    <!-- 更新资产领用，置为使用中 -->
    <update id="updateUse">
        update AM_ASSET a,AM_ASSET_CONSUMING_DETAIL b, AM_ASSET_CONSUMING c
        set a.STATUS_='2',a.USE_DEPT_=c.DEPT_,a.USE_USER_=c.RECEIVER_,a.REGION_=c.REGION_,a.LOCATION_=c.LOCATION_,a.DEPT_=c.DEPT_
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

</mapper>
