<view class="uni-collapse-item"><view data-event-opts="{{[['tap',[['onClick',[!isOpen]]]]]}}" class="{{['uni-collapse-item__title',(isOpen&&titleBorder==='auto')?'is-open':'',(titleBorder!=='none')?'uni-collapse-item-border':'']}}" bindtap="__e"><view class="uni-collapse-item__title-wrap"><block wx:if="{{$slots.title}}"><slot name="title"></slot></block><block wx:else><view class="{{['uni-collapse-item__title-box',(disabled)?'is-disabled':'']}}"><block wx:if="{{thumb}}"><image class="uni-collapse-item__title-img" src="{{thumb}}"></image></block><text class="uni-collapse-item__title-text">{{title}}</text></view></block></view><block wx:if="{{showArrow}}"><view class="{{['uni-collapse-item__title-arrow',(isOpen)?'uni-collapse-item__title-arrow-active':'',(showAnimation===true)?'uni-collapse-item--animation':'']}}"><uni-icons vue-id="1775f004-1" color="{{disabled?'#ddd':'#bbb'}}" size="14" type="bottom" bind:__l="__l"></uni-icons></view></block></view><view class="{{['uni-collapse-item__wrap',(showAnimation)?'is--transition':'']}}" style="{{'height:'+((isOpen?height:0)+'px')+';'}}"><view class="{{['uni-collapse-item__wrap-content','vue-ref',(isheight)?'open':'',(border&&isOpen)?'uni-collapse-item--border':'']}}" id="{{elId}}" data-ref="collapse--hook"><slot></slot></view></view></view>