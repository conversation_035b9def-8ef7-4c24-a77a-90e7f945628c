{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js!F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1752649876948}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}