{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue", "mtime": 1752649761442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnZVRhYmxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9QYWdlVGFibGUudnVlJw0KaW1wb3J0IEFzc2V0VHlwZUNob3NlbiBmcm9tICdAL3ZpZXdzL2NvbXBvbmVudHMvQXNzZXRUeXBlQ2hvc2VuLnZ1ZScNCmltcG9ydCB7IGdldEFzc2V0U3RhdHVzVHlwZSwgZ2V0QXNzZXRTdGF0dXNUZXh0IH0gZnJvbSAnLi4vanMvYXNzZXQuanMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogeyBQYWdlVGFibGUsIEFzc2V0VHlwZUNob3NlbiB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxZm9ybTogew0KICAgICAgICB0ZXJtaW5hbE5vOiAnJywNCiAgICAgICAgbm86ICcnLA0KICAgICAgICBsb2NhdGlvbk5hbWU6ICcnDQogICAgICB9LA0KICAgICAgaGlzdG9yeURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgaGlzdG9yeUxvYWRpbmc6IGZhbHNlLA0KICAgICAgY3VycmVudEFzc2V0OiBudWxsLA0KICAgICAgaGlzdG9yeUxpc3Q6IFtdDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIC8vIOmhtemdouWKoOi9veaXtuiHquWKqOafpeivouaVsOaNrg0KICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgIHRoaXMuc2VhcmNoQXNzZXQoKQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZWFyY2hBc3NldCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZ3JpZC5zZWFyY2godGhpcy5xZm9ybSkNCiAgICB9LA0KICAgIHZpZXdBc3NldEhpc3Rvcnkocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRBc3NldCA9IHJvdw0KICAgICAgdGhpcy5oaXN0b3J5RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmhpc3RvcnlMaXN0ID0gW10NCg0KICAgICAgLy8g5L2/55So6LWE5LqnSUTmn6Xor6Lljoblj7LorrDlvZUNCiAgICAgIGNvbnN0IGFzc2V0SWQgPSByb3cuaWQNCiAgICAgIGlmICghYXNzZXRJZCkgew0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5peg5rOV6I635Y+W6LWE5LqnSUTvvIzor7fnoa7kv53otYTkuqfmnIlJRCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLiRodHRwLnBvc3QoYC9hbS9hc3NldC90cmFjZS9oaXN0b3J5QnlJZC8ke2Fzc2V0SWR9YCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgIHRoaXMuaGlzdG9yeUxpc3QgPSByZXMuZGF0YSB8fCBbXQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn6I635Y+W6LWE5Lqn5Y6G5Y+y6K6w5b2V5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc6ZSZ6K+v77yM6K+356iN5ZCO6YeN6K+VJykNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRBc3NldFN0YXR1c1R5cGUodikgew0KICAgICAgcmV0dXJuIGdldEFzc2V0U3RhdHVzVHlwZSh2LnN0YXR1cykNCiAgICB9LA0KICAgIGdldEFzc2V0U3RhdHVzVGV4dCh2KSB7DQogICAgICByZXR1cm4gZ2V0QXNzZXRTdGF0dXNUZXh0KHYuc3RhdHVzKQ0KICAgIH0sDQogICAgZm9ybWF0RGF0ZShkYXRlLCBmb3JtYXQgPSAneXl5eS1NTS1kZCBISDptbTpzcycpIHsNCiAgICAgIGlmICghZGF0ZSkgcmV0dXJuICcnDQoNCiAgICAgIC8vIOWIm+W7uuS4gOS4quaWsOeahERhdGXlr7nosaENCiAgICAgIGNvbnN0IGQgPSBuZXcgRGF0ZShkYXRlKQ0KDQogICAgICAvLyDmoLzlvI/ljJblubTmnIjml6UNCiAgICAgIGNvbnN0IHllYXIgPSBkLmdldEZ1bGxZZWFyKCkNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGQuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhkLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKQ0KDQogICAgICAvLyDmoLzlvI/ljJbml7bliIbnp5INCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGQuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhkLmdldE1pbnV0ZXMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3Qgc2Vjb25kcyA9IFN0cmluZyhkLmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KDQogICAgICAvLyDmoLnmja7kvKDlhaXnmoTmoLzlvI/ov5Tlm57nm7jlupTnmoTml6XmnJ/lrZfnrKbkuLINCiAgICAgIGlmIChmb3JtYXQgPT09ICd5eXl5LU1NLWRkJykgew0KICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gDQogICAgICB9DQogICAgfSwNCiAgICBleHBvcnRIaXN0b3J5KCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRBc3NldCB8fCAhdGhpcy5jdXJyZW50QXNzZXQuaWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5peg5rOV6I635Y+W6LWE5Lqn5L+h5oGv77yM6K+36YeN5paw5omT5byA6K+m5oOFJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aCqOehruWumuimgeWvvOWHuui/meS4qui1hOS6p+eahOWOhuWPsuiusOW9leWQlz8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBjb25zdCBsb2FkSW5zdCA9IHRoaXMuJGxvYWRpbmcoeyBmdWxsc2NyZWVuOiB0cnVlLCB0ZXh0OiAn5q2j5Zyo5a+85Ye65paH5Lu277yM6K+36ICQ5b+D562J5b6FLi4uJyB9KQ0KICAgICAgICB0aGlzLiRqYXNwZXIoew0KICAgICAgICAgIHVybDogYC9hbS9hc3NldC90cmFjZS9leHBvcnRCeUFzc2V0SWQvJHt0aGlzLmN1cnJlbnRBc3NldC5pZH1gLA0KICAgICAgICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InDQogICAgICAgIH0pLnRoZW4oYmxvYiA9PiB7DQogICAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICAgIC8vIOeUn+aIkOaWh+S7tuWQjQ0KICAgICAgICAgIGNvbnN0IGFzc2V0Tm8gPSB0aGlzLmN1cnJlbnRBc3NldC5ubyB8fCAn5pyq55+l57yW5Y+3Jw0KICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gYOi1hOS6p+WOhuWPsuiusOW9lV8ke2Fzc2V0Tm99XyR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDEwKS5yZXBsYWNlKC8tL2csICcnKX0ueGxzeGANCiAgICAgICAgICB0aGlzLiRzYXZlQXMoYmxvYiwgZmlsZU5hbWUpDQogICAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWHuueUn+aIkOWHuumUmTonICsgZXJyKQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/asset/asset-trace", "sourcesContent": ["<template>\r\n  <div style=\"padding: 0 10px;\">\r\n    <div class=\"page-header\">\r\n      <div class=\"page-tollbar\">\r\n        <div class=\"opt\">\r\n          <el-button-group>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-search\" @click=\"searchAsset\">查询资产</el-button>\r\n          </el-button-group>\r\n        </div>\r\n      </div>\r\n      <div class=\"page-filter\">\r\n        <el-form :model=\"qform\" inline size=\"mini\" label-width=\"110px\" @submit.native.prevent=\"searchAsset\">\r\n          <el-form-item label=\"终端号：\">\r\n            <el-input v-model=\"qform.terminalNo\" clearable placeholder=\"请输入终端号\" autocomplete=\"off\"\r\n              style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"资产编号：\">\r\n            <el-input v-model=\"qform.no\" clearable placeholder=\"请输入资产编号\" autocomplete=\"off\" style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"业主名称：\">\r\n            <el-input v-model=\"qform.locationName\" clearable placeholder=\"请输入业主名称\" autocomplete=\"off\"\r\n              style=\"width:180px;\" />\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <div class=\"page-body\">\r\n      <page-table ref=\"grid\" v-table-height size=\"mini\" path=\"/am/asset/trace/page\" :query=\"qform\" stripe border\r\n        highlight-current-row @row-click=\"viewAssetHistory\">\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"130\" header-align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" header-align=\"center\" fixed=\"left\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAssetHistory(scope.row)\">{{ scope.row.no }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"130\" fixed=\"left\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAssetHistory(scope.row)\">{{ scope.row.name }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"规格型号\" prop=\"spec\" width=\"120\" header-align=\"center\" />\r\n        <el-table-column label=\"入库日期\" prop=\"inDate\" width=\"120\" header-align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDate(scope.row.inDate, 'yyyy-MM-dd') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"使用部门\" prop=\"useDeptName\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"使用人\" prop=\"useUserName\" width=\"90\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"terminalNo\" width=\"110\" header-align=\"center\" />\r\n        <el-table-column label=\"使用网点\" prop=\"locationName\" min-width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"定位地址\" prop=\"locAddr\" min-width=\"250\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"small\" hit :type=\"getAssetStatusType(scope.row)\" disable-transitions>{{\r\n              getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n\r\n    <!-- 资产历史记录对话框 -->\r\n    <el-dialog :title=\"'资产历史记录 - ' + (currentAsset ? currentAsset.name : '')\" :visible.sync=\"historyDialogVisible\"\r\n      width=\"80%\" :close-on-click-modal=\"false\">\r\n      <div v-loading=\"historyLoading\">\r\n        <div v-if=\"currentAsset\">\r\n          <el-descriptions :column=\"4\" border>\r\n            <el-descriptions-item label=\"资产编码\">{{ currentAsset.no }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"资产名称\">{{ currentAsset.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"资产类型\">{{ currentAsset.typeName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"规格型号\">{{ currentAsset.spec }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"终端号\">{{ currentAsset.terminalNo }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"使用网点\">{{ currentAsset.locationName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"定位地址\">{{ currentAsset.locAddr }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"当前状态\">\r\n              <el-tag size=\"small\" hit :type=\"getAssetStatusType(currentAsset)\" disable-transitions>{{\r\n                getAssetStatusText(currentAsset) }}</el-tag>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <el-table :data=\"historyList\" style=\"width: 100%; margin-top: 15px;\" border stripe>\r\n          <el-table-column label=\"资产编码\" prop=\"no\" width=\"120\" align=\"center\" fixed=\"left\" />\r\n          <el-table-column label=\"操作类型\" prop=\"operationTypeName\" width=\"100\" align=\"center\" fixed=\"left\" />\r\n          <el-table-column label=\"操作单号\" prop=\"operationNo\" width=\"150\" align=\"center\" />\r\n          <el-table-column label=\"操作时间\" prop=\"operationTime\" width=\"160\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDate(scope.row.operationTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作人\" prop=\"operatorName\" width=\"120\" align=\"center\" />\r\n          <el-table-column label=\"终端号\" prop=\"terminalNo\" width=\"120\" align=\"center\" />\r\n          <el-table-column label=\"业主名称\" prop=\"locationName\" width=\"150\" align=\"center\" />\r\n          <el-table-column label=\"更新信息\" prop=\"updateInfo\" min-width=\"250\" header-align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"exportHistory\">导出</el-button>\r\n        <el-button @click=\"historyDialogVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport AssetTypeChosen from '@/views/components/AssetTypeChosen.vue'\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nexport default {\r\n  components: { PageTable, AssetTypeChosen },\r\n  data() {\r\n    return {\r\n      qform: {\r\n        terminalNo: '',\r\n        no: '',\r\n        locationName: ''\r\n      },\r\n      historyDialogVisible: false,\r\n      historyLoading: false,\r\n      currentAsset: null,\r\n      historyList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    // 页面加载时自动查询数据\r\n    this.$nextTick(() => {\r\n      this.searchAsset()\r\n    })\r\n  },\r\n  methods: {\r\n    searchAsset() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    viewAssetHistory(row) {\r\n      this.currentAsset = row\r\n      this.historyDialogVisible = true\r\n      this.historyLoading = true\r\n      this.historyList = []\r\n\r\n      // 使用资产ID查询历史记录\r\n      const assetId = row.id\r\n      if (!assetId) {\r\n        this.historyLoading = false\r\n        this.$message.error('无法获取资产ID，请确保资产有ID')\r\n        return\r\n      }\r\n\r\n      this.$http.post(`/am/asset/trace/historyById/${assetId}`).then(res => {\r\n        this.historyLoading = false\r\n        if (res.code > 0) {\r\n          this.historyList = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取资产历史记录失败')\r\n        }\r\n      }).catch(() => {\r\n        this.historyLoading = false\r\n        this.$message.error('网络错误，请稍后重试')\r\n      })\r\n    },\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    formatDate(date, format = 'yyyy-MM-dd HH:mm:ss') {\r\n      if (!date) return ''\r\n\r\n      // 创建一个新的Date对象\r\n      const d = new Date(date)\r\n\r\n      // 格式化年月日\r\n      const year = d.getFullYear()\r\n      const month = String(d.getMonth() + 1).padStart(2, '0')\r\n      const day = String(d.getDate()).padStart(2, '0')\r\n\r\n      // 格式化时分秒\r\n      const hours = String(d.getHours()).padStart(2, '0')\r\n      const minutes = String(d.getMinutes()).padStart(2, '0')\r\n      const seconds = String(d.getSeconds()).padStart(2, '0')\r\n\r\n      // 根据传入的格式返回相应的日期字符串\r\n      if (format === 'yyyy-MM-dd') {\r\n        return `${year}-${month}-${day}`\r\n      } else {\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n      }\r\n    },\r\n    exportHistory() {\r\n      if (!this.currentAsset || !this.currentAsset.id) {\r\n        this.$message.error('无法获取资产信息，请重新打开详情')\r\n        return\r\n      }\r\n\r\n      this.$confirm('您确定要导出这个资产的历史记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const loadInst = this.$loading({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n        this.$jasper({\r\n          url: `/am/asset/trace/exportByAssetId/${this.currentAsset.id}`,\r\n          responseType: 'blob'\r\n        }).then(blob => {\r\n          loadInst.close()\r\n          // 生成文件名\r\n          const assetNo = this.currentAsset.no || '未知编号'\r\n          const fileName = `资产历史记录_${assetNo}_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`\r\n          this.$saveAs(blob, fileName)\r\n        }).catch(err => {\r\n          loadInst.close()\r\n          this.$message.error('导出生成出错:' + err)\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-header {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-tollbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-filter {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}