<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.bd.dao.BdPrintTemplateDAO">

    <sql id="meta">
			a.ID_
			,a.USER_
			,a.NAME_
			,a.KIND_
			,a.WIDTH_
			,a.HEIGHT_
			,a.TOP_
			,a.BOTTOM_
			,a.LEFT_
			,a.RIGHT_
			,a.TYPE_
			,a.PRINTER_
			,a.ACTIVE_
			,a.CTIME_
			,a.MTIME_
			,a.FLAG_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.bd.orm.BdPrintTemplate">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into BD_PRINT_TEMPLATE(ID_,USER_,NAME_,KIND_,WIDTH_,HEIGHT_,TOP_,BOTTOM_,LEFT_,RIGHT_,TYPE_,PRINTER_,ACTIVE_,CTIME_,MTIME_,FLAG_)
        values(#{id},#{user},#{name},#{kind},#{width},#{height},#{top},#{bottom},#{left},#{right},#{type},#{printer},'2',now(),now(),'1')
    </insert>

    <update id="update" parameterType="com.zy.dam.bd.orm.BdPrintTemplate">
		update BD_PRINT_TEMPLATE
		set NAME_=#{name},KIND_=#{kind},WIDTH_=#{width},HEIGHT_=#{height},TOP_=#{top},BOTTOM_=#{bottom},LEFT_=#{left},RIGHT_=#{right},TYPE_=#{type},PRINTER_=#{printer},MTIME_=now()
		where ID_=#{id}
	</update>

    <update id="updateActive">
		update BD_PRINT_TEMPLATE set ACTIVE_=#{1} where ID_=#{0}
	</update>
    <update id="updateTemplateByType">
		update BD_PRINT_TEMPLATE set ACTIVE_='2' where FLAG_='1' and TYPE_=#{0} and ID_!=#{1}
	</update>

    <select id="findAll" resultType="com.zy.dam.bd.vo.PrintTemplateVo">
        select
        <include refid="meta"/>
        from BD_PRINT_TEMPLATE a
        where a.FLAG_='1'
        order by a.MTIME_ desc
    </select>

    <select id="findByType" resultType="com.zy.dam.bd.vo.PrintTemplateVo">
        select
        <include refid="meta"/>
        from BD_PRINT_TEMPLATE a
        where a.FLAG_='1' and a.TYPE_=#{0}
        order by a.MTIME_ desc
    </select>

    <select id="findOne" resultType="com.zy.dam.bd.orm.BdPrintTemplate">
        select
        <include refid="meta"/>
        from BD_PRINT_TEMPLATE a where a.ID_=#{0}
    </select>

    <update id="delete">
		update BD_PRINT_TEMPLATE set FLAG_='9' where ID_=#{0}
	</update>

    <select id="findVo" resultType="com.zy.dam.bd.vo.PrintTemplateVo">
        select
        <include refid="meta"/>
        from BD_PRINT_TEMPLATE a where a.ID_=#{0}
    </select>

</mapper>
