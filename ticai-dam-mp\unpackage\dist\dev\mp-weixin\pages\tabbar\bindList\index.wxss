
.container.data-v-4a0277e6 {
	margin: 10rpx;
}
.list-block.data-v-4a0277e6 {
	padding: 0;
}
.list-item.data-v-4a0277e6 {
	margin-top: 1px;
	padding: 8px;
	background-color: #FFF;
	border-bottom: 1px solid #ffffff;
}
.list-item .row.data-v-4a0277e6 {
	margin: 4px 0;
	display: flex;
	flex-direction: row;
	align-items: center;
}
.list-item .row .label.data-v-4a0277e6 {
	font-size: 12px;
	min-width: 60px;
	white-space: nowrap;
	line-height: 24px;
}
.list-item .row .text.data-v-4a0277e6 {
	flex: 1;
	font-size: 12px;
	color: #666;
	line-height: 24px;
}
.list-item .row .time.data-v-4a0277e6 {
	font-size: 12px;
	min-width: 80px;
	white-space: nowrap;
	text-align: right;
}

/* 添加分割线 */
.list-item .row.row-with-divider.data-v-4a0277e6 {
	border-bottom: 1px solid #e5e5e5;
	padding-bottom: 15px;
}

/* 搜索容器样式 */
.search-container.data-v-4a0277e6 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.search-input-wrapper.data-v-4a0277e6 {
	flex: 1;
	min-width: 0;
	/* 确保可以收缩 */
}
.search-buttons.data-v-4a0277e6 {
	display: flex;
	gap: 8rpx;
	flex-shrink: 0;
	/* 防止按钮被压缩 */
}
.icon-btn.data-v-4a0277e6 {
	height: 70rpx;
	width: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	margin: 0;
	background-color: #ffffff !important;
	border-color: #e5e5e5 !important;
}
.data-type-tag.data-v-4a0277e6 {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
	color: #fff;
	margin-bottom: 8px;
}
.type-1.data-v-4a0277e6 {
	background-color: #67C23A;
	/* 绿色 - 已领用已绑定 */
}
.type-2.data-v-4a0277e6 {
	background-color: #E6A23C;
	/* 黄色 - 已领用未绑定 */
}
.type-3.data-v-4a0277e6 {
	background-color: #909399;
	/* 灰色 - 未绑定网点 */
}

/* 筛选面板样式 */
.filter-content.data-v-4a0277e6 {
	padding: 20rpx 20rpx 20rpx 0rpx;
	background-color: #ffffff;
}
.filter-row.data-v-4a0277e6 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
	position: relative;
}
.filter-label.data-v-4a0277e6 {
	font-size: 12px;
	color: #333;
	min-width: 120rpx;
	margin-right: 20rpx;
	margin-left: 0rpx;
}

/* 折叠面板标题样式*/
.data-v-4a0277e6 .uni-collapse-item__title-text {
	font-size: 12px !important;
}

/* 日期选择器样式 */
.date-picker-wrapper.data-v-4a0277e6 {
	flex: 1;
	position: relative;
	z-index: 1;
}

/* 日期选择器输入框样式 */
.data-v-4a0277e6 .uni-datetime-picker {
	height: 60rpx !important;
}
.data-v-4a0277e6 .uni-datetime-picker .uni-datetime-picker-text {
	font-size: 12px !important;
	height: 60rpx !important;
	line-height: 60rpx !important;
}
.data-v-4a0277e6 .uni-datetime-picker .uni-datetime-picker-view {
	height: 60rpx !important;
	line-height: 60rpx !important;
}

/* 日期选择器内部开始日期和结束日期文字样式 */
.data-v-4a0277e6 .uni-date__x-input {
	font-size: 12px;
}

/* 确保筛选内容区域不限制弹出层 - 只在展开时生效 */
.filter-content.data-v-4a0277e6 {
	position: relative !important;
	padding: 2px 15px;
}

/* 去掉选项数据外层长方形边框 */
.data-v-4a0277e6 .uni-data-checkbox {
	border: none !important;
	background: transparent !important;
}
.data-v-4a0277e6 .checklist-group {
	border: none !important;
	background: transparent !important;
	box-shadow: none !important;
}
.data-v-4a0277e6 .checklist-box {
	border: none !important;
	background: transparent !important;
	box-shadow: none !important;
}
.data-v-4a0277e6 .checklist-content {
	border: none !important;
	background: transparent !important;
}

/* 保留单选框圆形边框 */
.data-v-4a0277e6 .radio__inner {
	border: 1px solid #d9d9d9 !important;
}
.data-v-4a0277e6 .radio__inner.radio--checked {
	border-color: #007aff !important;
}

/* 按钮模式下的样式优化 */
.data-v-4a0277e6 .uni-data-checklist .checklist-group .checklist-box.is--button {
	margin-right: 10rpx !important;
	margin-left: 8rpx !important;
	padding: 8rpx 16rpx !important;
	transition: all 0.2s ease !important;
}
.data-v-4a0277e6 .uni-data-checklist .checklist-group .checklist-box.is--button .checklist-text {
	color: #666 !important;
	margin: 0 !important;
	font-size: 12px !important;
	margin-left: 8rpx !important;
}

/* 按钮模式下的选中状态样式 */
.data-v-4a0277e6 .uni-data-checklist .checklist-group .checklist-box.is--button.is-checked {
	border-color: #2979ff !important;
}

/* 日期选择器弹出层样式 - 相对定位，在网点日期下方显示 */
.data-v-4a0277e6 .uni-datetime-picker-popup,.data-v-4a0277e6 .uni-popper,.data-v-4a0277e6 .uni-datetime-picker-view,.data-v-4a0277e6 .uni-datetime-picker__container,.data-v-4a0277e6 .uni-datetime-picker-popup-view {
	z-index: 999 !important;
	position: absolute !important;
	top: 100% !important;
	left: 0 !important;
	right: 0 !important;
}

/* 日期选择器遮罩层 - 透明，不遮挡其他内容 */
.data-v-4a0277e6 .uni-datetime-picker-mask {
	z-index: 998 !important;
	position: absolute !important;
	background-color: transparent !important;
}

/* 日期选择器展开时的折叠面板样式 - 只设置溢出可见，不设置固定高度 */
.date-picker-expanded.data-v-4a0277e6 {
	overflow: visible !important;
}

/* 日期选择器展开时的折叠面板内容样式 */
.date-picker-expanded.filter-content.data-v-4a0277e6 {
	overflow: visible !important;
	position: relative !important;
}

/* 日期选择器展开时的折叠面板项样式 */
.data-v-4a0277e6 .date-picker-expanded .uni-collapse-item__content {
	overflow: visible !important;
}
.data-v-4a0277e6 .date-picker-expanded .uni-collapse-item__wrap {
	overflow: visible !important;
}

/* 确保日期选择器在小程序中的弹出层样式 - 相对定位在网点日期下方 */
.data-v-4a0277e6 .uni-calendar--fixed,.data-v-4a0277e6 .uni-calendar,.data-v-4a0277e6 .uni-date-single--x,.data-v-4a0277e6 .uni-date-range--x {
	z-index: 999 !important;
	position: absolute !important;
	top: 100% !important;
	left: 0 !important;
	right: 0 !important;
	-webkit-transform: none !important;
	        transform: none !important;
}

/* 小程序日历组件特殊处理 */
.data-v-4a0277e6 .uni-calendar--fixed {
	position: absolute !important;
	bottom: auto !important;
	top: 100% !important;
	-webkit-transform: translateY(0) !important;
	        transform: translateY(0) !important;
}

