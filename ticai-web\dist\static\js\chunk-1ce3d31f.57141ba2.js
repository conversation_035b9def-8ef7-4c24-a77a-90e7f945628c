(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ce3d31f"],{"00cb":function(e,t,o){},"337f":function(e,t,o){},"85f1":function(e,t,o){},"9ed6":function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"login-page"},[o("div",{staticClass:"login-container"},[o("el-form",{ref:"loginData",staticClass:"login-form",attrs:{model:e.loginData,rules:e.loginRules,"auto-complete":"on","label-position":"left"}},[o("div",{staticClass:"title-container"},[o("h3",{staticClass:"title"},[e._v(e._s(e.appName))])]),o("el-form-item",{attrs:{prop:"account"}},[o("span",{staticClass:"svg-container"},[o("svg-icon",{attrs:{"icon-class":"user"}})],1),o("el-input",{ref:"account",attrs:{placeholder:"请输入用户名",name:"account",type:"text",tabindex:"1","auto-complete":"off"},model:{value:e.loginData.account,callback:function(t){e.$set(e.loginData,"account",t)},expression:"loginData.account"}})],1),o("el-form-item",{attrs:{prop:"password"}},[o("span",{staticClass:"svg-container"},[o("svg-icon",{attrs:{"icon-class":"password"}})],1),o("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:"请输入密码",name:"password",tabindex:"2","auto-complete":"off"},model:{value:e.loginData.password,callback:function(t){e.$set(e.loginData,"password",t)},expression:"loginData.password"}}),o("span",{staticClass:"show-pwd",on:{click:e.showPwd}},[o("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1),o("el-form-item",{attrs:{prop:"verifyCode"}},[o("span",{staticClass:"svg-container"},[o("svg-icon",{attrs:{"icon-class":"verifycode"}})],1),o("el-input",{ref:"verifyCode",attrs:{placeholder:"请输入验证码",name:"verifyCode",tabindex:"3","auto-complete":"off"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginData.verifyCode,callback:function(t){e.$set(e.loginData,"verifyCode",t)},expression:"loginData.verifyCode"}}),o("span",{staticClass:"verify-code"},[o("img",{attrs:{src:e.verifyCodeImg,alt:""},on:{click:function(t){return t.preventDefault(),e.changeVerifyCode(t)}}})])],1),o("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v("登录")])],1)],1),o("login-footer"),o("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"修改密码",visible:e.pswdVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.pswdVisible=t}}},[o("el-form",{ref:"pswdform",attrs:{model:e.pswdform,rules:e.pswdRules,"label-width":"100px"}},[o("el-form-item",{attrs:{label:"原密码：",prop:"oldPassword"}},[o("el-input",{attrs:{type:"password",placeholder:"请输入原密码",maxlength:"32",autocomplete:"off"},model:{value:e.pswdform.oldPassword,callback:function(t){e.$set(e.pswdform,"oldPassword",t)},expression:"pswdform.oldPassword"}})],1),o("el-form-item",{attrs:{label:"新密码：",prop:"newPassword"}},[o("el-input",{attrs:{type:"password",placeholder:"请输入新密码",maxlength:"32",autocomplete:"off"},model:{value:e.pswdform.newPassword,callback:function(t){e.$set(e.pswdform,"newPassword",t)},expression:"pswdform.newPassword"}}),o("div",[e._v("规则：数字、小写字母、大写字母、特殊符号至少3种，不少于8位")])],1),o("el-form-item",{attrs:{label:"再次密码：",prop:"typePassword"}},[o("el-input",{attrs:{type:"password",placeholder:"请再次输入新密码",maxlength:"32",autocomplete:"off"},model:{value:e.pswdform.typePassword,callback:function(t){e.$set(e.pswdform,"typePassword",t)},expression:"pswdform.typePassword"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.pswdVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},s=[],r=(o("ac1f"),o("00b4"),o("83d6")),n=o.n(r),i=o("ed08"),l=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},d=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("footer",{staticClass:"login-footer"},[o("div",{staticClass:"footer-content"},[o("div",[o("a",{staticClass:"beian-link",attrs:{href:"https://beian.miit.gov.cn/",target:"_blank"}},[e._v(" 琼ICP备16002127号-6")])])])])}],c={name:"LoginFooter"},p=c,f=(o("f25d"),o("2877")),u=Object(f["a"])(p,l,d,!1,null,"6feeddc8",null),w=u.exports,g="";function m(){return"/api/verifyCode/"+(g=Object(i["k"])())}var v={name:"Login",components:{LoginFooter:w},data:function(){var e=this,t=function(e,t,o){!t||t.length<2?o(new Error("请输入有效的用户名")):o()},o=function(e,t,o){!t||t.length<6?o(new Error("密码不能小于6位")):o()},a=function(e,t,o){!t||t.length<4?o(new Error("请输入验证码")):o()},s=function(e,t,o){if(!t||t.length<8)o(new Error("密码不能少于8位"));else{var a=0;/\d/.test(t)&&a++,/[A-Z]/.test(t)&&a++,/[a-z]/.test(t)&&a++,/\+|\-|~|\!|@|#|\$|\%|\^|&|\*|,|\.|\;|`|\:/.test(t)&&a++,a<3?o(new Error("您输入的密码不符合规则")):o()}},r=function(t,o,a){o?o===e.pswdform.newPassword?a():a(new Error("两次输入的密码不一致")):a(new Error("请再次输入新密码"))};return{appName:n.a.title,verifyCodeImg:"",loginData:{account:"",password:"",verifyId:"",verifyCode:""},loginRules:{account:[{required:!0,trigger:"blur",validator:t}],password:[{required:!0,trigger:"blur",validator:o}],verifyCode:[{required:!0,trigger:"blur",validator:a}]},loading:!1,passwordType:"password",redirect:void 0,pswdVisible:!1,pswdform:{},pswdRules:{oldPassword:[{required:!0,validator:o,trigger:"blur"}],newPassword:[{required:!0,validator:s,trigger:"blur"}],typePassword:[{required:!0,validator:r,trigger:"blur"}]}}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.changeVerifyCode()},methods:{changeVerifyCode:function(){this.verifyCodeImg=m()},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.password.focus()}))},handleLogin:function(){var e=this;this.$refs.loginData.validate((function(t){return t?(e.loginData.verifyId=g,e.loading=!0,e.$store.dispatch("user/login",e.loginData).then((function(t){e.loading=!1,t.code>0&&(2===t.code?e.pswdVisible=!0:e.$router.push({path:e.redirect||"/"}))})).catch((function(){e.loading=!1,e.changeVerifyCode()})),!0):t}))},save:function(){var e=this;this.$refs.pswdform.validate((function(t){t&&e.$http({url:"/updatePassword",data:e.pswdform}).then((function(t){t.code>0&&(e.pswdVisible=!1,e.$router.push({path:e.redirect||"/"}))})).catch((function(t){return e.$alert("系统出错:"+t)}))}))}}},h=v,y=(o("dead"),o("b153"),Object(f["a"])(h,a,s,!1,null,"f19b7cfc",null));t["default"]=y.exports},b153:function(e,t,o){"use strict";o("85f1")},dead:function(e,t,o){"use strict";o("00cb")},f25d:function(e,t,o){"use strict";o("337f")}}]);