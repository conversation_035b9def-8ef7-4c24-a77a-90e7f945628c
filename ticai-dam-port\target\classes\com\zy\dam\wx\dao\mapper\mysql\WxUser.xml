<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.wx.dao.WxUserDAO">

    <sql id="meta">
			a.ID_
			,a.USER_
			,a.MA_OPEN_ID_
			,a.MA_UNION_ID_
			,a.WX_OPEN_ID_
			,a.NICK_
			,a.AVATAR_
			,a.MOBILE_
			,a.GENDER_
			,a.LOGIN_TIME_
			,a.LOGIN_IP_
			,a.LAST_TIME_
			,a.LAST_IP_
			,a.SMARK_
			,a.STIME_
			,a.STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.MTIME_
			,a.MUSER_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.wx.orm.WxUser">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into WX_USER(ID_,USER_,MA_OPEN_ID_,MA_UNION_ID_,WX_OPEN_ID_,NICK_,AVATAR_,MOBILE_,GENDER_,LOGIN_TIME_,LOGIN_IP_,LAST_TIME_,LAST_IP_,SMARK_,STIME_,STATUS_,CTIME_,CUSER_,MTIME_,MUSER_)
        values(#{id},#{user},#{maOpenId},#{maUnionId},#{wxOpenId},#{nick},#{avatar},#{mobile},#{gender},now(),#{loginIp},now(),#{lastIp},#{smark},#{stime},'0',now(),#{cuser},now(),#{muser})
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.wx.orm.WxUser">
		update WX_USER
		set MA_OPEN_ID_=#{maOpenId},MA_UNION_ID_=#{maUnionId},WX_OPEN_ID_=#{wxOpenId},NICK_=#{nick},AVATAR_=#{avatar},MOBILE_=#{mobile},GENDER_=#{gender},LOGIN_TIME_=#{loginTime},LOGIN_IP_=#{loginIp},LAST_TIME_=#{lastTime},LAST_IP_=#{lastIp},SMARK_=#{smark},STIME_=#{stime},STATUS_=#{status},CTIME_=#{ctime},CUSER_=#{cuser},MTIME_=#{mtime},MUSER_=#{muser}
		where ID_=#{id}
	</update>

    <update id="updateLogin">
        update WX_USER set
        NICK_=#{nick},AVATAR_=#{avatar},LOGIN_TIME_=now(),LOGIN_IP_=#{loginIp},LAST_TIME_=LOGIN_TIME_,LAST_IP_=LOGIN_IP_
        where ID_=#{id}
    </update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.wx.vo.UserVo">
        select
        <include refid="meta"/>
        ,b.NAME_ name_,b.DEPT_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.DEPT_) dept_name
        from WX_USER a left join SYS_USER b on a.USER_=b.ID_
        where a.STATUS_!='9'
        <if test="name != null">and b.NAME_ like concat('%',#{name},'%')</if>
        <if test="nick != null">and a.NICK_ like concat('%',#{nick},'%')</if>
        <if test="mobile != null">and a.MOBILE_ like concat('%',#{mobile},'%')</if>
        order by a.CTIME_ desc
    </select>

    <!-- 获取唯一的农业-微信用户数据 -->
    <select id="findOne" resultType="com.zy.dam.wx.orm.WxUser">
        select
        <include refid="meta"/>
        from WX_USER a where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update WX_USER set STATUS_='9' where ID_=#{0}
	</update>

    <select id="findVo" resultType="com.zy.dam.wx.vo.UserVo">
        select
        <include refid="meta"/>
        ,b.NAME_ name_,b.DEPT_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=b.DEPT_) dept_name
        from WX_USER a left join SYS_USER b on a.USER_=b.ID_
        where a.ID_=#{0}
    </select>

    <select id="findByOpenid" resultType="com.zy.dam.wx.orm.WxUser">
        select
        <include refid="meta"/>
        from WX_USER a
        where (a.MA_OPEN_ID_=#{0} or a.MA_OPEN_ID_=#{1})
    </select>

    <select id="findSysUserId" resultType="String">
        select ID_ from SYS_USER where FLAG_='1' and ACCOUNT_=#{0} and PASSWORD_=#{1}
    </select>

    <update id="bindUser">
        update WX_USER set USER_=#{1} where ID_=#{0}
    </update>

</mapper>
