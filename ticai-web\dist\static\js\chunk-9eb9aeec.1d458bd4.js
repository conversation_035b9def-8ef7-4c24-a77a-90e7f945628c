(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9eb9aeec"],{"0812":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"layout-lr"},[a("div",{staticClass:"left"},[a("div",{staticClass:"head"},[t._v("业务类型")]),a("div",{staticClass:"body"},[a("ul",t._l(t.typeList,(function(e){return a("li",{key:e.value,class:{act:t.activeItem&&t.activeItem.value==e.value},on:{click:function(a){return t.showType(e)}}},[t._v(t._s(e.text))])})),0)])]),a("div",{staticClass:"center"},[a("el-descriptions",{staticClass:"descr-1",attrs:{column:1,border:""}},[a("template",{slot:"extra"},[a("div",{staticClass:"button-bar"},[t.activeItem.value&&!t.data.type?a("span",[t._v("该类型还未设置编码，请点击设置")]):t._e(),t.activeItem.value?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-s-tools"},on:{click:t.setNo}},[t._v("设置")]):t._e()],1)]),a("el-descriptions-item",{attrs:{label:"编码类型"}},[t._v(t._s(t.data.type))]),a("el-descriptions-item",{attrs:{label:"编码前缀"}},[t._v(t._s(t.data.prefix))]),a("el-descriptions-item",{attrs:{label:"编码后缀"}},[t._v(t._s(t.data.suffix))]),a("el-descriptions-item",{attrs:{label:"日期格式"}},[t._v(t._s(t.data.dateFormat))]),a("el-descriptions-item",{attrs:{label:"流水长度"}},[t._v(t._s(t.data.len))])],2)],1),a("no-detail",{ref:"detail",on:{success:t.loadNo}})],1)},s=[],i=(a("a15b"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("4360"));function n(t){return i["a"].getters.dict[t]}var o=a("5e10"),r={components:{NoDetail:o["a"]},data:function(){return{typeList:[],activeItem:{},data:{}}},created:function(){this.loadType()},methods:{loadType:function(){this.typeList=n("NO_TYPE")},showType:function(t){this.activeItem=t,this.loadNo()},setNo:function(){this.$refs.detail.show(this.activeItem.value)},loadNo:function(){var t=this;this.$http("/sys/no/get/"+this.activeItem.value).then((function(e){e.code>0&&(t.data=e.data||{})}))}}},c=r,d=(a("94bd"),a("2877")),u=Object(d["a"])(c,l,s,!1,null,null,null);e["default"]=u.exports},"5e10":function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"编码规则",width:"500px",visible:t.noVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.noVisible=e}}},[a("el-form",{ref:"noform",attrs:{"label-width":"120px",model:t.nodata,rules:t.noRules}},[t.tag&&t.tag.name?a("el-form-item",{attrs:{label:"业务名称："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"300px"},attrs:{value:t.tag.name,readonly:""}})],1):t._e(),a("el-form-item",{attrs:{label:"编码前缀："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{maxlength:"6",autocomplete:"off"},model:{value:t.nodata.prefix,callback:function(e){t.$set(t.nodata,"prefix",e)},expression:"nodata.prefix"}})],1),a("el-form-item",{attrs:{label:"编码后缀："}},[a("el-input",{staticStyle:{width:"300px"},attrs:{maxlength:"6",autocomplete:"off"},model:{value:t.nodata.suffix,callback:function(e){t.$set(t.nodata,"suffix",e)},expression:"nodata.suffix"}})],1),a("el-form-item",{attrs:{label:"日期格式："}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择"},model:{value:t.nodata.dateFormat,callback:function(e){t.$set(t.nodata,"dateFormat",e)},expression:"nodata.dateFormat"}},t._l(t.dateFormatList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"流水号长度：",prop:"len"}},[a("el-input-number",{staticStyle:{width:"300px"},attrs:{maxlength:"1",autocomplete:"off",min:3,max:9},model:{value:t.nodata.len,callback:function(e){t.$set(t.nodata,"len",e)},expression:"nodata.len"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.removeable&&t.existed?a("el-button",{attrs:{type:"danger"},on:{click:t.remove}},[t._v("取消编码规则")]):t._e(),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)],1)},s=[],i={data:function(){return{dateFormatList:[{value:"",label:"不需要"},{value:"yyyyMMdd",label:"年月日(yyyyMMdd)"},{value:"yyMMdd",label:"短年月日(yyMMdd)"},{value:"yyyyMM",label:"年月(yyyyMM)"},{value:"yyMM",label:"短年月(yyMM)"},{value:"yyyy",label:"年(yyyy)"},{value:"yy",label:"短年(yy)"}],noVisible:!1,nodata:{code:null,prefix:null,dateFormat:null,len:5,suffix:null},noRules:{len:[{required:!0,message:"请输入流水号",trigger:"blur"}]},type:null,tag:null,removeable:!1,existed:!1}},methods:{show:function(t,e,a){var l=this;this.type=t,this.tag=e,this.removeable=a,this.existed=!1,this.$http("/sys/no/get/"+t).then((function(e){e.code>0&&(l.existed=null!=e.data,l.nodata=e.data&&"1"===e.data.status?e.data:{dataFormat:"",len:5},l.nodata.type=t,l.noVisible=!0)}))},remove:function(){var t=this;this.$confirm("是否要取消编码规则吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http("/sys/no/disable/"+t.type).then((function(e){e.code>0&&(t.noVisible=!1,t.$emit("remove",t.tag))}))})).catch((function(){}))},save:function(){var t=this;this.$refs.noform.validate((function(e){e&&t.$http({url:"/sys/no/save",data:t.nodata}).then((function(e){e.code>0&&(t.noVisible=!1,t.$message.success("保存编码规则成功"),t.$emit("success",t.tag))}))}))}}},n=i,o=a("2877"),r=Object(o["a"])(n,l,s,!1,null,null,null);e["a"]=r.exports},"94bd":function(t,e,a){"use strict";a("dde0")},dde0:function(t,e,a){}}]);