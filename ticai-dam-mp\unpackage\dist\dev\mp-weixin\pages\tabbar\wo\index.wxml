<view class="container"><view class="tab-host"><uni-grid vue-id="2bccbd3a-1" column="{{2}}" showBorder="{{false}}" square="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-grid-item vue-id="{{('2bccbd3a-2')+','+('2bccbd3a-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTab',[0]]]]]}}" class="{{['tool-item',(index==0)?'act':'']}}" bindtap="__e"><uni-badge vue-id="{{('2bccbd3a-3')+','+('2bccbd3a-2')}}" size="small" text="{{billCount.doing}}" absolute="rightTop" type="error" bind:__l="__l" vue-slots="{{['default']}}"><uni-icons vue-id="{{('2bccbd3a-4')+','+('2bccbd3a-3')}}" custom-prefix="zi" type="zi-task-doing" size="30" color="{{tabColor0}}" bind:__l="__l"></uni-icons></uni-badge><text class="icon-text">未完成</text></view></uni-grid-item><uni-grid-item vue-id="{{('2bccbd3a-5')+','+('2bccbd3a-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTab',[2]]]]]}}" class="{{['tool-item',(index==2)?'act':'']}}" bindtap="__e"><uni-badge vue-id="{{('2bccbd3a-6')+','+('2bccbd3a-5')}}" size="small" text="{{billCount.close}}" absolute="rightTop" type="error" bind:__l="__l" vue-slots="{{['default']}}"><uni-icons vue-id="{{('2bccbd3a-7')+','+('2bccbd3a-6')}}" custom-prefix="zi" type="zi-task-warning" size="30" color="{{tabColor2}}" bind:__l="__l"></uni-icons></uni-badge><text class="icon-text">已完成</text></view></uni-grid-item></uni-grid></view><view class="tab-content"><block wx:if="{{index==0}}"><view class="list-block"><block wx:if="{{$root.g0}}"><view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">工单号：</view><view class="text">{{item.$orig.no}}</view><view class="time">{{item.m0}}</view></view><view class="row"><view class="label">网点用户：</view><view class="text">{{item.$orig.locationName}}</view></view><view class="row"><view class="label">网点地址：</view><view class="text">{{item.$orig.locationAddress}}</view></view><view class="row"><view class="label">反映故障：</view><view class="text">{{item.$orig.faultReport}}</view></view><view class="row"><view class="label">现场检查：</view><view class="text">{{item.$orig.faultCheck}}</view></view><view class="row"><view class="button-sp-area" style="float:right;"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['solveBill',['$event']]]]]}}" bindtap="__e">完成</button><button style="margin-left:40rpx;" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['viewBill',['$event']]]]]}}" bindtap="__e">详情</button></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="2bccbd3a-8" bind:__l="__l" vue-slots="{{['default']}}">沒有未完成工单</uni-text></block></view></block><block wx:else><block wx:if="{{index==1}}"><view class="list-block"><block wx:if="{{$root.g1}}"><view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i1__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">工单号：</view><view class="text">{{item.$orig.no}}</view><view class="time">{{item.m1}}</view></view><view class="row"><view class="label">网点用户：</view><view class="text">{{item.$orig.locationName}}</view></view><view class="row"><view class="label">网点地址：</view><view class="text">{{item.$orig.locationAddress}}</view></view><view class="row"><view class="label">反映故障：</view><view class="text">{{item.$orig.faultReport}}</view></view><view class="row"><view class="label">现场检查：</view><view class="text">{{item.$orig.faultCheck}}</view></view><view class="row"><view class="button-sp-area" style="float:right;"><button type="primary" size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['reviewBill',['$event']]]]]}}" bindtap="__e">评价</button><button size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['detailBill',['$event']]]]]}}" bindtap="__e">详情</button></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="2bccbd3a-9" bind:__l="__l" vue-slots="{{['default']}}">没有已完成工单</uni-text></block></view></block><block wx:else><block wx:if="{{index==2}}"><view class="list-block"><block wx:if="{{$root.g2}}"><view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="__i2__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">工单号：</view><view class="text">{{item.$orig.no}}</view><view class="time">{{item.m2}}</view></view><view class="row"><view class="label">网点用户：</view><view class="text">{{item.$orig.locationName}}</view></view><view class="row"><view class="label">网点地址：</view><view class="text">{{item.$orig.locationAddress}}</view></view><view class="row"><view class="label">反映故障：</view><view class="text">{{item.$orig.faultReport}}</view></view><view class="row"><view class="label">现场检查：</view><view class="text">{{item.$orig.faultCheck}}</view></view><view class="row"><view class="label">服务评价：</view><view class="text">{{item.m3}}</view><view class="button-sp-area" style="float:right;"><button size="mini" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['detailBill',['$event']]]]]}}" bindtap="__e">详情</button></view></view></view></block></view></block><block wx:else><uni-text class="zy-empty" vue-id="2bccbd3a-10" bind:__l="__l" vue-slots="{{['default']}}">没有已完成工单</uni-text></block></view></block></block></block></view><watermark vue-id="2bccbd3a-11" bind:__l="__l"></watermark></view>