<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetTraceRecordDAO">
    <!-- 插入资产操作历史记录 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetTrace">
        INSERT INTO AM_ASSET_TRACE (
            ID_,
            ASSET_ID_,
            OPERATION_TYPE_,
            OPERATION_TIME_,
            OPERATOR_,
            OPERATOR_NAME_,
            LOCATION_ID_,
            LOCATION_CODE_,
            LOCATION_NAME_,
            MEMO_,
            REF_ID_,
            REF_NO_,
            CUSER_,
            CTIME_,
            MUSER_,
            MTIME_,
            FLAG_
        ) VALUES (
            #{id},
            #{assetId},
            #{operationType},
            #{operationTime},
            #{operator},
            #{operatorName},
            #{locationId},
            #{locationCode},
            #{locationName},
            #{memo},
            #{refId},
            #{refNo},
            #{cuser},
            #{operationTime},
            #{muser},
            #{operationTime},
            #{flag}
        )
    </insert>
</mapper>
