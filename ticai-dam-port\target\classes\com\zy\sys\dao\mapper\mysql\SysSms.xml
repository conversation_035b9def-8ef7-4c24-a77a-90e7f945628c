<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysSmsDAO">

    <insert id="insert" parameterType="com.zy.sys.orm.SysSms">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_SMS(ID_,MOBILE_,CONTENT_,TYPE_,BID_,TIME_,USER_)
        values(#{id},#{mobile},#{content},#{type},#{bid},now(),#{user})
    </insert>

</mapper>
