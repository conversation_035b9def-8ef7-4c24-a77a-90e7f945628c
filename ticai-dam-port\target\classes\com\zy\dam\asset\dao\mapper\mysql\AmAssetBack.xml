<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetBackDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.USER_
			,a.TIME_
			,a.DEPT_
            ,a.REGION_
			,a.MEMO_
			,a.REF_
			,a.STATUS_
			,a.CDEPT_
			,a.CUSER_
			,a.CTIME_
			,a.CHECK_USER_
			,a.CHECK_TIME_
			,a.CHECK_MEMO_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.asset.orm.AmAssetBack">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET_BACK(ID_,NO_,USER_,TIME_,DEPT_,REGION_,MEMO_,REF_,STATUS_,CDEPT_,CUSER_,CTIME_,CHECK_USER_,CHECK_TIME_,CHECK_MEMO_)
        values(#{id},#{no},#{user},timestamp(date_add(curdate(), interval - 0
        day)),#{dept},#{region},#{memo},#{ref},#{status},#{cdept},#{cuser},now(),#{checkUser},#{checkTime},#{checkMemo})
    </insert>

    <!-- 更新数据 -->
    <update id="updateCheck" parameterType="com.zy.dam.asset.orm.AmAssetBack">
		update AM_ASSET_BACK
		set STATUS_=#{status},CHECK_USER_=#{checkUser},CHECK_TIME_=#{checkTime},CHECK_MEMO_=#{checkMemo}
		where ID_=#{id}
	</update>

    <!-- 获取唯一的资管-资产领用退库数据 -->
    <select id="findOne" resultType="com.zy.dam.asset.orm.AmAssetBack">
        select
        <include refid="meta"/>
        from AM_ASSET_BACK a where a.ID_=#{0}
    </select>

    <insert id="insertDetail">
		insert into AM_ASSET_BACK_DETAIL(BILL_, ASSET_, ORD_, DEPT_, REGION_, LOCATION_, USE_DEPT_, USE_USER_, ASSET_STATUS_,LAST_LNG_,LAST_LAT_,LAST_LOC_ADDR_)
		values(#{id}, #{asset}, #{ord}, #{dept}, #{region}, #{location}, #{useDept}, #{useUser}, #{assetStatus}, #{lastLng}, #{lastLat}, #{lastLocAddr})
	</insert>

    <!-- 更新资产退库，置为空置 -->
    <update id="updateBack">
        update AM_ASSET a,AM_ASSET_BACK_DETAIL b, AM_ASSET_BACK c
        set a.STATUS_='1',a.USE_DEPT_=null,a.USE_USER_=null,a.REGION_=c.REGION_,a.LOCATION_=NULL,a.LNG_=NULL,a.LAT_=NULL,a.LOC_ADDR_=NULL
        where a.ID_=b.ASSET_ and b.BILL_=c.ID_ and c.ID_=#{0}
    </update>

    <update id="updateSnStatus">
        update AM_ASSET_SN set FLAG_='9' where ASSET_=#{0}
    </update>

</mapper>
