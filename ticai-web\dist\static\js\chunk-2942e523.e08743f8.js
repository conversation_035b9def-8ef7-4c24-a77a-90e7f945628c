(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2942e523"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},4736:function(e,t,a){},"4f8b":function(e,t,a){"use strict";a("4736")},"6ecd":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},r=[],s=a("53ca"),l=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),i={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var o={pageNumber:1},r=Object(s["a"])(e);"undefined"===r?o.pageNumber=1:"number"===r?o.pageNumber=e:"object"===r?(this.params=e,"number"===typeof t&&(o.pageNumber=t),"boolean"===typeof t&&this.empty()):o.pageNumber=e.pageNumber,this.pi=o.pageNumber,this.paging&&(this.params.pageNumber=o.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(l["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],o=0;o<t.length;o++)t[o][e]&&a.push(t[o][e]);return a}}},n=i,c=(a("b2d4"),a("2877")),u=Object(c["a"])(n,o,r,!1,null,"bdcc19d8",null);t["a"]=u.exports},"841c":function(e,t,a){"use strict";var o=a("d784"),r=a("825a"),s=a("1d80"),l=a("129f"),i=a("14c3");o("search",1,(function(e,t,a){return[function(t){var a=s(this),o=void 0==t?void 0:t[e];return void 0!==o?o.call(t,a):new RegExp(t)[e](String(a))},function(e){var o=a(t,e,this);if(o.done)return o.value;var s=r(e),n=String(this),c=s.lastIndex;l(c,0)||(s.lastIndex=0);var u=i(s,n);return l(s.lastIndex,c)||(s.lastIndex=c),null===u?-1:u.index}]}))},ac65:function(e,t,a){},b2d4:function(e,t,a){"use strict";a("ac65")},c2a2:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter"},[a("el-form",{attrs:{model:e.qform,inline:""},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("div",{staticClass:"filter-item"},[a("label",[e._v("帐号：")]),a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.account,callback:function(t){e.$set(e.qform,"account",t)},expression:"qform.account"}})],1)]),a("div",{staticClass:"filter-item"},[a("label",[e._v("姓名：")]),a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.name,callback:function(t){e.$set(e.qform,"name",t)},expression:"qform.name"}})],1)]),a("div",{staticClass:"filter-item"},[a("label",[e._v("所属单位：")]),a("div",[a("tree-box",{attrs:{data:e.deptTree,"expand-all":"",clearable:""},model:{value:e.qform.dept,callback:function(t){e.$set(e.qform,"dept",t)},expression:"qform.dept"}})],1)]),a("div",{staticClass:"filter-item"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)])],1),a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{path:"/sys/user/page",size:"mini",stripe:"",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"帐号",prop:"account",width:"120"}}),a("el-table-column",{attrs:{label:"姓名",prop:"name",width:"150"}}),a("el-table-column",{attrs:{label:"性别",prop:"gender",width:"50",align:"center",formatter:e.colGender}}),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"60",align:"center",formatter:e.fnStatus}}),a("el-table-column",{attrs:{label:"所属机构",prop:"deptName",width:"100"}}),a("el-table-column",{attrs:{label:"用户类型",prop:"type",width:"70",align:"center",formatter:e.fnType}}),a("el-table-column",{attrs:{label:"角色",prop:"roleNames"}}),a("el-table-column",{attrs:{label:"操作",width:"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["admin"!=t.row.id?a("div",[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.assignRole(t.row)}}},[e._v("角色")]),a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.remove(t.row)}}},[e._v("删除")]),a("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.resetPassword(t.row)}}},[e._v("重置密码")])],1):e._e()]}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"用户信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"所属机构：",prop:"dept"}},[/2|3/.test(e.form.type)?a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.form.deptName,callback:function(t){e.$set(e.form,"deptName",t)},expression:"form.deptName"}}):a("tree-box",{attrs:{data:e.deptTree,"expand-all":"",clearable:!1},model:{value:e.form.dept,callback:function(t){e.$set(e.form,"dept",t)},expression:"form.dept"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名：",prop:"name"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"性别：",prop:"gender"}},[a("el-radio-group",{model:{value:e.form.gender,callback:function(t){e.$set(e.form,"gender",t)},expression:"form.gender"}},[a("el-radio",{attrs:{label:"1"}},[e._v("男")]),a("el-radio",{attrs:{label:"2"}},[e._v("女")])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"帐号：",prop:"account"}},[a("el-input",{attrs:{readonly:"update"==e.opt,maxlength:"32",autocomplete:"off"},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1)],1),a("el-col",{attrs:{span:12}},[a("span",{staticClass:"form-memo"},[e._v(e._s("add"==e.opt?"初始化密码为：Hntc@1234":"帐号不能修改"))])]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编码：",prop:"no"}},[a("el-input",{attrs:{maxlength:"12",autocomplete:"off"},model:{value:e.form.no,callback:function(t){e.$set(e.form,"no",t)},expression:"form.no"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系方式：",prop:"phone"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),a("role",{ref:"role",on:{success:e.search}}),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"重置密码",visible:e.passwordVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.passwordVisible=t}}},[a("el-form",{ref:"passwordform",attrs:{model:e.passwordform,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"姓名：",prop:"name"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.passwordform.name,callback:function(t){e.$set(e.passwordform,"name",t)},expression:"passwordform.name"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"帐号：",prop:"account"}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.passwordform.account,callback:function(t){e.$set(e.passwordform,"account",t)},expression:"passwordform.account"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"重置密码：",prop:"password"}},[a("el-input",{attrs:{maxlength:"64",autocomplete:"off",placeholder:"不填表示重置为Hntc@1234"},model:{value:e.passwordform.password,callback:function(t){e.$set(e.passwordform,"password",t)},expression:"passwordform.password"}})],1)],1),a("el-col",{attrs:{span:12}},[a("span",{staticClass:"form-memo"},[e._v("密码包含：大写字母、小写字母、数字和特殊符号,长度8~20")])])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.passwordVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.savePassword}},[e._v("确 定")])],1)],1)],1)},r=[],s=(a("ac1f"),a("841c"),a("6ecd")),l=a("ee5a"),i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],ref:"dialog",attrs:{title:"角色分配",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("el-table",{ref:"grid",attrs:{data:e.list,stripe:!0,border:!0,"max-height":e.maxHeight},on:{"row-click":e.handleRowClick}},[a("el-table-column",{attrs:{type:"selection",width:"50"}}),a("el-table-column",{attrs:{label:"名称",prop:"name",width:"250"}}),a("el-table-column",{attrs:{label:"说明",prop:"memo"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)],1)],1)},n=[],c=(a("d3b7"),a("0643"),a("4e3e"),a("159b"),{data:function(){return{visible:!1,keyword:null,list:null,user:null}},computed:{maxHeight:function(){return document.documentElement.clientHeight-330}},created:function(){},mounted:function(){},methods:{show:function(e){var t=this;this.visible=!0,this.user=e,null!=this.data?this.selection():this.$http("/sys/role/list").then((function(e){t.list=e,t.selection()})).catch((function(){return t.$message.error("无法加载角色列表")}))},selection:function(){var e=this;this.$http("/sys/user/roleId/"+this.user).then((function(t){var a=t.data;if(a&&a.length){var o=e.$refs.grid;e.list.forEach((function(e){for(var t=0;t<a.length;t++)if(a[t]===e.id)return o.toggleRowSelection(e,!0)}))}})).catch((function(){return e.$message.error("无法加载角色列表")}))},handleRowClick:function(e){this.$refs.grid.toggleRowSelection(e)},close:function(){this.visible=!1},confirm:function(){var e=this,t={key:this.user,values:[]};this.$refs.grid.selection.forEach((function(e){t.values.push(e.id)})),this.$http({url:"/sys/user/assignRole",data:t}).then((function(t){e.visible=!1,e.$message.success("保存成功"),e.$emit("success")})).catch((function(){e.$message.error("无法分配角色")}))}}}),u=c,d=a("2877"),p=Object(d["a"])(u,i,n,!1,null,null,null),f=p.exports,h={1:"在职",5:"离职",8:"禁用"},m={1:"内部用户",2:"外部用户"},b={components:{PageTable:s["a"],TreeBox:l["a"],Role:f},data:function(){return{detailVisible:!1,opt:null,qform:{account:null,name:null,dept:null},deptTree:[],form:{gender:"1"},rules:{dept:[{required:!0,message:"请选择部门"}],account:[{required:!0,message:"请输入帐号",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],type:[{required:!0,message:"请输入用户类型",trigger:"blur"}]},passwordVisible:!1,passwordform:{}}},created:function(){this.loadDeptTree()},mounted:function(){this.search()},methods:{loadDeptTree:function(){var e=this;this.$http("/sys/dept/treeByType/1").then((function(t){e.deptTree=t})).catch((function(){e.$alert("加载机构树出错")}))},fnStatus:function(e,t,a,o){return h[a]||""},fnType:function(e,t,a,o){return m[a]||""},search:function(){"0"===this.qform.dept&&(this.qform.dept=null),this.$refs.grid.search(this.qform)},clearValidate:function(){var e=this;this.$refs.dataform?this.$refs.dataform.clearValidate():this.$nextTick((function(){e.$refs.dataform.clearValidate()}))},add:function(){this.opt="add",this.detailVisible=!0,this.clearValidate(),this.form={gender:"1"}},detail:function(e){this.opt="update",this.detailVisible=!0,this.clearValidate(),this.form=Object.assign({},e)},assignRole:function(e){this.$refs.role.show(e.id)},remove:function(e){var t=this;this.$confirm("此操作将永久删除该用户, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/user/delete/"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},resetPassword:function(e){this.passwordform=e,this.passwordVisible=!0},savePassword:function(){var e=this;this.$http({url:"/sys/user/resetPassword",data:{value:this.passwordform.id,text:this.passwordform.password}}).then((function(t){t.code>0&&(e.$message.success("重置成功"),e.passwordVisible=!1)})).catch((function(){}))},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&(e.form.type||(e.form.type="1"),e.$http({url:"/sys/user/"+e.opt,data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.detailVisible=!1,e.search())})).catch((function(){})))}))}}},g=b,v=Object(d["a"])(g,o,r,!1,null,null,null);t["default"]=v.exports},ee5a:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-popover",{ref:"popover",attrs:{placement:"bottom-start",trigger:"click"},on:{show:e.onShowPopover,hide:e.onHidePopover}},[a("el-tree",{ref:"tree",staticClass:"select-tree",style:"min-width: "+e.treeWidth,attrs:{"highlight-current":"",data:e.data,props:e.props,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"default-expand-all":e.expandAll},on:{"node-click":e.onClickNode}}),a("el-input",{ref:"input",class:{rotate:e.showStatus},style:"width: "+e.width+"px",attrs:{slot:"reference",clearable:e.clearable,"suffix-icon":"el-icon-arrow-down",placeholder:e.placeholder},slot:"reference",model:{value:e.labelModel,callback:function(t){e.labelModel=t},expression:"labelModel"}})],1)},r=[],s=(a("99af"),a("4de4"),a("d3b7"),a("0643"),a("2382"),a("b775")),l={name:"TreeBox",model:{prop:"value",event:"selected"},props:{value:{type:String,default:null},width:{type:String,default:null},expandAll:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},data:{type:Array,default:null},path:{type:String,default:null},params:{type:Object,default:null},placeholder:{type:String,required:!1,default:"请选择"},labelField:{type:String,default:null},props:{type:Object,required:!1,default:function(){return{parent:"pid",value:"id",label:"label",children:"children"}}}},data:function(){return{showStatus:!1,treeWidth:"auto",labelModel:"",valueModel:"0"}},watch:{labelModel:function(e){e||(this.valueModel=""),this.$refs.tree.filter(e)},value:function(e){this.labelModel=this.queryTree(this.data,e)}},created:function(){var e=this;null!=this.path?Object(s["a"])({url:this.path,data:this.params}).then((function(t){e.init(t),e.$emit("loaded",t)})).catch((function(e){console.log(e)})):this.init(this.data)},methods:{init:function(e){var t=this;this.data=e||[],this.labelField&&(this.props.label=this.labelField),this.value&&(this.labelModel=this.queryTree(this.data,this.value)),this.$nextTick((function(){t.treeWidth="".concat((t.width||t.$refs.input.$refs.input.clientWidth)-24,"px")}))},onClickNode:function(e){this.labelModel=e[this.props.label],this.valueModel=e[this.props.value],this.onCloseTree()},onCloseTree:function(){this.$refs.popover.showPopper=!1},onShowPopover:function(){this.showStatus=!0,this.$refs.tree.filter(!1)},onHidePopover:function(){this.showStatus=!1,this.$emit("selected",this.valueModel)},filterNode:function(e,t){return!e||-1!==t[this.props.label].indexOf(e)},queryTree:function(e,t){var a=[];a=a.concat(e);while(a.length){var o=a.shift();if(o[this.props.children]&&(a=a.concat(o[this.props.children])),o[this.props.value]===t)return o[this.props.label]}return""}}},i=l,n=(a("4f8b"),a("2877")),c=Object(n["a"])(i,o,r,!1,null,null,null);t["a"]=c.exports}}]);