(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6150b720"],{4082:function(e,n,t){},"820b":function(e,n,t){"use strict";t("4082")},af8f:function(e,n,t){"use strict";t.r(n);var i=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("div")},o=[],r={name:"DpWo",data:function(){return{}},beforeMount:function(){window.addEventListener("resize",this.resizeHandler)},beforeDestroy:function(){window.removeEventListener("resize",this.resizeHandler)},created:function(){this.resizeHandler()},mounted:function(){},methods:{resizeHandler:function(){this.containerHeight=document.documentElement.clientHeight-67,this.infoHeight=document.documentElement.clientHeight-280}}},c=r,s=(t("820b"),t("2877")),u=Object(s["a"])(c,i,o,!1,null,"40b89663",null);n["default"]=u.exports}}]);