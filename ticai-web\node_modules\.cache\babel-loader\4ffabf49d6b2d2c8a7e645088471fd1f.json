{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js!F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backApply.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backApply.vue", "mtime": 1752649876947}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}