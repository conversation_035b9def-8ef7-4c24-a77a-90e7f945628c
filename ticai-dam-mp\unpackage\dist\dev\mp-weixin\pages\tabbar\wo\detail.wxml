<view class="container"><view class="form-view"><view class="form-view-item"><label class="label">网点用户</label><text class="text">{{dataModel.locationName}}</text></view><view class="form-view-item"><label class="label">网点地址</label><text class="text">{{dataModel.locationAddress}}</text></view><view class="form-view-item"><label class="label">联系人</label><text class="text">{{dataModel.contact}}</text></view><view class="form-view-item"><label class="label">联系电话</label><text class="text">{{dataModel.phone}}</text></view><view class="form-view-item"><label class="label">故障反映</label><text class="text">{{dataModel.faultReport}}</text></view><view class="form-view-item"><label class="label">现场检查</label><text class="text">{{dataModel.faultCheck}}</text></view><view class="form-view-item"><label class="label">材料费</label><text class="text">{{dataModel.matCost}}</text></view><view class="form-view-item"><label class="label">维修费</label><text class="text">{{dataModel.maiCost}}</text></view><view class="form-view-item"><label class="label">合计金额</label><text class="text">{{dataModel.amount}}</text></view></view><block wx:for="{{dataModel.detailList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><uni-card vue-id="{{'69cb61d4-1-'+index}}" title="{{item.name}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-view"><view class="form-view-item"><label class="label">规格型号</label><text class="text">{{item.spec}}</text></view><view class="form-view-item"><label class="label">序列号</label><text class="text">{{item.sn}}</text></view><view class="form-view-item"><label class="label">故障类型</label><text class="text">{{item.fault}}</text></view><view class="form-view-item"><label class="label">是否保修</label><text class="text">{{item.flag=='1'?'是':'否'}}</text></view><view class="form-view-item"><label class="label">维修情况</label><text class="text">{{item.solve}}</text></view></view></uni-card></block><view class="form-view"><view class="form-view-item"><label class="label">服务评价</label><text class="text">{{$root.m0}}</text></view><view class="form-view-item"><label class="label">服务意见</label><text class="text">{{dataModel.serviceMemo}}</text></view><view class="form-view-item"><label class="label">确认手机</label><text class="text">{{dataModel.confirmMobile}}</text></view></view><uni-file-picker bind:input="__e" vue-id="69cb61d4-2" title="附件" limit="{{6}}" fileMediatype="image" mode="grid" readonly="{{true}}" value="{{imageValue}}" data-event-opts="{{[['^input',[['__set_model',['','imageValue','$event',[]]]]]]}}" bind:__l="__l"></uni-file-picker></view>