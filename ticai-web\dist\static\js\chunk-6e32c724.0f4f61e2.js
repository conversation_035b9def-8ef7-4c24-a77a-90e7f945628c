(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e32c724"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"5ced":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-table",{ref:"grid",attrs:{data:e.list,stripe:!0,border:!0}},[i("el-table-column",{attrs:{type:"index",width:"50"}}),i("el-table-column",{attrs:{label:"横幅图片",width:"500"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("img",{staticStyle:{height:"190px","max-width":"500px"},attrs:{src:e.row.url,alt:""}})]}}])}),i("el-table-column",{attrs:{label:"横幅信息","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("ul",{staticClass:"property"},[i("li",[i("label",[e._v("名称：")]),i("span",[e._v(e._s(t.row.name))])]),i("li",[i("label",[e._v("小程序链接：")]),i("span",[e._v(e._s(t.row.link1))])]),i("li",[i("label",[e._v("小程序链接：")]),i("span",[e._v(e._s(t.row.link2))])])])]}}])}),i("el-table-column",{attrs:{label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{domProps:{innerHTML:e._s(e.statusHtml(t.row.status))}}),i("hr"),i("div",[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(i){return i.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")])],1),"1"!=t.row.status?i("div",{staticStyle:{"margin-top":"10px"}},[i("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(i){return i.stopPropagation(),e.opt(t.row,"enable")}}},[e._v("启用")])],1):e._e(),"5"!=t.row.status?i("div",{staticStyle:{"margin-top":"10px"}},[i("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(i){return i.stopPropagation(),e.opt(t.row,"disable")}}},[e._v("禁用")])],1):e._e(),i("div",{staticStyle:{"margin-top":"10px"}},[i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(i){return i.stopPropagation(),e.remove(t.row,"delete")}}},[e._v("删除")])],1)]}}])},[i("template",{slot:"header"},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-refresh"},on:{click:e.search}},[e._v("刷新")]),i("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)],2)],1),i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"横幅信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[i("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules}},[i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"名称：","label-width":e.formLabelWidth,prop:"name"}},[i("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"排序：","label-width":e.formLabelWidth,prop:"ord"}},[i("el-input-number",{attrs:{min:0,max:9999,autocomplete:"off",controls:!1},model:{value:e.form.ord,callback:function(t){e.$set(e.form,"ord",t)},expression:"form.ord"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"小程序链接：","label-width":e.formLabelWidth}},[i("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.link1,callback:function(t){e.$set(e.form,"link1",t)},expression:"form.link1"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"微信链接：","label-width":e.formLabelWidth}},[i("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.link2,callback:function(t){e.$set(e.form,"link2",t)},expression:"form.link2"}})],1)],1)],1)],1),i("el-card",{attrs:{header:"上传图片，建议16:9"}},[i("upload-image",{attrs:{type:"BANNER_PIC",limit:1},on:{showFile:e.showFile},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),i("el-dialog",{attrs:{title:"图片预览",top:"30px",visible:e.dialogImgVisible},on:{"update:visible":function(t){e.dialogImgVisible=t}}},[i("img",{attrs:{width:"100%",src:e.dialogImgUrl,alt:""}})])],1)},a=[],n=(i("ac1f"),i("841c"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-upload",e._g(e._b({attrs:{action:"","list-type":"picture-card",limit:e.limit,"file-list":e.fileList,"on-exceed":e.handleExceed,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload}},"el-upload",e.$attrs,!1),e.$listeners),[i("i",{staticClass:"el-icon-plus"})])}),s=[],r=(i("a434"),i("b0c0"),i("a9e3"),i("5c96")),o=i("b775"),c={name:"Upload",props:{type:{type:String,default:"GB"},limit:{type:Number,default:null},pixelX:{type:Number,default:null},pixelY:{type:Number,default:null},value:{type:Array,default:function(){return[]}}},data:function(){return{fileList:this.value}},watch:{value:function(e){this.fileList=e}},methods:{handlePreview:function(e){if(e.response&&e.response.data){var t=e.response.data;e={id:t.id,name:t.name,ext:t.ext,url:"/attach"+t.path}}this.$emit("showFile",e)},handleRemove:function(e,t){var i=this;if(e.id){for(var l=0;l<this.fileList.length;l++)if(this.fileList[l].id===e.id){this.fileList.splice(l,1),this.$emit("input",this.fileList);break}Object(o["a"])("/fileRemove/"+e.id).then((function(t){t.code>0&&(r["Message"].success("删除成功"),i.$emit("removeFile",e))}))}},handleExceed:function(e,t){this.$message.warning("当前限制选择 "+this.limit+" 个文件，本次选择了 "+e.length+" 个文件，共选择了 "+(e.length+t.length)+" 个文件")},beforeUpload:function(e){var t=this,i=new FormData;return i.append("file",e,e.name),this.pixelX&&i.set("scaleWidth",this.pixelX),this.pixelY&&i.set("scaleHeight",this.pixelY),Object(o["a"])({url:"/imageUpload/"+this.type,data:i}).then((function(e){var i=e.data;i&&(t.value.push({id:i.id,name:i.name,ext:i.ext,url:"/attach"+i.path}),t.$emit("input",t.fileList))})),!1}}},u=c,d=i("2877"),f=Object(d["a"])(u,n,s,!1,null,null,null),p=f.exports,m={components:{UploadImage:p},data:function(){return{formLabelWidth:"100px",detailVisible:!1,fileList:[],dialogImgUrl:"",dialogImgVisible:!1,list:[],form:{},rules:{name:[{required:!0,message:"请输入横幅名称",trigger:"blur"}],ord:[{required:!0,message:"请输入横幅排序",trigger:"blur"}]}}},created:function(){this.search()},methods:{statusHtml:function(e){switch(e){case"0":return'<span><i class="fa fa-circle"></i> 待发布</span>';case"1":return'<span class="success"><i class="fa fa-circle"></i> 已启用</span>';case"5":return'<span class="danger"><i class="fa fa-circle"></i> 已禁用</span>'}return""},search:function(){var e=this;this.$http("/wx/banner/list").then((function(t){e.list=t}))},add:function(){this.detailVisible=!0,this.form={ord:1},this.fileList=[]},detail:function(e){this.detailVisible=!0,this.form=Object.assign({},e),this.fileList=[{id:e.pic,url:e.url}]},showFile:function(e){this.dialogImgUrl=e.url,this.dialogImgVisible=!0},remove:function(e){var t=this;this.$confirm("此操作将永久删除该横幅, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/wx/banner/opt/delete_"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},opt:function(e,t){var i=this;this.$http({url:"/wx/banner/opt/"+t+"_"+e.id}).then((function(e){e.code>0&&(i.$message.success("删除成功"),i.detailVisible=!1,i.search())}))},save:function(){var e=this;if(null==this.fileList||0===this.fileList.length)return this.$message.warning("请上传图片");this.$refs.dataform.validate((function(t){t&&(e.form.pic=e.fileList[0].id,e.form.url=e.fileList[0].url,e.$http({url:"/wx/banner/save",data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.search()),e.detailVisible=!1})))}))}}},h=m,b=Object(d["a"])(h,l,a,!1,null,null,null);t["default"]=b.exports},"841c":function(e,t,i){"use strict";var l=i("d784"),a=i("825a"),n=i("1d80"),s=i("129f"),r=i("14c3");l("search",1,(function(e,t,i){return[function(t){var i=n(this),l=void 0==t?void 0:t[e];return void 0!==l?l.call(t,i):new RegExp(t)[e](String(i))},function(e){var l=i(t,e,this);if(l.done)return l.value;var n=a(e),o=String(this),c=n.lastIndex;s(c,0)||(n.lastIndex=0);var u=r(n,o);return s(n.lastIndex,c)||(n.lastIndex=c),null===u?-1:u.index}]}))},a434:function(e,t,i){"use strict";var l=i("23e7"),a=i("23cb"),n=i("a691"),s=i("50c4"),r=i("7b0b"),o=i("65f0"),c=i("8418"),u=i("1dde"),d=i("ae40"),f=u("splice"),p=d("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,h=Math.min,b=9007199254740991,g="Maximum allowed length exceeded";l({target:"Array",proto:!0,forced:!f||!p},{splice:function(e,t){var i,l,u,d,f,p,v=r(this),w=s(v.length),x=a(e,w),y=arguments.length;if(0===y?i=l=0:1===y?(i=0,l=w-x):(i=y-2,l=h(m(n(t),0),w-x)),w+i-l>b)throw TypeError(g);for(u=o(v,l),d=0;d<l;d++)f=x+d,f in v&&c(u,d,v[f]);if(u.length=l,i<l){for(d=x;d<w-l;d++)f=d+l,p=d+i,f in v?v[p]=v[f]:delete v[p];for(d=w;d>w-l+i;d--)delete v[d-1]}else if(i>l)for(d=w-l;d>x;d--)f=d+l-1,p=d+i-1,f in v?v[p]=v[f]:delete v[p];for(d=0;d<i;d++)v[d+x]=arguments[d+2];return v.length=w-l+i,u}})}}]);