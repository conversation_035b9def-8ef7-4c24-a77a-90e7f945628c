{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?2574", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?f7be", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?c972", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?fa29", "uni-app:///pages/tabbar/wo/solve.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?3974", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/solve.vue?da03"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "dataModel", "solveDate", "solveTime", "formRules", "faultCheck", "rules", "required", "errorMessage", "take<PERSON><PERSON><PERSON>", "servicePoint", "serviceUser", "confirmMobile", "confirmCode", "imageValue", "fileList", "smsTimeCount", "smsTimer", "onLoad", "onUnload", "clearInterval", "methods", "loadData", "ctx", "that", "res", "name", "extname", "url", "uploadSelect", "e", "uploadDelete", "uploadFile", "filePath", "header", "success", "submit1", "sendSms", "id", "submit"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACuFjzB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QAAAC;QAAAC;MAAA;MACAC;QACAC;UACAC;YACAC;YACAC;UACA;QACA;QACAC;UACAH;YACAC;YACAC;UACA;QACA;QACAE;UACAJ;YACAC;YACAC;UACA;QACA;QACAG;UACAL;YACAC;YACAC;UACA;QACA;QACAI;UACAN;YACAC;YACAC;UACA;QACA;QACAK;UACAP;YACAC;YACAC;UACA;QACA;MACA;MACAM;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACA;UACAC;UACAA;UACA;YACAC;cACAD;gBAAAE;gBAAAC;gBAAAC;cAAA;cACAJ;YACA;UACA;QACA;MACA;QACAD;MACA;IACA;IACAM;MACA;QACA;QACAC;UAAA;QAAA;MACA;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACArC;QACAiC;QACAK;QACAC;UACA;UACA;UACA;QACA;QACAR;QACAS;UACA;UACA;UACA;UACA;UACAnC;UACAwB;QACA;MACA;IACA;IACAY;MACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;MAEA;MACA;QACA;UAAArB;QAAA;QACAS;UACAxB;QACA;QACAuB;UACA;UACAA;QACA;UAAAA;QAAA;MACA;IACA;IACAc;MACA;MACA;;MAEA;MACA;QACAjB;QACA;MACA;MAEA;MACA;MACA;QACAI;QACA;UACAJ;UACAI;QACA;MACA;MACAD;QAAAX;QAAA0B;MAAA;QACA;UACAd;UACAD;QACA;UACAC;UACAJ;UACAG;QACA;MACA;IACA;IACAgB;MACA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;MAEA;MACA;QACA;UAAAxB;QAAA;QACAS;UACAxB;QACA;QACAuB;UACA;UACAA;QACA;UAAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/wo/solve.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/wo/solve.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./solve.vue?vue&type=template&id=580e3e20&\"\nvar renderjs\nimport script from \"./solve.vue?vue&type=script&lang=js&\"\nexport * from \"./solve.vue?vue&type=script&lang=js&\"\nimport style0 from \"./solve.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/wo/solve.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./solve.vue?vue&type=template&id=580e3e20&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms\" */ \"@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item\" */ \"@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-group/uni-group\" */ \"@dcloudio/uni-ui/lib/uni-group/uni-group.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniCard: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-card/uni-card\" */ \"@dcloudio/uni-ui/lib/uni-card/uni-card.vue\"\n      )\n    },\n    uniRate: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate\" */ \"@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue\"\n      )\n    },\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-row/uni-row\" */ \"@dcloudio/uni-ui/lib/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-col/uni-col\" */ \"@dcloudio/uni-ui/lib/uni-col/uni-col.vue\"\n      )\n    },\n    uniFilePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker\" */ \"@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function (evt, item) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.flag = evt.detail.value\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./solve.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./solve.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<uni-forms ref=\"form\" :modelValue=\"dataModel\" :rules=\"formRules\" border label-align=\"right\" label-width=\"80\">\r\n\t\t\t<uni-forms-item label=\"网点用户\" required name=\"locationName\">\r\n\t\t\t\t<uni-group>{{ dataModel.locationName }}</uni-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点地址\" required name=\"locationAddress\">\r\n\t\t\t\t<uni-group>{{ dataModel.locationAddress }}</uni-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系人\" required name=\"contact\">\r\n\t\t\t\t<uni-group>{{ dataModel.contact }}</uni-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系电话\" required name=\"phone\">\r\n\t\t\t\t<uni-group>{{ dataModel.phone }}</uni-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"故障反映\" name=\"faultReport\">\r\n\t\t\t\t<uni-group>{{ dataModel.faultReport }}</uni-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"现场检查\" required name=\"faultCheck\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.faultCheck\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"维修时长\\n(分钟)\" required name=\"takeMinute\">\r\n\t\t\t\t<uni-easyinput type=\"number\" v-model=\"dataModel.takeMinute\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"材料费\" name=\"matCost\">\r\n\t\t\t\t<uni-easyinput type=\"number\" v-model=\"dataModel.matCost\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"维修费\" name=\"maiCost\">\r\n\t\t\t\t<uni-easyinput type=\"number\" v-model=\"dataModel.maiCost\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-card v-for=\"(item, index) in dataModel.detailList\" :key=\"item.id\" :title=\"item.name\">\r\n\t\t\t\t<uni-forms-item label=\"规格型号\">\r\n\t\t\t\t\t<uni-group>{{ item.spec }}</uni-group>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"序列号\">\r\n\t\t\t\t\t<uni-group>{{ item.sn }}</uni-group>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"故障类型\" required name=\"fault\">\r\n\t\t\t\t\t<uni-easyinput type=\"text\" v-model=\"item.fault\" />\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"是否保修\" required>\r\n\t\t\t\t\t<radio-group @change=\"(evt) => { item.flag = evt.detail.value }\">\r\n\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t<radio value=\"1\" checked=\"true\" />是\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t\t<radio value=\"2\" />否\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t</radio-group>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"维修情况\" required name=\"solve\">\r\n\t\t\t\t\t<uni-easyinput type=\"text\" v-model=\"item.solve\" />\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t</uni-card>\r\n\t\t\t<uni-forms-item label=\"服务评价:\" required name=\"servicePoint\">\r\n\t\t\t\t<uni-rate v-model=\"dataModel.servicePoint\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"服务意见:\" name=\"serviceMemo\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.serviceMemo\" placeholder=\"请输入服务意见\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"服务人员:\" required name=\"serviceUser\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.serviceUser\" placeholder=\"请输入服务人员\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"确认手机:\" required name=\"confirmMobile\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.confirmMobile\" placeholder=\"请输入手机号码\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"确认码:\" required name=\"confirmCode\">\r\n\t\t\t\t<uni-row>\r\n\t\t\t\t\t<uni-col :span=\"12\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"text\" size=\"mini\" v-model=\"dataModel.confirmCode\" placeholder=\"请输入确认码\" />\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t\t<uni-col :span=\"12\">\r\n\t\t\t\t\t\t<text v-if=\"smsTimeCount > 0\" class=\"form-text\">{{ smsTimeCount }}秒可重发</text>\r\n\t\t\t\t\t\t<button v-else type=\"primary\" size=\"mini\" plain=\"true\" @click=\"sendSms\">获取确认码</button>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t</uni-row>\r\n\t\t\t</uni-forms-item>\r\n\t\t</uni-forms>\r\n\t\t<uni-file-picker v-model=\"imageValue\" title=\"附件\" :limit=\"6\" fileMediatype=\"image\" mode=\"grid\"\r\n\t\t\t@select=\"uploadSelect\" @delete=\"uploadDelete\" />\r\n\t\t<view style=\"margin-top: 20rpx;\">\r\n\t\t\t<button type=\"primary\" @click=\"submit\">提 交</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport settings from '../../../utils/settings.js'\r\nimport * as ctx from '../../../utils/context.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdataModel: { solveDate: '', solveTime: '' },\r\n\t\t\tformRules: {\r\n\t\t\t\tfaultCheck: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写现场检查故障'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\ttakeMinute: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写维修时长'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tservicePoint: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请选择服务评价'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tserviceUser: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写服务人员'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tconfirmMobile: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写确认手机号'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tconfirmCode: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写确认码'\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageValue: [],\r\n\t\t\tfileList: [],\r\n\t\t\tsmsTimeCount: 0,\r\n\t\t\tsmsTimer: null,\r\n\t\t}\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.loadData(option.id);\r\n\t},\r\n\tonUnload() {\r\n\t\t// 页面卸载时清理定时器，防止内存泄漏\r\n\t\tif (this.smsTimer) {\r\n\t\t\tclearInterval(this.smsTimer);\r\n\t\t\tthis.smsTimer = null;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tloadData(id) {\r\n\t\t\tconst that = this;\r\n\t\t\tif (id) {\r\n\t\t\t\tctx.post('/wx/wo/get/' + id, function (res) {\r\n\t\t\t\t\tif (res.code < 0 || res.data == null) return ctx.error('无法获取任务信息', 'back')\r\n\t\t\t\t\tthat.dataModel = res.data\r\n\t\t\t\t\tthat.dataModel.confirmMobile = res.data.phone\r\n\t\t\t\t\tif (res.data.attachList) {\r\n\t\t\t\t\t\tres.data.attachList.forEach(file => {\r\n\t\t\t\t\t\t\tthat.imageValue.push({ name: file.name, extname: file.extName, url: settings.attach_host + file.path })\r\n\t\t\t\t\t\t\tthat.fileList.push(file)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tctx.error('无效参数', 'back')\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadSelect(e) {\r\n\t\t\tif (e.tempFiles) {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\te.tempFiles.forEach(file => that.uploadFile(file));\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadDelete(e) {\r\n\t\t\tfor (let i = 0; i < this.fileList.length; i++) {\r\n\t\t\t\tif (this.fileList[i].fileId === e.tempFile.uuid) {\r\n\t\t\t\t\tthis.fileList.splice(i, 1);\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadFile(file) {\r\n\t\t\tconst that = this\r\n\t\t\twx.uploadFile({\r\n\t\t\t\turl: settings.api_host + '/wx/upload/GD',\r\n\t\t\t\tfilePath: file.path,\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'zy_token': wx.getStorageSync('ZY_TOKEN'),\r\n\t\t\t\t\t'appId': settings.api_id,\r\n\t\t\t\t\t'appSecret': settings.api_secret\r\n\t\t\t\t},\r\n\t\t\t\tname: 'file',\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (res.statusCode != 200) return ctx.error('上传失败')\r\n\t\t\t\t\tconst ret = JSON.parse(res.data)\r\n\t\t\t\t\tif (ret.code < 0) return ctx.error(ret.msg)\r\n\t\t\t\t\tlet data = ret.data\r\n\t\t\t\t\tdata.fileId = file.uuid\r\n\t\t\t\t\tthat.fileList.push(data)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsubmit1() {\r\n\t\t\tif (this.dataModel.detailList && this.dataModel.detailList.length > 0) {\r\n\t\t\t\tfor (let i = 0; i < this.dataModel.detailList.length; i++) {\r\n\t\t\t\t\tconst item = this.dataModel.detailList[i];\r\n\t\t\t\t\tif (!item.fault || item.fault.trim() === '') {\r\n\t\t\t\t\t\treturn ctx.error('请填写故障类型');\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!item.solve || item.solve.trim() === '') {\r\n\t\t\t\t\t\treturn ctx.error('请填写维修情况');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tconst that = this\r\n\t\t\tthis.$refs.form.validate().then(_ => {\r\n\t\t\t\tconst data = Object.assign({ fileList: [] }, that.dataModel)\r\n\t\t\t\tthat.fileList.forEach(r => {\r\n\t\t\t\t\tdata.fileList.push(r.id)\r\n\t\t\t\t})\r\n\t\t\t\tctx.post('/wx/wo/solve', data, function (res) {\r\n\t\t\t\t\tif (res.code < 0) return ctx.error(res.msg)\r\n\t\t\t\t\tctx.ok('保存成功', 'back')\r\n\t\t\t\t}).catch(() => { ctx.error('网络异常') })\r\n\t\t\t}).catch(() => { })\r\n\t\t},\r\n\t\tsendSms() {\r\n\t\t\t// 手机号码验证规则\r\n\t\t\tif (!this.dataModel.confirmMobile || !/^1[3-9]\\d{9}$/.test(this.dataModel.confirmMobile)) return ctx.error('请输入有效的手机号码')\r\n\r\n\t\t\t// 清理之前的定时器，防止重复创建\r\n\t\t\tif (this.smsTimer) {\r\n\t\t\t\tclearInterval(this.smsTimer);\r\n\t\t\t\tthis.smsTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\tconst that = this;\r\n\t\t\tthis.smsTimeCount = 1200\r\n\t\t\tthis.smsTimer = setInterval(() => {\r\n\t\t\t\tthat.smsTimeCount--;\r\n\t\t\t\tif (that.smsTimeCount <= 0) {\r\n\t\t\t\t\tclearInterval(that.smsTimer);\r\n\t\t\t\t\tthat.smsTimer = null;\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t\tctx.post('/wx/wo/sendSms', { confirmMobile: this.dataModel.confirmMobile, id: this.dataModel.id }, (res) => {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.dataModel.smsId = res.data;\r\n\t\t\t\t\tctx.ok('验证码发送成功');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.smsTimeCount = 0;\r\n\t\t\t\t\tclearInterval(that.smsTimer);\r\n\t\t\t\t\tctx.error(res.msg || '验证码发送失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tsubmit() {\r\n\t\t\tif (!this.dataModel.smsId) return ctx.error('请填写验证手机，并获取验证码')\r\n\t\t\tif (this.dataModel.detailList && this.dataModel.detailList.length > 0) {\r\n\t\t\t\tfor (let i = 0; i < this.dataModel.detailList.length; i++) {\r\n\t\t\t\t\tconst item = this.dataModel.detailList[i];\r\n\t\t\t\t\tif (!item.fault || item.fault.trim() === '') {\r\n\t\t\t\t\t\treturn ctx.error('请填写故障类型');\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!item.solve || item.solve.trim() === '') {\r\n\t\t\t\t\t\treturn ctx.error('请填写维修情况');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tconst that = this\r\n\t\t\tthis.$refs.form.validate().then(_ => {\r\n\t\t\t\tconst data = Object.assign({ fileList: [] }, that.dataModel)\r\n\t\t\t\tthat.fileList.forEach(r => {\r\n\t\t\t\t\tdata.fileList.push(r.id)\r\n\t\t\t\t})\r\n\t\t\t\tctx.post('/wx/wo/finish', data, function (res) {\r\n\t\t\t\t\tif (res.code < 0) return ctx.error(res.msg)\r\n\t\t\t\t\tctx.ok('保存成功', 'back')\r\n\t\t\t\t}).catch(() => { ctx.error('网络异常') })\r\n\t\t\t}).catch(() => { })\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tmargin: 12rpx;\r\n}\r\n\r\n.radio {\r\n\tmargin-left: 20rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./solve.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./solve.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623947\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}