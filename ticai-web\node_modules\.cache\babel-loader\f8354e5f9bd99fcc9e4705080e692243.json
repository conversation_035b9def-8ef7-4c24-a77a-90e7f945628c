{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue", "mtime": 1752649761442}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["PageTable", "AssetTypeChosen", "getAssetStatusType", "getAssetStatusText", "components", "data", "qform", "terminalNo", "no", "locationName", "historyDialogVisible", "historyLoading", "currentAsset", "historyList", "mounted", "_this", "$nextTick", "searchAsset", "methods", "$refs", "grid", "search", "viewAssetHistory", "row", "_this2", "assetId", "id", "$message", "error", "$http", "post", "concat", "then", "res", "code", "msg", "catch", "v", "status", "formatDate", "date", "format", "arguments", "length", "undefined", "d", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "exportHistory", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "loadInst", "$loading", "fullscreen", "text", "$jasper", "url", "responseType", "blob", "close", "assetNo", "fileName", "toISOString", "slice", "replace", "$saveAs", "err"], "sources": ["src/views/asset/asset-trace/index.vue"], "sourcesContent": ["<template>\r\n  <div style=\"padding: 0 10px;\">\r\n    <div class=\"page-header\">\r\n      <div class=\"page-tollbar\">\r\n        <div class=\"opt\">\r\n          <el-button-group>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-search\" @click=\"searchAsset\">查询资产</el-button>\r\n          </el-button-group>\r\n        </div>\r\n      </div>\r\n      <div class=\"page-filter\">\r\n        <el-form :model=\"qform\" inline size=\"mini\" label-width=\"110px\" @submit.native.prevent=\"searchAsset\">\r\n          <el-form-item label=\"终端号：\">\r\n            <el-input v-model=\"qform.terminalNo\" clearable placeholder=\"请输入终端号\" autocomplete=\"off\"\r\n              style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"资产编号：\">\r\n            <el-input v-model=\"qform.no\" clearable placeholder=\"请输入资产编号\" autocomplete=\"off\" style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"业主名称：\">\r\n            <el-input v-model=\"qform.locationName\" clearable placeholder=\"请输入业主名称\" autocomplete=\"off\"\r\n              style=\"width:180px;\" />\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <div class=\"page-body\">\r\n      <page-table ref=\"grid\" v-table-height size=\"mini\" path=\"/am/asset/trace/page\" :query=\"qform\" stripe border\r\n        highlight-current-row @row-click=\"viewAssetHistory\">\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"130\" header-align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" header-align=\"center\" fixed=\"left\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAssetHistory(scope.row)\">{{ scope.row.no }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"130\" fixed=\"left\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAssetHistory(scope.row)\">{{ scope.row.name }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"规格型号\" prop=\"spec\" width=\"120\" header-align=\"center\" />\r\n        <el-table-column label=\"入库日期\" prop=\"inDate\" width=\"120\" header-align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDate(scope.row.inDate, 'yyyy-MM-dd') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"使用部门\" prop=\"useDeptName\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"使用人\" prop=\"useUserName\" width=\"90\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"terminalNo\" width=\"110\" header-align=\"center\" />\r\n        <el-table-column label=\"使用网点\" prop=\"locationName\" min-width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"定位地址\" prop=\"locAddr\" min-width=\"250\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag size=\"small\" hit :type=\"getAssetStatusType(scope.row)\" disable-transitions>{{\r\n              getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n\r\n    <!-- 资产历史记录对话框 -->\r\n    <el-dialog :title=\"'资产历史记录 - ' + (currentAsset ? currentAsset.name : '')\" :visible.sync=\"historyDialogVisible\"\r\n      width=\"80%\" :close-on-click-modal=\"false\">\r\n      <div v-loading=\"historyLoading\">\r\n        <div v-if=\"currentAsset\">\r\n          <el-descriptions :column=\"4\" border>\r\n            <el-descriptions-item label=\"资产编码\">{{ currentAsset.no }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"资产名称\">{{ currentAsset.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"资产类型\">{{ currentAsset.typeName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"规格型号\">{{ currentAsset.spec }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"终端号\">{{ currentAsset.terminalNo }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"使用网点\">{{ currentAsset.locationName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"定位地址\">{{ currentAsset.locAddr }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"当前状态\">\r\n              <el-tag size=\"small\" hit :type=\"getAssetStatusType(currentAsset)\" disable-transitions>{{\r\n                getAssetStatusText(currentAsset) }}</el-tag>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <el-table :data=\"historyList\" style=\"width: 100%; margin-top: 15px;\" border stripe>\r\n          <el-table-column label=\"资产编码\" prop=\"no\" width=\"120\" align=\"center\" fixed=\"left\" />\r\n          <el-table-column label=\"操作类型\" prop=\"operationTypeName\" width=\"100\" align=\"center\" fixed=\"left\" />\r\n          <el-table-column label=\"操作单号\" prop=\"operationNo\" width=\"150\" align=\"center\" />\r\n          <el-table-column label=\"操作时间\" prop=\"operationTime\" width=\"160\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDate(scope.row.operationTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作人\" prop=\"operatorName\" width=\"120\" align=\"center\" />\r\n          <el-table-column label=\"终端号\" prop=\"terminalNo\" width=\"120\" align=\"center\" />\r\n          <el-table-column label=\"业主名称\" prop=\"locationName\" width=\"150\" align=\"center\" />\r\n          <el-table-column label=\"更新信息\" prop=\"updateInfo\" min-width=\"250\" header-align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"exportHistory\">导出</el-button>\r\n        <el-button @click=\"historyDialogVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport AssetTypeChosen from '@/views/components/AssetTypeChosen.vue'\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nexport default {\r\n  components: { PageTable, AssetTypeChosen },\r\n  data() {\r\n    return {\r\n      qform: {\r\n        terminalNo: '',\r\n        no: '',\r\n        locationName: ''\r\n      },\r\n      historyDialogVisible: false,\r\n      historyLoading: false,\r\n      currentAsset: null,\r\n      historyList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    // 页面加载时自动查询数据\r\n    this.$nextTick(() => {\r\n      this.searchAsset()\r\n    })\r\n  },\r\n  methods: {\r\n    searchAsset() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    viewAssetHistory(row) {\r\n      this.currentAsset = row\r\n      this.historyDialogVisible = true\r\n      this.historyLoading = true\r\n      this.historyList = []\r\n\r\n      // 使用资产ID查询历史记录\r\n      const assetId = row.id\r\n      if (!assetId) {\r\n        this.historyLoading = false\r\n        this.$message.error('无法获取资产ID，请确保资产有ID')\r\n        return\r\n      }\r\n\r\n      this.$http.post(`/am/asset/trace/historyById/${assetId}`).then(res => {\r\n        this.historyLoading = false\r\n        if (res.code > 0) {\r\n          this.historyList = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取资产历史记录失败')\r\n        }\r\n      }).catch(() => {\r\n        this.historyLoading = false\r\n        this.$message.error('网络错误，请稍后重试')\r\n      })\r\n    },\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    formatDate(date, format = 'yyyy-MM-dd HH:mm:ss') {\r\n      if (!date) return ''\r\n\r\n      // 创建一个新的Date对象\r\n      const d = new Date(date)\r\n\r\n      // 格式化年月日\r\n      const year = d.getFullYear()\r\n      const month = String(d.getMonth() + 1).padStart(2, '0')\r\n      const day = String(d.getDate()).padStart(2, '0')\r\n\r\n      // 格式化时分秒\r\n      const hours = String(d.getHours()).padStart(2, '0')\r\n      const minutes = String(d.getMinutes()).padStart(2, '0')\r\n      const seconds = String(d.getSeconds()).padStart(2, '0')\r\n\r\n      // 根据传入的格式返回相应的日期字符串\r\n      if (format === 'yyyy-MM-dd') {\r\n        return `${year}-${month}-${day}`\r\n      } else {\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n      }\r\n    },\r\n    exportHistory() {\r\n      if (!this.currentAsset || !this.currentAsset.id) {\r\n        this.$message.error('无法获取资产信息，请重新打开详情')\r\n        return\r\n      }\r\n\r\n      this.$confirm('您确定要导出这个资产的历史记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        const loadInst = this.$loading({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n        this.$jasper({\r\n          url: `/am/asset/trace/exportByAssetId/${this.currentAsset.id}`,\r\n          responseType: 'blob'\r\n        }).then(blob => {\r\n          loadInst.close()\r\n          // 生成文件名\r\n          const assetNo = this.currentAsset.no || '未知编号'\r\n          const fileName = `资产历史记录_${assetNo}_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`\r\n          this.$saveAs(blob, fileName)\r\n        }).catch(err => {\r\n          loadInst.close()\r\n          this.$message.error('导出生成出错:' + err)\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-header {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-tollbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.page-filter {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGA,OAAAA,SAAA;AACA,OAAAC,eAAA;AACA,SAAAC,kBAAA,IAAAA,mBAAA,EAAAC,kBAAA,IAAAA,mBAAA;AAEA;EACAC,UAAA;IAAAJ,SAAA,EAAAA,SAAA;IAAAC,eAAA,EAAAA;EAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QACAC,UAAA;QACAC,EAAA;QACAC,YAAA;MACA;MACAC,oBAAA;MACAC,cAAA;MACAC,YAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,WAAA;IACA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MACA,KAAAE,KAAA,CAAAC,IAAA,CAAAC,MAAA,MAAAf,KAAA;IACA;IACAgB,gBAAA,WAAAA,iBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAZ,YAAA,GAAAW,GAAA;MACA,KAAAb,oBAAA;MACA,KAAAC,cAAA;MACA,KAAAE,WAAA;;MAEA;MACA,IAAAY,OAAA,GAAAF,GAAA,CAAAG,EAAA;MACA,KAAAD,OAAA;QACA,KAAAd,cAAA;QACA,KAAAgB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAC,KAAA,CAAAC,IAAA,gCAAAC,MAAA,CAAAN,OAAA,GAAAO,IAAA,WAAAC,GAAA;QACAT,MAAA,CAAAb,cAAA;QACA,IAAAsB,GAAA,CAAAC,IAAA;UACAV,MAAA,CAAAX,WAAA,GAAAoB,GAAA,CAAA5B,IAAA;QACA;UACAmB,MAAA,CAAAG,QAAA,CAAAC,KAAA,CAAAK,GAAA,CAAAE,GAAA;QACA;MACA,GAAAC,KAAA;QACAZ,MAAA,CAAAb,cAAA;QACAa,MAAA,CAAAG,QAAA,CAAAC,KAAA;MACA;IACA;IACA1B,kBAAA,WAAAA,mBAAAmC,CAAA;MACA,OAAAnC,mBAAA,CAAAmC,CAAA,CAAAC,MAAA;IACA;IACAnC,kBAAA,WAAAA,mBAAAkC,CAAA;MACA,OAAAlC,mBAAA,CAAAkC,CAAA,CAAAC,MAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAAF,IAAA;;MAEA;MACA,IAAAK,CAAA,OAAAC,IAAA,CAAAN,IAAA;;MAEA;MACA,IAAAO,IAAA,GAAAF,CAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,CAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,CAAA,CAAAS,OAAA,IAAAF,QAAA;;MAEA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,CAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,CAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,CAAA,CAAAe,UAAA,IAAAR,QAAA;;MAEA;MACA,IAAAX,MAAA;QACA,UAAAV,MAAA,CAAAgB,IAAA,OAAAhB,MAAA,CAAAkB,KAAA,OAAAlB,MAAA,CAAAsB,GAAA;MACA;QACA,UAAAtB,MAAA,CAAAgB,IAAA,OAAAhB,MAAA,CAAAkB,KAAA,OAAAlB,MAAA,CAAAsB,GAAA,OAAAtB,MAAA,CAAAwB,KAAA,OAAAxB,MAAA,CAAA0B,OAAA,OAAA1B,MAAA,CAAA4B,OAAA;MACA;IACA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,UAAAlD,YAAA,UAAAA,YAAA,CAAAc,EAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAmC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlC,IAAA;QACA,IAAAmC,QAAA,GAAAL,MAAA,CAAAM,QAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACAR,MAAA,CAAAS,OAAA;UACAC,GAAA,qCAAAzC,MAAA,CAAA+B,MAAA,CAAAlD,YAAA,CAAAc,EAAA;UACA+C,YAAA;QACA,GAAAzC,IAAA,WAAA0C,IAAA;UACAP,QAAA,CAAAQ,KAAA;UACA;UACA,IAAAC,OAAA,GAAAd,MAAA,CAAAlD,YAAA,CAAAJ,EAAA;UACA,IAAAqE,QAAA,2CAAA9C,MAAA,CAAA6C,OAAA,OAAA7C,MAAA,KAAAe,IAAA,GAAAgC,WAAA,GAAAC,KAAA,QAAAC,OAAA;UACAlB,MAAA,CAAAmB,OAAA,CAAAP,IAAA,EAAAG,QAAA;QACA,GAAAzC,KAAA,WAAA8C,GAAA;UACAf,QAAA,CAAAQ,KAAA;UACAb,MAAA,CAAAnC,QAAA,CAAAC,KAAA,aAAAsD,GAAA;QACA;MACA,GAAA9C,KAAA,cACA;IACA;EACA;AACA", "ignoreList": []}]}