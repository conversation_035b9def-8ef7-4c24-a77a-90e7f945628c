<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetDAO">

    <sql id="meta">
        a.ID_
        ,a.PROP_
        ,a.TYPE_
        ,a.NO_
        ,a.NAME_
        ,a.SN_
        ,a.BC1_
        ,a.BC2_
        ,a.BC3
        ,a.IMP_
        ,a.CORP_
        ,a.DEPT_
        ,a.REGION_
        ,a.LOCATION_
        ,a.MA_DATE_
        ,a.GU_DATE_
        ,a.EX_DATE_
        ,a.BO_DATE_
        ,a.BO_AMOUNT_
        ,a.EX_DAY_
        ,a.EX_NAME_
        ,a.USE_DEPT_
        ,a.USE_USER_
        ,a.RESERVE1_
        ,a.RESERVE2_
        ,a.RESERVE3_
        ,a.LNG_
        ,a.LAT_
        ,a.LOC_USER_
        ,a.LOC_TIME_
        ,a.LOC_ADDR_
        ,a.STATUS_
        ,a.LEND_STATUS_
        ,a.CUSER_
        ,a.CTIME_
        ,a.MUSER_
        ,a.MTIME_
        ,a.LAST_ID_
        ,a.FLAG_
        ,a.LEND_STATUS_
        ,a.BRAND_
        ,a.SPEC_
        ,a.TAKE_DATE_
        ,a.VALUE_
    </sql>
    <resultMap id="VO" type="com.zy.dam.asset.vo.AssetVo">
        <result column="a.ATTR_" jdbcType="BLOB" javaType="String"/>
    </resultMap>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_ ,a.PROP_ ,a.TYPE_ ,a.NO_ ,a.NAME_ ,a.SN_ ,a.BC1_ ,a.IMP_ ,a.CORP_ ,a.DEPT_ ,a.REGION_ ,a.LOCATION_
        ,a.MA_DATE_ ,a.GU_DATE_ ,a.EX_DATE_ ,a.BO_DATE_ ,a.BO_AMOUNT_ ,a.EX_DAY_ ,a.EX_NAME_ ,a.USE_DEPT_ ,a.USE_USER_
        ,a.STATUS_ ,a.LEND_STATUS_ ,a.BRAND_ ,a.SPEC_ ,a.TAKE_DATE_ ,a.VALUE_ ,(select m.NAME_ from AM_REGION m where
        m.ID_=a.REGION_) region_name ,(select m.NAME_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_name ,(select
        m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name ,(select m.NAME_ from SYS_DEPT m
        where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and
        m.ID_=a.USE_DEPT_) use_dept_name ,(select m.NAME_ from SYS_USER m where m.FLAG_='1' and m.ID_=a.USE_USER_)
        use_user_name from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="ignoreId != null">and a.ID_!=#{ignoreId}</if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        <if test="dept != null">and a.DEPT_=#{dept}</if>
        <if test="useDept != null">and a.USE_DEPT_=#{useDept}</if>
        <if test="useUser != null">and (b.NAME_ like concat('%', #{useUser}, '%') or b.NO_ like concat('%', #{useUser},
            '%') or b.ACCOUNT_ like concat('%', #{useUser}, '%'))
        </if>
        <if test="spec != null">and a.SPEC_ like concat('%',#{spec},'%')</if>
        <if test="imp != null">and a.IMP_=#{imp}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="statusList != null">
            <foreach collection="statusList" item="statusItem" open="and a.STATUS_ in (" separator="," close=")">
                #{statusItem}
            </foreach>
        </if>
        <if test="exBegin != null">and a.EX_DATE_ &gt;= #{exBegin}</if>
        <if test="exEnd != null">and a.EX_DATE_ &lt; date_sub(#{exBegin}, interval 1 day)</if>
        <if test="exDateIndex != 0">
            <choose>
                <when test="exDateIndex = 1">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 91 day)
                </when>
                <when test="exDateIndex = 2">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 31 day)
                </when>
                <when test="exDateIndex = 8">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 8 day)
                </when>
                <when test="exDateIndex = 9">and a.EX_DATE_ &lt; curdate()</when>
            </choose>
        </if>
        <if test="noBegin != null">and a.NO_ &gt;= #{noBegin}</if>
        <if test="noEnd != null">and a.NO_ &lt;= #{noEnd}</if>
        <if test="takeBegin != null">and a.TAKE_DATE_ &gt;= #{takeBegin}</if>
        <if test="takeEnd != null">and a.TAKE_DATE_ &lt; date_add(#{takeEnd}, interval 1 day)</if>
        order by a.NO_ desc
    </select>

    <select id="findSummary" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_, a.PROP_, a.TYPE_, a.NO_, a.NAME_, a.SN_, a.BC1_, a.IMP_, a.CORP_, a.DEPT_, a.REGION_, a.LOCATION_, a.MA_DATE_, a.GU_DATE_, a.EX_DATE_, a.BO_DATE_, a.BO_AMOUNT_, a.EX_DAY_, a.EX_NAME_, a.USE_DEPT_, a.USE_USER_, a.BRAND_, a.SPEC_, a.VALUE_, a.TAKE_DATE_, a.STATUS_, a.LEND_STATUS_, (select m.NAME_ from AM_REGION m where m.ID_ = a.REGION_) region_name, (select m.NAME_ from AM_LOCATION m where m.ID_ = a.LOCATION_) location_name, (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = a.TYPE_) type_name,
            (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.DEPT_)                                                                                                                                                                                                                                                                                    dept_name, (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.USE_DEPT_) use_dept_name, (select m.NAME_ from SYS_USER m where m.FLAG_ = '1' and m.ID_ = a.USE_USER_) use_user_name,
            (select m.NAME_ from SYS_USER m where m.FLAG_ = '1' and m.ID_ = a.LOC_USER_)                                                                                                                                                                                                                                                                                loc_user_name
        from AM_ASSET a
        where a.ID_ = #{0}
    </select>

    <!-- 获取唯一的资管-资产基本信息数据 -->
    <select id="findVo" resultMap="VO">
        select
        <include refid="meta"/>
        ,a.ATTR_ ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name ,(select m.NAME_ from AM_LOCATION
        m where m.ID_=a.LOCATION_) location_name ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and
        m.CODE_=a.TYPE_) type_name ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name ,(select m.NAME_ from
        SYS_USER m where m.FLAG_='1' and m.ID_=a.USE_USER_) use_user_name ,(select m.NAME_ from SYS_USER m where
        m.FLAG_='1' and m.ID_=a.LOC_USER_) loc_user_name from AM_ASSET a where a.ID_=#{0}
    </select>

    <update id="updateLocation">
        update AM_ASSET
        set LNG_=#{1}, LAT_=#{2}, LOC_USER_=#{3}, LOC_ADDR_=#{4}, LOC_TIME_=#{5}
        where ID_ = #{0}
    </update>

    <select id="findIdByRfid" resultType="String">
        select ID_
        from AM_ASSET
        where FLAG_ = '1' and BC1_ = #{0}
    </select>

    <update id="cleanRfid">
        update AM_ASSET
        set BC1_=NULL
        where FLAG_ = '1' and BC1_ = #{0}
    </update>

    <update id="updateRfid">
        update AM_ASSET
        set BC1_=#{1}, PRINT_TIME_=now()
        where ID_ = #{0}
    </update>

    <select id="listType" resultType="com.zy.model.TreeNode">
        select a.CODE_ id_, a.PCODE_ pid_, a.NAME_
        from AM_ASSET_TYPE a
        where a.FLAG_ = '1'
        order by a.ORD_, a.CODE_
    </select>

    <select id="getWithLocation" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="meta"/>
        ,a.ATTR_ ,b.NAME_ location_name,b.CONTACT_ location_contact,b.ADDRESS_ location_address,b.PHONE_ location_phone
        from AM_ASSET a left join AM_LOCATION b on a.LOCATION_=b.ID_ where a.ID_=#{0}
    </select>

    <select id="findBySn" resultType="com.zy.dam.asset.vo.AssetLocationVo">
        select a.ID_, a.LOCATION_, a.ASSET_, a.SN_, b.CODE_ location_code, b.REGION_ location_region, b.NAME_ location_name, b.ADDRESS_ location_address, b.PHONE_ location_phone, c.NAME_ asset_name, c.NO_ asset_no
        from AM_LOCATION_ASSET a left join AM_ASSET c on a.ASSET_ = c.ID_, AM_LOCATION b
        where a.LOCATION_ = b.ID_ and a.FLAG_ = '1' and a.SN_ = #{0}
    </select>

    <select id="findSnByAsset" resultType="String">
        select SN_
        from AM_ASSET
        where ID_ = #{0}
    </select>

    <select id="findAssetBySn" resultType="String">
        select ID_
        from AM_ASSET
        where SN_ = #{0}
        limit 0, 1
    </select>

    <update id="cleanAssetSn">
        update AM_ASSET
        set SN_=NULL
        where ID_ != #{0} and SN_ = #{0}
    </update>

    <update id="bindSn">
        update AM_ASSET
        set SN_=#{1}
        where ID_ = #{0}
    </update>

    <update id="bindSnWithAsset">
        update AM_ASSET
        set SN_=#{1}, REGION_=#{2}, LOCATION_=#{3}
        where ID_ = #{0}
    </update>

    <insert id="insertSn">
        insert into AM_ASSET_SN(ID_, ASSET_, LAST_SN_, LAST_ASSET_, NOW_SN_, USER_, TIME_, FLAG_)
        values (uuid(), #{0}, #{1}, #{2},
                #{3}, #{4}, now(), '1')
    </insert>

    <update id="updateLocationAsset">
        update AM_LOCATION_ASSET
        set ASSET_=#{1}, TIME_=now(), USER_=#{2}
        where ID_ = #{0}
    </update>

    <select id="getBySn" resultType="com.zy.dam.asset.vo.AssetLocationVo">
        select c.ID_, a.LOCATION_, a.ASSET_, a.SN_, b.CODE_ location_code, b.REGION_ location_region, b.NAME_ location_name, b.ADDRESS_ location_address,
        b.PHONE_ location_phone, c.NAME_ asset_name, c.NO_ asset_no, b.CONTACT_ locationContact, c.SPEC_
        from AM_LOCATION_ASSET a left join AM_ASSET c on a.ASSET_ = c.ID_, AM_LOCATION b
        where a.LOCATION_ = b.ID_ and a.FLAG_ = '1' and a.SN_ = #{0}
    </select>

    <select id="getByNo" resultType="com.zy.dam.asset.vo.AssetLocationVo">
        select c.ID_,
               c.LOCATION_,
               c.ID_ asset,
               c.SN_ sn,
               l.CODE_ location_code,
               l.REGION_ location_region,
               l.NAME_ location_name,
               l.ADDRESS_ location_address,
               l.PHONE_ location_phone,
               c.NAME_ asset_name,
               c.NO_ asset_no,
               l.CONTACT_ locationContact,
               c.SPEC_
        from AM_ASSET c
        left join AM_LOCATION l on c.LOCATION_ = l.ID_
        where c.FLAG_ = '1' and c.NO_ = #{0}
        limit 1
    </select>

    <update id="updatePrint">
        update AM_ASSET
        set PRINT_TIME_=now()
        where ID_ = #{0}
    </update>

    <update id="updateSnAsset">
        update AM_ASSET
        set USE_USER_=#{1}, STATUS_='2'
        where ID_ = #{0}
    </update>

    <select id="findIdByAsset" resultType="String">
        select ID_
        from AM_ASSET_SN
        where FLAG_ = '1' and ASSET_ = #{0}
        limit 0,1
    </select>

    <select id="getAssetBindingList" resultType="com.zy.dam.asset.vo.AssetSnVo">
        select a.*, a.TIME_ bind_time, c.ACCOUNT_, c.NAME_, c.GENDER_, c.STATUS_,
        (select f.NAME_ from SYS_DEPT f where f.ID_ = c.DEPT_) dept_name, a.NOW_SN_,
        (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = d.TYPE_) type_name,
        d.NO_, d.NAME_ asset_name, d.SPEC_, g.NAME_ location_name, g.ADDRESS_ location_address
        from AM_ASSET_SN a left join WX_USER b on a.USER_ = b.ID_ left join SYS_USER c on b.USER_ = c.ID_
        and c.FLAG_ = '1' left join AM_ASSET d on a.ASSET_ = d.ID_ and d.FLAG_ = '1'
        left join AM_LOCATION g on g.ID_ = d.LOCATION_ and g.FLAG_ = '1'
        where a.FLAG_ != '9' and a.USER_ = #{1}
        order by a.TIME_ desc
    </select>

    <select id="getAssetBindingListByNo" resultType="com.zy.dam.asset.vo.AssetSnVo">
        select a.* ,a.TIME_ bind_time,c.ACCOUNT_,c.NAME_,c.GENDER_,c.STATUS_ ,(select f.NAME_ from SYS_DEPT f where
        f.ID_=c.DEPT_) dept_name ,a.NOW_SN_,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=d.TYPE_)
        type_name ,d.NO_,d.NAME_ asset_name,d.SPEC_,g.NAME_ location_name,g.ADDRESS_ location_address from AM_ASSET_SN a
        left join WX_USER b on a.USER_= b.ID_ left join SYS_USER c on b.USER_= c.ID_ and c.FLAG_='1' left join AM_ASSET
        d on a.ASSET_= d.ID_ and d.FLAG_='1' left join AM_LOCATION g on g.ID_=d.LOCATION_ and g.FLAG_='1' where
        a.FLAG_!='9'
        <if test="keyword != null">
            and (a.NOW_SN_ like concat('%',#{keyword},'%') or d.NO_ like concat('%',#{keyword},'%'))
        </if>
        order by a.TIME_ desc
    </select>

    <select id="findAssetBackScan" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_, a.PROP_, a.TYPE_, a.NO_, a.NAME_, a.SN_, a.BC1_, a.IMP_, a.CORP_, a.DEPT_, a.REGION_, a.LOCATION_,
        a.MA_DATE_, a.GU_DATE_, a.EX_DATE_, a.BO_DATE_, a.BO_AMOUNT_, a.EX_DAY_, a.EX_NAME_, a.USE_DEPT_, a.USE_USER_, a.STATUS_,
        a.LEND_STATUS_, a.SPEC_, b.NAME_ use_user_name, c.NAME_ location_name, c.CONTACT_ location_contact, c.PHONE_ location_phone,
        c.ADDRESS_ location_address,a.LOC_ADDR_,a.LNG_,a.LAT_,
        (select m.NAME_ from AM_REGION m where m.ID_ = a.REGION_) region_name,
        (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = a.TYPE_) type_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.DEPT_) dept_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.USE_DEPT_) use_dept_name
        from AM_ASSET a left join SYS_USER b on a.USE_USER_ = b.ID_ left join AM_LOCATION c on a.LOCATION_ = c.ID_
        where a.FLAG_ = '1' and a.STATUS_ = '2' and a.ID_ = #{0}
    </select>

    <select id="findAssetConsumingScan" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_, a.PROP_, a.TYPE_, a.NO_, a.NAME_, a.SN_, a.BC1_, a.IMP_, a.CORP_, a.DEPT_, a.REGION_, a.LOCATION_, a.MA_DATE_,
        a.GU_DATE_, a.EX_DATE_, a.BO_DATE_, a.BO_AMOUNT_, a.EX_DAY_, a.EX_NAME_, a.USE_DEPT_, a.USE_USER_, a.STATUS_, a.LEND_STATUS_,
        a.SPEC_, b.NAME_ use_user_name, c.NAME_ location_name, c.CONTACT_ location_contact, c.PHONE_ location_phone, c.ADDRESS_ location_address,
        (select m.NAME_ from AM_REGION m where m.ID_ = a.REGION_) region_name,
        (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = a.TYPE_) type_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.DEPT_) dept_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.USE_DEPT_) use_dept_name
        from AM_ASSET a left join SYS_USER b on a.USE_USER_ = b.ID_ left join AM_LOCATION c on a.LOCATION_ = c.ID_
        where a.FLAG_ = '1' and a.STATUS_ = '1' and a.ID_ = #{0}
    </select>

    <select id="findAssetConsumingNo" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_, a.PROP_, a.TYPE_, a.NO_, a.NAME_, a.SN_, a.BC1_, a.IMP_, a.CORP_, a.DEPT_, a.REGION_, a.LOCATION_,
        a.MA_DATE_, a.GU_DATE_, a.EX_DATE_, a.BO_DATE_, a.BO_AMOUNT_, a.EX_DAY_, a.EX_NAME_, a.USE_DEPT_, a.USE_USER_, a.STATUS_,
        a.LEND_STATUS_, a.SPEC_, b.NAME_ use_user_name, c.NAME_ location_name, c.CONTACT_ location_contact, c.PHONE_ location_phone,
        c.ADDRESS_ location_address,
        (select m.NAME_ from AM_REGION m where m.ID_ = a.REGION_) region_name,
        (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = a.TYPE_) type_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.DEPT_)                                                                                                                                                                                                dept_name, (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.USE_DEPT_) use_dept_name
        from AM_ASSET a left join SYS_USER b on a.USE_USER_ = b.ID_ left join AM_LOCATION c on a.LOCATION_ = c.ID_
        where a.FLAG_ = '1' and a.STATUS_ = '1' and a.NO_ = #{0}
    </select>

    <select id="findAssetBackNo" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_, a.PROP_, a.TYPE_, a.NO_, a.NAME_, a.SN_, a.BC1_, a.IMP_, a.CORP_, a.DEPT_, a.REGION_, a.LOCATION_, a.MA_DATE_,
        a.GU_DATE_, a.EX_DATE_, a.BO_DATE_, a.BO_AMOUNT_, a.EX_DAY_, a.EX_NAME_, a.USE_DEPT_, a.USE_USER_, a.STATUS_, a.LEND_STATUS_,
        a.SPEC_, b.NAME_ use_user_name, c.NAME_ location_name, c.CONTACT_ location_contact, c.PHONE_ location_phone,
        c.ADDRESS_ location_address,a.LOC_ADDR_,a.LNG_,a.LAT_,
        (select m.NAME_ from AM_REGION m where m.ID_ = a.REGION_) region_name,
        (select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_ = '1' and m.CODE_ = a.TYPE_) type_name,
        (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.DEPT_)                                                                                                                                                                                                dept_name, (select m.NAME_ from SYS_DEPT m where m.FLAG_ = '1' and m.ID_ = a.USE_DEPT_) use_dept_name
        from AM_ASSET a left join SYS_USER b on a.USE_USER_ = b.ID_ left join AM_LOCATION c on a.LOCATION_ = c.ID_
        where a.FLAG_ = '1' and a.STATUS_ = '2' and a.NO_ = #{0}
    </select>


    <!-- 统一的公共筛选条件片段 -->
    <sql id="commonAssetFilters">
        <if test="keyword != null">
            AND (
                COALESCE(asn.NOW_SN_, d.SN_) = #{keyword}
                OR d.NO_ = #{keyword}
                OR c.ACCOUNT_ = #{keyword}
                OR c.NAME_ like concat('%',#{keyword},'%')
                OR g.NAME_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="dept != null">
            AND c.DEPT_ = #{dept}
        </if>
        <!-- 判断是否领用 -->
        <if test="isReceived != null">
            AND ((isReceived == 'yes' AND d.USE_USER_ IS NOT NULL AND d.USE_USER_ != '')
            OR (isReceived == 'no' AND (d.USE_USER_ IS NULL OR d.USE_USER_ = '')))
        </if>
        <!-- 判断是否定位 -->
        <if test="isLocated != null">
            AND ((isLocated == 'yes' AND d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL)
            OR (isLocated == 'no' AND (d.LNG_ IS NULL OR d.LAT_ IS NULL)))
        </if>
        <!-- 判断是否绑定： -->
        <if test="isBound != null">
            AND ((isBound == 'yes' AND g.ID_ IS NOT NULL)
            OR (isBound == 'no' AND g.ID_ IS NULL))
        </if>
    </sql>

    <!-- 绑定资产分页查询 -->
    <select id="getAmAssetPage" resultType="com.zy.dam.asset.vo.AssetSnVo">
        SELECT * FROM (
        <!-- 已领用已绑定数据 -->
        SELECT
        d.ID_ AS id,
        a.TIME_ AS bindTime,
        g.CREATE_TIME_ AS locationTime,
        c.ACCOUNT_ AS account,
        c.NAME_ AS name,
        c.GENDER_ AS gender,
        c.STATUS_ AS status,
        (SELECT f.NAME_ FROM SYS_DEPT f WHERE f.ID_ = c.DEPT_) AS deptName,
        a.NOW_SN_ AS nowSn,
        (SELECT m.NAME_ FROM AM_ASSET_TYPE m WHERE m.FLAG_ = '1' AND m.CODE_ = d.TYPE_) AS typeName,
        d.NO_ AS no,
        d.NAME_ AS assetName,
        d.SPEC_ AS spec,
        g.NAME_ AS locationName,
        g.ADDRESS_ AS locationAddress,
        g.MEMO_ AS locationMemo,
        CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
        d.LOC_ADDR_ AS locAddr,
        1 AS dataType
        FROM AM_ASSET_SN a
        JOIN AM_ASSET d ON a.ASSET_ = d.ID_ AND d.FLAG_ = '1'
        LEFT JOIN SYS_USER c ON d.USE_USER_ = c.ID_ AND c.FLAG_ = '1'
        LEFT JOIN AM_LOCATION g ON g.ID_ = d.LOCATION_ AND g.FLAG_ = '1'
        WHERE a.FLAG_ != '9'
        AND NOT EXISTS (
        SELECT 1 FROM AM_ASSET_SN a2
        WHERE a2.ASSET_ = a.ASSET_ AND a2.FLAG_ != '9'
        AND (a2.TIME_ > a.TIME_ OR (a2.TIME_ = a.TIME_ AND a2.ID_ > a.ID_))
        )
        <if test="keyword != null">
            AND (c.ACCOUNT_ LIKE concat('%',#{keyword},'%')
            OR c.NAME_ LIKE concat('%',#{keyword},'%')
            OR a.NOW_SN_ LIKE concat('%',#{keyword},'%')
            OR d.NO_ LIKE concat('%',#{keyword},'%')
            OR d.NAME_ LIKE concat('%',#{keyword},'%')
            OR d.SPEC_ LIKE concat('%',#{keyword},'%')
            OR g.MEMO_ LIKE concat('%',#{keyword},'%'))
        </if>
        <if test="dept != null">AND c.DEPT_ = #{dept}</if>
        <if test="dateStart != null">
            AND g.CREATE_TIME_ >= #{dateStart}
        </if>
        <if test="dateEnd != null">
            AND g.CREATE_TIME_ &lt; DATE_ADD(#{dateEnd}, INTERVAL 1 DAY)
        </if>
        <if test="isLocated != null">
            <choose>
                <when test="isLocated == 'yes'">
                    AND d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL
                </when>
                <when test="isLocated == 'no'">
                    AND (d.LNG_ IS NULL OR d.LAT_ IS NULL)
                </when>
            </choose>
        </if>

        UNION ALL

        <!-- 已领用未绑定数据 -->
        SELECT
        d.ID_ AS id,
        NULL AS bindTime,
        g.CREATE_TIME_ AS locationTime,
        use_user.ACCOUNT_ AS account,
        use_user.NAME_ AS name,
        use_user.GENDER_ AS gender,
        use_user.STATUS_ AS status,
        (SELECT f.NAME_ FROM SYS_DEPT f WHERE f.ID_ = use_user.DEPT_) AS deptName,
        d.SN_ AS nowSn,
        (SELECT m.NAME_ FROM AM_ASSET_TYPE m WHERE m.FLAG_ = '1' AND m.CODE_ = d.TYPE_) AS typeName,
        d.NO_ AS no,
        d.NAME_ AS assetName,
        d.SPEC_ AS spec,
        g.NAME_ AS locationName,
        g.ADDRESS_ AS locationAddress,
        g.MEMO_ AS locationMemo,
        CASE WHEN d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL THEN '是' ELSE '否' END AS whetherLocation,
        d.LOC_ADDR_ AS locAddr,
        2 AS dataType
        FROM AM_ASSET d
        LEFT JOIN SYS_USER use_user ON d.USE_USER_ = use_user.ID_ AND use_user.FLAG_ = '1'
        LEFT JOIN AM_LOCATION g ON d.LOCATION_ = g.ID_ AND g.FLAG_ = '1'
        WHERE d.FLAG_ = '1'
        AND d.STATUS_ = '2'
        AND NOT EXISTS (
        SELECT 1 FROM AM_ASSET_SN asn
        WHERE asn.ASSET_ = d.ID_ AND asn.FLAG_ != '9'
        )
        <if test="keyword != null">
            AND (use_user.ACCOUNT_ LIKE concat('%',#{keyword},'%')
            OR use_user.NAME_ LIKE concat('%',#{keyword},'%')
            OR d.SN_ LIKE concat('%',#{keyword},'%')
            OR d.NO_ LIKE concat('%',#{keyword},'%')
            OR d.NAME_ LIKE concat('%',#{keyword},'%')
            OR d.SPEC_ LIKE concat('%',#{keyword},'%')
            OR g.MEMO_ LIKE concat('%',#{keyword},'%'))
        </if>
        <if test="dept != null">AND use_user.DEPT_ = #{dept}</if>
        <if test="dateStart != null">
            AND g.CREATE_TIME_ >= #{dateStart}
        </if>
        <if test="dateEnd != null">
            AND g.CREATE_TIME_ &lt; DATE_ADD(#{dateEnd}, INTERVAL 1 DAY)
        </if>
        <if test="isLocated != null">
            <choose>
                <when test="isLocated == 'yes'">
                    AND d.LNG_ IS NOT NULL AND d.LAT_ IS NOT NULL
                </when>
                <when test="isLocated == 'no'">
                    AND (d.LNG_ IS NULL OR d.LAT_ IS NULL)
                </when>
            </choose>
        </if>

        UNION ALL

        <!-- 没有绑定设备的网点（网点未匹配任何设备） -->
        SELECT
        g.ID_ AS id,
        NULL AS bindTime,
        g.CREATE_TIME_ AS locationTime,
        NULL AS account,
        NULL AS name,
        NULL AS gender,
        NULL AS status,
        NULL AS deptName,
        NULL AS nowSn,
        NULL AS typeName,
        NULL AS no,
        NULL AS assetName,
        NULL AS spec,
        g.NAME_ AS locationName,
        g.ADDRESS_ AS locationAddress,
        g.MEMO_ AS locationMemo,
        NULL AS whetherLocation,
        NULL AS locAddr,
        3 AS dataType
        FROM AM_LOCATION g
        WHERE g.FLAG_ = '1'
        AND NOT EXISTS (
        SELECT 1 FROM AM_ASSET d WHERE d.LOCATION_ = g.ID_ AND d.FLAG_ = '1'
        )
        <if test="keyword != null">
            AND (g.NAME_ LIKE CONCAT('%', #{keyword}, '%')
            OR g.ADDRESS_ LIKE CONCAT('%', #{keyword}, '%')
            OR g.CONTACT_ LIKE CONCAT('%', #{keyword}, '%')
            OR g.MEMO_ LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <!-- 网点数据的筛选逻辑-->
        <if test="isBound != null and isBound == 'yes'">
            AND 1 = 0
        </if>
        <if test="isReceived != null and isReceived == 'yes'">
            AND 1 = 0
        </if>
        <if test="isLocated != null and isLocated == 'yes'">
            AND 1 = 0
        </if>
        <if test="dateStart != null">
            AND g.CREATE_TIME_ >= #{dateStart}
        </if>
        <if test="dateEnd != null">
            AND g.CREATE_TIME_ &lt; DATE_ADD(#{dateEnd}, INTERVAL 1 DAY)
        </if>
        ) AS combined_results
        WHERE 1 = 1
        <!-- 数据类型筛选 -->
        <if test="dataType != null">
            <choose>
                <when test="dataType == 1">
                    AND combined_results.dataType = 1
                </when>
                <when test="dataType == 2">
                    AND combined_results.dataType = 2
                </when>
                <when test="dataType == 3">
                    AND combined_results.dataType = 3
                </when>
            </choose>
        </if>
        <!-- 绑定状态筛选 -->
        <if test="isBound != null">
            <choose>
                <when test="isBound == 'yes'">
                    AND combined_results.dataType = 1
                </when>
                <when test="isBound == 'no'">
                    AND combined_results.dataType IN (2, 3)
                </when>
            </choose>
        </if>
        <!-- 领用状态筛选 -->
        <if test="isReceived != null">
            <choose>
                <when test="isReceived == 'yes'">
                    AND combined_results.dataType IN (1, 2)
                </when>
                <when test="isReceived == 'no'">
                    AND combined_results.dataType = 3
                </when>
            </choose>
        </if>
        ORDER BY dataType, COALESCE(bindTime, locationTime) DESC, id
    </select>

    <select id="findStatusByAsset" resultType="String">
        select STATUS_
        from AM_ASSET
        where ID_ = #{0}
    </select>

    <!-- 根据资产ID获取网点ID -->
    <select id="findLocationById" resultType="String">
        select LOCATION_ from AM_ASSET where FLAG_='1' and ID_=#{0} limit 0,1
    </select>

</mapper>
