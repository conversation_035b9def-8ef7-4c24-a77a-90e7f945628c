(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d03da"],{"66ae":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",[l("div",{staticClass:"page-header"},[l("div",{staticClass:"page-tollbar"},[l("div",{staticClass:"opt"},[l("el-button-group",[l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length||/2|3/.test(e.items[0].status),type:"success",size:"mini",icon:"el-icon-edit"},on:{click:e.solveWo}},[e._v("完成工单")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length||"2"!=e.items[0].status,type:"warning",size:"mini",icon:"el-icon-check"},on:{click:e.closeWo}},[e._v("关闭工单")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length,size:"mini",icon:"el-icon-document"},on:{click:e.preview}},[e._v("查看详情")])],1)],1),l("div",{staticClass:"search"},[l("el-button-group",[l("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1)]),l("div",{staticClass:"page-filter"},[l("el-form",{attrs:{model:e.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[l("el-form-item",{attrs:{label:"单号："}},[l("el-input",{staticStyle:{width:"100px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.no,callback:function(t){e.$set(e.qform,"no",t)},expression:"qform.no"}})],1),l("el-form-item",{attrs:{label:"网点名称："}},[l("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.locationName,callback:function(t){e.$set(e.qform,"locationName",t)},expression:"qform.locationName"}})],1),l("el-form-item",{attrs:{label:"联系人："}},[l("el-input",{staticStyle:{width:"90px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.contact,callback:function(t){e.$set(e.qform,"contact",t)},expression:"qform.contact"}})],1),l("el-form-item",{attrs:{label:"设备型号："}},[l("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.spec,callback:function(t){e.$set(e.qform,"spec",t)},expression:"qform.spec"}})],1),l("el-form-item",{attrs:{label:"保障时间："}},[l("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.qdate,callback:function(t){e.qdate=t},expression:"qdate"}})],1),l("el-form-item",{attrs:{label:"当前状态："}},[l("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:e.qform.status,callback:function(t){e.$set(e.qform,"status",t)},expression:"qform.status"}},e._l(e.statusOptions,(function(e){return l("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1),l("el-form-item",{directives:[{name:"root-dept",rawName:"v-root-dept"}],attrs:{label:"所属部门："}},[l("dept-tree-box",{staticStyle:{width:"50px"},attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.qform.dept,callback:function(t){e.$set(e.qform,"dept",t)},expression:"qform.dept"}})],1),l("el-form-item",{attrs:{label:"服务人员："}},[l("el-input",{staticStyle:{width:"90px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.serviceStaff,callback:function(t){e.$set(e.qform,"serviceStaff",t)},expression:"qform.serviceStaff"}})],1)],1)],1)]),l("div",{staticClass:"page-body"},[l("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/wo/bill/page",query:e.qform,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":e.selectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),l("el-table-column",{attrs:{label:"单号",prop:"no",width:"140",align:"center",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(l){return l.stopPropagation(),e.viewItem(t.row)}}},[e._v(e._s(t.row.no))])]}}])}),l("el-table-column",{attrs:{label:"网点名称",prop:"locationName",width:"160","header-align":"center"}}),l("el-table-column",{attrs:{label:"网点地址",prop:"locationAddress","min-width":"250","header-align":"center"}}),l("el-table-column",{attrs:{label:"联系人",prop:"contact",width:"80",align:"center"}}),l("el-table-column",{attrs:{label:"联系电话",prop:"phone",width:"110",align:"center"}}),l("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"100",align:"center"}}),l("el-table-column",{attrs:{label:"服务人员",prop:"serviceStaff",width:"100",align:"center"}}),l("el-table-column",{attrs:{label:"报障时间",prop:"reportTime",width:"140",align:"center"}}),l("el-table-column",{attrs:{label:"排障时间",prop:"solveTime",width:"140",align:"center"}}),l("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-tag",{attrs:{size:"small",hit:"",type:e.getStatusType(t.row),"disable-transitions":""}},[e._v(e._s(e.getStatusText(t.row)))])]}}])})],1)],1),l("SolveWo",{ref:"solveWo",on:{success:e.search}}),l("CloseWo",{ref:"closeWo",on:{success:e.search}}),l("ViewWo",{ref:"viewWo"})],1)},o=[],s=(l("b64b"),l("d3b7"),l("ac1f"),l("841c"),l("0643"),l("4e3e"),l("159b"),l("6ecd")),i=l("f71b"),n=l("f5d2"),r=l("c9ab"),c=l("5edd"),u={1:"待解决",2:"已完成",3:"已关闭"},f={components:{PageTable:s["a"],SolveWo:i["a"],CloseWo:n["a"],ViewWo:r["a"],DeptTreeBox:c["a"]},data:function(){return{fullscreenLoading:!1,statusOptions:[],qform:{keyword:null},qdate:[],items:[]}},watch:{qdate:function(e){this.qform.reportBegin=e&&2===e.length?e[0]:null,this.qform.reportEnd=e&&2===e.length?e[1]:null}},mounted:function(){var e=this;Object.keys(u).forEach((function(t){e.statusOptions.push({value:t,text:u[t]})})),this.search()},methods:{getStatusType:function(e){switch(e.status){case"1":return"";case"2":return"success";case"3":return"info"}return""},getStatusText:function(e){return u[e.status]||""},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(e){this.items=e||[]},showOne:function(e,t){var l=this;t&&t.id||(t=1===this.items.length?this.items[0]:null),t&&e&&this.$http("/wo/bill/get/"+t.id).then((function(t){l.fullscreenLoading=!1,t.code>0&&t.data&&e.show(t.data)})).catch((function(){l.fullscreenLoading=!1,l.$message.error("网络超时")}))},solveWo:function(){this.showOne(this.$refs.solveWo)},closeWo:function(){this.showOne(this.$refs.closeWo)},preview:function(){this.showOne(this.$refs.viewWo)},viewItem:function(e){this.showOne(this.$refs.viewWo,e)}}},d=f,m=l("2877"),p=Object(m["a"])(d,a,o,!1,null,null,null);t["default"]=p.exports}}]);