<view class="container"><uni-forms class="vue-ref" vue-id="7322d29e-1" modelValue="{{dataModel}}" rules="{{formRules}}" border="{{true}}" label-align="right" label-width="80" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('7322d29e-2')+','+('7322d29e-1')}}" label="终端号" name="lastSn" bind:__l="__l" vue-slots="{{['default']}}"><uni-row vue-id="{{('7322d29e-3')+','+('7322d29e-2')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('7322d29e-4')+','+('7322d29e-3')}}" span="{{17}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-5')+','+('7322d29e-4')}}" type="text" size="mini" placeholder="请输入终端号" value="{{sn}}" data-event-opts="{{[['^input',[['__set_model',['','sn','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('7322d29e-6')+','+('7322d29e-3')}}" span="{{7}}" bind:__l="__l" vue-slots="{{['default']}}"><button class="ceshi" type="primary" plain="true" data-event-opts="{{[['tap',[['searchSn',['$event']]]]]}}" bindtap="__e">核查</button></uni-col></uni-row></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-7')+','+('7322d29e-1')}}" label="资产编码" name="assetNo" bind:__l="__l" vue-slots="{{['default']}}"><uni-row vue-id="{{('7322d29e-8')+','+('7322d29e-7')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('7322d29e-9')+','+('7322d29e-8')}}" span="{{17}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-10')+','+('7322d29e-9')}}" type="text" size="mini" placeholder="请输入资产编码" value="{{assetNo}}" data-event-opts="{{[['^input',[['__set_model',['','assetNo','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('7322d29e-11')+','+('7322d29e-8')}}" span="{{7}}" bind:__l="__l" vue-slots="{{['default']}}"><button class="ceshi" type="primary" plain="true" data-event-opts="{{[['tap',[['searchAssetNo',['$event']]]]]}}" bindtap="__e">核查</button></uni-col></uni-row></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-12')+','+('7322d29e-1')}}" label="网点用户" name="locationName" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-13')+','+('7322d29e-12')}}" type="text" value="{{dataModel.locationName}}" data-event-opts="{{[['^input',[['__set_model',['$0','locationName','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-14')+','+('7322d29e-1')}}" label="网点地址" name="locationAddress" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-15')+','+('7322d29e-14')}}" type="text" value="{{dataModel.locationAddress}}" data-event-opts="{{[['^input',[['__set_model',['$0','locationAddress','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-16')+','+('7322d29e-1')}}" label="联系人" required="{{true}}" name="contact" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-17')+','+('7322d29e-16')}}" type="text" value="{{dataModel.contact}}" data-event-opts="{{[['^input',[['__set_model',['$0','contact','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-18')+','+('7322d29e-1')}}" label="联系电话" required="{{true}}" name="phone" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-19')+','+('7322d29e-18')}}" type="text" value="{{dataModel.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-20')+','+('7322d29e-1')}}" label="规格型号" name="spec" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-21')+','+('7322d29e-20')}}" type="text" value="{{dataModel.spec}}" data-event-opts="{{[['^input',[['__set_model',['$0','spec','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-22')+','+('7322d29e-1')}}" label="序列号" name="sn" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-23')+','+('7322d29e-22')}}" type="text" value="{{dataModel.sn}}" data-event-opts="{{[['^input',[['__set_model',['$0','sn','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-24')+','+('7322d29e-1')}}" label="材料费" name="matCost" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-25')+','+('7322d29e-24')}}" type="number" value="{{dataModel.matCost}}" data-event-opts="{{[['^input',[['__set_model',['$0','matCost','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-26')+','+('7322d29e-1')}}" label="维修费" name="maiCost" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-27')+','+('7322d29e-26')}}" type="number" value="{{dataModel.maiCost}}" data-event-opts="{{[['^input',[['__set_model',['$0','maiCost','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-28')+','+('7322d29e-1')}}" label="故障现象" required="{{true}}" name="fault" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-29')+','+('7322d29e-28')}}" type="textarea" value="{{dataModel.fault}}" data-event-opts="{{[['^input',[['__set_model',['$0','fault','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-30')+','+('7322d29e-1')}}" label="是否保修" name="maintainFlag" bind:__l="__l" vue-slots="{{['default']}}"><radio-group data-event-opts="{{[['change',[['e0',['$event']]]]]}}" bindchange="__e"><label class="radio"><radio value="1" checked="true"></radio>是</label><label class="radio"><radio value="2"></radio>否</label></radio-group></uni-forms-item><uni-forms-item vue-id="{{('7322d29e-31')+','+('7322d29e-1')}}" label="维修情况" name="solve" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('7322d29e-32')+','+('7322d29e-31')}}" type="textarea" value="{{dataModel.solve}}" data-event-opts="{{[['^input',[['__set_model',['$0','solve','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><uni-file-picker vue-id="7322d29e-33" title="附件" limit="{{6}}" fileMediatype="image" mode="grid" value="{{imageValue}}" data-event-opts="{{[['^select',[['uploadSelect']]],['^delete',[['uploadDelete']]],['^input',[['__set_model',['','imageValue','$event',[]]]]]]}}" bind:select="__e" bind:delete="__e" bind:input="__e" bind:__l="__l"></uni-file-picker><view style="margin-top:20rpx;"><button type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button></view></view>