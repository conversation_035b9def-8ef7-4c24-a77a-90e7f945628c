(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aaf50958"],{"1bbe":function(e,t,a){"use strict";a("4b15")},"30a2":function(e,t,a){},"4b15":function(e,t,a){},"6b08":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增盘点计划")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.download}},[e._v("选中导出")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.exportQuery}},[e._v("根据条件导出")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:e.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入盘点名称",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"盘点状态："}},[a("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:e.qform.status,callback:function(t){e.$set(e.qform,"status",t)},expression:"qform.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"盘点时间："}},[a("el-date-picker",{staticStyle:{width:"300px","margin-left":"20px"},attrs:{type:"daterange",clearable:"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",editable:""},model:{value:e.qDate,callback:function(t){e.qDate=t},expression:"qDate"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/inventory/page",stripe:"",border:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{label:"盘点名称",prop:"name",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"盘点日期",prop:"date",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"负责人",prop:"userName",width:"80"}}),a("el-table-column",{attrs:{label:"盘点备注",prop:"memo","min-width":"150"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",formatter:e.colStatus}}),a("el-table-column",{attrs:{label:"操作",width:"300","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-button",{attrs:{disabled:"2"!=s.status,size:"mini",type:"success"},on:{click:function(t){return e.finish(s)}}},[e._v("完成")]),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.detail(s)}}},[e._v("详情")]),a("el-button",{attrs:{disabled:"1"!=s.status,size:"mini",type:"warning"},on:{click:function(t){return e.edit(s)}}},[e._v("修改")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.del(s)}}},[e._v("删除")]),a("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download"},on:{click:function(t){return e.exportInventory(s)}}},[e._v("导出")])]}}])})],1)],1),a("detail-edit",{ref:"detailEdit",on:{success:e.search}}),a("detail-view",{ref:"detailView"})],1)},i=[],n=(a("99af"),a("b0c0"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"custom-class":"dialog-tab",width:"1100px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产盘点计划")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[e._v("盘点任务")]),a("el-menu-item",{attrs:{index:"2"}},[a("div",{staticStyle:{"line-height":"30px","margin-top":"8px"}},[a("el-badge",{staticClass:"item",attrs:{value:e.assetList.length}},[e._v("盘点资产")])],1)])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"550px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"baseform",attrs:{model:e.data,"status-icon":"",rules:e.baseRules,size:"small","label-width":"120px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"盘点名称：",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入盘点名称"},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"盘点日期：",prop:"date"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择盘点日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.date,callback:function(t){e.$set(e.data,"date",t)},expression:"data.date"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"负责人：",prop:"user"}},[a("user-chosen",{staticStyle:{width:"100%"},attrs:{type:"1",clearable:"",placeholder:"请选择负责人"},model:{value:e.data.user,callback:function(t){e.$set(e.data,"user",t)},expression:"data.user"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协助人：",prop:"userList"}},[a("user-chosen",{staticStyle:{width:"100%"},attrs:{type:"1",multiple:"",clearable:"",placeholder:"请选择负责人"},model:{value:e.userList,callback:function(t){e.userList=t},expression:"userList"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"部门/区域：",prop:"dept"}},[a("dept-region-chosen",{staticStyle:{width:"100%"},attrs:{simple:!1,multiple:"",clearable:"",placeholder:"请选择部门/区域"},on:{selected:e.changeRegion},model:{value:e.regions,callback:function(t){e.regions=t},expression:"regions"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDept"}},[a("dept-region-chosen",{staticStyle:{width:"100%"},attrs:{simple:!1,multiple:"",clearable:"",placeholder:"请选择使用部门"},on:{selected:e.changeUseDept},model:{value:e.useDeptRegions,callback:function(t){e.useDeptRegions=t},expression:"useDeptRegions"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"盘点备注：",prop:"memo"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",clearable:"",placeholder:"请输入盘点备注",rows:3,maxlength:250,"show-word-limit":""},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"550px"}},[a("div",{staticClass:"location-head"},[a("span",{staticClass:"location-title"},[e._v("盘点资产")]),a("div",{staticClass:"search-area"},[a("div",{staticClass:"filter-item"},[a("el-dropdown",{on:{command:e.handleCommand}},[a("el-button",{attrs:{type:"primary",size:"mini"}},[a("i",{staticClass:"el-icon-download"}),e._v(" 下载/导入/导出 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"downloadTerminal"}},[e._v("下载终端号模板")]),a("el-dropdown-item",{attrs:{command:"downloadAsset"}},[e._v("下载资产编号模板")]),a("el-dropdown-item",{attrs:{divided:"",command:"importTerminal"}},[e._v("导入终端号")]),a("el-dropdown-item",{attrs:{command:"importAsset"}},[e._v("导入资产编号")]),a("el-dropdown-item",{attrs:{divided:"",command:"exportAssets"}},[e._v("导出资产信息")])],1)],1)],1),a("div",{staticClass:"filter-item"},[a("el-button",{attrs:{type:"primary",size:"mini",icon:e.isAllSelected?"el-icon-close":"el-icon-check"},on:{click:e.toggleSelectAll}},[e._v(" "+e._s(e.isAllSelected?"清空":"全选")+" ")])],1),a("div",{staticClass:"filter-item"},[a("span",{staticClass:"filter-label"},[e._v("检索:")]),a("el-input",{staticStyle:{width:"150px"},attrs:{size:"mini",clearable:"",placeholder:"资产编码/名称",autocomplete:"off"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1),a("div",{staticClass:"filter-item"},[a("span",{staticClass:"filter-label"},[e._v("规格型号:")]),a("el-input",{staticStyle:{width:"150px"},attrs:{size:"mini",clearable:"",placeholder:"输入规格型号",autocomplete:"off"},model:{value:e.specKeyword,callback:function(t){e.specKeyword=t},expression:"specKeyword"}})],1),a("div",{staticClass:"filter-item"},[a("span",{staticClass:"filter-label"},[e._v("资产类型:")]),a("el-select",{staticStyle:{width:"180px"},attrs:{size:"mini",multiple:"","collapse-tags":"",clearable:"",placeholder:"请选择资产类型"},on:{change:e.filterAssets},model:{value:e.selectedTypes,callback:function(t){e.selectedTypes=t},expression:"selectedTypes"}},e._l(e.assetTypeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)])]),a("el-table",{ref:"grid",attrs:{data:e.filteredAssets,size:"small",border:""},on:{select:e.handleAssetSelect,"select-all":e.handleAssetSelectAll}},[a("el-table-column",{attrs:{type:"selection",width:"50",selectable:e.isAssetSelectable}}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"终端号",prop:"sn",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","header-align":"center","min-width":"200"}}),a("el-table-column",{attrs:{label:"规格型号",prop:"spec","header-align":"center","min-width":"150"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"所在区域",prop:"regionName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"所在地点",prop:"locationName",width:"120",align:"center"}})],1),e.filteredAssetsTotal.length>0?a("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[a("el-pagination",{attrs:{"current-page":e.assetPage.currentPage,"page-sizes":[10,20,50,100],"page-size":e.assetPage.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filteredAssetsTotal.length},on:{"size-change":e.handleAssetSizeChange,"current-change":e.handleAssetCurrentChange}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.trySave}},[e._v("提 交")])],1)],2):e._e(),a("el-dialog",{attrs:{title:"导入终端号",visible:e.terminalImportVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.terminalImportVisible=t}}},[e.importedData.length?a("div",[a("el-table",{ref:"importTable",staticStyle:{width:"100%"},attrs:{data:e.importedData,height:"400",border:""}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),e._l(e.importedHeaders,(function(t,s){return a("el-table-column",{key:s,attrs:{prop:"col"+s,label:t},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row["col"+s])+" ")]}}],null,!0)})})),a("el-table-column",{attrs:{label:"匹配状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.matched?a("el-tag",{attrs:{type:"success"}},[e._v("已匹配")]):a("el-tag",{attrs:{type:"info"}},[e._v("未匹配")])]}}])})],2),a("div",{staticStyle:{"margin-top":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[a("div",[a("span",[e._v("共 "),a("strong",[e._v(e._s(e.importedData.length))]),e._v(" 条数据")]),a("span",{staticStyle:{"margin-left":"15px"}},[a("el-tag",{attrs:{type:"success",size:"small"}},[e._v("已匹配: "+e._s(e.getMatchedCount(!0)))]),a("el-tag",{staticStyle:{"margin-left":"5px"},attrs:{type:"info",size:"small"}},[e._v("未匹配: "+e._s(e.getMatchedCount(!1)))])],1)]),a("div",[a("el-button",{on:{click:e.cancelImport}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirmImport}},[e._v("确认导入")])],1)])],1):a("div",[a("el-upload",{staticClass:"upload-demo",attrs:{drag:"",action:"/api/am/inventory/parseExcel",headers:e.uploadHeaders,"on-success":e.handleTerminalImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeUpload,accept:".xlsx,.xls"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传excel文件，且不超过10MB")])])],1)]),a("el-dialog",{attrs:{title:"导入资产编号",visible:e.assetImportVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.assetImportVisible=t}}},[e.importedData.length?a("div",[a("el-table",{ref:"importTable",staticStyle:{width:"100%"},attrs:{data:e.importedData,height:"400",border:""}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),e._l(e.importedHeaders,(function(t,s){return a("el-table-column",{key:s,attrs:{prop:"col"+s,label:t},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row["col"+s])+" ")]}}],null,!0)})})),a("el-table-column",{attrs:{label:"匹配状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.matched?a("el-tag",{attrs:{type:"success"}},[e._v("已匹配")]):a("el-tag",{attrs:{type:"info"}},[e._v("未匹配")])]}}])})],2),a("div",{staticStyle:{"margin-top":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[a("div",[a("span",[e._v("共 "),a("strong",[e._v(e._s(e.importedData.length))]),e._v(" 条数据")]),a("span",{staticStyle:{"margin-left":"15px"}},[a("el-tag",{attrs:{type:"success",size:"small"}},[e._v("已匹配: "+e._s(e.getMatchedCount(!0)))]),a("el-tag",{staticStyle:{"margin-left":"5px"},attrs:{type:"info",size:"small"}},[e._v("未匹配: "+e._s(e.getMatchedCount(!1)))])],1)]),a("div",[a("el-button",{on:{click:e.cancelImport}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirmImport}},[e._v("确认导入")])],1)])],1):a("div",[a("el-upload",{staticClass:"upload-demo",attrs:{drag:"",action:"/api/am/inventory/parseExcel",headers:e.uploadHeaders,"on-success":e.handleAssetImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeUpload,accept:".xlsx,.xls"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传excel文件，且不超过10MB")])])],1)])],1)},r=[],o=a("2909"),c=a("b85c"),d=(a("4de4"),a("7db0"),a("a630"),a("caad"),a("d81d"),a("fb6a"),a("a434"),a("4ec9"),a("2532"),a("3ca3"),a("4d90"),a("76d6"),a("2382"),a("fffc"),a("a573"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("ad10")),u=a("92cc"),h=a("5f87"),p={components:{UserChosen:d["a"],DeptRegionChosen:u["a"]},data:function(){return{visible:!1,activeIndex:"1",data:{},regions:[],useDeptRegions:[],baseRules:{date:[{required:!0,message:"请选择盘点日期",trigger:"blur"}],name:[{required:!0,message:"请输入盘点名称",trigger:"blur"}],user:[{required:!0,message:"请选中盘点负责人",trigger:"blur"}]},userList:[],qform:{keyword:""},assetList:[],tableData:[],keyword:"",specKeyword:"",selectedTypes:[],selectedAssetIds:[],assetPage:{currentPage:1,pageSize:20},terminalImportVisible:!1,assetImportVisible:!1,uploadHeaders:{Authorization:"Bearer "+Object(h["a"])()},importedData:[],importedHeaders:[],importType:"",selectedImportItems:[],deptTreeData:[]}},computed:{assetTypeOptions:function(){var e=new Map;return this.assetList.forEach((function(t){t.type&&t.typeName&&!e.has(t.type)&&e.set(t.type,{value:t.type,label:t.typeName})})),Array.from(e.values())},filteredAssets:function(){var e=this,t=this.assetList.filter((function(t){var a,s,i,n,l=!e.keyword||(null===(a=t.typeName)||void 0===a?void 0:a.toLowerCase().includes(e.keyword.toLowerCase()))||(null===(s=t.no)||void 0===s?void 0:s.toLowerCase().includes(e.keyword.toLowerCase()))||(null===(i=t.name)||void 0===i?void 0:i.toLowerCase().includes(e.keyword.toLowerCase())),r=!e.specKeyword||(null===(n=t.spec)||void 0===n?void 0:n.toLowerCase().includes(e.specKeyword.toLowerCase())),o=0===e.selectedTypes.length||e.selectedTypes.includes(t.type);return l&&r&&o})),a=(this.assetPage.currentPage-1)*this.assetPage.pageSize,s=a+this.assetPage.pageSize;return t.slice(a,s)},filteredAssetsTotal:function(){var e=this;return this.assetList.filter((function(t){var a,s,i,n,l=!e.keyword||(null===(a=t.typeName)||void 0===a?void 0:a.toLowerCase().includes(e.keyword.toLowerCase()))||(null===(s=t.no)||void 0===s?void 0:s.toLowerCase().includes(e.keyword.toLowerCase()))||(null===(i=t.name)||void 0===i?void 0:i.toLowerCase().includes(e.keyword.toLowerCase())),r=!e.specKeyword||(null===(n=t.spec)||void 0===n?void 0:n.toLowerCase().includes(e.specKeyword.toLowerCase())),o=0===e.selectedTypes.length||e.selectedTypes.includes(t.type);return l&&r&&o}))},isAllSelected:function(){var e=this;if(0===this.filteredAssetsTotal.length)return!1;var t=this.filteredAssetsTotal.map((function(e){return e.id}));return t.every((function(t){return e.selectedAssetIds.includes(t)}))}},watch:{keyword:function(){var e=this;this.assetPage.currentPage=1,this.$nextTick((function(){e.restoreSelectionState()}))},specKeyword:function(){var e=this;this.assetPage.currentPage=1,this.$nextTick((function(){e.restoreSelectionState()}))}},mounted:function(){this.loadDeptTree()},methods:{loadDeptTree:function(){var e=this;this.$http("/sys/dept/tree").then((function(t){e.deptTreeData=t||[]}))},findDeptNo:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.deptTreeData,s=Object(c["a"])(a);try{for(s.s();!(t=s.n()).done;){var i,n,l=t.value;if(l.id===e)return(null===(i=l.tag)||void 0===i?void 0:i.no)||null;if(null!==(n=l.children)&&void 0!==n&&n.length){var r=this.findDeptNo(e,l.children);if(r)return r}}}catch(o){s.e(o)}finally{s.f()}return null},findDeptPath:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.deptTreeData,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=Object(c["a"])(a);try{for(i.s();!(t=i.n()).done;){var n,l=t.value,r=[].concat(Object(o["a"])(s),[l.id]);if(l.id===e)return r;if(null!==(n=l.children)&&void 0!==n&&n.length){var d=this.findDeptPath(e,l.children,r);if(d)return d}}}catch(u){i.e(u)}finally{i.f()}return null},show:function(e){var t=this;this.clear(),this.data=e||{assetList:[]};var a=[];this.data.userList&&this.data.userList.forEach((function(e){a.push(e.user)})),this.userList=a;var s=[],i=[];this.data.regionList&&this.data.regionList.forEach((function(e){if(e.ownerDept){var a=t.findDeptPath(e.ownerDept);a?s.push(a):s.push([e.ownerDept])}})),this.data.useDeptList&&this.data.useDeptList.forEach((function(e){if(e.dept){var a=t.findDeptPath(e.dept);a?i.push(a):i.push([e.dept])}})),this.regions=s,this.useDeptRegions=i,this.data.assetList&&this.data.assetList.length>0?this.selectedAssetIds=this.data.assetList.map((function(e){return e.id})):this.selectedAssetIds=[],this.visible=!0,this.regions.length>0||this.useDeptRegions.length>0?this.loadAssetsByRegions():this.assetList=[]},clear:function(){this.activeIndex="1",this.data={},this.regions=[],this.useDeptRegions=[],this.userList=[],this.assetList=[],this.keyword="",this.specKeyword="",this.selectedTypes=[],this.selectedAssetIds=[],this.assetPage.currentPage=1,this.assetPage.pageSize=20},filterAssets:function(){var e=this;this.assetPage.currentPage=1,this.$nextTick((function(){e.restoreSelectionState()}))},isAssetSelectable:function(e){return!0},handleAssetSelect:function(e,t){var a=t.id;if(this.selectedAssetIds.includes(a)){var s=this.selectedAssetIds.indexOf(a);this.selectedAssetIds.splice(s,1)}else this.selectedAssetIds.push(a)},handleAssetSelectAll:function(e){var t=this,a=this.filteredAssets.map((function(e){return e.id}));0===e.length?a.forEach((function(e){var a=t.selectedAssetIds.indexOf(e);a>-1&&t.selectedAssetIds.splice(a,1)})):a.forEach((function(e){t.selectedAssetIds.includes(e)||t.selectedAssetIds.push(e)}))},handleAssetSizeChange:function(e){var t=this;this.assetPage.pageSize=e,this.assetPage.currentPage=1,this.$nextTick((function(){t.restoreSelectionState()}))},handleAssetCurrentChange:function(e){var t=this;this.assetPage.currentPage=e,this.$nextTick((function(){t.restoreSelectionState()}))},restoreSelectionState:function(){var e=this;this.$refs.grid&&(this.$refs.grid.clearSelection(),this.filteredAssets.forEach((function(t){e.selectedAssetIds.includes(t.id)&&e.$refs.grid.toggleRowSelection(t,!0)})))},toggleSelectAll:function(){var e=this;if(this.isAllSelected)this.selectedAssetIds=[],this.$nextTick((function(){e.restoreSelectionState()}));else{var t=this.filteredAssetsTotal.map((function(e){return e.id}));t.forEach((function(t){e.selectedAssetIds.includes(t)||e.selectedAssetIds.push(t)})),this.$nextTick((function(){e.restoreSelectionState()})),this.$message.success("已选中 ".concat(t.length," 条资产"))}},handleSelectMenu:function(e){this.activeIndex=e},loadAssetsByRegions:function(){var e=this;if(0!==this.regions.length||0!==this.useDeptRegions.length){var t={regionList:[]};this.regions.forEach((function(e){e.length>0&&t.regionList.push({dept:e[e.length-1]})})),this.useDeptRegions.forEach((function(e){e.length>0&&t.regionList.push({useDept:e[e.length-1]})})),this.$http({url:"/am/asset/dept",data:t}).then((function(t){t.code>0&&(e.assetList=t.data||[],e.$nextTick((function(){e.restoreSelectionState()})))})).catch((function(t){e.assetList=[]}))}else this.assetList=[]},changeRegion:function(){this.assetPage.currentPage=1,this.selectedAssetIds=[],this.loadAssetsByRegions()},changeUseDept:function(){this.assetPage.currentPage=1,this.selectedAssetIds=[],this.loadAssetsByRegions()},removeAsset:function(e){var t=this;this.$confirm("确定要移除资产【"+this.assetList[e].name+"】吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.assetList.splice(e,1)}))},trySave:function(){var e=this;this.$refs.baseform.validate((function(t){if(t){if(0===e.regions.length&&0===e.useDeptRegions.length)return void e.$message.error("请至少选择部门/区域或使用部门其中一个");if(0===e.selectedAssetIds.length)return void e.$message.error("请至少选择一个资产进行盘点");e.data.userList=e.userList,e.data.regionList=[],e.data.useDeptList=[],e.regions.forEach((function(t){if(t.length){var a=t[t.length-1],s=e.findDeptNo(a);e.data.regionList.push({value:a,text:s})}})),e.useDeptRegions.forEach((function(t){if(t.length){var a=t[t.length-1],s=e.findDeptNo(a);e.data.useDeptList.push({value:a,text:s})}})),e.data.assetList=Object(o["a"])(e.selectedAssetIds),e.save()}}))},save:function(){var e=this,t=this.selectedAssetIds.length>100?"正在保存 ".concat(this.selectedAssetIds.length," 条资产数据，请耐心等待..."):"正在保存...",a=this.$loading({lock:!0,text:t,spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$http({url:"/am/inventory/save",data:this.data}).then((function(t){a.close(),t.code>0&&(e.visible=!1,e.$emit("success"),e.$message.success("保存成功"))})).catch((function(t){a.close(),e.$message.error("保存失败: "+(t.message||"网络错误"))}))},_filterChange:function(e){this.assetList=this.$refs.grid.assetList.length},handleCommand:function(e){"downloadTerminal"===e?this.downloadTemplate("terminal_template.xlsx","终端号模板.xlsx"):"downloadAsset"===e?this.downloadTemplate("asset_template.xlsx","资产编号模板.xlsx"):"importTerminal"===e?this.importTerminal():"importAsset"===e?this.importAsset():"exportAssets"===e&&this.exportAssets()},downloadTemplate:function(e,t){var a="/templates/"+e,s=document.createElement("a");s.href=a,s.setAttribute("download",t),document.body.appendChild(s),s.click(),document.body.removeChild(s),this.$message.success(t+"下载成功")},exportAssets:function(){var e=this;if(0!==this.filteredAssetsTotal.length){var t={deptIds:[],useDeptIds:[],keyword:this.keyword||"",spec:this.specKeyword||"",types:this.selectedTypes.length>0?this.selectedTypes:[]};this.regions.forEach((function(e){e.length>0&&t.deptIds.push(e[e.length-1])})),this.useDeptRegions.forEach((function(e){e.length>0&&t.useDeptIds.push(e[e.length-1])})),0!==t.deptIds.length||0!==t.useDeptIds.length||t.keyword||t.spec||0!==t.types.length?this.$http({url:"/am/asset/exportInventory",method:"post",data:t,responseType:"blob"}).then((function(t){var a=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),s=window.URL.createObjectURL(a),i=document.createElement("a");i.href=s;var n=new Date,l=n.getFullYear()+"-"+String(n.getMonth()+1).padStart(2,"0")+"-"+String(n.getDate()).padStart(2,"0")+"_"+String(n.getHours()).padStart(2,"0")+"-"+String(n.getMinutes()).padStart(2,"0");i.setAttribute("download","盘点资产信息_".concat(l,".xlsx")),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(s),e.$message.success("资产信息导出成功")})).catch((function(t){e.$message.error("导出失败: "+(t.message||"网络错误"))})):this.$message.warning("请先选择部门/区域或使用部门，或设置其他筛选条件")}else this.$message.warning("没有可导出的资产数据")},importTerminal:function(){this.importType="terminal",this.importedData=[],this.importedHeaders=[],this.terminalImportVisible=!0},importAsset:function(){this.importType="asset",this.importedData=[],this.importedHeaders=[],this.assetImportVisible=!0},beforeUpload:function(e){var t="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type,a=e.size/1024/1024<10;return t?!!a||(this.$message.error("文件大小不能超过10MB!"),!1):(this.$message.error("只能上传Excel文件!"),!1)},handleTerminalImportSuccess:function(e,t,a){e.code>0&&e.data?this.processImportedData(e.data,"terminal"):this.$message.error(e.msg||"导入失败")},handleAssetImportSuccess:function(e,t,a){e.code>0&&e.data?this.processImportedData(e.data,"asset"):this.$message.error(e.msg||"导入失败")},processImportedData:function(e,t){var a=this;if(e&&e.length){this.importedHeaders=e[0];for(var s=[],i=function(){var i=e[n];if(!i||!i.length)return 1;for(var l={matched:!1},r=0;r<a.importedHeaders.length;r++)l["col"+r]=i[r]||"";if("terminal"===t){var o=l.col0,c=a.assetList.find((function(e){return e.sn===o}));c&&(l.matched=!0,l.assetId=c.id,l.assetName=c.name,l.assetNo=c.no)}else if("asset"===t){var d=l.col0,u=a.assetList.find((function(e){return e.no===d}));u&&(l.matched=!0,l.assetId=u.id,l.assetName=u.name)}s.push(l)},n=1;n<e.length;n++)i();this.importedData=s}else this.$message.error("导入的文件没有数据")},handleImportError:function(e,t,a){this.$message.error("导入失败: "+e)},cancelImport:function(){this.importedData=[],this.importedHeaders=[],"terminal"===this.importType?this.terminalImportVisible=!1:this.assetImportVisible=!1},confirmImport:function(){var e=this.$refs.importTable?this.$refs.importTable.selection:[];if(e&&e.length){var t=e.filter((function(e){return e.matched})).map((function(e){return e.assetId}));0!==t.length?(this.selectAssetsInTable(t),this.cancelImport(),this.$message.success("已选中 ".concat(t.length," 条匹配的资产"))):this.$message.warning("所选数据中没有匹配的资产")}else this.$message.warning("请选择要导入的数据")},selectAssetsInTable:function(e){var t=this;this.$refs.grid&&(e.forEach((function(e){t.selectedAssetIds.includes(e)||t.selectedAssetIds.push(e)})),this.restoreSelectionState())},getMatchedCount:function(e){return this.importedData&&this.importedData.length?this.importedData.filter((function(t){return t.matched===e})).length:0}}},m=p,g=(a("1bbe"),a("2877")),f=Object(g["a"])(m,l,r,!1,null,"93153b3a",null),v=f.exports,b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"custom-class":"dialog-tab",width:"1100px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产盘点详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[e._v("盘点任务")]),a("el-menu-item",{attrs:{index:"2"}},[a("div",{staticStyle:{"line-height":"30px","margin-top":"8px"}},[a("el-badge",{staticClass:"item",attrs:{value:e.data.doingList.length}},[e._v("待盘资产")])],1)]),a("el-menu-item",{attrs:{index:"3"}},[a("div",{staticStyle:{"line-height":"30px","margin-top":"8px"}},[a("el-badge",{staticClass:"item",attrs:{value:e.data.doneList.length}},[e._v("已盘资产")])],1)]),a("el-menu-item",{attrs:{index:"4"}},[a("div",{staticStyle:{"line-height":"30px","margin-top":"8px"}},[a("el-badge",{staticClass:"item",attrs:{value:e.data.otherList.length}},[e._v("盘盈资产")])],1)])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"550px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"baseform",attrs:{model:e.data,"status-icon":"",size:"small","label-width":"120px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"盘点名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",attrs:{readonly:""},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"盘点日期：",prop:"date"}},[a("el-input",{staticClass:"form-static",attrs:{readonly:""},model:{value:e.data.date,callback:function(t){e.$set(e.data,"date",t)},expression:"data.date"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"负责人：",prop:"user"}},[a("el-input",{staticClass:"form-static",attrs:{readonly:""},model:{value:e.data.userName,callback:function(t){e.$set(e.data,"userName",t)},expression:"data.userName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协助人：",prop:"userList"}},[a("el-input",{staticClass:"form-static",attrs:{readonly:""},model:{value:e.userListText,callback:function(t){e.userListText=t},expression:"userListText"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"部门/区域：",prop:"region"}},[a("dept-region-chosen",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{simple:!1,multiple:"",disabled:"",placeholder:"部门/区域"},model:{value:e.regions,callback:function(t){e.regions=t},expression:"regions"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDept"}},[a("dept-region-chosen",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{simple:!1,multiple:"",disabled:"",placeholder:"使用部门"},model:{value:e.useDeptRegions,callback:function(t){e.useDeptRegions=t},expression:"useDeptRegions"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"盘点备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{type:"textarea",readonly:"",rows:3},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"550px"}},[a("el-table",{attrs:{data:e.currentDoingList,size:"small",border:""}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"终端号",prop:"sn",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","header-align":"center","min-width":"250"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"所在区域",prop:"regionName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"所在地点",prop:"locationName",width:"120",align:"center"}})],1),e.data.doingList&&e.data.doingList.length>0?a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"center"},attrs:{"current-page":e.doingPage.currentPage,"page-sizes":[10,20,50,100],"page-size":e.doingPage.pageSize,total:e.data.doingList.length,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.handleDoingSizeChange,"current-change":e.handleDoingCurrentChange}}):e._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"550px"}},[a("el-table",{attrs:{data:e.currentDoneList,size:"small",border:""}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"终端号",prop:"sn",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","header-align":"center","min-width":"250"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"所在区域",prop:"regionName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"所在地点",prop:"locationName",width:"120",align:"center"}})],1),e.data.doneList&&e.data.doneList.length>0?a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"center"},attrs:{"current-page":e.donePage.currentPage,"page-sizes":[10,20,50,100],"page-size":e.donePage.pageSize,total:e.data.doneList.length,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.handleDoneSizeChange,"current-change":e.handleDoneCurrentChange}}):e._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===e.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"550px"}},[a("el-table",{attrs:{data:e.currentOtherList,size:"small",border:""}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"终端号",prop:"sn",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","header-align":"center","min-width":"250"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"所在区域",prop:"regionName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"所在地点",prop:"locationName",width:"120",align:"center"}})],1),e.data.otherList&&e.data.otherList.length>0?a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"center"},attrs:{"current-page":e.otherPage.currentPage,"page-sizes":[10,20,50,100],"page-size":e.otherPage.pageSize,total:e.data.otherList.length,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.handleOtherSizeChange,"current-change":e.handleOtherCurrentChange}}):e._e()],1)],2):e._e()],1)},y=[],w=(a("a15b"),{components:{DeptRegionChosen:u["a"]},data:function(){return{visible:!1,activeIndex:"1",data:{},userListText:"",regions:[],useDeptRegions:[],deptTreeData:[],doingPage:{currentPage:1,pageSize:20},donePage:{currentPage:1,pageSize:20},otherPage:{currentPage:1,pageSize:20}}},computed:{currentDoingList:function(){if(!this.data.doingList||0===this.data.doingList.length)return[];var e=(this.doingPage.currentPage-1)*this.doingPage.pageSize,t=e+this.doingPage.pageSize;return this.data.doingList.slice(e,t)},currentDoneList:function(){if(!this.data.doneList||0===this.data.doneList.length)return[];var e=(this.donePage.currentPage-1)*this.donePage.pageSize,t=e+this.donePage.pageSize;return this.data.doneList.slice(e,t)},currentOtherList:function(){if(!this.data.otherList||0===this.data.otherList.length)return[];var e=(this.otherPage.currentPage-1)*this.otherPage.pageSize,t=e+this.otherPage.pageSize;return this.data.otherList.slice(e,t)}},mounted:function(){this.loadDeptTree()},methods:{loadDeptTree:function(){var e=this;this.$http("/sys/dept/tree").then((function(t){e.deptTreeData=t||[]}))},findDeptNo:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.deptTreeData,s=Object(c["a"])(a);try{for(s.s();!(t=s.n()).done;){var i,n,l=t.value;if(l.id===e)return(null===(i=l.tag)||void 0===i?void 0:i.no)||null;if(null!==(n=l.children)&&void 0!==n&&n.length){var r=this.findDeptNo(e,l.children);if(r)return r}}}catch(o){s.e(o)}finally{s.f()}return null},findDeptPath:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.deptTreeData,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=Object(c["a"])(a);try{for(i.s();!(t=i.n()).done;){var n,l=t.value,r=[].concat(Object(o["a"])(s),[l.id]);if(l.id===e)return r;if(null!==(n=l.children)&&void 0!==n&&n.length){var d=this.findDeptPath(e,l.children,r);if(d)return d}}}catch(u){i.e(u)}finally{i.f()}return null},show:function(e){var t=this;this.data=e||{doingList:[],doneList:[],otherList:[]};var a=[];this.data.userList&&this.data.userList.forEach((function(e){a.push(e.userName)})),this.userListText=a.join(", ");var s=[],i=[];this.data.regionList&&this.data.regionList.forEach((function(e){if(e.ownerDept){var a=t.findDeptPath(e.ownerDept);a?s.push(a):s.push([e.ownerDept])}})),this.data.useDeptList&&this.data.useDeptList.forEach((function(e){if(e.dept){var a=t.findDeptPath(e.dept);a?i.push(a):i.push([e.dept])}})),this.regions=s,this.useDeptRegions=i,this.resetPagination(),this.visible=!0},handleSelectMenu:function(e){this.activeIndex=e},resetPagination:function(){this.doingPage.currentPage=1,this.donePage.currentPage=1,this.otherPage.currentPage=1},handleDoingSizeChange:function(e){this.doingPage.pageSize=e,this.doingPage.currentPage=1},handleDoingCurrentChange:function(e){this.doingPage.currentPage=e},handleDoneSizeChange:function(e){this.donePage.pageSize=e,this.donePage.currentPage=1},handleDoneCurrentChange:function(e){this.donePage.currentPage=e},handleOtherSizeChange:function(e){this.otherPage.pageSize=e,this.otherPage.currentPage=1},handleOtherCurrentChange:function(e){this.otherPage.currentPage=e}}}),x=w,S=(a("a8f3"),Object(g["a"])(x,b,y,!1,null,"13f80fbe",null)),_=S.exports,L=a("5c96"),D=a("ed08"),C={1:"待盘点",2:"正在盘点",3:"已盘点",8:"已作废"},$={components:{PageTable:n["a"],DetailEdit:v,DetailView:_},data:function(){return{fullscreenLoading:!1,qform:{keyword:null},more:!1,regions:[],statusOptions:Object(D["h"])(C),qDate:[],items:[]}},watch:{qDate:function(e){null!=e&&(this.qform.begin=e&&2===e.length?e[0]:null,this.qform.end=e&&2===e.length?e[1]:null)}},mounted:function(){var e=this;this.search(),this.$route.query&&"create"===this.$route.query.module&&this.$nextTick((function(){e.add()}))},methods:{colStatus:function(e){return C[e.status]},search:function(){this.$refs.grid.search(this.qform)},add:function(){this.$refs.detailEdit.show()},del:function(e){var t=this;this.$confirm("您确定要永久删除这个盘点任务吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http("/am/inventory/delete/"+e.id).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.search())}))})).catch((function(){}))},finish:function(e){var t=this;this.$confirm("您确定设置完成这个盘点任务吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http("/am/inventory/finish/"+e.id).then((function(e){e.code>0&&(t.$message.success("设置完成成功"),t.search())}))})).catch((function(){}))},edit:function(e){var t=this;this.$http("/am/inventory/get/"+e.id).then((function(e){e.code>0&&e.data&&t.$refs.detailEdit.show(e.data)})).catch((function(){t.$message.error("网络超时")}))},detail:function(e){var t=this;this.fullscreenLoading=!0,this.$http("/am/inventory/get/"+e.id).then((function(e){t.fullscreenLoading=!1,e.code>0&&e.data&&t.$refs.detailView.show(e.data)})).catch((function(){t.fullscreenLoading=!1,t.$message.error("网络超时")}))},exportInventory:function(e){var t=this;this.$confirm("您确定要导出这个盘点任务的资产吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=t.$loading({fullscreen:!0,text:"正在导出文件，请耐心等待..."});t.$jasper({url:"/am/inventory/export/"+e.id,responseType:"blob"}).then((function(s){a.close(),t.$saveAs(s,"盘点资产_".concat(e.name,"_").concat(e.date,".xlsx"))})).catch((function(e){a.close(),t.$message.error("导出生成出错:"+e)}))})).catch((function(){}))},handleSelectionChange:function(e){this.items=e},download:function(){var e=this;if(0===this.items.length)return this.$message.warning("至少要选择一条盘点记录");var t=[];this.items.forEach((function(e){return t.push(e.id)}));var a=L["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/am/inventory/exportBatch",data:t,responseType:"blob"}).then((function(t){a.close(),e.$saveAs(t,"资产盘点明细.xlsx")})).catch((function(t){a.close(),e.$message.error("导出生成出错:"+t)}))},exportQuery:function(){var e=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=L["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});e.$jasper({url:"/am/inventory/exportAll",data:e.qform,responseType:"blob"}).then((function(a){t.close(),e.$saveAs(a,"资产盘点明细.xlsx")})).catch((function(a){t.close(),e.$message.error("导出生成出错:"+a)}))}))}}},k=$,z=Object(g["a"])(k,s,i,!1,null,null,null);t["default"]=z.exports},"6ecd":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},i=[],n=a("53ca"),l=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),r={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var s={pageNumber:1},i=Object(n["a"])(e);"undefined"===i?s.pageNumber=1:"number"===i?s.pageNumber=e:"object"===i?(this.params=e,"number"===typeof t&&(s.pageNumber=t),"boolean"===typeof t&&this.empty()):s.pageNumber=e.pageNumber,this.pi=s.pageNumber,this.paging&&(this.params.pageNumber=s.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(l["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],s=0;s<t.length;s++)t[s][e]&&a.push(t[s][e]);return a}}},o=r,c=(a("b2d4"),a("2877")),d=Object(c["a"])(o,s,i,!1,null,"bdcc19d8",null);t["a"]=d.exports},"92cc":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-cascader",e._g(e._b({attrs:{options:e.options,props:e.props},on:{change:e.changeMe}},"el-cascader",e.$attrs,!1),e.$listeners))},i=[],n=a("b775"),l={name:"DeptRegionChosen",props:{anyNode:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},simple:{type:Boolean,default:!0}},data:function(){return{options:[],props:{checkStrictly:this.anyNode,multiple:this.multiple,value:"id"}}},mounted:function(){this.load()},methods:{load:function(){var e=this;Object(n["a"])("/sys/dept/tree").then((function(t){t.length&&(e.options=t)}))},changeMe:function(e){this.simple?this.$emit("input",e&&e.length?e[e.length-1]:null):this.$emit("input",e),this.$emit("selected",e)}}},r=l,o=a("2877"),c=Object(o["a"])(r,s,i,!1,null,null,null);t["a"]=c.exports},a8f3:function(e,t,a){"use strict";a("30a2")},ac65:function(e,t,a){},ad10:function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",e._g(e._b({attrs:{loading:e.loading,filterable:"","filter-method":e.filter}},"el-select",e.$attrs,!1),e.$listeners),e._l(e.options,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.no||t.account))])])})),1)},i=[],n=(a("4de4"),a("b0c0"),a("d3b7"),a("0643"),a("2382"),a("4e3e"),a("159b"),a("b775")),l={name:"UserChosen",props:{type:{type:String,default:null}},data:function(){return{loading:!1,options:[],list:[]}},mounted:function(){this.load()},methods:{load:function(){var e=this;this.loading=!0,Object(n["a"])("/sys/user/list/"+(this.type||"all")).then((function(t){e.loading=!1,t&&t.length&&(e.list=t,e.filter())})).catch((function(t){e.loading=!1,console.log(t)}))},getData:function(){return this.options},filter:function(e){if(e){var t=[];this.options.forEach((function(a){if(a.name&&-1!==a.name.indexOf(e))t.push(a);else if(a.no){if(-1!==a.no.indexOf(e))return void t.push(a)}else if(a.account&&-1!==a.account.indexOf(e))return void t.push(a)})),this.options=t}else this.options=this.list}}},r=l,o=a("2877"),c=Object(o["a"])(r,s,i,!1,null,null,null);t["a"]=c.exports},b2d4:function(e,t,a){"use strict";a("ac65")}}]);