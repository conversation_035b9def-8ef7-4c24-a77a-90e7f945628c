(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-39031f02"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"6ecd":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},o=[],i=a("53ca"),l=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),n={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var r={pageNumber:1},o=Object(i["a"])(e);"undefined"===o?r.pageNumber=1:"number"===o?r.pageNumber=e:"object"===o?(this.params=e,"number"===typeof t&&(r.pageNumber=t),"boolean"===typeof t&&this.empty()):r.pageNumber=e.pageNumber,this.pi=r.pageNumber,this.paging&&(this.params.pageNumber=r.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(l["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],r=0;r<t.length;r++)t[r][e]&&a.push(t[r][e]);return a}}},s=n,c=(a("b2d4"),a("2877")),u=Object(c["a"])(s,r,o,!1,null,"bdcc19d8",null);t["a"]=u.exports},"841c":function(e,t,a){"use strict";var r=a("d784"),o=a("825a"),i=a("1d80"),l=a("129f"),n=a("14c3");r("search",1,(function(e,t,a){return[function(t){var a=i(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,a):new RegExp(t)[e](String(a))},function(e){var r=a(t,e,this);if(r.done)return r.value;var i=o(e),s=String(this),c=i.lastIndex;l(c,0)||(i.lastIndex=0);var u=n(i,s);return l(i.lastIndex,c)||(i.lastIndex=c),null===u?-1:u.index}]}))},ac65:function(e,t,a){},b2d4:function(e,t,a){"use strict";a("ac65")},f6b7:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter"},[a("el-form",{attrs:{model:e.qform},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"检索：","label-width":e.formLabelWidth}},[a("el-input",{attrs:{placeholder:"输入编码、名称",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{staticClass:"filter-item",attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)],1)],1)],1),a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/sys/region/page",query:e.qform,stripe:!0,border:!0}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"编码",prop:"code",width:"110"}}),a("el-table-column",{attrs:{label:"上级编码",prop:"pcode",width:"100"}}),a("el-table-column",{attrs:{label:"显示名称",prop:"name",width:"130"}}),a("el-table-column",{attrs:{label:"区划名称",prop:"regionName",width:"180"}}),a("el-table-column",{attrs:{label:"区划全称",prop:"fullName"}}),a("el-table-column",{attrs:{label:"排序",prop:"ord",width:"80"}}),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return a.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return a.stopPropagation(),e.remove(t.row)}}},[e._v("删除")])]}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"行政区域信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"上级编码：","label-width":e.formLabelWidth,prop:"pcode"}},[a("el-input",{class:e.readonlyCls,attrs:{type:"number",maxlength:"12",readonly:e.editing,autocomplete:"off"},model:{value:e.form.pcode,callback:function(t){e.$set(e.form,"pcode",t)},expression:"form.pcode"}})],1)],1),a("el-col",{staticClass:"form-memo",attrs:{span:12}},[e._v(" 最高级区域的上级为0 ")])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"区划编码：","label-width":e.formLabelWidth,prop:"code"}},[a("el-input",{class:e.readonlyCls,attrs:{type:"number",maxlength:"12",readonly:e.editing,autocomplete:"off"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1)],1),a("el-col",{staticClass:"form-memo",attrs:{span:12}},[e._v(" 上级编码+本级编码(区县2位、镇3位、村3位) ")])],1),a("el-form-item",{attrs:{label:"显示名称：","label-width":e.formLabelWidth,prop:"name"}},[a("el-input",{attrs:{maxlength:"64",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"区划名称：","label-width":e.formLabelWidth,prop:"regionName"}},[a("el-input",{attrs:{maxlength:"64",autocomplete:"off"},model:{value:e.form.regionName,callback:function(t){e.$set(e.form,"regionName",t)},expression:"form.regionName"}})],1),a("el-form-item",{attrs:{label:"区划全称：","label-width":e.formLabelWidth,prop:"fullName"}},[a("el-input",{attrs:{maxlength:"64",autocomplete:"off"},model:{value:e.form.fullName,callback:function(t){e.$set(e.form,"fullName",t)},expression:"form.fullName"}})],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"维度：","label-width":e.formLabelWidth,prop:"ord"}},[a("el-input-number",{attrs:{min:0,max:90,autocomplete:"off",controls:!1},model:{value:e.form.lat,callback:function(t){e.$set(e.form,"lat",t)},expression:"form.lat"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"经度：","label-width":e.formLabelWidth,prop:"ord"}},[a("el-input-number",{attrs:{min:0,max:180,autocomplete:"off",controls:!1},model:{value:e.form.lng,callback:function(t){e.$set(e.form,"lng",t)},expression:"form.lng"}})],1)],1)],1),a("el-form-item",{attrs:{label:"排序：","label-width":e.formLabelWidth,prop:"ord"}},[a("el-input-number",{attrs:{min:0,max:9999,autocomplete:"off",controls:!1},model:{value:e.form.ord,callback:function(t){e.$set(e.form,"ord",t)},expression:"form.ord"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},o=[],i=(a("ac1f"),a("841c"),a("6ecd")),l={components:{PageTable:i["a"]},data:function(){return{formLabelWidth:"100px",detailVisible:!1,editing:!1,readonlyCls:"",qform:{keyword:null},form:{},rules:{code:[{required:!0,message:"请输入区划编码",trigger:"blur"}],pcode:[{required:!0,message:"请输入上级编码",trigger:"blur"}],name:[{required:!0,message:"请输入显示名称",trigger:"blur"}],regionName:[{required:!0,message:"请输入区划名称",trigger:"blur"}],fullName:[{required:!0,message:"请输入区划全称",trigger:"blur"}]}}},watch:{editing:function(e,t){this.readonlyCls=e?"readonly":""}},created:function(){},mounted:function(){},methods:{search:function(){this.$refs.grid.search(this.qform)},add:function(){this.detailVisible=!0,this.editing=!1,this.form={ord:0},this.clearValidate()},detail:function(e){this.detailVisible=!0,this.editing=!0,this.form=Object.assign({},e),this.clearValidate()},remove:function(e){var t=this;this.$confirm("此操作将永久删除该区划, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/region/delete/"+e.code}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},clearValidate:function(){this.$refs.dataform&&this.$refs.dataform.clearValidate()},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&e.$http({url:"/sys/region/save",data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.search()),e.detailVisible=!1})).catch((function(){}))}))}}},n=l,s=a("2877"),c=Object(s["a"])(n,r,o,!1,null,null,null);t["default"]=c.exports}}]);