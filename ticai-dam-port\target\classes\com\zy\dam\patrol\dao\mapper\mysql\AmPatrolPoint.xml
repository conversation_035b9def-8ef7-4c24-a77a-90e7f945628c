<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPointDAO">

    <sql id="meta">
			a.ID_
			,a.GROUP_
			,a.NO_
			,a.NAME_
			,a.LNG_
			,a.LAT_
			,a.ADDRESS_
			,a.STATUS_
			,a.MEMO_
			,a.FLAG_
	</sql>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
        select
        <include refid="meta"/>
        ,b.NAME_ group_name
        from AM_PATROL_POINT a left join AM_PATROL_POINT_GROUP b on a.GROUP_=b.ID_
        where a.FLAG_='1'
        <if test="group != null">and a.GROUP_=#{group}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        order by a.NO_
    </select>

    <select id="listAll" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
		select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.ADDRESS_,b.NAME_ group_name
		from AM_PATROL_POINT a left join AM_PATROL_POINT_GROUP b on a.GROUP_=b.ID_
        where a.FLAG_='1' and a.LNG_ &gt; 0 and a.LAT_ &gt; 0
        order by a.NO_
    </select>

    <select id="listSimple" resultType="com.zy.model.SimpleData">
		select a.ID_,a.NO_,a.NAME_
		from AM_PATROL_POINT a
        where a.FLAG_='1'
        order by a.NO_
    </select>

    <!-- 获取唯一的资管-巡检点位数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPoint">
        select
        <include refid="meta"/>
        from AM_PATROL_POINT a where a.ID_=#{0}
    </select>

    <select id="listGroup" resultType="com.zy.dam.patrol.vo.PatrolPointGroup">
		select ID_,NAME_,ORD_ from AM_PATROL_POINT_GROUP where FLAG_='1' order by ORD_
	</select>

    <update id="updateLocation">
        update AM_PATROL_POINT set LNG_=#{1},LAT_=#{2},ADDRESS_=#{3} where ID_=#{0}
    </update>
</mapper>
