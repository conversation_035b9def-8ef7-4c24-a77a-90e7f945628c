{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js!F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue", "mtime": 1752649761445}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}