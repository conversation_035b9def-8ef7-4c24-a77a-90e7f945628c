(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c156f544"],{"0fa3":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter"},[a("div",{staticClass:"filter-item"},[a("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.addRoot}},[e._v("新增顶级机构")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:e.search}},[e._v("刷新")])],1)]),a("el-table",{ref:"treegrid",attrs:{"default-expand-all":"",border:"","row-key":"id",data:e.treeData,"tree-props":{children:"children"}}},[a("el-table-column",{attrs:{prop:"label",label:"名称"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return a("span",{},["0"==r.tag.pid?a("svg-icon",{attrs:{"icon-class":"building"}}):a("svg-icon",{attrs:{"icon-class":"door"}}),e._v(" "+e._s(r.label)+" ")],1)}}])}),a("el-table-column",{attrs:{prop:"opt",label:"操作",width:"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return["1"===r.tag.type&&"0"!=r.pid?[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.edit(r.tag)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:function(t){return e.addChild(r.tag)}}},[e._v("下级")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(t){return e.remove(r.tag)}}},[e._v("删除")])]:e._e()]}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"单位信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules}},[a("el-row",[null!=e.form.parentName?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上级单位：","label-width":e.formLabelWidth,prop:"parentName"}},[e._v(" "+e._s(e.form.parentName)+" ")])],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位名称：","label-width":e.formLabelWidth,prop:"name"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位编号：","label-width":e.formLabelWidth,prop:"no"}},[a("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.no,callback:function(t){e.$set(e.form,"no",t)},expression:"form.no"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位地址：","label-width":e.formLabelWidth,prop:"address"}},[a("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位排序：","label-width":e.formLabelWidth,prop:"ord"}},[a("el-input-number",{staticClass:"num-default",staticStyle:{width:"100%"},attrs:{autocomplete:"off",min:0,controls:!1},model:{value:e.form.ord,callback:function(t){e.$set(e.form,"ord",t)},expression:"form.ord"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"负责人：","label-width":e.formLabelWidth,prop:"manager"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.manager,callback:function(t){e.$set(e.form,"manager",t)},expression:"form.manager"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"手机号：","label-width":e.formLabelWidth,prop:"mobile"}},[a("el-input",{attrs:{maxlength:"11",autocomplete:"off"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},o=[];a("b0c0"),a("ac1f"),a("841c"),a("00b4");function l(e,t,a){if(!t){if(e.required)return a(new Error(e.name?e.name+"不能为空":"手机号码不能为空"));a()}var r=/^1[3-9]\d{9}$/;if(!r.test(t))return a(new Error(e.name?e.name+"不正确":"手机号码不正确"));a()}var i={data:function(){return{detailVisible:!1,formLabelWidth:"100px",treeData:[],form:{},rules:{name:[{required:!0,message:"请输入单位名称",trigger:"blur"}],mobile:[{trigger:"blur",validator:l}]}}},created:function(){this.search()},methods:{search:function(){var e=this;this.$http({url:"/sys/dept/tree"}).then((function(t){e.treeData=t})).catch((function(){e.$message.error("无法加载到组织结构树")}))},addRoot:function(){this.detailVisible=!0,this.form={}},addChild:function(e){this.detailVisible=!0,this.form={pid:e.id,parentName:e.name}},edit:function(e){this.detailVisible=!0,this.form=Object.assign({},e)},remove:function(e){var t=this;this.$confirm("此操作将永久删除该部门, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/dept/delete/"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&(e.form.type="1",e.$http({url:"/sys/dept/save",data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.detailVisible=!1,e.search())})).catch((function(){})))}))}}},n=i,s=a("2877"),c=Object(s["a"])(n,r,o,!1,null,null,null);t["default"]=c.exports},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"841c":function(e,t,a){"use strict";var r=a("d784"),o=a("825a"),l=a("1d80"),i=a("129f"),n=a("14c3");r("search",1,(function(e,t,a){return[function(t){var a=l(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,a):new RegExp(t)[e](String(a))},function(e){var r=a(t,e,this);if(r.done)return r.value;var l=o(e),s=String(this),c=l.lastIndex;i(c,0)||(l.lastIndex=0);var d=n(l,s);return i(l.lastIndex,c)||(l.lastIndex=c),null===d?-1:d.index}]}))}}]);