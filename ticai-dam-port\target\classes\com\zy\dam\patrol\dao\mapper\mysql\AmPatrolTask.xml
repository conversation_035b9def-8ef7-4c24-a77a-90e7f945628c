<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolTaskDAO">

    <sql id="meta">
			a.ID_
			,a.PLAN_
			,a.START_TIME_
			,a.END_TIME_
			,a.EXEC_STATUS_
			,a.NO_
			,a.PUSER_
			,a.EUSER_
			,a.EXEC_START_
			,a.EXEC_END_
			,a.START_LNG_
			,a.START_LAT_
			,a.END_LNG_
			,a.END_LAT_
			,a.RESULT_
			,a.MEMO_
			,a.PERIOD_STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.FLAG_
	</sql>

    <select id="findByPlan" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        from AM_PATROL_TASK a where a.FLAG_='1' and a.PLAN_=#{0}
        order by START_TIME_
    </select>

    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_
        <if test="keyword != null">and (b.NAME_ like concat('%',#{keyword},'%') or b.NO_ like concat('%',#{keyword},'%') )</if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="execStatus != null">and a.EXEC_STATUS_=#{execStatus}</if>
        <if test="userName != null">and (o.NAME_ like concat('%',#{userName},'%') or o.NO_ like concat('%',#{userName},'%') )</if>
        <if test="begin != null">and a.START_TIME_ &gt;=#{begin}</if>
        <if test="end != null">and a.END_TIME_ &lt; date_add(#{end}, interval 1 day)</if>
        order by a.START_TIME_
    </select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_
        and a.ID_=#{0}
    </select>

    <select id="query" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        ,case when a.START_TIME_&gt;now() then UNIX_TIMESTAMP(a.START_TIME_)-UNIX_TIMESTAMP(now()) else 0 end will_second
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_
        <if test="user != null">and a.EUSER_=#{user}</if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="periodStatusList != null">
            <foreach collection="periodStatusList" item="ps" open="and a.PERIOD_STATUS_ in (" separator="," close=")">#{ps}</foreach>
        </if>
        <if test="execStatus != null">and a.EXEC_STATUS_=#{execStatus}</if>
        order by a.START_TIME_
        limit #{offset},#{limit}
    </select>

    <select id="findPathByPlan" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
        select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.ADDRESS_,a.MEMO_
        from AM_PATROL_POINT a, AM_PATROL_PATH_POINT b, AM_PATROL_PLAN_ITEM c
        where a.FLAG_='1' and c.FLAG_='1' and a.ID_=b.POINT_ and b.PATH_=c.REF_
        and c.PLAN_=#{0}
        order by b.ORD_,a.NO_
    </select>

    <select id="findPointByPlan" resultType="com.zy.dam.patrol.vo.PatrolPointVo">
        select a.ID_,a.NO_,a.NAME_,a.LNG_,a.LAT_,a.ADDRESS_,a.MEMO_
        from AM_PATROL_POINT a, AM_PATROL_PLAN_ITEM c
        where a.ID_=c.REF_
        and c.PLAN_=#{0}
        order by c.ORD_,a.NO_
    </select>

    <select id="findAssetByPlan" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_
        ,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_
        ,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.FLAG_='1' and m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from SYS_USER m where m.FLAG_='1' and m.ID_=a.LOC_USER_) loc_user_name
        from AM_ASSET a, AM_PATROL_PLAN_ITEM c
        where a.ID_=c.REF_
        and c.PLAN_=#{0}
        order by c.ORD_,a.NO_
    </select>

    <select id="countTask" resultType="com.zy.dam.patrol.vo.PatrolTaskCount">
        select sum(doing) doing,sum(done) done,sum(abnormal) abnormal from (
        select count(0) doing, 0 done, 0 abnormal from AM_PATROL_TASK where EXEC_STATUS_ in ('0', '1') and EUSER_=#{0}
        union select 0 doing, count(0) done, 0 abnormal from AM_PATROL_TASK where EXEC_STATUS_ in ('2', '5') and EUSER_=#{0}
        union select 0 doing, 0 done, count(0) abnormal from AM_PATROL_TASK where RESULT_='2' and EUSER_=#{0}
        ) a
    </select>

    <select id="queryDoing" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        ,case when a.START_TIME_&gt;now() then UNIX_TIMESTAMP(a.START_TIME_)-UNIX_TIMESTAMP(now()) else 0 end will_second
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_ and a.EUSER_=#{user}
        and a.EXEC_STATUS_ in ('0','1')
        order by a.START_TIME_
        limit #{offset},#{limit}
    </select>

    <select id="queryDone" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        ,case when a.START_TIME_&gt;now() then UNIX_TIMESTAMP(a.START_TIME_)-UNIX_TIMESTAMP(now()) else 0 end will_second
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_ and a.EUSER_=#{user}
        and a.EXEC_STATUS_ in ('2','5')
        order by a.START_TIME_ desc
        limit #{offset},#{limit}
    </select>

    <select id="queryAbnormal" resultType="com.zy.dam.patrol.vo.PatrolTaskVo">
        select
        <include refid="meta"/>
        ,b.NAME_ plan_name,b.TYPE_ plan_type
        ,o.NAME_ euser_name,o.PHONE_ euser_phone
        ,case when a.START_TIME_&gt;now() then UNIX_TIMESTAMP(a.START_TIME_)-UNIX_TIMESTAMP(now()) else 0 end will_second
        from AM_PATROL_TASK a left join SYS_USER o on o.ID_=a.EUSER_, AM_PATROL_PLAN b
        where a.FLAG_='1' and b.FLAG_='1' and a.PLAN_=b.ID_ and a.EUSER_=#{user}
        and a.EXEC_STATUS_ in ('2','5') and a.RESULT_='2'
        order by a.START_TIME_ desc
        limit #{offset},#{limit}
    </select>

    <update id="updateResult">
        update AM_PATROL_TASK set EXEC_STATUS_='2',EXEC_START_=now(),RESULT_=#{result},MEMO_=#{memo},START_LNG_=#{startLng},START_LNG_=#{startLng} where ID_=#{id}
    </update>
</mapper>
