(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b9f79"],{"34ce":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("div",{staticClass:"page-filter"},[o("el-form",{attrs:{inline:"",size:"mini","label-width":"110px"}},[o("el-form-item",{attrs:{label:"统计月份："}},[o("el-date-picker",{attrs:{type:"month","value-format":"yyyy-MM",format:"yyyy年MM月",clearable:"",autocomplete:"off"},model:{value:t.month,callback:function(e){t.month=e},expression:"month"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadData}},[t._v("统计")]),o("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:t.exportMonth}},[t._v("导出明细")])],1)],1)],1),o("el-divider"),o("v-chart",{staticStyle:{width:"100%",height:"350px"},attrs:{autoresize:"",option:t.typeOptions}}),o("el-divider"),o("v-chart",{staticStyle:{width:"100%",height:"350px"},attrs:{autoresize:"",option:t.regionOptions}})],1)},a=[],n=(o("b0c0"),o("d3b7"),o("ac1f"),o("5319"),o("0643"),o("4e3e"),o("159b"),o("ed08")),r=o("5c96"),s={data:function(){return{month:Object(n["b"])().replace(/\-\d{2}$/,""),typeOptions:{title:{text:"故障类型分析",subtext:"",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}次"},legend:{bottom:10,left:"center"},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{type:"bar",name:"故障类型",data:[],label:{show:!0,position:"top",formatter:"{c} 次"}}]},regionOptions:{title:{text:"故障区域分布",subtext:"",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{bottom:10,left:"center"},series:[{type:"pie",name:"故障区域",radius:"65%",center:["50%","50%"],selectedMode:"single",emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:[],label:{formatter:"{name|{b}}\n{time|{c} 次}",minMargin:5,edgeDistance:10,lineHeight:15,rich:{time:{fontSize:10,color:"#36F"}}}}]}}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.month&&this.$http("/rp/wo/countMonth/"+this.month).then((function(e){if(e.code>0){var o=e.data,i=[],a=[];o.typeValues.forEach((function(t){i.push(t.name),a.push(t.value)})),t.typeOptions.xAxis.data=i,t.typeOptions.series[0].data=a,t.regionOptions.series[0].data=o.regionValues}}))},exportMonth:function(){var t=this;if(this.month){var e=r["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/rp/wo/exportMonth/"+this.month,responseType:"blob"}).then((function(o){e.close(),t.$saveAs(o,t.month+"_故障维护记录表.xlsx")})).catch((function(o){e.close(),t.$message.error("导出报表生成出错:"+o)}))}}}},l=s,c=o("2877"),p=Object(c["a"])(l,i,a,!1,null,null,null);e["default"]=p.exports}}]);