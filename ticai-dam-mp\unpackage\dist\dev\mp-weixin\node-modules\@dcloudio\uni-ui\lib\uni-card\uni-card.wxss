@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-card {
  margin: 10px;
  padding: 0 8px;
  border-radius: 4px;
  overflow: hidden;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  background-color: #fff;
  flex: 1;
}
.uni-card .uni-card__cover {
  position: relative;
  margin-top: 10px;
  flex-direction: row;
  overflow: hidden;
  border-radius: 4px;
}
.uni-card .uni-card__cover .uni-card__cover-image {
  flex: 1;
  vertical-align: middle;
}
.uni-card .uni-card__header {
  display: flex;
  border-bottom: 1px #EBEEF5 solid;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-box {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-avatar {
  width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 5px;
  margin-right: 10px;
}
.uni-card .uni-card__header .uni-card__header-avatar .uni-card__header-avatar-image {
  flex: 1;
  width: 40px;
  height: 40px;
}
.uni-card .uni-card__header .uni-card__header-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-content .uni-card__header-content-title {
  font-size: 15px;
  color: #3a3a3a;
}
.uni-card .uni-card__header .uni-card__header-content .uni-card__header-content-subtitle {
  font-size: 12px;
  margin-top: 5px;
  color: #909399;
}
.uni-card .uni-card__header .uni-card__header-extra {
  line-height: 12px;
}
.uni-card .uni-card__header .uni-card__header-extra .uni-card__header-extra-text {
  font-size: 12px;
  color: #909399;
}
.uni-card .uni-card__content {
  padding: 10px;
  font-size: 14px;
  color: #6a6a6a;
  line-height: 22px;
}
.uni-card .uni-card__actions {
  font-size: 12px;
}
.uni-card--border {
  border: 1px solid #EBEEF5;
}
.uni-card--shadow {
  position: relative;
  box-shadow: 0 0px 6px 1px rgba(165, 165, 165, 0.2);
}
.uni-card--full {
  margin: 0;
  border-left-width: 0;
  border-left-width: 0;
  border-radius: 0;
}
.uni-card--full:after {
  border-radius: 0;
}
.uni-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

