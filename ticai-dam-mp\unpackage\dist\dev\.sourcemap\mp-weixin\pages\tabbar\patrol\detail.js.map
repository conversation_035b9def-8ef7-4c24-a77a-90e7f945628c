{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?e7ee", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?fdb3", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?6f20", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?d519", "uni-app:///pages/tabbar/patrol/detail.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?78a9", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/detail.vue?6d8d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "task", "resultOption", "value", "text", "formData", "result", "memo", "lng", "lat", "address", "formRules", "rules", "required", "errorMessage", "imageValue", "activeTab", "doingCount", "abnormalCount", "normalCount", "fileList", "list", "onLoad", "ctx", "that", "callback", "methods", "formatTime", "item", "uploadSelect", "uploadDelete", "uploadFile", "url", "filePath", "header", "name", "success", "submit"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,8UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyBlzB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QAAAD;QAAAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAL;UACAM;YAAAC;YAAAC;UAAA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;QACA;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAD;UACAC;UACAA;UACAD;YACAf;YACAC;YACAgB;cACAD;YACA;UACA;QACA;MACA;IACA;MACAD;IACA;EACA;EACAG;IACAC;MACAC;IACA;IACAC;MAAA;MACA;QAAA;MAAA;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACApC;QACAqC;QACAC;QACAC;UAAA;UAAA;UAAA;QAAA;QACAC;QACAC;UACA;UACA;UACA;UACA;UACApC;UACAwB;QACA;MACA;IACA;IACAa;MACA;MACA;QACArC;QACAA;QACAwB;UACAxB;QACA;QACAuB;UACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAA4nC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACAhpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/patrol/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/patrol/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=3fdeb3fc&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/patrol/detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=3fdeb3fc&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms\" */ \"@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item\" */ \"@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniDataCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox\" */ \"@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniFilePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker\" */ \"@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<uni-forms ref=\"form\" :modelValue=\"formData\" :rules=\"formRules\">\r\n\t\t\t<uni-forms-item label=\"巡检单号\" class=\"form-static\">{{ task.no }}</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"巡检名称\" class=\"form-static\">{{ task.planName }}</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"计划时间\" class=\"form-static\">{{ task.timeText }}</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"巡检结论\" required name=\"result\">\r\n\t\t\t\t<uni-data-checkbox v-model=\"formData.result\" :localdata=\"resultOption\"></uni-data-checkbox>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"当前位置\" name=\"address\">\r\n\t\t\t\t<uni-easyinput type=\"text\" prefixIcon=\"location\" v-model=\"formData.address\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"结论描述\" name=\"memo\">\r\n\t\t\t\t<uni-easyinput type=\"textarea\" v-model=\"formData.memo\" placeholder=\"请输入结论描述\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t</uni-forms>\r\n\t\t<uni-file-picker v-model=\"imageValue\" title=\"附件\" :limit=\"6\" fileMediatype=\"image\" mode=\"grid\"\r\n\t\t\t@select=\"uploadSelect\" @delete=\"uploadDelete\" />\r\n\t\t<view style=\"margin-top: 20rpx;\">\r\n\t\t\t<button type=\"primary\" @click=\"submit\">提交结论</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport settings from '../../../utils/settings.js'\r\nimport * as ctx from '../../../utils/context.js'\r\nimport { formatTimeRange } from '../../../utils/timeUtils.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttask: {},\r\n\t\t\tresultOption: [{ value: '1', text: '正常' }, { value: '2', text: '异常' }],\r\n\t\t\tformData: {\r\n\t\t\t\tresult: '',\r\n\t\t\t\tmemo: '',\r\n\t\t\t\tlng: 0,\r\n\t\t\t\tlat: 0,\r\n\t\t\t\taddress: ''\r\n\t\t\t},\r\n\t\t\tformRules: {\r\n\t\t\t\tresult: {\r\n\t\t\t\t\trules: [{ required: true, errorMessage: '请选择结论' }]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageValue: [],\r\n\t\t\tactiveTab: 0,\r\n\t\t\tdoingCount: 0,\r\n\t\t\tabnormalCount: 0,\r\n\t\t\tnormalCount: 0,\r\n\t\t\tfileList: [],\r\n\t\t\tlist: []\r\n\t\t}\r\n\t},\r\n\tonLoad: function (options) {\r\n\t\tconst that = this\r\n\t\tif (options.id) {\r\n\t\t\tctx.post('/wx/patrol/task/get/' + options.id, function (res) {\r\n\t\t\t\tif (res.code < 0 || res.data == null) return app.error('无法获取任务信息', 'back')\r\n\t\t\t\tconst task = res.data\r\n\t\t\t\tthat.formatTime(task)\r\n\t\t\t\tthat.task = task\r\n\t\t\t\tthat.list = task.pointList || task.assetList || []\r\n\t\t\t\tthat.formData.result = task.result || ''\r\n\t\t\t\tthat.formData.memo = task.memo || ''\r\n\t\t\t\tthat.formData.startLng = task.startLng\r\n\t\t\t\tthat.formData.startLat = task.startLat\r\n\t\t\t\tctx.getLocation((res) => {\r\n\t\t\t\t\tthat.formData.startLat = res.latitude\r\n\t\t\t\t\tthat.formData.startLng = res.longitude\r\n\t\t\t\t\tctx.getLocationAddress({\r\n\t\t\t\t\t\tlng: res.longitude,\r\n\t\t\t\t\t\tlat: res.latitude,\r\n\t\t\t\t\t\tcallback(r) {\r\n\t\t\t\t\t\t\tthat.formData.address = r.formatted_address\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\tctx.alert('无效参数', 'back')\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tformatTime(item) {\r\n\t\t\titem.timeText = formatTimeRange(item)\r\n\t\t},\r\n\t\tuploadSelect(e) {\r\n\t\t\tif (e.tempFiles) e.tempFiles.forEach(file => this.uploadFile(file))\r\n\t\t},\r\n\t\tuploadDelete(e) {\r\n\t\t\tfor (let i = 0; i < this.fileList.length; i++) {\r\n\t\t\t\tif (this.fileList[i].fileId === e.tempFile.uuid) {\r\n\t\t\t\t\tthis.fileList.splice(i, 1)\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadFile(file) {\r\n\t\t\tconst that = this\r\n\t\t\twx.uploadFile({\r\n\t\t\t\turl: settings.api_host + '/wx/upload/PATROL_TASK',\r\n\t\t\t\tfilePath: file.path,\r\n\t\t\t\theader: { 'zy_token': wx.getStorageSync('ZY_TOKEN'), 'appId': settings.api_id, 'appSecret': settings.api_secret },\r\n\t\t\t\tname: 'file',\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (res.statusCode != 200) return ctx.error('上传失败')\r\n\t\t\t\t\tconst ret = JSON.parse(res.data)\r\n\t\t\t\t\tif (ret.code < 0) return ctx.error(ret.msg)\r\n\t\t\t\t\tlet data = ret.data\r\n\t\t\t\t\tdata.fileId = file.uuid\r\n\t\t\t\t\tthat.fileList.push(data)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsubmit() {\r\n\t\t\tconst that = this\r\n\t\t\tthis.$refs.form.validate().then(data => {\r\n\t\t\t\tdata.id = that.task.id\r\n\t\t\t\tdata.fileList = []\r\n\t\t\t\tthat.fileList.forEach(r => {\r\n\t\t\t\t\tdata.fileList.push(r.id)\r\n\t\t\t\t})\r\n\t\t\t\tctx.post('/wx/patrol/task/saveResult', data, function (res) {\r\n\t\t\t\t\tif (res.code < 0) return ctx.error(res.msg)\r\n\t\t\t\t\tctx.ok('保存成功', 'back')\r\n\t\t\t\t})\r\n\t\t\t}).catch()\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tmargin: 12rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623961\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}