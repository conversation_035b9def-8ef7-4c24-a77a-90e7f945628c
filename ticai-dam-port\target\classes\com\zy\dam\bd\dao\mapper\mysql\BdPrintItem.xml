<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.bd.dao.BdPrintItemDAO">

    <sql id="meta">
			a.ID_
			,a.TEMPLATE_
			,a.INDEX_
			,a.FIELD_INDEX_
			,a.FIELD_TYPE_
			,a.FIELD_CODE_
			,a.FIELD_NAME_
			,a.X_
			,a.Y_
			,a.WIDTH_
			,a.HEIGHT_
			,a.FONT_SIZE_
			,a.ALIGN_
			,a.FLAG_
	</sql>
    <insert id="insert" parameterType="com.zy.dam.bd.orm.BdPrintItem">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into BD_PRINT_ITEM(ID_,TEMPLATE_,INDEX_,FIELD_INDEX_,FIELD_TYPE_,FIELD_CODE_,FIELD_NAME_,X_,Y_,WIDTH_,HEIGHT_,FONT_SIZE_,ALIGN_,FLAG_)
        values(#{id},#{template},#{index},#{fieldIndex},#{fieldType},#{fieldCode},#{fieldName},#{x},#{y},#{width},#{height},#{fontSize},#{align},'1')
    </insert>

    <select id="findByTemplate" resultType="com.zy.dam.bd.vo.PrintItemVo">
		select
			a.INDEX_
			,a.FIELD_INDEX_
			,a.FIELD_TYPE_
			,a.FIELD_CODE_
			,a.FIELD_NAME_
			,a.X_
			,a.Y_
			,a.WIDTH_
			,a.HEIGHT_
			,a.FONT_SIZE_
			,a.ALIGN_
		from BD_PRINT_ITEM a where a.FLAG_='1' and a.TEMPLATE_=#{0}
	</select>

    <update id="deleteByTemplate">
		update BD_PRINT_ITEM set FLAG_='9' where TEMPLATE_=#{0}
	</update>

</mapper>
