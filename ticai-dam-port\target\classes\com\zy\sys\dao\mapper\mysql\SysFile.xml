<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.sys.dao.SysFileDAO">

    <sql id="meta">
			a.ID_
			,a.TYPE_
			,a.RID_
			,a.NAME_
			,a.EXT_
			,a.LENGTH_
			,a.PATH_
			,a.REAL_PATH_
			,a.ORD_
			,a.TIME_
			,a.TIME_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.sys.orm.SysFile">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into SYS_FILE(ID_,TYPE_,RID_,NAME_,<PERSON>XT_,LENGTH_,PATH_,REAL_PATH_,ORD_,TIME_)
        values(#{id},#{type},#{rid},#{name},#{ext},#{length},#{path},#{realPath},#{ord},now())
    </insert>

    <!-- 更新数据 -->
    <update id="updateRid">
		update SYS_FILE set RID_=#{1} where ID_=#{0}
	</update>

    <update id="updateR">
		update SYS_FILE set RID_=#{1},ORD_=#{2} where ID_=#{0}
	</update>

    <update id="copyRid">
		insert into SYS_FILE(ID_,TYPE_,RID_,NAME_,EXT_,LENGTH_,PATH_,REAL_PATH_,ORD_,TIME_)
		select uuid(),TYPE_,#{1},NAME_,EXT_,LENGTH_,PATH_,REAL_PATH_,ORD_,TIME_ from SYS_FILE where ID_=#{0}
	</update>

    <!-- 获取唯一的系统-文件信息数据 -->
    <select id="findOne" resultType="com.zy.sys.orm.SysFile">
        select
        <include refid="meta"/>
        from SYS_FILE a where a.ID_=#{0}
    </select>

    <select id="findByRid" resultType="com.zy.sys.orm.SysFile">
        select
        <include refid="meta"/>
        from SYS_FILE a where a.TYPE_=#{0} and a.RID_=#{1}
        order by a.ORD_,a.TIME_ desc
    </select>

    <select id="findIdByRid" resultType="String">
		select ID_ from SYS_FILE a where a.TYPE_=#{0} and a.RID_=#{1}
	</select>

    <!-- 删除 -->
    <update id="delete" parameterType="java.lang.String">
		delete from SYS_FILE where ID_=#{0}
	</update>

    <delete id="deleteByRid" parameterType="java.lang.String">
		delete from SYS_FILE where TYPE_=#{0} and RID_=#{1}
	</delete>

    <update id="removeRid" parameterType="java.lang.String">
		update SYS_FILE set RID_=NULL where TYPE_=#{0} and RID_=#{1}
	</update>

    <select id="findVueFile" resultType="com.zy.model.VueFile">
        select ID_,NAME_,EXT_,PATH_ from SYS_FILE a where a.ID_=#{0}
    </select>

    <select id="findVueFileByRid" resultType="com.zy.model.VueFile">
        select ID_,NAME_,EXT_,PATH_ from SYS_FILE a where a.TYPE_=#{0} and a.RID_=#{1}
        order by a.ORD_,a.TIME_ desc
    </select>

    <update id="deleteFile">
        update SYS_FILE set FLAG_='9' where FLAG_='1' and RID_=#{0}
    </update>

</mapper>
