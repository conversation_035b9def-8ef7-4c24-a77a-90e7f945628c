{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\store\\modules\\app.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\store\\modules\\app.js", "mtime": 1752649821933}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Cookies", "state", "sidebar", "opened", "get", "withoutAnimation", "device", "moduleName", "mutations", "TOGGLE_SIDEBAR", "set", "CLOSE_SIDEBAR", "TOGGLE_DEVICE", "SET_MODULE_NAME", "actions", "toggleSideBar", "_ref", "commit", "closeSideBar", "_ref2", "_ref3", "toggleDevice", "_ref4", "setModuleName", "_ref5", "namespaced"], "sources": ["F:/work/ticai/ticai-web/src/store/modules/app.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst state = {\r\n  sidebar: {\r\n    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,\r\n    withoutAnimation: false\r\n  },\r\n  device: 'desktop',\r\n  moduleName: ''\r\n}\r\n\r\nconst mutations = {\r\n  TOGGLE_SIDEBAR: state => {\r\n    state.sidebar.opened = !state.sidebar.opened\r\n    state.sidebar.withoutAnimation = false\r\n    if (state.sidebar.opened) {\r\n      Cookies.set('sidebarStatus', 1)\r\n    } else {\r\n      Cookies.set('sidebarStatus', 0)\r\n    }\r\n  },\r\n  CLOSE_SIDEBAR: (state, withoutAnimation) => {\r\n    Cookies.set('sidebarStatus', 0)\r\n    state.sidebar.opened = false\r\n    state.sidebar.withoutAnimation = withoutAnimation\r\n  },\r\n  TOGGLE_DEVICE: (state, device) => {\r\n    state.device = device\r\n  },\r\n  SET_MODULE_NAME: (state, moduleName) => {\r\n    state.moduleName = moduleName\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  toggleSideBar({ commit }) {\r\n    commit('TOGGLE_SIDEBAR')\r\n  },\r\n  closeSideBar({ commit }, { withoutAnimation }) {\r\n    commit('CLOSE_SIDEBAR', withoutAnimation)\r\n  },\r\n  toggleDevice({ commit }, device) {\r\n    commit('TOGGLE_DEVICE', device)\r\n  },\r\n  setModuleName({ commit }, moduleName) {\r\n    commit('SET_MODULE_NAME', moduleName)\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAE;IACPC,MAAM,EAAEH,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAACJ,OAAO,CAACI,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;IAC7EC,gBAAgB,EAAE;EACpB,CAAC;EACDC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE;AACd,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAER,KAAK,EAAI;IACvBA,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAACF,KAAK,CAACC,OAAO,CAACC,MAAM;IAC5CF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAG,KAAK;IACtC,IAAIJ,KAAK,CAACC,OAAO,CAACC,MAAM,EAAE;MACxBH,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACLV,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC;EACF,CAAC;EACDC,aAAa,EAAE,SAAfA,aAAaA,CAAGV,KAAK,EAAEI,gBAAgB,EAAK;IAC1CL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/BT,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,KAAK;IAC5BF,KAAK,CAACC,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;EACnD,CAAC;EACDO,aAAa,EAAE,SAAfA,aAAaA,CAAGX,KAAK,EAAEK,MAAM,EAAK;IAChCL,KAAK,CAACK,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDO,eAAe,EAAE,SAAjBA,eAAeA,CAAGZ,KAAK,EAAEM,UAAU,EAAK;IACtCN,KAAK,CAACM,UAAU,GAAGA,UAAU;EAC/B;AACF,CAAC;AAED,IAAMO,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAa;IAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACpBA,MAAM,CAAC,gBAAgB,CAAC;EAC1B,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAAC,KAAA,EAAmC;IAAA,IAAhCH,MAAM,GAAAE,KAAA,CAANF,MAAM;IAAA,IAAMZ,gBAAgB,GAAAe,KAAA,CAAhBf,gBAAgB;IACzCY,MAAM,CAAC,eAAe,EAAEZ,gBAAgB,CAAC;EAC3C,CAAC;EACDgB,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAahB,MAAM,EAAE;IAAA,IAAlBW,MAAM,GAAAK,KAAA,CAANL,MAAM;IACnBA,MAAM,CAAC,eAAe,EAAEX,MAAM,CAAC;EACjC,CAAC;EACDiB,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAajB,UAAU,EAAE;IAAA,IAAtBU,MAAM,GAAAO,KAAA,CAANP,MAAM;IACpBA,MAAM,CAAC,iBAAiB,EAAEV,UAAU,CAAC;EACvC;AACF,CAAC;AAED,eAAe;EACbkB,UAAU,EAAE,IAAI;EAChBxB,KAAK,EAALA,KAAK;EACLO,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}