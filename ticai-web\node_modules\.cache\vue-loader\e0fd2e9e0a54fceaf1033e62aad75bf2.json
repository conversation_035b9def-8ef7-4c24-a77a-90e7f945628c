{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\asset-trace\\index.vue", "mtime": 1752649761442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}