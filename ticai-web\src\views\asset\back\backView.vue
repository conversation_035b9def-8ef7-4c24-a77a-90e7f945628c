<template>
  <div>
    <el-dialog v-dialog-drag title="退库申请信息" width="900px" :visible.sync="visible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="dataform" size="small" label-width="140px" :model="form">
        <el-form-item label="实际退库日期：">
          <el-input v-model="form.time" readonly class="form-static" style="width:100%" />
        </el-form-item>
        <el-form-item label="使用区域/地点：">
          <el-input v-model="regionText" readonly class="form-static" style="width:100%" />
        </el-form-item>
        <el-form-item label="领用说明：">
          <el-input v-model="form.memo" type="textarea" readonly class="form-static" style="width:100%" />
        </el-form-item>
      </el-form>
      <upload-file v-if="fileList.length" v-model="fileList" multiple disabled />
      <div style="margin-top: 10px;">退库资产明细：</div>
      <el-divider></el-divider>
      <el-table :data="assetList" size="small" border>
        <el-table-column label="资产类型" prop="typeName" width="180" header-align="center" />
        <el-table-column label="资产编码" prop="no" width="130" align="center">
          <template slot-scope="scope">
            <el-link type="danger" size="mini" @click.stop="viewAsset(scope.row.id)">{{ scope.row.no }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="资产名称" prop="name" min-width="180">
          <template slot-scope="scope">
            <el-link type="primary" size="mini" @click.stop="viewAsset(scope.row.id)">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="所属部门" prop="deptName" width="150" header-align="center" />
        <el-table-column label="当前状态" prop="status" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAssetStatusType(scope.row)" size="small">{{ getAssetStatusText(scope.row) }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <asset-view ref="assetView" />
  </div>
</template>
<script>

import { getAssetStatusType, getAssetStatusText } from '../js/asset.js'

import AssetView from '../account/DetailView.vue'
import UploadFile from '@/views/components/UploadFile.vue'

export default {
  components: { AssetView, UploadFile },
  data() {
    return {
      visible: false,
      form: { },
      regionText: '',
      assetList: [],
      fileList: [],
      attachContext: this.$store.getters.attachContext
    }
  },
  methods: {
    getAssetStatusType(v) {
      return getAssetStatusType(v.status)
    },
    getAssetStatusText(v) {
      return getAssetStatusText(v.status)
    },
    show(item) {
      this.visible = true
      this.regionText = item.deptName && item.regionName ? `${item.deptName}/${item.regionName}` : (item.deptName || item.regionName || '');
      this.form = item
      this.assetList = item.details || []
      this.fileList = item.fileList || []
    },
    viewAsset(id) {
      this.$refs.assetView.show(id)
    }
  }
}
</script>
