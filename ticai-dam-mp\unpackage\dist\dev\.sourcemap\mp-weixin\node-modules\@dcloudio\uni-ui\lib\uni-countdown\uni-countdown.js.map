{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?133b", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?4305", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?9bfb", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?c0f1", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?2857", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue?7f36"], "names": ["t", "name", "emits", "props", "showDay", "type", "default", "showHour", "showMinute", "showColon", "start", "backgroundColor", "color", "fontSize", "splitorColor", "day", "hour", "minute", "second", "timestamp", "filterShow", "data", "timer", "syncFlag", "d", "h", "i", "s", "leftTime", "seconds", "computed", "dayText", "hourText", "minuteText", "secondText", "timeStyle", "width", "lineHeight", "borderRadius", "splitorStyle", "margin", "watch", "immediate", "handler", "clearInterval", "created", "destroyed", "methods", "to<PERSON><PERSON><PERSON><PERSON>", "timeUp", "countDown", "validFilterShow", "startData", "update", "changeFlag"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAqyB,CAAgB,qzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACazzB;AAGA;;;;;;;;;;;;;;AACA,mBAEA;EADAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAmBA;EACAC;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,IACAvB,QAGA,KAHAA;QACAD,kBAEA,KAFAA;QACAE,WACA,KADAA;MAEA;QACAD;QACAD;QACAE;QACAuB;QAAA;QACAC;QACAC;MACA;IACA;IACAC;MACA;QAAA1B;QAAAF;MACA;QACAC;QACAC;QACA2B;MACA;IACA;EACA;EACAC;IACA1B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAR;MACAgC;MACAC;QACA;UACA;QACA;UACA;UACAC;QACA;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACAF;EACA;EAOAG;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAL;MACA;IACA;IACAM;MACA;MACA;QAAAlC;QAAAC;QAAAC;MACA;QACAH;QACAC;QACAC;QACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAiC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;MACAR;MACA;MACA;QACA;QACA;UACA;UACA;QACA;QACA;MACA;IACA;IACAS;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAAggD,CAAgB,o9CAAG,EAAC,C;;;;;;;;;;;ACAphD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-countdown.vue?vue&type=template&id=02c75d70&scoped=true&\"\nvar renderjs\nimport script from \"./uni-countdown.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-countdown.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-countdown.vue?vue&type=style&index=0&id=02c75d70&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"02c75d70\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-countdown.vue?vue&type=template&id=02c75d70&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.showDay ? _vm.__get_style([_vm.timeStyle]) : null\n  var s1 = _vm.showDay ? _vm.__get_style([_vm.splitorStyle]) : null\n  var s2 = _vm.showHour ? _vm.__get_style([_vm.timeStyle]) : null\n  var s3 = _vm.showHour ? _vm.__get_style([_vm.splitorStyle]) : null\n  var s4 = _vm.showMinute ? _vm.__get_style([_vm.timeStyle]) : null\n  var s5 = _vm.showMinute ? _vm.__get_style([_vm.splitorStyle]) : null\n  var s6 = _vm.__get_style([_vm.timeStyle])\n  var s7 = !_vm.showColon ? _vm.__get_style([_vm.splitorStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n        s5: s5,\n        s6: s6,\n        s7: s7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-countdown.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-countdown.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-countdown\">\r\n\t\t<text v-if=\"showDay\" :style=\"[timeStyle]\" class=\"uni-countdown__number\">{{ d }}</text>\r\n\t\t<text v-if=\"showDay\" :style=\"[splitorStyle]\" class=\"uni-countdown__splitor\">{{dayText}}</text>\r\n\t\t<text v-if=\"showHour\" :style=\"[timeStyle]\" class=\"uni-countdown__number\">{{ h }}</text>\r\n\t\t<text v-if=\"showHour\" :style=\"[splitorStyle]\" class=\"uni-countdown__splitor\">{{ showColon ? ':' : hourText }}</text>\r\n\t\t<text v-if=\"showMinute\" :style=\"[timeStyle]\" class=\"uni-countdown__number\">{{ i }}</text>\r\n\t\t<text v-if=\"showMinute\" :style=\"[splitorStyle]\" class=\"uni-countdown__splitor\">{{ showColon ? ':' : minuteText }}</text>\r\n\t\t<text :style=\"[timeStyle]\" class=\"uni-countdown__number\">{{ s }}</text>\r\n\t\t<text v-if=\"!showColon\" :style=\"[splitorStyle]\" class=\"uni-countdown__splitor\">{{secondText}}</text>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tinitVueI18n\r\n\t} from '@dcloudio/uni-i18n'\r\n\timport messages from './i18n/index.js'\r\n\tconst {\r\n\t\tt\r\n\t} = initVueI18n(messages)\r\n\t/**\r\n\t * Countdown 倒计时\r\n\t * @description 倒计时组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=25\r\n\t * @property {String} backgroundColor 背景色\r\n\t * @property {String} color 文字颜色\r\n\t * @property {Number} day 天数\r\n\t * @property {Number} hour 小时\r\n\t * @property {Number} minute 分钟\r\n\t * @property {Number} second 秒\r\n\t * @property {Number} timestamp 时间戳\r\n\t * @property {Boolean} showDay = [true|false] 是否显示天数\n\t * @property {Boolean} showHour = [true|false] 是否显示小时\n\t * @property {Boolean} showMinute = [true|false] 是否显示分钟\r\n\t * @property {Boolean} show-colon = [true|false] 是否以冒号为分隔符\r\n\t * @property {String} splitorColor 分割符号颜色\r\n\t * @event {Function} timeup 倒计时时间到触发事件\r\n\t * @example <uni-countdown :day=\"1\" :hour=\"1\" :minute=\"12\" :second=\"40\"></uni-countdown>\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniCountdown',\r\n\t\temits: ['timeup'],\r\n\t\tprops: {\r\n\t\t\tshowDay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowHour: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowMinute: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowColon: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\n\t\t\tfontSize: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 14\n\t\t\t},\r\n\t\t\tsplitorColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\r\n\t\t\tday: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\thour: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tminute: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tsecond: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\ttimestamp: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\n\t\t\tfilterShow : {\n\t\t\t\ttype:Object,\n\t\t\t\tdefault:{}\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tsyncFlag: false,\r\n\t\t\t\td: '00',\r\n\t\t\t\th: '00',\r\n\t\t\t\ti: '00',\r\n\t\t\t\ts: '00',\r\n\t\t\t\tleftTime: 0,\r\n\t\t\t\tseconds: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tdayText() {\r\n\t\t\t\treturn t(\"uni-countdown.day\")\r\n\t\t\t},\r\n\t\t\thourText(val) {\r\n\t\t\t\treturn t(\"uni-countdown.h\")\r\n\t\t\t},\r\n\t\t\tminuteText(val) {\r\n\t\t\t\treturn t(\"uni-countdown.m\")\r\n\t\t\t},\r\n\t\t\tsecondText(val) {\r\n\t\t\t\treturn t(\"uni-countdown.s\")\r\n\t\t\t},\r\n\t\t\ttimeStyle() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcolor,\r\n\t\t\t\t\tbackgroundColor,\n\t\t\t\t\tfontSize\r\n\t\t\t\t} = this\r\n\t\t\t\treturn {\r\n\t\t\t\t\tcolor,\r\n\t\t\t\t\tbackgroundColor,\n\t\t\t\t\tfontSize: `${fontSize}px`,\n\t\t\t\t\twidth: `${fontSize * 22 / 14}px`, // 按字体大小为 14px 时的比例缩放\n \t\t\t\t\tlineHeight: `${fontSize * 20 / 14}px`,\n\t\t\t\t\tborderRadius: `${fontSize * 3 / 14}px`,\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsplitorStyle() {\n\t\t\t\tconst { splitorColor, fontSize, backgroundColor } = this\n\t\t\t\treturn {\n\t\t\t\t\tcolor: splitorColor,\n\t\t\t\t\tfontSize: `${fontSize * 12 / 14}px`,\n\t\t\t\t\tmargin: backgroundColor ? `${fontSize * 4 / 14}px` : ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tday(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\thour(val) {\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tminute(val) {\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tsecond(val) {\r\n\t\t\t\tthis.changeFlag()\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal, oldVal) {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.startData();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (!oldVal) return\r\n\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated: function(e) {\r\n\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\tthis.countDown()\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\tdestroyed() {\r\n\t\t\tclearInterval(this.timer)\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\tunmounted() {\r\n\t\t\tclearInterval(this.timer)\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\ttoSeconds(timestamp, day, hours, minutes, seconds) {\r\n\t\t\t\tif (timestamp) {\r\n\t\t\t\t\treturn timestamp - parseInt(new Date().getTime() / 1000, 10)\r\n\t\t\t\t}\r\n\t\t\t\treturn day * 60 * 60 * 24 + hours * 60 * 60 + minutes * 60 + seconds\r\n\t\t\t},\r\n\t\t\ttimeUp() {\r\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\tthis.$emit('timeup')\r\n\t\t\t},\r\n\t\t\tcountDown() {\r\n\t\t\t\tlet seconds = this.seconds\r\n\t\t\t\tlet [day, hour, minute, second] = [0, 0, 0, 0]\r\n\t\t\t\tif (seconds > 0) {\r\n\t\t\t\t\tday = Math.floor(seconds / (60 * 60 * 24))\r\n\t\t\t\t\thour = Math.floor(seconds / (60 * 60)) - (day * 24)\r\n\t\t\t\t\tminute = Math.floor(seconds / 60) - (day * 24 * 60) - (hour * 60)\r\n\t\t\t\t\tsecond = Math.floor(seconds) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.timeUp()\r\n\t\t\t\t}\n\t\t\t\tthis.d  = String(day).padStart(this.validFilterShow(this.filterShow.d), '0')\n\t\t\t\tthis.h = String(hour).padStart(this.validFilterShow(this.filterShow.h), '0')\n\t\t\t\tthis.i = String(minute).padStart(this.validFilterShow(this.filterShow.m), '0')\n\t\t\t\tthis.s = String(second).padStart(this.validFilterShow(this.filterShow.s), '0')\n\t\t\t},\n\t\t\tvalidFilterShow(filter){\n\t\t\t\treturn (filter && filter > 0) ? filter : 2;\n\t\t\t},\r\n\t\t\tstartData() {\r\n\t\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\t\tif (this.seconds <= 0) {\n\t\t\t\t\tthis.seconds = this.toSeconds(0, 0, 0, 0, 0)\n\t\t\t\t\tthis.countDown()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\tthis.countDown()\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\tthis.seconds--\r\n\t\t\t\t\tif (this.seconds < 0) {\r\n\t\t\t\t\t\tthis.timeUp()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.countDown()\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\n\t\t\tupdate(){\n\t\t\t\tthis.startData();\n\t\t\t},\r\n\t\t\tchangeFlag() {\n\t\t\t\tif (!this.syncFlag) {\r\n\t\t\t\t\tthis.seconds = this.toSeconds(this.timestamp, this.day, this.hour, this.minute, this.second)\r\n\t\t\t\t\tthis.startData();\r\n\t\t\t\t\tthis.syncFlag = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t$font-size: 14px;\n\r\n\t.uni-countdown {\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: flex-start;\r\n\t\talign-items: center;\n\r\n\t\t&__splitor {\r\n\t\t\tmargin: 0 2px;\r\n\t\t\tfont-size: $font-size;\n\t\t\tcolor: #333;\n\t\t}\r\n\r\n\t\t&__number {\n\t\t\tborder-radius: 3px;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: $font-size;\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-countdown.vue?vue&type=style&index=0&id=02c75d70&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-countdown.vue?vue&type=style&index=0&id=02c75d70&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650629408\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}