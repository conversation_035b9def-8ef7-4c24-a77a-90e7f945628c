(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54ef68e3"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},6943:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"layout-lr"},[a("div",{staticClass:"left"},[a("div",{staticClass:"head",staticStyle:{"padding-left":"30px"}},[t._v(" 目录列表 "),a("el-button",{staticStyle:{float:"right","margin-top":"10px","margin-right":"4px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:t.addCategory}})],1),a("div",{staticClass:"body"},[a("ul",t._l(t.categoryList,(function(e){return a("li",{key:e.id,class:{act:t.activeItem&&t.activeItem.id==e.id},on:{click:function(a){return t.showCategory(e)}}},[t._v(t._s(e.name))])})),0)])]),a("div",{staticClass:"center"},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("span",{staticClass:"point-group"},[t._v("当前目录："),a("b",[t._v(t._s(t.activeItem.name))])]),a("el-button-group",[t.activeItem&&t.activeItem.id&&"0"!=t.activeItem.id?[a("el-button",{attrs:{type:"warning",size:"mini",icon:"el-icon-edit"},on:{click:t.editCategory}},[t._v("编辑目录")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:t.removeCategory}},[t._v("删除目录")])]:t._e()],2)],1),a("div",{staticClass:"search",staticStyle:{width:"350px"}},[t.activeItem&&t.activeItem.id?[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:t.addKpi}},[t._v("新增指标项")])]:t._e()],2)])]),a("el-divider"),a("div",{staticClass:"kpi-head"},[a("span",{staticClass:"kpi-title"},[t._v("指标项")]),a("div",{staticClass:"kpi-search"},[a("el-input",{attrs:{size:"mini",clearable:"",placeholder:"输入关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}},[a("template",{slot:"prepend"},[t._v("检索:")]),a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.searchKpi},slot:"append"})],2)],1)]),a("page-table",{ref:"grid",attrs:{size:"mini",path:"/am/patrol/kpi/page",query:t.qform,stripe:"",border:"","max-height":t.tableHeight},on:{loaded:t.kpiLoaded}},[a("el-table-column",{attrs:{label:"编码",prop:"no",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"类型",prop:"mode",width:"60",align:"center",formatter:t.colMode}}),a("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"200"}}),a("el-table-column",{attrs:{label:"排序",prop:"ord",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"选项数",prop:"itemCount",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"操作",width:"170",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return["admin"!=e.row.id?a("div",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(a){return a.stopPropagation(),t.editKpi(e.row)}}},[t._v("编辑")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),t.removeKpi(e.row)}}},[t._v("删除")])],1):t._e()]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"目录信息",width:"600px",visible:t.categoryVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.categoryVisible=e}}},[a("el-form",{ref:"categoryform",attrs:{model:t.categoryData,"label-width":"120px",rules:t.categoryRules}},[a("el-form-item",{attrs:{label:"目录名称：",prop:"name"}},[a("el-input",{attrs:{maxlength:"20",autocomplete:"off"},model:{value:t.categoryData.name,callback:function(e){t.$set(t.categoryData,"name",e)},expression:"categoryData.name"}})],1),a("el-form-item",{attrs:{label:"目录排序："}},[a("el-input-number",{attrs:{autocomplete:"off",min:1,max:999},model:{value:t.categoryData.ord,callback:function(e){t.$set(t.categoryData,"ord",e)},expression:"categoryData.ord"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.categoryVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.saveCategory}},[t._v("确 定")])],1)],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"指标项",width:"1000px",visible:t.kpiVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.kpiVisible=e}}},[a("el-form",{ref:"kpiform",attrs:{model:t.kpiData,"label-width":"120px",rules:t.kpiRules}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"指标名称：",prop:"name"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{autocomplete:"off"},model:{value:t.kpiData.name,callback:function(e){t.$set(t.kpiData,"name",e)},expression:"kpiData.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"指标编码：",prop:"no"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{autocomplete:"off"},model:{value:t.kpiData.no,callback:function(e){t.$set(t.kpiData,"no",e)},expression:"kpiData.no"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"指标类型：",prop:"mode"}},[a("radio-box",{staticStyle:{width:"100%"},attrs:{options:t.modeOptions},model:{value:t.kpiData.mode,callback:function(e){t.$set(t.kpiData,"mode",e)},expression:"kpiData.mode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"排序："}},[a("el-input-number",{attrs:{autocomplete:"off",min:1,max:999},model:{value:t.kpiData.ord,callback:function(e){t.$set(t.kpiData,"ord",e)},expression:"kpiData.ord"}})],1)],1)],1),a("el-form-item",{attrs:{label:"指导意见：",prop:"connent"}},[a("el-input",{attrs:{type:"textarea",rows:5,maxlength:"500","show-word-limit":"",placeholder:"请输入指定意见",autocomplete:"off"},model:{value:t.kpiData.content,callback:function(e){t.$set(t.kpiData,"content",e)},expression:"kpiData.content"}})],1)],1),"1"===t.kpiData.mode||"2"===t.kpiData.mode?a("el-table",{ref:"optionGrid",attrs:{data:t.optionList,size:"mini",stripe:!0,border:!0}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{label:"选项号",prop:"value",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{attrs:{size:"mini",autocomplete:"off"},model:{value:e.row.no,callback:function(a){t.$set(e.row,"no",a)},expression:"scope.row.no"}})]}}],null,!1,2360665083)}),a("el-table-column",{attrs:{label:"选项内容",prop:"content"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{attrs:{size:"mini",autocomplete:"off"},nativeOn:{keydown:function(e){return t.nameKeyDown(e)}},model:{value:e.row.content,callback:function(a){t.$set(e.row,"content",a)},expression:"scope.row.content"}})]}}],null,!1,1280698544)}),a("el-table-column",{attrs:{width:"70",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return a.stopPropagation(),t.removeOption(e.$index)}}},[t._v("删除")])]}}],null,!1,2074686964)},[a("template",{slot:"header"},[a("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.addOption}},[t._v("新增")])],1)],2)],1):t._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.kpiVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.saveKpi}},[t._v("确 定")])],1)],1)],1)},o=[],n=(a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),r=a("73a0"),s={1:"单选",2:"多选",3:"判断",4:"填写"},l={components:{PageTable:n["a"],RadioBox:r["a"]},data:function(){return{categoryList:[],activeItem:{},modeOptions:[],tableHeight:Math.max(document.documentElement.clientHeight-300,200),categoryVisible:!1,categoryData:{category:""},categoryRules:{name:[{required:!0,message:"请输入目录名称",trigger:"blur"}]},qform:{keyword:""},kpiVisible:!1,kpiData:{category:"",mode:"1",ord:1},kpiRules:{no:[{required:!0,message:"请填写指标编码",trigger:"blur"}],mode:[{required:!0,message:"请选择指标类型",trigger:"blur"}],name:[{required:!0,message:"请选择指标名称",trigger:"blur"}],content:[{required:!0,message:"请输入指标指导意见",trigger:"blur"}]},lastMode:"1",lastOrd:0,optionList:[{}]}},created:function(){var t=this;this.loadCategory(),Object.keys(s).forEach((function(e){return t.modeOptions.push({value:e,text:s[e]})}))},methods:{colMode:function(t,e,a){return s[a]||""},loadCategory:function(){var t=this;this.$http("/am/patrol/kpi/category/list").then((function(e){if(t.categoryList=e||[],t.activeItem&&t.activeItem.id)for(var a=0;a<e.length;a++)if(e[a].id===t.activeItem.id){t.activeItem=e[a];break}}))},showCategory:function(t){this.activeItem=t,this.qform.category=t.id,this.qform.keyword="",this.lastMode="1",this.lastOrd=0,this.searchKpi()},addCategory:function(){this.categoryVisible=!0,this.categoryData={ord:1}},editCategory:function(){if(this.activeItem.id){this.categoryVisible=!0;var t=JSON.stringify(this.activeItem);this.categoryData=JSON.parse(t)}},saveCategory:function(){var t=this;this.$refs.categoryform.validate((function(e){e&&t.$http({url:"/am/patrol/kpi/category/save",data:t.categoryData}).then((function(e){e.code>0&&(t.$message.success("保存目录信息成功"),t.categoryVisible=!1,t.loadCategory())}))}))},removeCategory:function(){var t=this;this.$confirm("此操作将永久删除该目录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/am/patrol/kpi/category/delete/"+t.activeItem.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.activeItem={},t.loadCategory())}))})).catch((function(){}))},searchKpi:function(){this.$refs.grid.search(this.qform)},kpiLoaded:function(t){var e=this;t&&t.rows&&t.rows.forEach((function(t){t.ord>e.lastOrd&&(e.lastOrd=t.ord)}))},addKpi:function(){this.kpiVisible=!0,this.optionList=[],this.kpiData={category:this.activeItem.id,mode:this.lastMode,ord:++this.lastOrd}},editKpi:function(t){var e=this;this.$http("/am/patrol/kpi/get/"+t.id).then((function(t){t.code>0&&t.data&&(e.kpiVisible=!0,e.kpiData=t.data,e.optionList=t.data.itemList||[])}))},saveKpi:function(){var t=this;this.$refs.kpiform.validate((function(e){e&&(t.kpiData.itemList=[],t.optionList.forEach((function(e){e.no&&e.content&&t.kpiData.itemList.push(e)})),t.$http({url:"/am/patrol/kpi/save",data:t.kpiData}).then((function(e){e.code>0&&(t.$message.success("保存目录信息成功"),t.kpiVisible=!1,t.lastMode=t.kpiData.mode,t.searchKpi())})))}))},removeKpi:function(t){var e=this;this.$confirm("此操作将永久删除该指标项, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:"/am/patrol/kpi/delete/"+t.id}).then((function(t){t.code>0&&(e.$message.success("删除成功"),e.searchKpi())}))})).catch((function(){}))},addOption:function(){this.optionList.push({})},removeOption:function(t){this.optionList.splice(t,1)},nameKeyDown:function(t){if(13===t.keyCode||9===t.keyCode){for(var e=0;e<this.optionList.length;e++)if(!this.optionList[e].content)return void this.focusRow(e);this.addOption(),this.focusRow(this.optionList.length-1)}},focusRow:function(t){var e=this;this.$nextTick((function(){try{e.$refs.optionGrid.$el.children[2].children[0].children[1].children[t].children[1].children[0].children[0].children[0].focus()}catch(a){console.log(a)}}))}}},c=l,u=(a("e4442"),a("2877")),d=Object(u["a"])(c,i,o,!1,null,null,null);e["default"]=d.exports},"6ecd":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[t.total>1?a("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),a("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},o=[],n=a("53ca"),r=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),s={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var a=this;if(this.path){var i={pageNumber:1},o=Object(n["a"])(t);"undefined"===o?i.pageNumber=1:"number"===o?i.pageNumber=t:"object"===o?(this.params=t,"number"===typeof e&&(i.pageNumber=e),"boolean"===typeof e&&this.empty()):i.pageNumber=t.pageNumber,this.pi=i.pageNumber,this.paging&&(this.params.pageNumber=i.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(r["a"])({url:this.path,data:this.params}).then((function(t){a.loading=!1,a.paging?a.renderPage(t):a.renderList(t.rows?t.rows:t),a.$emit("loaded",t)})).catch((function(t){a.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var a=[],i=0;i<e.length;i++)e[i][t]&&a.push(e[i][t]);return a}}},l=s,c=(a("b2d4"),a("2877")),u=Object(c["a"])(l,i,o,!1,null,"bdcc19d8",null);e["a"]=u.exports},"73a0":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",t._g(t._b({on:{change:t.changeMe}},"el-radio-group",t.$attrs,!1),t.$listeners),[t.button?[t.all?a("el-radio-button",{attrs:{label:""}},[t._v(t._s(t.all))]):t._e(),t._l(t.options,(function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.text))])}))]:[t.all?a("el-radio",{attrs:{label:""}},[t._v(t._s(t.all))]):t._e(),t._l(t.options,(function(e){return a("el-radio",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.text))])}))]],2)},o=[],n={name:"RadioBox",props:{button:{type:Boolean,default:!1},all:{type:String,default:null},options:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{changeMe:function(t){this.$emit("selected",t)}}},r=n,s=a("2877"),l=Object(s["a"])(r,i,o,!1,null,null,null);e["a"]=l.exports},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),n=a("1d80"),r=a("129f"),s=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=n(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var n=o(t),l=String(this),c=n.lastIndex;r(c,0)||(n.lastIndex=0);var u=s(n,l);return r(n.lastIndex,c)||(n.lastIndex=c),null===u?-1:u.index}]}))},a434:function(t,e,a){"use strict";var i=a("23e7"),o=a("23cb"),n=a("a691"),r=a("50c4"),s=a("7b0b"),l=a("65f0"),c=a("8418"),u=a("1dde"),d=a("ae40"),p=u("splice"),f=d("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,h=Math.min,g=9007199254740991,b="Maximum allowed length exceeded";i({target:"Array",proto:!0,forced:!p||!f},{splice:function(t,e){var a,i,u,d,p,f,v=s(this),y=r(v.length),k=o(t,y),w=arguments.length;if(0===w?a=i=0:1===w?(a=0,i=y-k):(a=w-2,i=h(m(n(e),0),y-k)),y+a-i>g)throw TypeError(b);for(u=l(v,i),d=0;d<i;d++)p=k+d,p in v&&c(u,d,v[p]);if(u.length=i,a<i){for(d=k;d<y-i;d++)p=d+i,f=d+a,p in v?v[f]=v[p]:delete v[f];for(d=y;d>y-i+a;d--)delete v[d-1]}else if(a>i)for(d=y-i;d>k;d--)p=d+i-1,f=d+a-1,p in v?v[f]=v[p]:delete v[f];for(d=0;d<a;d++)v[d+k]=arguments[d+2];return v.length=y-i+a,u}})},ac65:function(t,e,a){},b2d4:function(t,e,a){"use strict";a("ac65")},d18b:function(t,e,a){},e4442:function(t,e,a){"use strict";a("d18b")}}]);