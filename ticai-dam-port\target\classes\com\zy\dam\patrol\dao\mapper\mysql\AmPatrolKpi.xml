<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolKpiDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.CATEGORY_
			,a.MODE_
			,a.CONTENT_
			,a.POINT_
			,a.ORD_
			,a.STATUS_
			,a.FLAG_
	</sql>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolKpiVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_KPI_ITEM m where m.FLAG_='1' and m.KPI_=a.ID_) item_count
        from AM_PATROL_KPI a
        where a.FLAG_='1' and a.CATEGORY_=#{category}
        <if test="keyword != null">and a.NAME_ like concat('%',#{keyword},'%')</if>
        order by a.ORD_,a.NO_
    </select>

    <!-- 获取唯一的资管-巡检指标项数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolKpi">
        select
        <include refid="meta"/>
        from AM_PATROL_KPI a where a.ID_=#{0}
    </select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolKpiVo">
        select
        <include refid="meta"/>
        from AM_PATROL_KPI a where a.ID_=#{0}
    </select>


    <select id="listCategory" resultType="com.zy.dam.patrol.vo.PatrolKpiCategory">
		select ID_,NAME_,ORD_ from AM_PATROL_CATEGORY where FLAG_='1' order by ORD_
	</select>

    <select id="listItem" resultType="com.zy.dam.patrol.vo.PatrolKpiItem">
		select ID_, KPI_,NO_,CONTENT_,POINT_,ORD_,FLAG_ from AM_PATROL_KPI_ITEM where FLAG_='1' and KPI_=#{0} order by ORD_,NO_
	</select>

    <select id="findNodeList" resultType="com.zy.model.VueTreeNode">
        select * from (
        select a.ID_,null PID_,a.NAME_ label, null leaf,a.ORD_ from AM_PATROL_CATEGORY a where a.FLAG_='1'
        union all
        select a.ID_,a.CATEGORY_ PID_,a.NAME_ label, 1 leaf,a.ORD_ from AM_PATROL_KPI a where a.FLAG_='1'
        ) p order by ORD_
    </select>

    <select id="pageAsset" resultType="com.zy.dam.patrol.vo.AssetKpiVo">
        select
        a.ID_
        ,a.NO_
        ,a.NAME_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_LOCATION m where m.ID_=a.LOCATION_) location_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select count(0) from AM_ASSET_KPI m where m.ASSET_=a.ID_) kpi_count
        from AM_ASSET a
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        order by a.NO_ desc
    </select>

    <select id="findAssetKpi" resultType="String">
        select KPI_ from AM_ASSET_KPI where ASSET_=#{0} order by ORD_
    </select>

    <select id="pageAssetType" resultType="com.zy.dam.patrol.vo.AssetTypeKpiVo">
        select a.CODE_,a.NAME_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.PCODE_) pname
        ,(select count(0) from AM_ASSET_TYPE_KPI m where m.TYPE_=a.CODE_) kpi_count
        from AM_ASSET_TYPE a
        where a.FLAG_='1'
        <if test="name != null">and a.NAME_ like concat('%', #{name}, '%')</if>
        order by a.ORD_,a.CODE_
    </select>

    <select id="findAssetTypeKpi" resultType="String">
        select KPI_ from AM_ASSET_TYPE_KPI where TYPE_=#{0} order by ORD_
    </select>

</mapper>
