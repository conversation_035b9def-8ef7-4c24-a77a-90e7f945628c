{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1752649876948}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1747730941642}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aScNCmltcG9ydCBQYWdlVGFibGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1BhZ2VUYWJsZS52dWUnDQppbXBvcnQgUmVnaW9uIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9SZWdpb24udnVlJw0KaW1wb3J0IFRyZWVCb3ggZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1RyZWVCb3gudnVlJw0KaW1wb3J0IFVzZXJDaG9zZW4gZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1VzZXJDaG9zZW4udnVlJw0KaW1wb3J0IE1hcExvY2F0aW9uIGZyb20gJ0Avdmlld3MvbWFwL3V0aWwvbG9jYXRpb24udnVlJw0KaW1wb3J0IFVwbG9hZEZpbGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1VwbG9hZEZpbGUudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgUGFnZVRhYmxlLCBSZWdpb24sIFRyZWVCb3gsIFVzZXJDaG9zZW4sIE1hcExvY2F0aW9uLCBVcGxvYWRGaWxlIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHJlZ2lvbkxpc3Q6IFtdLA0KICAgICAgZGVwdFRyZWU6IFtdLCAvLyDmnLrmnoTmlbDmja7liJfooagNCiAgICAgIGFjdGl2ZUl0ZW06IHt9LA0KICAgICAgdGFibGVIZWlnaHQ6IDMwMCwNCiAgICAgIHJlZ2lvblZpc2libGU6IGZhbHNlLA0KICAgICAgcmVnaW9uRGF0YTogeyByZWdpb246ICcnIH0sDQogICAgICByZWdpb25SdWxlczogew0KICAgICAgICBjb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWMuuWfn+e8lueggScsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXljLrln5/lkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIGRlcHQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5omA5bGe5py65p6EJywgdHJpZ2dlcjogJ2JsdXInIH1dLA0KICAgICAgICByZWdpb246IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5omA5Zyo5Yy65YiSJywgdHJpZ2dlcjogJ2JsdXInIH1dDQogICAgICB9LA0KICAgICAgcWZvcm06IHsga2V5d29yZDogJycgfSwNCiAgICAgIGxvY2F0aW9uVmlzaWJsZTogZmFsc2UsDQogICAgICBsb2NhdGlvbkRhdGE6IHsgcmVnaW9uOiAnJyB9LA0KICAgICAgbG9jYXRpb25SdWxlczogew0KICAgICAgICBjb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWcsOeCuee8lueggScsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlnLDngrnlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0NCiAgICAgIH0sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRMaXN0OiBbXSwNCiAgICAgIHVwbG9hZFZpc2libGU6IGZhbHNlLA0KICAgICAgc2hvd1VwbG9hZEFsbDogdHJ1ZSwNCiAgICAgIGFtTG9jYXRpb25Bc3NldDogW3t9XSwNCiAgICAgIC8vIOaJuemHj+S/ruaUueebuOWFsw0KICAgICAgYmF0Y2hGaWxlTGlzdDogW10sDQogICAgICBiYXRjaFVwbG9hZExpc3Q6IFtdLA0KICAgICAgYmF0Y2hVcGxvYWRWaXNpYmxlOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmxvYWREZXB0VHJlZSgpDQogICAgdGhpcy5sb2FkUmVnaW9uKCkNCiAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICB0aGlzLiRyZWZzLmdyaWQuc2V0TWF4SGVpZ2h0KE1hdGgubWF4KGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSAzODAsIDIwMCkpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmb3JtYXREYXRlVGltZShkYXRlVGltZSkgew0KICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICcnDQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVRpbWUpDQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpDQogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhkYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpDQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhkYXRlLmdldE1pbnV0ZXMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3Qgc2Vjb25kcyA9IFN0cmluZyhkYXRlLmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gDQogICAgfSwNCiAgICBsb2FkRGVwdFRyZWUoKSB7DQogICAgICB0aGlzLiRodHRwKCcvc3lzL2RlcHQvdHJlZUJ5VHlwZS8xJykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmRlcHRUcmVlID0gcmVzDQogICAgICB9KS5jYXRjaCgoKSA9PiB7IHRoaXMuJGFsZXJ0KCfliqDovb3mnLrmnoTmoJHlh7rplJknKSB9KQ0KICAgIH0sDQogICAgbG9hZFJlZ2lvbigpIHsNCiAgICAgIHRoaXMuJGh0dHAoJy9hbS9yZWdpb24vbGlzdCcpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5yZWdpb25MaXN0ID0gcmVzIHx8IFtdDQogICAgICAgIGlmICh0aGlzLmFjdGl2ZUl0ZW0gJiYgdGhpcy5hY3RpdmVJdGVtLmlkKSB7DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByZXMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgIGlmIChyZXNbaV0uaWQgPT09IHRoaXMuYWN0aXZlSXRlbS5pZCkgew0KICAgICAgICAgICAgICB0aGlzLmFjdGl2ZUl0ZW0gPSByZXNbaV0NCiAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNob3dSZWdpb24oaXRlbSkgew0KICAgICAgdGhpcy5hY3RpdmVJdGVtID0gaXRlbQ0KICAgICAgdGhpcy5xZm9ybS5yZWdpb24gPSBpdGVtLmlkDQogICAgICB0aGlzLnFmb3JtLmtleXdvcmQgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hMb2NhdGlvbigpDQogICAgfSwNCiAgICBhZGRSZWdpb24oKSB7DQogICAgICB0aGlzLnJlZ2lvblZpc2libGUgPSB0cnVlDQogICAgICB0aGlzLnJlZ2lvbkRhdGEgPSB7IG9yZDogMSB9DQogICAgfSwNCiAgICBlZGl0UmVnaW9uKCkgew0KICAgICAgaWYgKCF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHJldHVybg0KICAgICAgdGhpcy5yZWdpb25WaXNpYmxlID0gdHJ1ZQ0KICAgICAgY29uc3QganNvbiA9IEpTT04uc3RyaW5naWZ5KHRoaXMuYWN0aXZlSXRlbSkNCiAgICAgIHRoaXMucmVnaW9uRGF0YSA9IEpTT04ucGFyc2UoanNvbikNCiAgICB9LA0KICAgIHNhdmVSZWdpb24oKSB7DQogICAgICB0aGlzLiRyZWZzLnJlZ2lvbmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL3JlZ2lvbi9zYXZlJywgZGF0YTogdGhpcy5yZWdpb25EYXRhIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjljLrln5/kv6Hmga/miJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLnJlZ2lvblZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmxvYWRSZWdpb24oKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICByZW1vdmVSZWdpb24oKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XljLrln58sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgeyBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLCB0eXBlOiAnd2FybmluZycgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vcmVnaW9uL2RlbGV0ZS8nICsgdGhpcy5hY3RpdmVJdGVtLmlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgICB0aGlzLmFjdGl2ZUl0ZW0gPSB7fQ0KICAgICAgICAgICAgdGhpcy5sb2FkUmVnaW9uKCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7IH0pDQogICAgfSwNCiAgICBzZWFyY2hMb2NhdGlvbigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLlj4LmlbA6JywgSlNPTi5zdHJpbmdpZnkodGhpcy5xZm9ybSkpDQogICAgICB0aGlzLiRyZWZzLmdyaWQuc2VhcmNoKHRoaXMucWZvcm0pDQogICAgfSwNCiAgICBhZGRMb2NhdGlvbigpIHsNCiAgICAgIHRoaXMubG9jYXRpb25WaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5sb2NhdGlvbkRhdGEgPSB7IHJlZ2lvbjogdGhpcy5hY3RpdmVJdGVtLmlkIH0NCiAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0ID0gW10NCiAgICB9LA0KICAgIC8vIGVkaXRMb2NhdGlvbihpdGVtKSB7DQogICAgLy8gICB0aGlzLmxvY2F0aW9uVmlzaWJsZSA9IHRydWUNCiAgICAvLyAgIGNvbnN0IGpzb24gPSBKU09OLnN0cmluZ2lmeShpdGVtKQ0KICAgIC8vICAgdGhpcy5sb2NhdGlvbkRhdGEgPSBKU09OLnBhcnNlKGpzb24pDQogICAgLy8gfSwNCiAgICBlZGl0TG9jYXRpb24oaXRlbSkgew0KICAgICAgdGhpcy4kaHR0cCgnL2FtL2xvY2F0aW9uL2dldC8nICsgaXRlbS5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPiAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgdGhpcy5sb2NhdGlvblZpc2libGUgPSB0cnVlDQogICAgICAgICAgdGhpcy5sb2NhdGlvbkRhdGEgPSByZXMuZGF0YQ0KICAgICAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0ID0gcmVzLmRhdGEuYW1Mb2NhdGlvbkFzc2V0IHx8IFtdDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlTG9jYXRpb24oKSB7DQogICAgICB0aGlzLiRyZWZzLmxvY2F0aW9uZm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGRldGFpbHMgPSBbXQ0KICAgICAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0LmZvckVhY2gociA9PiBkZXRhaWxzLnB1c2goeyBzbjogci5zbiB9KSkNCiAgICAgICAgICBpZiAoIWRldGFpbHMubGVuZ3RoKSByZXR1cm4gdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flvZXlhaXnu4jnq6/kv6Hmga8nKQ0KICAgICAgICAgIHRoaXMubG9jYXRpb25EYXRhLmFtTG9jYXRpb25Bc3NldCA9IGRldGFpbHMNCiAgICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL2xvY2F0aW9uL3NhdmVEZXZpY2UnLCBkYXRhOiB0aGlzLmxvY2F0aW9uRGF0YSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5Yy65Z+f5L+h5oGv5oiQ5YqfJykNCiAgICAgICAgICAgICAgdGhpcy5sb2NhdGlvblZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgcmVtb3ZlTG9jYXRpb24oaXRlbSkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5Zyw54K5LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLCBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL2xvY2F0aW9uL2RlbGV0ZS8nICsgaXRlbS5pZCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hMb2NhdGlvbigpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4geyB9KQ0KICAgIH0sDQogICAgbWFwUGluKCkgew0KICAgICAgY29uc3QgbGwgPSB0aGlzLmxvY2F0aW9uRGF0YS5sYXQgPyB7IGxuZzogdGhpcy5sb2NhdGlvbkRhdGEubG5nLCBsYXQ6IHRoaXMubG9jYXRpb25EYXRhLmxhdCB9IDogbnVsbA0KICAgICAgdGhpcy4kcmVmcy5tYXBMb2NhdGlvbi5zaG93KGxsKQ0KICAgIH0sDQogICAgcGluZWQocikgew0KICAgICAgdGhpcy4kc2V0KHRoaXMubG9jYXRpb25EYXRhLCAnYWRkcmVzcycsIHIuYWRkcmVzcykNCiAgICAgIHRoaXMuJHNldCh0aGlzLmxvY2F0aW9uRGF0YSwgJ2xuZycsIHIubG5nbGF0ID8gci5sbmdsYXQubG5nIDogbnVsbCkNCiAgICAgIHRoaXMuJHNldCh0aGlzLmxvY2F0aW9uRGF0YSwgJ2xhdCcsIHIubG5nbGF0ID8gci5sbmdsYXQubGF0IDogbnVsbCkNCiAgICB9LA0KICAgIHVwbG9hZCgpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXQ0KICAgICAgdGhpcy51cGxvYWRMaXN0ID0gW10NCiAgICAgIHRoaXMudXBsb2FkVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIHVwbG9hZFJlbW92ZSgpIHsNCiAgICAgIHRoaXMudXBsb2FkTGlzdCA9IFtdDQogICAgfSwNCiAgICB0b2dnbGVFcnIoKSB7DQogICAgICB0aGlzLnNob3dVcGxvYWRBbGwgPSAhdGhpcy5zaG93VXBsb2FkQWxsDQogICAgfSwNCiAgICB1cGxvYWRlZChmaWxlTGlzdCkgew0KICAgICAgaWYgKGZpbGVMaXN0ICYmIGZpbGVMaXN0Lmxlbmd0aCkgew0KICAgICAgICBjb25zdCBsb2FkSW5zdCA9IExvYWRpbmcuc2VydmljZSh7IGZ1bGxzY3JlZW46IHRydWUsIHRleHQ6ICfop6PmnpDmlofku7bkuK0uLi4nIH0pDQogICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vdXBsb2FkRmlsZScsIGRhdGE6IGZpbGVMaXN0WzBdIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICB0aGlzLnNob3dVcGxvYWRBbGwgPSB0cnVlDQogICAgICAgICAgICB0aGlzLnVwbG9hZExpc3QgPSByZXMuZGF0YQ0KICAgICAgICAgIH0NCiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRVcGxvYWQoKSB7DQogICAgICBpZiAodGhpcy51cGxvYWRMaXN0Lmxlbmd0aCA9PT0gMCkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5Y+v5o+Q5Lqk55qE5pWw5o2uJykNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+aVsOaNruS4iuS8oOS4rS4uLicgfSkNCiAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vdXBsb2FkRGF0YScsIGRhdGE6IHRoaXMudXBsb2FkTGlzdCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykNCiAgICAgICAgICB0aGlzLnVwbG9hZFZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICAgICAgfSBlbHNlIGlmIChyZXMuY29kZSA9PT0gMikgew0KICAgICAgICAgIHRoaXMudXBsb2FkTGlzdCA9IHJlcy5kYXRhDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a2Y5Zyo6ZSZ6K+v55qE5pWw5o2u6KGMJykNCiAgICAgICAgfQ0KICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc6LaF5pe2JykNCiAgICAgIH0pDQogICAgfSwNCiAgICBhZGRBc3NldCgpIHsNCiAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0LnB1c2goe30pDQogICAgfSwNCiAgICByZW1vdmVBc3NldChyb3dJbmRleCkgew0KICAgICAgdGhpcy5hbUxvY2F0aW9uQXNzZXQuc3BsaWNlKHJvd0luZGV4LCAxKQ0KICAgIH0sDQogICAgLy8g5om56YeP5L+u5pS555u45YWz5pa55rOVDQogICAgaGFuZGxlQmF0Y2hDb21tYW5kKGNvbW1hbmQpIHsNCiAgICAgIGlmIChjb21tYW5kID09PSAnZXhwb3J0Jykgew0KICAgICAgICB0aGlzLmV4cG9ydEJhdGNoKCkNCiAgICAgIH0gZWxzZSBpZiAoY29tbWFuZCA9PT0gJ2ltcG9ydCcpIHsNCiAgICAgICAgdGhpcy5pbXBvcnRCYXRjaCgpDQogICAgICB9DQogICAgfSwNCiAgICBleHBvcnRCYXRjaCgpIHsNCiAgICAgIGlmICghdGhpcy5hY3RpdmVJdGVtIHx8ICF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5LiA5Liq5Yy65Z+fJykNCiAgICAgIH0NCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ato+WcqOWvvOWHuuaWh+S7tu+8jOivt+iAkOW/g+etieW+hS4uLicgfSkNCiAgICAgIHRoaXMuJGphc3Blcih7IHVybDogYC9hbS9sb2NhdGlvbi9leHBvcnRCYXRjaC8ke3RoaXMuYWN0aXZlSXRlbS5pZH1gLCByZXNwb25zZVR5cGU6ICdibG9iJyB9KS50aGVuKGJsb2IgPT4gew0KICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIHRoaXMuJHNhdmVBcyhibG9iLCAn572R54K55om56YeP5L+u5pS55qih5p2/Lnhsc3gnKQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflr7zlh7rnlJ/miJDlh7rplJk6JyArIGVycikNCiAgICAgIH0pDQogICAgfSwNCiAgICBpbXBvcnRCYXRjaCgpIHsNCiAgICAgIGlmICghdGhpcy5hY3RpdmVJdGVtIHx8ICF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5LiA5Liq5Yy65Z+fJykNCiAgICAgIH0NCiAgICAgIHRoaXMuYmF0Y2hGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLmJhdGNoVXBsb2FkTGlzdCA9IFtdDQogICAgICB0aGlzLmJhdGNoVXBsb2FkVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGJhdGNoVXBsb2FkUmVtb3ZlKCkgew0KICAgICAgdGhpcy5iYXRjaFVwbG9hZExpc3QgPSBbXQ0KICAgIH0sDQogICAgcmVtb3ZlQmF0Y2hSb3coaW5kZXgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOi/meadoeaVsOaNruWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRMaXN0LnNwbGljZShpbmRleCwgMSkNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBnZXRCYXRjaEVycm9yQ291bnQoKSB7DQogICAgICByZXR1cm4gdGhpcy5iYXRjaFVwbG9hZExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yb3dNc2cgIT0gbnVsbCkubGVuZ3RoDQogICAgfSwNCiAgICBiYXRjaFVwbG9hZGVkKGZpbGVMaXN0KSB7DQogICAgICBpZiAoZmlsZUxpc3QgJiYgZmlsZUxpc3QubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ino+aekOaWh+S7tuS4rS4uLicgfSkNCiAgICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9hbS9sb2NhdGlvbi91cGxvYWRCYXRjaEZpbGUnLCBkYXRhOiBmaWxlTGlzdFswXSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy5iYXRjaFVwbG9hZExpc3QgPSByZXMuZGF0YQ0KICAgICAgICAgIH0NCiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRCYXRjaFVwZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmJhdGNoVXBsb2FkTGlzdC5sZW5ndGggPT09IDApIHJldHVybiB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+aPkOS6pOeahOaVsOaNricpDQoNCiAgICAgIC8vIOi/h+a7pOWHuuayoeaciemUmeivr+eahOaVsOaNrg0KICAgICAgY29uc3QgdmFsaWREYXRhID0gdGhpcy5iYXRjaFVwbG9hZExpc3QuZmlsdGVyKGl0ZW0gPT4gIWl0ZW0ucm93TXNnKQ0KICAgICAgaWYgKHZhbGlkRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5pyJ5pWI55qE5pWw5o2u5Y+v5Lul5o+Q5Lqk77yM6K+35YWI5Yig6Zmk6ZSZ6K+v6KGM5oiW5L+u5q2j5pWw5o2uJykNCiAgICAgIH0NCg0KICAgICAgY29uc3QgZXJyb3JDb3VudCA9IHRoaXMuZ2V0QmF0Y2hFcnJvckNvdW50KCkNCiAgICAgIGlmIChlcnJvckNvdW50ID4gMCkgew0KICAgICAgICB0aGlzLiRjb25maXJtKGDlvZPliY3mnIkgJHtlcnJvckNvdW50fSDmnaHplJnor6/mlbDmja7lsIbooqvlv73nlaXvvIzlj6rmj5DkuqQgJHt2YWxpZERhdGEubGVuZ3RofSDmnaHmnInmlYjmlbDmja7vvIzmmK/lkKbnu6fnu63vvJ9gLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jaGVja0R1cGxpY2F0ZXNBbmRVcGRhdGUodmFsaWREYXRhKQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2hlY2tBbmRDb25maXJtRHVwbGljYXRlcyh2YWxpZERhdGEpDQogICAgICB9DQogICAgfSwNCiAgICBjaGVja0FuZENvbmZpcm1EdXBsaWNhdGVzKGRhdGEpIHsNCiAgICAgIC8vIOajgOafpemHjeWkjee8lueggQ0KICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9hbS9sb2NhdGlvbi9jaGVja0JhdGNoRHVwbGljYXRlcycsIGRhdGE6IGRhdGEgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDEgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBkdXBsaWNhdGVJbmZvID0gcmVzLmRhdGENCiAgICAgICAgICBpZiAoZHVwbGljYXRlSW5mby5oYXNEdXBsaWNhdGVzKSB7DQogICAgICAgICAgICBsZXQgbWVzc2FnZSA9ICcnDQogICAgICAgICAgICBjb25zdCBpbXBvcnREdXBsaWNhdGVzID0gZHVwbGljYXRlSW5mby5pbXBvcnREdXBsaWNhdGVzIHx8IFtdDQogICAgICAgICAgICBjb25zdCBkYkR1cGxpY2F0ZXMgPSBkdXBsaWNhdGVJbmZvLmRiRHVwbGljYXRlcyB8fCBbXQ0KDQogICAgICAgICAgICBpZiAoaW1wb3J0RHVwbGljYXRlcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOajgOa1i+WIsOS7peS4i+e9keeCuee8lueggeWcqOWvvOWFpeaVsOaNruS4remHjeWkjeWHuueOsO+8miR7aW1wb3J0RHVwbGljYXRlcy5qb2luKCcsICcpfeOAglxuYA0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBpZiAoZGJEdXBsaWNhdGVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgbWVzc2FnZSArPSBg5qOA5rWL5Yiw5Lul5LiL572R54K557yW56CB5Zyo5pWw5o2u5bqT5Lit5a2Y5Zyo5aSa5p2h6K6w5b2V77yaJHtkYkR1cGxpY2F0ZXMuam9pbignLCAnKX3jgIJcbmANCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOmHjeWkjee8lueggeWwhuS7peacgOWQjuS4gOadoeaVsOaNruS4uuWHhlxuYA0KICAgICAgICAgICAgbWVzc2FnZSArPSBg5piv5ZCm57un57ut5omn6KGM5om56YeP5pu05paw77yfYA0KDQogICAgICAgICAgICB0aGlzLiRjb25maXJtKG1lc3NhZ2UsICflj5HnjrDph43lpI3nvJbnoIEnLCB7DQogICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5pu05pawJywNCiAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiBmYWxzZQ0KICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZG9CYXRjaFVwZGF0ZShkYXRhKQ0KICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgICAvLyDnlKjmiLflj5bmtojvvIzkuI3miafooYzmm7TmlrANCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOayoeaciemHjeWkjee8luegge+8jOebtOaOpeabtOaWsA0KICAgICAgICAgICAgdGhpcy5kb0JhdGNoVXBkYXRlKGRhdGEpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOajgOafpeWksei0pe+8jOebtOaOpeabtOaWsA0KICAgICAgICAgIHRoaXMuZG9CYXRjaFVwZGF0ZShkYXRhKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIC8vIOajgOafpeWksei0pe+8jOebtOaOpeabtOaWsA0KICAgICAgICB0aGlzLmRvQmF0Y2hVcGRhdGUoZGF0YSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBkb0JhdGNoVXBkYXRlKGRhdGEpIHsNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+aVsOaNruS4iuS8oOS4rS4uLicgfSkNCiAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vYmF0Y2hVcGRhdGUnLCBkYXRhOiBkYXRhIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAxKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmibnph4/mm7TmlrDmiJDlip9gKQ0KICAgICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICAgICAgfSBlbHNlIGlmIChyZXMuY29kZSA9PT0gMikgew0KICAgICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRMaXN0ID0gcmVzLmRhdGENCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfpg6jliIbmlbDmja7mm7TmlrDlpLHotKXvvIzor7fmn6XnnIvplJnor6/kv6Hmga8nKQ0KICAgICAgICB9DQogICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, null]}