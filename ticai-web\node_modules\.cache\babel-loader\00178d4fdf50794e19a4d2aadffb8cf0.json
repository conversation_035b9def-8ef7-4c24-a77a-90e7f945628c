{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1752649876948}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Loading", "PageTable", "Region", "TreeBox", "UserChosen", "MapLocation", "UploadFile", "components", "data", "regionList", "deptTree", "activeItem", "tableHeight", "regionVisible", "regionData", "region", "regionRules", "code", "required", "message", "trigger", "name", "dept", "qform", "keyword", "locationVisible", "locationData", "locationRules", "fileList", "uploadList", "uploadVisible", "showUploadAll", "amLocationAsset", "batchFileList", "batchUploadList", "batchUploadVisible", "mounted", "loadDeptTree", "loadRegion", "searchLocation", "$refs", "grid", "setMaxHeight", "Math", "max", "document", "documentElement", "clientHeight", "methods", "formatDateTime", "dateTime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "_this", "$http", "then", "res", "catch", "$alert", "_this2", "id", "i", "length", "showRegion", "item", "addRegion", "ord", "editRegion", "json", "JSON", "stringify", "parse", "saveRegion", "_this3", "regionform", "validate", "valid", "url", "$message", "success", "removeRegion", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "console", "log", "search", "addLocation", "editLocation", "_this5", "saveLocation", "_this6", "locationform", "details", "for<PERSON>ach", "r", "push", "sn", "warning", "removeLocation", "_this7", "mapPin", "ll", "lat", "lng", "mapLocation", "show", "pined", "$set", "address", "lnglat", "upload", "uploadRemove", "toggleErr", "uploaded", "_this8", "loadInst", "service", "fullscreen", "text", "close", "submitUpload", "_this9", "$emit", "error", "addAsset", "removeAsset", "rowIndex", "splice", "handleBatchCommand", "command", "exportBatch", "importBatch", "_this0", "$jasper", "responseType", "blob", "$saveAs", "err", "batchUploadRemove", "removeBatchRow", "index", "_this1", "getBatchErrorCount", "filter", "rowMsg", "batchUploaded", "_this10", "submitBatchUpdate", "_this11", "validData", "errorCount", "checkDuplicatesAndUpdate", "checkAndConfirmDuplicates", "_this12", "duplicateInfo", "hasDuplicates", "importDuplicates", "dbDuplicates", "join", "dangerouslyUseHTMLString", "doBatchUpdate", "_this13"], "sources": ["src/views/bd/region/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"layout-lr\">\r\n    <div class=\"left\">\r\n      <div class=\"head\">区域列表</div>\r\n      <div class=\"body\">\r\n        <ul>\r\n          <li :class=\"{ act: activeItem == null || activeItem.id == null }\" @click=\"showRegion({})\">所有区域</li>\r\n          <li v-for=\"item in regionList\" :key=\"item.id\" :class=\"{ act: activeItem && activeItem.id == item.id }\"\r\n            @click=\"showRegion(item)\">{{ item.name }}</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <el-descriptions title=\"区域信息\" :column=\"3\" border class=\"descr-3\">\r\n        <template slot=\"extra\">\r\n          <div class=\"button-bar\">\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addRegion\">新增区域</el-button>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-upload\" @click=\"upload\">网点导入</el-button>\r\n            <template v-if=\"activeItem && activeItem.id\">\r\n              <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-edit\" @click=\"editRegion\">编辑区域</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"removeRegion\">删除区域</el-button>\r\n              <el-button type=\"success\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addLocation\">新增网点</el-button>\r\n              <el-dropdown @command=\"handleBatchCommand\" style=\"margin-left:10px\">\r\n                <el-button type=\"warning\" size=\"mini\">\r\n                  批量修改<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"export\">导出</el-dropdown-item>\r\n                  <el-dropdown-item command=\"import\">导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"activeItem && activeItem.id\">\r\n          <el-descriptions-item label=\"所属部门\">{{ activeItem.deptName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域代码\">{{ activeItem.code }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域名称\">{{ activeItem.name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"所在区划\">{{ activeItem.regionName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"负责人\">{{ activeItem.userName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"联系电话\">{{ activeItem.userPhone }}</el-descriptions-item>\r\n          <el-descriptions-item :span=\"3\" label=\"区域范围\">{{ activeItem.scope }}</el-descriptions-item>\r\n        </template>\r\n      </el-descriptions>\r\n      <el-divider></el-divider>\r\n      <div class=\"location-head\">\r\n        <span class=\"location-title\">区域网点</span>\r\n        <div class=\"location-search\">\r\n          <el-input v-model=\"qform.keyword\" clearable size=\"mini\" placeholder=\"输入关键字\" autocomplete=\"off\">\r\n            <template slot=\"prepend\">检索:</template>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchLocation\"></el-button>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <page-table ref=\"grid\" size=\"mini\" path=\"/am/location/page\" :query=\"qform\" stripe border>\r\n        <el-table-column v-if=\"activeItem == null || activeItem.id == null\" label=\"所属区域\" prop=\"regionName\" width=\"100\"\r\n          align=\"center\" />\r\n        <el-table-column label=\"网点编码\" prop=\"code\" width=\"110\" align=\"center\" />\r\n        <el-table-column label=\"网点名称\" prop=\"name\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"负责人\" prop=\"contact\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"联系方式\" prop=\"phone\" width=\"150\" align=\"center\" />\r\n        <el-table-column label=\"地址\" prop=\"address\" width=\"800\" align=\"center\" />\r\n        <el-table-column label=\"网点时间\" prop=\"createTime\" align=\"center\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.createTime ? formatDateTime(scope.row.createTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"{ row }\">\r\n            <div v-if=\"row.id != 'admin'\">\r\n              <el-button type=\"primary\" size=\"mini\" @click.stop=\"editLocation(row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" @click.stop=\"removeLocation(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n    <el-dialog v-dialog-drag title=\"区域信息\" width=\"800px\" :visible.sync=\"regionVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"regionform\" :model=\"regionData\" :rules=\"regionRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\" prop=\"code\">\r\n              <el-input v-model=\"regionData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\" prop=\"name\">\r\n              <el-input v-model=\"regionData.name\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <tree-box v-model=\"regionData.dept\" :data=\"deptTree\" :expand-all=\"true\" :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所在区划：\" prop=\"region\">\r\n              <region v-model=\"regionData.region\" root=\"460000\" :start-level=\"1\" with-root any-node\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"user\">\r\n              <user-chosen v-model=\"regionData.user\" type=\"1\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域排序：\" prop=\"ord\">\r\n              <el-input-number v-model=\"regionData.ord\" autocomplete=\"off\" :min=\"1\" :max=\"999\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"区域范围：\" prop=\"scope\">\r\n              <el-input v-model=\"regionData.scope\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"regionVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveRegion\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog v-dialog-drag title=\"区域地点\" width=\"800px\" :visible.sync=\"locationVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"locationform\" :model=\"locationData\" :rules=\"locationRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\">\r\n              <el-input v-model=\"activeItem.code\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\">\r\n              <el-input v-model=\"activeItem.name\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点编码：\" prop=\"code\">\r\n              <el-input v-model=\"locationData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点名称：\" prop=\"name\">\r\n              <el-input v-model=\"locationData.name\" maxlength=\"64\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"contact\">\r\n              <el-input v-model=\"locationData.contact\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话：\" prop=\"phone\">\r\n              <el-input v-model=\"locationData.phone\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点地址：\" prop=\"address\">\r\n              <el-input v-model=\"locationData.address\" maxlength=\"128\" autocomplete=\"off\">\r\n                <template slot=\"append\">\r\n                  <el-button size=\"mini\" icon=\"el-icon-map-location\" @click=\"mapPin\">地图定位</el-button>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点备注：\" prop=\"memo\">\r\n              <el-input v-model=\"locationData.memo\" maxlength=\"128\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table ref=\"optionGrid\" :data=\"amLocationAsset\" size=\"mini\" :stripe=\"true\" :border=\"true\">\r\n        <el-table-column type=\"index\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"sn\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.sn\" size=\"mini\" autocomplete=\"off\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column width=\"70\" align=\"center\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"danger\" @click.stop=\"removeAsset(scope.$index)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"locationVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveLocation\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!--------网点导入------->\r\n    <el-dialog ref=\"uploadDlg\" title=\"批量导入\" fullscreen class=\"dialog-full\" :visible.sync=\"uploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"fileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"uploaded\" @removeFile=\"uploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th>结果提示</th>\r\n              <th>所属市县编号</th>\r\n              <th>销售终端编号</th>\r\n              <th>门店编号</th>\r\n              <th>业主姓名</th>\r\n              <th>负责人</th>\r\n              <th>联系方式</th>\r\n              <th>门店地址</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"item in uploadList\">\r\n              <tr v-if=\"showUploadAll || item.rowMsg != null\" :key=\"item.rowNum\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ item.rowNum }}</td>\r\n                <td class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.region }}</td>\r\n                <td>{{ item.sn }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>{{ item.contact }}</td>\r\n                <td>{{ item.phone }}</td>\r\n                <td>{{ item.address }}</td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <el-button size=\"small\" type=\"warning\" @click=\"toggleErr\">{{ showUploadAll ? '查看错误' : '查看全部' }}</el-button>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"uploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitUpload\">确定上传</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog ref=\"batchUploadDlg\" title=\"网点批量修改\" fullscreen class=\"dialog-full\" :visible.sync=\"batchUploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"batchFileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"batchUploaded\" @removeFile=\"batchUploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th v-if=\"getBatchErrorCount() > 0\">结果提示</th>\r\n              <th>网点编码</th>\r\n              <th>网点名称</th>\r\n              <th>网点备注</th>\r\n              <th>操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"(item, index) in batchUploadList\">\r\n              <tr :key=\"index\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ index + 1 }}</td>\r\n                <td v-if=\"getBatchErrorCount() > 0\" class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>\r\n                  <el-input\r\n                    v-model=\"item.memo\"\r\n                    size=\"mini\"\r\n                    placeholder=\"请输入网点备注\"\r\n                    maxlength=\"200\"\r\n                    show-word-limit\r\n                    clearable\r\n                    style=\"width: 100%;\"\r\n                  />\r\n                </td>\r\n                <td>\r\n                  <el-button type=\"danger\" size=\"mini\" @click=\"removeBatchRow(index)\">删除</el-button>\r\n                </td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <span style=\"color: #909399; font-size: 12px;\">\r\n            共 {{ batchUploadList.length }} 条数据，其中 {{ getBatchErrorCount() }} 条错误\r\n          </span>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"batchUploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBatchUpdate\">确定更新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <map-location ref=\"mapLocation\" @success=\"pined\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { Loading } from 'element-ui'\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport Region from '@/views/components/Region.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport MapLocation from '@/views/map/util/location.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { PageTable, Region, TreeBox, UserChosen, MapLocation, UploadFile },\r\n  data() {\r\n    return {\r\n      regionList: [],\r\n      deptTree: [], // 机构数据列表\r\n      activeItem: {},\r\n      tableHeight: 300,\r\n      regionVisible: false,\r\n      regionData: { region: '' },\r\n      regionRules: {\r\n        code: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],\r\n        dept: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],\r\n        region: [{ required: true, message: '请选择所在区划', trigger: 'blur' }]\r\n      },\r\n      qform: { keyword: '' },\r\n      locationVisible: false,\r\n      locationData: { region: '' },\r\n      locationRules: {\r\n        code: [{ required: true, message: '请输入地点编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入地点名称', trigger: 'blur' }]\r\n      },\r\n      fileList: [],\r\n      uploadList: [],\r\n      uploadVisible: false,\r\n      showUploadAll: true,\r\n      amLocationAsset: [{}],\r\n      // 批量修改相关\r\n      batchFileList: [],\r\n      batchUploadList: [],\r\n      batchUploadVisible: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDeptTree()\r\n    this.loadRegion()\r\n    this.searchLocation()\r\n    this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight - 380, 200))\r\n  },\r\n  methods: {\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegion() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionList = res || []\r\n        if (this.activeItem && this.activeItem.id) {\r\n          for (let i = 0; i < res.length; i++) {\r\n            if (res[i].id === this.activeItem.id) {\r\n              this.activeItem = res[i]\r\n              break\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    showRegion(item) {\r\n      this.activeItem = item\r\n      this.qform.region = item.id\r\n      this.qform.keyword = ''\r\n      this.searchLocation()\r\n    },\r\n    addRegion() {\r\n      this.regionVisible = true\r\n      this.regionData = { ord: 1 }\r\n    },\r\n    editRegion() {\r\n      if (!this.activeItem.id) return\r\n      this.regionVisible = true\r\n      const json = JSON.stringify(this.activeItem)\r\n      this.regionData = JSON.parse(json)\r\n    },\r\n    saveRegion() {\r\n      this.$refs.regionform.validate(valid => {\r\n        if (valid) {\r\n          this.$http({ url: '/am/region/save', data: this.regionData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.regionVisible = false\r\n              this.loadRegion()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeRegion() {\r\n      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/region/delete/' + this.activeItem.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.activeItem = {}\r\n            this.loadRegion()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    searchLocation() {\r\n      console.log('搜索参数:', JSON.stringify(this.qform))\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    addLocation() {\r\n      this.locationVisible = true\r\n      this.locationData = { region: this.activeItem.id }\r\n      this.amLocationAsset = []\r\n    },\r\n    // editLocation(item) {\r\n    //   this.locationVisible = true\r\n    //   const json = JSON.stringify(item)\r\n    //   this.locationData = JSON.parse(json)\r\n    // },\r\n    editLocation(item) {\r\n      this.$http('/am/location/get/' + item.id).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          this.locationVisible = true\r\n          this.locationData = res.data\r\n          this.amLocationAsset = res.data.amLocationAsset || []\r\n        }\r\n      })\r\n    },\r\n    saveLocation() {\r\n      this.$refs.locationform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.amLocationAsset.forEach(r => details.push({ sn: r.sn }))\r\n          if (!details.length) return this.$message.warning('请录入终端信息')\r\n          this.locationData.amLocationAsset = details\r\n          this.$http({ url: '/am/location/saveDevice', data: this.locationData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.locationVisible = false\r\n              this.searchLocation()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeLocation(item) {\r\n      this.$confirm('此操作将永久删除该地点, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/location/delete/' + item.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.searchLocation()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    mapPin() {\r\n      const ll = this.locationData.lat ? { lng: this.locationData.lng, lat: this.locationData.lat } : null\r\n      this.$refs.mapLocation.show(ll)\r\n    },\r\n    pined(r) {\r\n      this.$set(this.locationData, 'address', r.address)\r\n      this.$set(this.locationData, 'lng', r.lnglat ? r.lnglat.lng : null)\r\n      this.$set(this.locationData, 'lat', r.lnglat ? r.lnglat.lat : null)\r\n    },\r\n    upload() {\r\n      this.fileList = []\r\n      this.uploadList = []\r\n      this.uploadVisible = true\r\n    },\r\n    uploadRemove() {\r\n      this.uploadList = []\r\n    },\r\n    toggleErr() {\r\n      this.showUploadAll = !this.showUploadAll\r\n    },\r\n    uploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.showUploadAll = true\r\n            this.uploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitUpload() {\r\n      if (this.uploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/uploadData', data: this.uploadList }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success('上传成功')\r\n          this.$emit('success')\r\n          this.uploadVisible = false\r\n          this.search()\r\n        } else if (res.code === 2) {\r\n          this.uploadList = res.data\r\n          this.$message.error('存在错误的数据行')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n        // this.$message.error('网络超时')\r\n      })\r\n    },\r\n    addAsset() {\r\n      this.amLocationAsset.push({})\r\n    },\r\n    removeAsset(rowIndex) {\r\n      this.amLocationAsset.splice(rowIndex, 1)\r\n    },\r\n    // 批量修改相关方法\r\n    handleBatchCommand(command) {\r\n      if (command === 'export') {\r\n        this.exportBatch()\r\n      } else if (command === 'import') {\r\n        this.importBatch()\r\n      }\r\n    },\r\n    exportBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: `/am/location/exportBatch/${this.activeItem.id}`, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '网点批量修改模板.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    importBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      this.batchFileList = []\r\n      this.batchUploadList = []\r\n      this.batchUploadVisible = true\r\n    },\r\n    batchUploadRemove() {\r\n      this.batchUploadList = []\r\n    },\r\n    removeBatchRow(index) {\r\n      this.$confirm('确定要删除这条数据吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchUploadList.splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    getBatchErrorCount() {\r\n      return this.batchUploadList.filter(item => item.rowMsg != null).length\r\n    },\r\n    batchUploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadBatchFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.batchUploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitBatchUpdate() {\r\n      if (this.batchUploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n\r\n      // 过滤出没有错误的数据\r\n      const validData = this.batchUploadList.filter(item => !item.rowMsg)\r\n      if (validData.length === 0) {\r\n        return this.$message.warning('没有有效的数据可以提交，请先删除错误行或修正数据')\r\n      }\r\n\r\n      const errorCount = this.getBatchErrorCount()\r\n      if (errorCount > 0) {\r\n        this.$confirm(`当前有 ${errorCount} 条错误数据将被忽略，只提交 ${validData.length} 条有效数据，是否继续？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.checkDuplicatesAndUpdate(validData)\r\n        }).catch(() => {})\r\n      } else {\r\n        this.checkAndConfirmDuplicates(validData)\r\n      }\r\n    },\r\n    checkAndConfirmDuplicates(data) {\r\n      // 检查重复编码\r\n      this.$http({ url: '/am/location/checkBatchDuplicates', data: data }).then(res => {\r\n        if (res.code === 1 && res.data) {\r\n          const duplicateInfo = res.data\r\n          if (duplicateInfo.hasDuplicates) {\r\n            let message = ''\r\n            const importDuplicates = duplicateInfo.importDuplicates || []\r\n            const dbDuplicates = duplicateInfo.dbDuplicates || []\r\n\r\n            if (importDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在导入数据中重复出现：${importDuplicates.join(', ')}。\\n`\r\n            }\r\n\r\n            if (dbDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在数据库中存在多条记录：${dbDuplicates.join(', ')}。\\n`\r\n            }\r\n            message += `重复编码将以最后一条数据为准\\n`\r\n            message += `是否继续执行批量更新？`\r\n\r\n            this.$confirm(message, '发现重复编码', {\r\n              confirmButtonText: '确定更新',\r\n              cancelButtonText: '取消',\r\n              type: 'warning',\r\n              dangerouslyUseHTMLString: false\r\n            }).then(() => {\r\n              this.doBatchUpdate(data)\r\n            }).catch(() => {\r\n              // 用户取消，不执行更新\r\n            })\r\n          } else {\r\n            // 没有重复编码，直接更新\r\n            this.doBatchUpdate(data)\r\n          }\r\n        } else {\r\n          // 检查失败，直接更新\r\n          this.doBatchUpdate(data)\r\n        }\r\n      }).catch(() => {\r\n        // 检查失败，直接更新\r\n        this.doBatchUpdate(data)\r\n      })\r\n    },\r\n    doBatchUpdate(data) {\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/batchUpdate', data: data }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success(`批量更新成功`)\r\n          this.batchUploadVisible = false\r\n          this.searchLocation()\r\n        } else if (res.code === 2) {\r\n          this.batchUploadList = res.data\r\n          this.$message.error('部分数据更新失败，请查看错误信息')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang='scss' scope>\r\n.layout-lr {\r\n  height: calc(100vh - 52px);\r\n}\r\n\r\n.layout-lr .left {\r\n  min-width: 220px;\r\n}\r\n\r\n.layout-lr .center {\r\n  overflow: hidden;\r\n}\r\n\r\n.location-head {\r\n  height: 40px;\r\n}\r\n\r\n.location-title {\r\n  line-height: 32px;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n}\r\n\r\n.location-search {\r\n  width: 300px;\r\n  float: right;\r\n}\r\n\r\n.upload-block {\r\n  padding: 0 4px;\r\n  width: calc(100vw - 12px);\r\n  height: calc(100vh - 250px);\r\n  overflow: auto;\r\n}\r\n\r\n.upload-block table {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.upload-block table th,\r\n.upload-block table td {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n}\r\n\r\n.upload-block table tr.err {\r\n  background-color: #faee92;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA,SAAAA,OAAA;AACA,OAAAC,SAAA;AACA,OAAAC,MAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAN,SAAA,EAAAA,SAAA;IAAAC,MAAA,EAAAA,MAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAC,UAAA,EAAAA;EAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,UAAA;QAAAC,MAAA;MAAA;MACAC,WAAA;QACAC,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,IAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,IAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAL,MAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAG,KAAA;QAAAC,OAAA;MAAA;MACAC,eAAA;MACAC,YAAA;QAAAX,MAAA;MAAA;MACAY,aAAA;QACAV,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,IAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAQ,QAAA;MACAC,UAAA;MACAC,aAAA;MACAC,aAAA;MACAC,eAAA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,UAAA;IACA,KAAAC,cAAA;IACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA,CAAAC,IAAA,CAAAC,GAAA,CAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACA5B,YAAA,WAAAA,aAAA;MAAA,IAAA+B,KAAA;MACA,KAAAC,KAAA,2BAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAA1D,QAAA,GAAA6D,GAAA;MACA,GAAAC,KAAA;QAAAJ,KAAA,CAAAK,MAAA;MAAA;IACA;IACAnC,UAAA,WAAAA,WAAA;MAAA,IAAAoC,MAAA;MACA,KAAAL,KAAA,oBAAAC,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAjE,UAAA,GAAA8D,GAAA;QACA,IAAAG,MAAA,CAAA/D,UAAA,IAAA+D,MAAA,CAAA/D,UAAA,CAAAgE,EAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,GAAA,CAAAM,MAAA,EAAAD,CAAA;YACA,IAAAL,GAAA,CAAAK,CAAA,EAAAD,EAAA,KAAAD,MAAA,CAAA/D,UAAA,CAAAgE,EAAA;cACAD,MAAA,CAAA/D,UAAA,GAAA4D,GAAA,CAAAK,CAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAE,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAApE,UAAA,GAAAoE,IAAA;MACA,KAAAxD,KAAA,CAAAR,MAAA,GAAAgE,IAAA,CAAAJ,EAAA;MACA,KAAApD,KAAA,CAAAC,OAAA;MACA,KAAAe,cAAA;IACA;IACAyC,SAAA,WAAAA,UAAA;MACA,KAAAnE,aAAA;MACA,KAAAC,UAAA;QAAAmE,GAAA;MAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,UAAAvE,UAAA,CAAAgE,EAAA;MACA,KAAA9D,aAAA;MACA,IAAAsE,IAAA,GAAAC,IAAA,CAAAC,SAAA,MAAA1E,UAAA;MACA,KAAAG,UAAA,GAAAsE,IAAA,CAAAE,KAAA,CAAAH,IAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA,CAAAiD,UAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAnB,KAAA;YAAAuB,GAAA;YAAApF,IAAA,EAAAgF,MAAA,CAAA1E;UAAA,GAAAwD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAtD,IAAA;cACAuE,MAAA,CAAAK,QAAA,CAAAC,OAAA;cACAN,MAAA,CAAA3E,aAAA;cACA2E,MAAA,CAAAlD,UAAA;YACA;UACA;QACA;MACA;IACA;IACAyD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA9B,IAAA;QACA0B,MAAA,CAAA3B,KAAA;UAAAuB,GAAA,yBAAAI,MAAA,CAAArF,UAAA,CAAAgE;QAAA,GAAAL,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACA+E,MAAA,CAAAH,QAAA,CAAAC,OAAA;YACAE,MAAA,CAAArF,UAAA;YACAqF,MAAA,CAAA1D,UAAA;UACA;QACA;MACA,GAAAkC,KAAA;IACA;IACAjC,cAAA,WAAAA,eAAA;MACA8D,OAAA,CAAAC,GAAA,UAAAlB,IAAA,CAAAC,SAAA,MAAA9D,KAAA;MACA,KAAAiB,KAAA,CAAAC,IAAA,CAAA8D,MAAA,MAAAhF,KAAA;IACA;IACAiF,WAAA,WAAAA,YAAA;MACA,KAAA/E,eAAA;MACA,KAAAC,YAAA;QAAAX,MAAA,OAAAJ,UAAA,CAAAgE;MAAA;MACA,KAAA3C,eAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACAyE,YAAA,WAAAA,aAAA1B,IAAA;MAAA,IAAA2B,MAAA;MACA,KAAArC,KAAA,uBAAAU,IAAA,CAAAJ,EAAA,EAAAL,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA,QAAAsD,GAAA,CAAA/D,IAAA;UACAkG,MAAA,CAAAjF,eAAA;UACAiF,MAAA,CAAAhF,YAAA,GAAA6C,GAAA,CAAA/D,IAAA;UACAkG,MAAA,CAAA1E,eAAA,GAAAuC,GAAA,CAAA/D,IAAA,CAAAwB,eAAA;QACA;MACA;IACA;IACA2E,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAApE,KAAA,CAAAqE,YAAA,CAAAnB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAmB,OAAA;UACAF,MAAA,CAAA5E,eAAA,CAAA+E,OAAA,WAAAC,CAAA;YAAA,OAAAF,OAAA,CAAAG,IAAA;cAAAC,EAAA,EAAAF,CAAA,CAAAE;YAAA;UAAA;UACA,KAAAJ,OAAA,CAAAjC,MAAA,SAAA+B,MAAA,CAAAf,QAAA,CAAAsB,OAAA;UACAP,MAAA,CAAAlF,YAAA,CAAAM,eAAA,GAAA8E,OAAA;UACAF,MAAA,CAAAvC,KAAA;YAAAuB,GAAA;YAAApF,IAAA,EAAAoG,MAAA,CAAAlF;UAAA,GAAA4C,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAtD,IAAA;cACA2F,MAAA,CAAAf,QAAA,CAAAC,OAAA;cACAc,MAAA,CAAAnF,eAAA;cACAmF,MAAA,CAAArE,cAAA;YACA;UACA;QACA;MACA;IACA;IACA6E,cAAA,WAAAA,eAAArC,IAAA;MAAA,IAAAsC,MAAA;MACA,KAAApB,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA9B,IAAA;QACA+C,MAAA,CAAAhD,KAAA;UAAAuB,GAAA,2BAAAb,IAAA,CAAAJ;QAAA,GAAAL,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAoG,MAAA,CAAAxB,QAAA,CAAAC,OAAA;YACAuB,MAAA,CAAA9E,cAAA;UACA;QACA;MACA,GAAAiC,KAAA;IACA;IACA8C,MAAA,WAAAA,OAAA;MACA,IAAAC,EAAA,QAAA7F,YAAA,CAAA8F,GAAA;QAAAC,GAAA,OAAA/F,YAAA,CAAA+F,GAAA;QAAAD,GAAA,OAAA9F,YAAA,CAAA8F;MAAA;MACA,KAAAhF,KAAA,CAAAkF,WAAA,CAAAC,IAAA,CAAAJ,EAAA;IACA;IACAK,KAAA,WAAAA,MAAAZ,CAAA;MACA,KAAAa,IAAA,MAAAnG,YAAA,aAAAsF,CAAA,CAAAc,OAAA;MACA,KAAAD,IAAA,MAAAnG,YAAA,SAAAsF,CAAA,CAAAe,MAAA,GAAAf,CAAA,CAAAe,MAAA,CAAAN,GAAA;MACA,KAAAI,IAAA,MAAAnG,YAAA,SAAAsF,CAAA,CAAAe,MAAA,GAAAf,CAAA,CAAAe,MAAA,CAAAP,GAAA;IACA;IACAQ,MAAA,WAAAA,OAAA;MACA,KAAApG,QAAA;MACA,KAAAC,UAAA;MACA,KAAAC,aAAA;IACA;IACAmG,YAAA,WAAAA,aAAA;MACA,KAAApG,UAAA;IACA;IACAqG,SAAA,WAAAA,UAAA;MACA,KAAAnG,aAAA,SAAAA,aAAA;IACA;IACAoG,QAAA,WAAAA,SAAAvG,QAAA;MAAA,IAAAwG,MAAA;MACA,IAAAxG,QAAA,IAAAA,QAAA,CAAAiD,MAAA;QACA,IAAAwD,QAAA,GAAArI,OAAA,CAAAsI,OAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACA,KAAAnE,KAAA;UAAAuB,GAAA;UAAApF,IAAA,EAAAoB,QAAA;QAAA,GAAA0C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAmH,MAAA,CAAArG,aAAA;YACAqG,MAAA,CAAAvG,UAAA,GAAA0C,GAAA,CAAA/D,IAAA;UACA;UACA6H,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA9G,UAAA,CAAAgD,MAAA,oBAAAgB,QAAA,CAAAsB,OAAA;MACA,IAAAkB,QAAA,GAAArI,OAAA,CAAAsI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAnE,KAAA;QAAAuB,GAAA;QAAApF,IAAA,OAAAqB;MAAA,GAAAyC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA;UACA0H,MAAA,CAAA9C,QAAA,CAAAC,OAAA;UACA6C,MAAA,CAAAC,KAAA;UACAD,MAAA,CAAA7G,aAAA;UACA6G,MAAA,CAAApC,MAAA;QACA,WAAAhC,GAAA,CAAAtD,IAAA;UACA0H,MAAA,CAAA9G,UAAA,GAAA0C,GAAA,CAAA/D,IAAA;UACAmI,MAAA,CAAA9C,QAAA,CAAAgD,KAAA;QACA;QACAR,QAAA,CAAAI,KAAA;MACA,GAAAjE,KAAA;QACA6D,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACAK,QAAA,WAAAA,SAAA;MACA,KAAA9G,eAAA,CAAAiF,IAAA;IACA;IACA8B,WAAA,WAAAA,YAAAC,QAAA;MACA,KAAAhH,eAAA,CAAAiH,MAAA,CAAAD,QAAA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAC,WAAA;MACA,WAAAD,OAAA;QACA,KAAAE,WAAA;MACA;IACA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MACA,UAAA3I,UAAA,UAAAA,UAAA,CAAAgE,EAAA;QACA,YAAAkB,QAAA,CAAAsB,OAAA;MACA;MACA,IAAAkB,QAAA,GAAArI,OAAA,CAAAsI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAe,OAAA;QAAA3D,GAAA,8BAAAzB,MAAA,MAAAxD,UAAA,CAAAgE,EAAA;QAAA6E,YAAA;MAAA,GAAAlF,IAAA,WAAAmF,IAAA;QACApB,QAAA,CAAAI,KAAA;QACAa,MAAA,CAAAI,OAAA,CAAAD,IAAA;MACA,GAAAjF,KAAA,WAAAmF,GAAA;QACAtB,QAAA,CAAAI,KAAA;QACAa,MAAA,CAAAzD,QAAA,CAAAgD,KAAA,aAAAc,GAAA;MACA;IACA;IACAN,WAAA,WAAAA,YAAA;MACA,UAAA1I,UAAA,UAAAA,UAAA,CAAAgE,EAAA;QACA,YAAAkB,QAAA,CAAAsB,OAAA;MACA;MACA,KAAAlF,aAAA;MACA,KAAAC,eAAA;MACA,KAAAC,kBAAA;IACA;IACAyH,iBAAA,WAAAA,kBAAA;MACA,KAAA1H,eAAA;IACA;IACA2H,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9B,IAAA;QACAyF,MAAA,CAAA7H,eAAA,CAAA+G,MAAA,CAAAa,KAAA;QACAC,MAAA,CAAAlE,QAAA,CAAAC,OAAA;MACA,GAAAtB,KAAA;IACA;IACAwF,kBAAA,WAAAA,mBAAA;MACA,YAAA9H,eAAA,CAAA+H,MAAA,WAAAlF,IAAA;QAAA,OAAAA,IAAA,CAAAmF,MAAA;MAAA,GAAArF,MAAA;IACA;IACAsF,aAAA,WAAAA,cAAAvI,QAAA;MAAA,IAAAwI,OAAA;MACA,IAAAxI,QAAA,IAAAA,QAAA,CAAAiD,MAAA;QACA,IAAAwD,QAAA,GAAArI,OAAA,CAAAsI,OAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACA,KAAAnE,KAAA;UAAAuB,GAAA;UAAApF,IAAA,EAAAoB,QAAA;QAAA,GAAA0C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAmJ,OAAA,CAAAlI,eAAA,GAAAqC,GAAA,CAAA/D,IAAA;UACA;UACA6H,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACA4B,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,SAAApI,eAAA,CAAA2C,MAAA,oBAAAgB,QAAA,CAAAsB,OAAA;;MAEA;MACA,IAAAoD,SAAA,QAAArI,eAAA,CAAA+H,MAAA,WAAAlF,IAAA;QAAA,QAAAA,IAAA,CAAAmF,MAAA;MAAA;MACA,IAAAK,SAAA,CAAA1F,MAAA;QACA,YAAAgB,QAAA,CAAAsB,OAAA;MACA;MAEA,IAAAqD,UAAA,QAAAR,kBAAA;MACA,IAAAQ,UAAA;QACA,KAAAvE,QAAA,uBAAA9B,MAAA,CAAAqG,UAAA,sFAAArG,MAAA,CAAAoG,SAAA,CAAA1F,MAAA;UACAqB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAA9B,IAAA;UACAgG,OAAA,CAAAG,wBAAA,CAAAF,SAAA;QACA,GAAA/F,KAAA;MACA;QACA,KAAAkG,yBAAA,CAAAH,SAAA;MACA;IACA;IACAG,yBAAA,WAAAA,0BAAAlK,IAAA;MAAA,IAAAmK,OAAA;MACA;MACA,KAAAtG,KAAA;QAAAuB,GAAA;QAAApF,IAAA,EAAAA;MAAA,GAAA8D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA,UAAAsD,GAAA,CAAA/D,IAAA;UACA,IAAAoK,aAAA,GAAArG,GAAA,CAAA/D,IAAA;UACA,IAAAoK,aAAA,CAAAC,aAAA;YACA,IAAA1J,OAAA;YACA,IAAA2J,gBAAA,GAAAF,aAAA,CAAAE,gBAAA;YACA,IAAAC,YAAA,GAAAH,aAAA,CAAAG,YAAA;YAEA,IAAAD,gBAAA,CAAAjG,MAAA;cACA1D,OAAA,+HAAAgD,MAAA,CAAA2G,gBAAA,CAAAE,IAAA;YACA;YAEA,IAAAD,YAAA,CAAAlG,MAAA;cACA1D,OAAA,qIAAAgD,MAAA,CAAA4G,YAAA,CAAAC,IAAA;YACA;YACA7J,OAAA;YACAA,OAAA;YAEAwJ,OAAA,CAAA1E,QAAA,CAAA9E,OAAA;cACA+E,iBAAA;cACAC,gBAAA;cACAC,IAAA;cACA6E,wBAAA;YACA,GAAA3G,IAAA;cACAqG,OAAA,CAAAO,aAAA,CAAA1K,IAAA;YACA,GAAAgE,KAAA;cACA;YAAA,CACA;UACA;YACA;YACAmG,OAAA,CAAAO,aAAA,CAAA1K,IAAA;UACA;QACA;UACA;UACAmK,OAAA,CAAAO,aAAA,CAAA1K,IAAA;QACA;MACA,GAAAgE,KAAA;QACA;QACAmG,OAAA,CAAAO,aAAA,CAAA1K,IAAA;MACA;IACA;IACA0K,aAAA,WAAAA,cAAA1K,IAAA;MAAA,IAAA2K,OAAA;MACA,IAAA9C,QAAA,GAAArI,OAAA,CAAAsI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAnE,KAAA;QAAAuB,GAAA;QAAApF,IAAA,EAAAA;MAAA,GAAA8D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA;UACAkK,OAAA,CAAAtF,QAAA,CAAAC,OAAA;UACAqF,OAAA,CAAAhJ,kBAAA;UACAgJ,OAAA,CAAA5I,cAAA;QACA,WAAAgC,GAAA,CAAAtD,IAAA;UACAkK,OAAA,CAAAjJ,eAAA,GAAAqC,GAAA,CAAA/D,IAAA;UACA2K,OAAA,CAAAtF,QAAA,CAAAgD,KAAA;QACA;QACAR,QAAA,CAAAI,KAAA;MACA,GAAAjE,KAAA;QACA6D,QAAA,CAAAI,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}