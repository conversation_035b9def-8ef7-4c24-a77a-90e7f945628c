<view class="container"><view data-event-opts="{{[['tap',[['backScan',['$event']]]]]}}" style="text-align:center;" bindtap="__e"><uni-icons vue-id="282bdff4-1" type="scan" size="80" bind:__l="__l"></uni-icons></view><uni-row vue-id="282bdff4-2" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('282bdff4-3')+','+('282bdff4-2')}}" span="{{18}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" style="height:60rpx;" vue-id="{{('282bdff4-4')+','+('282bdff4-3')}}" type="text" placeholder="请输入资产编码搜索资产信息" value="{{no}}" data-event-opts="{{[['^input',[['__set_model',['','no','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('282bdff4-5')+','+('282bdff4-2')}}" span="{{6}}" bind:__l="__l" vue-slots="{{['default']}}"><button class="ceshi" type="primary" plain="true" data-event-opts="{{[['tap',[['searchNo',['$event']]]]]}}" bindtap="__e">搜索</button></uni-col></uni-row><text class="caption">资产退库列表：</text><view class="list-block"><block wx:if="{{$root.g0}}"><view><block wx:for="{{doneList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">资产编码：</view><view class="text">{{item.no}}</view></view><view class="row"><view class="label">资产名称：</view><view class="text">{{item.name}}</view></view><view class="row"><view class="label">领用人：</view><view class="text">{{item.useUserName}}</view></view><view class="row"><view class="label">终端号：</view><view class="text">{{item.sn}}</view></view><view class="row"><view class="label">网点名称：</view><view class="text">{{item.locationName}}</view></view></view></block></view></block></view><view class="test"></view><view class="back"><button type="primary" data-event-opts="{{[['tap',[['saveBack',['$event']]]]]}}" bindtap="__e">提交退库</button></view></view>