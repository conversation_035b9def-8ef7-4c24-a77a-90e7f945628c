<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPathDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.MEMO_
			,a.DISTANCE_
			,a.RADIUS_
			,a.ORD_
			,a.FLAG_
	</sql>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPathVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_PATH_POINT m where m.PATH_=a.ID_) point_count
        from AM_PATROL_PATH a
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        order by a.ORD_,a.NO_
    </select>

    <select id="listSimple" resultType="com.zy.model.SimpleData">
        select a.ID_,a.NO_,a.NAME_
        from AM_PATROL_PATH a
        where a.FLAG_='1'
        order by a.ORD_,a.NO_
    </select>

    <!-- 获取唯一的资管-巡检路线数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPath">
        select
        <include refid="meta"/>
        from AM_PATROL_PATH a where a.ID_=#{0}
    </select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolPathVo">
        select
        <include refid="meta"/>
        ,(select count(0) from AM_PATROL_PATH_POINT m where m.PATH_=a.ID_) point_count
        from AM_PATROL_PATH a where a.ID_=#{0}
    </select>
</mapper>
