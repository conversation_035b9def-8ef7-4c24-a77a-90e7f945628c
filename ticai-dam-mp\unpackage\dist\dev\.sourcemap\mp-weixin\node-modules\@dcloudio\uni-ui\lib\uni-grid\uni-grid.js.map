{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?70cf", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?70ed", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?ad2c", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?e733", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?f9e6", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue?2847"], "names": ["name", "emits", "props", "column", "type", "default", "showBorder", "borderColor", "square", "highlight", "provide", "grid", "data", "elId", "width", "created", "mounted", "methods", "init", "setTimeout", "item", "change", "_getSize", "uni", "in", "select", "boundingClientRect", "exec", "fn"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgyB,CAAgB,gzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACapzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,eAWA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAEAC,0BACAC,SACAC,8BACAC,qBACAC;QACA;QACAC;MACA;IAQA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA2/C,CAAgB,+8CAAG,EAAC,C;;;;;;;;;;;ACA/gD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-grid.vue?vue&type=template&id=302e5534&scoped=true&\"\nvar renderjs\nimport script from \"./uni-grid.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-grid.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-grid.vue?vue&type=style&index=0&id=302e5534&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"302e5534\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-grid.vue?vue&type=template&id=302e5534&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-grid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-grid.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-grid-wrap\">\r\n\t\t<view :id=\"elId\" ref=\"uni-grid\" class=\"uni-grid\" :class=\"{ 'uni-grid--border': showBorder }\" :style=\"{ 'border-left-color':borderColor}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = uni.requireNativePlugin('dom');\r\n\t// #endif\r\n\r\n\t/**\r\n\t * Grid 宫格\r\n\t * @description 宫格组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=27\r\n\t * @property {Number} column 每列显示个数\r\n\t * @property {String} borderColor 边框颜色\r\n\t * @property {Boolean} showBorder 是否显示边框\r\n\t * @property {Boolean} square 是否方形显示\r\n\t * @property {Boolean} Boolean 点击背景是否高亮\r\n\t * @event {Function} change 点击 grid 触发，e={detail:{index:0}}，index 为当前点击 gird 下标\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniGrid',\r\n\t\temits:['change'],\r\n\t\tprops: {\r\n\t\t\t// 每列显示个数\r\n\t\t\tcolumn: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 3\r\n\t\t\t},\r\n\t\t\t// 是否显示边框\r\n\t\t\tshowBorder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 边框颜色\r\n\t\t\tborderColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#D2D2D2'\r\n\t\t\t},\r\n\t\t\t// 是否正方形显示,默认为 true\r\n\t\t\tsquare: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\thighlight: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tgrid: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tconst elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\t\treturn {\r\n\t\t\t\telId,\r\n\t\t\t\twidth: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.children = []\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\tthis.init()\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis._getSize((width) => {\r\n\t\t\t\t\t\tthis.children.forEach((item, index) => {\r\n\t\t\t\t\t\t\titem.width = width\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 50)\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.$emit('change', e)\r\n\t\t\t},\r\n\t\t\t_getSize(fn) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t.in(this)\r\n\t\t\t\t\t.select(`#${this.elId}`)\r\n\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t.exec(ret => {\r\n\t\t\t\t\t\tthis.width = parseInt((ret[0].width - 1) / this.column) + 'px'\r\n\t\t\t\t\t\tfn(this.width)\r\n\t\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tdom.getComponentRect(this.$refs['uni-grid'], (ret) => {\r\n\t\t\t\t\tthis.width = parseInt((ret.size.width - 1) / this.column) + 'px'\r\n\t\t\t\t\tfn(this.width)\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.uni-grid-wrap {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: column;\r\n\t\t/* #ifdef H5 */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-grid {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\t// flex: 1;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.uni-grid--border {\n\t\tposition: relative;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-left-color: #D2D2D2;\r\n\t\tborder-left-style: solid;\r\n\t\tborder-left-width: 0.5px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 1;\n\t\tborder-left: 1px #D2D2D2 solid;\n\t\t/* #endif */\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-grid.vue?vue&type=style&index=0&id=302e5534&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-grid.vue?vue&type=style&index=0&id=302e5534&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650629204\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}