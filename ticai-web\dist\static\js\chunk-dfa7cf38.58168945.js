(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-dfa7cf38"],{"06c5":function(t,e,s){"use strict";s.d(e,"a",(function(){return r}));s("a630"),s("fb6a"),s("b0c0"),s("d3b7"),s("ac1f"),s("00b4"),s("25f0"),s("3ca3");var i=s("6b75");function r(t,e){if(t){if("string"==typeof t)return Object(i["a"])(t,e);var s={}.toString.call(t).slice(8,-1);return"Object"===s&&t.constructor&&(s=t.constructor.name),"Map"===s||"Set"===s?Array.from(t):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?Object(i["a"])(t,e):void 0}}},"13d5":function(t,e,s){"use strict";var i=s("23e7"),r=s("d58f").left,a=s("a640"),n=s("ae40"),o=a("reduce"),h=n("reduce",{1:0});i({target:"Array",proto:!0,forced:!o||!h},{reduce:function(t){return r(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},1861:function(t,e,s){"use strict";function i(t,e){if(null==t)return{};var s={};for(var i in t)if({}.hasOwnProperty.call(t,i)){if(-1!==e.indexOf(i))continue;s[i]=t[i]}return s}Object.defineProperty(e,"__esModule",{value:!0});class r{constructor(t,e,s){this.line=void 0,this.column=void 0,this.index=void 0,this.line=t,this.column=e,this.index=s}}class a{constructor(t,e){this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=t,this.end=e}}function n(t,e){const{line:s,column:i,index:a}=t;return new r(s,i+e,a+e)}const o="BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED";var h={ImportMetaOutsideModule:{message:"import.meta may appear only with 'sourceType: \"module\"'",code:o},ImportOutsideModule:{message:"'import' and 'export' may appear only with 'sourceType: \"module\"'",code:o}};const c={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},p=t=>"UpdateExpression"===t.type?c.UpdateExpression[""+t.prefix]:c[t.type];var l={AccessorIsGenerator:({kind:t})=>`A ${t}ter cannot be a generator.`,ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitUsingNotInAsyncContext:"'await using' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",BadGetterArity:"A 'get' accessor must not have any formal parameters.",BadSetterArity:"A 'set' accessor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accessor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:({kind:t})=>`Missing initializer in ${t} declaration.`,DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.",DecoratorsBeforeAfterExport:"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeferImportRequiresNamespace:'Only `import defer * as x from "./module"` is valid.',DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:({exportName:t})=>`\`${t}\` has already been exported. Exported identifiers must be unique.`,DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",DynamicImportPhaseRequiresImportExpressions:({phase:t})=>`'import.${t}(...)' can only be parsed when using the 'createImportExpressions' option.`,ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:({localName:t,exportName:e})=>`A string literal cannot be used as an exported binding without \`from\`.\n- Did you mean \`export { '${t}' as '${e}' } from 'some-module'\`?`,ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:({type:t})=>`'${"ForInStatement"===t?"for-in":"for-of"}' loop variable declaration may not have an initializer.`,ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:({type:t})=>`Unsyntactic ${"BreakStatement"===t?"break":"continue"}.`,IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportAttributesUseAssert:"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.",ImportBindingIsString:({importName:t})=>`A string literal cannot be used as an imported binding.\n- Did you mean \`import { "${t}" as foo }\`?`,ImportCallArity:"`import()` requires exactly one or two arguments.",ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:({radix:t})=>`Expected number in radix ${t}.`,InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:({reservedWord:t})=>`Escape sequence in keyword ${t}.`,InvalidIdentifier:({identifierName:t})=>`Invalid identifier ${t}.`,InvalidLhs:({ancestor:t})=>`Invalid left-hand side in ${p(t)}.`,InvalidLhsBinding:({ancestor:t})=>`Binding invalid left-hand side in ${p(t)}.`,InvalidLhsOptionalChaining:({ancestor:t})=>`Invalid optional chaining in the left-hand side of ${p(t)}.`,InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:({unexpected:t})=>`Unexpected character '${t}'.`,InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:({identifierName:t})=>`Private name #${t} is not defined.`,InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:({labelName:t})=>`Label '${t}' is already declared.`,LetInLexicalBinding:"'let' is disallowed as a lexically bound name.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:({missingPlugin:t})=>`This experimental syntax requires enabling the parser plugin: ${t.map(t=>JSON.stringify(t)).join(", ")}.`,MissingOneOfPlugins:({missingPlugin:t})=>`This experimental syntax requires enabling one of the following parser plugin(s): ${t.map(t=>JSON.stringify(t)).join(", ")}.`,MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:({key:t})=>`Duplicate key "${t}" is not allowed in module attributes.`,ModuleExportNameHasLoneSurrogate:({surrogateCharCode:t})=>`An export name cannot include a lone surrogate, found '\\u${t.toString(16)}'.`,ModuleExportUndefined:({localName:t})=>`Export '${t}' is not defined.`,MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:({identifierName:t})=>`Private names are only allowed in property accesses (\`obj.#${t}\`) or in \`in\` expressions (\`#${t} in obj\`).`,PrivateNameRedeclaration:({identifierName:t})=>`Duplicate private name #${t}.`,RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level or inside a block.",SloppyFunctionAnnexB:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",SourcePhaseImportRequiresDefault:'Only `import source x from "./module"` is valid.',StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:({keyword:t})=>`Unexpected keyword '${t}'.`,UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:({reservedWord:t})=>`Unexpected reserved word '${t}'.`,UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:({expected:t,unexpected:e})=>`Unexpected token${e?` '${e}'.`:""}${t?`, expected "${t}"`:""}`,UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script`.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:({target:t,onlyValidPropertyName:e})=>`The only valid meta property for ${t} is ${t}.${e}.`,UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationExport:"Using declaration cannot be exported.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:({identifierName:t})=>`Identifier '${t}' has already been declared.`,YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",YieldNotInGeneratorFunction:"'yield' is only allowed within generator functions.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},u={StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:({referenceName:t})=>`Assigning to '${t}' in strict mode.`,StrictEvalArgumentsBinding:({bindingName:t})=>`Binding '${t}' in strict mode.`,StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."};const d=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]);var m=Object.assign({PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:({token:t})=>`Invalid topic token ${t}. In order to use ${t} as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "${t}" }.`,PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:({type:t})=>`Hack-style pipe body cannot be an unparenthesized ${p({type:t})}; please wrap it in parentheses.`},{PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'});const f=["message"];function y(t,e,s){Object.defineProperty(t,e,{enumerable:!1,configurable:!0,value:s})}function x({toMessage:t,code:e,reasonCode:s,syntaxPlugin:i}){const a="MissingPlugin"===s||"MissingOneOfPlugins"===s;{const t={AccessorCannotDeclareThisParameter:"AccesorCannotDeclareThisParameter",AccessorCannotHaveTypeParameters:"AccesorCannotHaveTypeParameters",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference",SetAccessorCannotHaveOptionalParameter:"SetAccesorCannotHaveOptionalParameter",SetAccessorCannotHaveRestParameter:"SetAccesorCannotHaveRestParameter",SetAccessorCannotHaveReturnType:"SetAccesorCannotHaveReturnType"};t[s]&&(s=t[s])}return function n(o,h){const c=new SyntaxError;return c.code=e,c.reasonCode=s,c.loc=o,c.pos=o.index,c.syntaxPlugin=i,a&&(c.missingPlugin=h.missingPlugin),y(c,"clone",(function(t={}){var e;const{line:s,column:i,index:a}=null!=(e=t.loc)?e:o;return n(new r(s,i,a),Object.assign({},h,t.details))})),y(c,"details",h),Object.defineProperty(c,"message",{configurable:!0,get(){const e=`${t(h)} (${o.line}:${o.column})`;return this.message=e,e},set(t){Object.defineProperty(this,"message",{value:t,writable:!0})}}),c}}function g(t,e){if(Array.isArray(t))return e=>g(e,t[0]);const s={};for(const r of Object.keys(t)){const a=t[r],n="string"===typeof a?{message:()=>a}:"function"===typeof a?{message:a}:a,{message:o}=n,h=i(n,f),c="string"===typeof o?()=>o:o;s[r]=x(Object.assign({code:"BABEL_PARSER_SYNTAX_ERROR",reasonCode:r,toMessage:c},e?{syntaxPlugin:e}:{},h))}return s}const P=Object.assign({},g(h),g(l),g(u),g`pipelineOperator`(m));function b(){return{sourceType:"script",sourceFilename:void 0,startIndex:0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowNewTargetOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,allowYieldOutsideFunction:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createImportExpressions:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0,annexB:!0}}function T(t){const e=b();if(null==t)return e;if(null!=t.annexB&&!1!==t.annexB)throw new Error("The `annexB` option can only be set to `false`.");for(const s of Object.keys(e))null!=t[s]&&(e[s]=t[s]);if(1===e.startLine)null==t.startIndex&&e.startColumn>0?e.startIndex=e.startColumn:null==t.startColumn&&e.startIndex>0&&(e.startColumn=e.startIndex);else if((null==t.startColumn||null==t.startIndex)&&null!=t.startIndex)throw new Error("With a `startLine > 1` you must also specify `startIndex` and `startColumn`.");return e}const{defineProperty:A}=Object,S=(t,e)=>{t&&A(t,e,{enumerable:!1,value:t[e]})};function E(t){return S(t.loc.start,"index"),S(t.loc.end,"index"),t}var w=t=>class extends t{parse(){const t=E(super.parse());return 256&this.optionFlags&&(t.tokens=t.tokens.map(E)),t}parseRegExpLiteral({pattern:t,flags:e}){let s=null;try{s=new RegExp(t,e)}catch(r){}const i=this.estreeParseLiteral(s);return i.regex={pattern:t,flags:e},i}parseBigIntLiteral(t){let e;try{e=BigInt(t)}catch(i){e=null}const s=this.estreeParseLiteral(e);return s.bigint=String(s.value||t),s}parseDecimalLiteral(t){const e=null,s=this.estreeParseLiteral(e);return s.decimal=String(s.value||t),s}estreeParseLiteral(t){return this.parseLiteral(t,"Literal")}parseStringLiteral(t){return this.estreeParseLiteral(t)}parseNumericLiteral(t){return this.estreeParseLiteral(t)}parseNullLiteral(){return this.estreeParseLiteral(null)}parseBooleanLiteral(t){return this.estreeParseLiteral(t)}estreeParseChainExpression(t,e){const s=this.startNodeAtNode(t);return s.expression=t,this.finishNodeAt(s,"ChainExpression",e)}directiveToStmt(t){const e=t.value;delete t.value,this.castNodeTo(e,"Literal"),e.raw=e.extra.raw,e.value=e.extra.expressionValue;const s=this.castNodeTo(t,"ExpressionStatement");return s.expression=e,s.directive=e.extra.rawValue,delete e.extra,s}fillOptionalPropertiesForTSESLint(t){}cloneEstreeStringLiteral(t){const{start:e,end:s,loc:i,range:r,raw:a,value:n}=t,o=Object.create(t.constructor.prototype);return o.type="Literal",o.start=e,o.end=s,o.loc=i,o.range=r,o.raw=a,o.value=n,o}initFunction(t,e){super.initFunction(t,e),t.expression=!1}checkDeclaration(t){null!=t&&this.isObjectProperty(t)?this.checkDeclaration(t.value):super.checkDeclaration(t)}getObjectOrClassMethodParams(t){return t.value.params}isValidDirective(t){var e;return"ExpressionStatement"===t.type&&"Literal"===t.expression.type&&"string"===typeof t.expression.value&&!(null!=(e=t.expression.extra)&&e.parenthesized)}parseBlockBody(t,e,s,i,r){super.parseBlockBody(t,e,s,i,r);const a=t.directives.map(t=>this.directiveToStmt(t));t.body=a.concat(t.body),delete t.directives}parsePrivateName(){const t=super.parsePrivateName();return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(t):t}convertPrivateNameToPrivateIdentifier(t){const e=super.getPrivateNameSV(t);return t=t,delete t.id,t.name=e,this.castNodeTo(t,"PrivateIdentifier")}isPrivateName(t){return this.getPluginOption("estree","classFeatures")?"PrivateIdentifier"===t.type:super.isPrivateName(t)}getPrivateNameSV(t){return this.getPluginOption("estree","classFeatures")?t.name:super.getPrivateNameSV(t)}parseLiteral(t,e){const s=super.parseLiteral(t,e);return s.raw=s.extra.raw,delete s.extra,s}parseFunctionBody(t,e,s=!1){super.parseFunctionBody(t,e,s),t.expression="BlockStatement"!==t.body.type}parseMethod(t,e,s,i,r,a,n=!1){let o=this.startNode();o.kind=t.kind,o=super.parseMethod(o,e,s,i,r,a,n),delete o.kind;const{typeParameters:h}=t;h&&(delete t.typeParameters,o.typeParameters=h,this.resetStartLocationFromNode(o,h));const c=this.castNodeTo(o,"FunctionExpression");return t.value=c,"ClassPrivateMethod"===a&&(t.computed=!1),"ObjectMethod"===a?("method"===t.kind&&(t.kind="init"),t.shorthand=!1,this.finishNode(t,"Property")):this.finishNode(t,"MethodDefinition")}nameIsConstructor(t){return"Literal"===t.type?"constructor"===t.value:super.nameIsConstructor(t)}parseClassProperty(...t){const e=super.parseClassProperty(...t);return this.getPluginOption("estree","classFeatures")?(this.castNodeTo(e,"PropertyDefinition"),e):e}parseClassPrivateProperty(...t){const e=super.parseClassPrivateProperty(...t);return this.getPluginOption("estree","classFeatures")?(this.castNodeTo(e,"PropertyDefinition"),e.computed=!1,e):e}parseClassAccessorProperty(t){const e=super.parseClassAccessorProperty(t);return this.getPluginOption("estree","classFeatures")?(e.abstract&&this.hasPlugin("typescript")?(delete e.abstract,this.castNodeTo(e,"TSAbstractAccessorProperty")):this.castNodeTo(e,"AccessorProperty"),e):e}parseObjectProperty(t,e,s,i){const r=super.parseObjectProperty(t,e,s,i);return r&&(r.kind="init",this.castNodeTo(r,"Property")),r}finishObjectProperty(t){return t.kind="init",this.finishNode(t,"Property")}isValidLVal(t,e,s){return"Property"===t?"value":super.isValidLVal(t,e,s)}isAssignable(t,e){return null!=t&&this.isObjectProperty(t)?this.isAssignable(t.value,e):super.isAssignable(t,e)}toAssignable(t,e=!1){if(null!=t&&this.isObjectProperty(t)){const{key:s,value:i}=t;this.isPrivateName(s)&&this.classScope.usePrivateName(this.getPrivateNameSV(s),s.loc.start),this.toAssignable(i,e)}else super.toAssignable(t,e)}toAssignableObjectExpressionProp(t,e,s){"Property"!==t.type||"get"!==t.kind&&"set"!==t.kind?"Property"===t.type&&t.method?this.raise(P.PatternHasMethod,t.key):super.toAssignableObjectExpressionProp(t,e,s):this.raise(P.PatternHasAccessor,t.key)}finishCallExpression(t,e){const s=super.finishCallExpression(t,e);var i,r;"Import"===s.callee.type?(this.castNodeTo(s,"ImportExpression"),s.source=s.arguments[0],s.options=null!=(i=s.arguments[1])?i:null,s.attributes=null!=(r=s.arguments[1])?r:null,delete s.arguments,delete s.callee):"OptionalCallExpression"===s.type?this.castNodeTo(s,"CallExpression"):s.optional=!1;return s}toReferencedArguments(t){"ImportExpression"!==t.type&&super.toReferencedArguments(t)}parseExport(t,e){const s=this.state.lastTokStartLoc,i=super.parseExport(t,e);switch(i.type){case"ExportAllDeclaration":i.exported=null;break;case"ExportNamedDeclaration":1===i.specifiers.length&&"ExportNamespaceSpecifier"===i.specifiers[0].type&&(this.castNodeTo(i,"ExportAllDeclaration"),i.exported=i.specifiers[0].exported,delete i.specifiers);case"ExportDefaultDeclaration":{var r;const{declaration:t}=i;"ClassDeclaration"===(null==t?void 0:t.type)&&(null==(r=t.decorators)?void 0:r.length)>0&&t.start===i.start&&this.resetStartLocation(i,s)}break}return i}stopParseSubscript(t,e){const s=super.stopParseSubscript(t,e);return e.optionalChainMember?this.estreeParseChainExpression(s,t.loc.end):s}parseMember(t,e,s,i,r){const a=super.parseMember(t,e,s,i,r);return"OptionalMemberExpression"===a.type?this.castNodeTo(a,"MemberExpression"):a.optional=!1,a}isOptionalMemberExpression(t){return"ChainExpression"===t.type?"MemberExpression"===t.expression.type:super.isOptionalMemberExpression(t)}hasPropertyAsPrivateName(t){return"ChainExpression"===t.type&&(t=t.expression),super.hasPropertyAsPrivateName(t)}isObjectProperty(t){return"Property"===t.type&&"init"===t.kind&&!t.method}isObjectMethod(t){return"Property"===t.type&&(t.method||"get"===t.kind||"set"===t.kind)}castNodeTo(t,e){const s=super.castNodeTo(t,e);return this.fillOptionalPropertiesForTSESLint(s),s}cloneIdentifier(t){const e=super.cloneIdentifier(t);return this.fillOptionalPropertiesForTSESLint(e),e}cloneStringLiteral(t){return"Literal"===t.type?this.cloneEstreeStringLiteral(t):super.cloneStringLiteral(t)}finishNodeAt(t,e,s){return E(super.finishNodeAt(t,e,s))}finishNode(t,e){const s=super.finishNode(t,e);return this.fillOptionalPropertiesForTSESLint(s),s}resetStartLocation(t,e){super.resetStartLocation(t,e),E(t)}resetEndLocation(t,e=this.state.lastTokEndLoc){super.resetEndLocation(t,e),E(t)}};class I{constructor(t,e){this.token=void 0,this.preserveSpace=void 0,this.token=t,this.preserveSpace=!!e}}const C={brace:new I("{"),j_oTag:new I("<tag"),j_cTag:new I("</tag"),j_expr:new I("<tag>...</tag>",!0)};C.template=new I("`",!0);const v=!0,N=!0,k=!0,L=!0,M=!0,O=!0;class D{constructor(t,e={}){this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=t,this.keyword=e.keyword,this.beforeExpr=!!e.beforeExpr,this.startsExpr=!!e.startsExpr,this.rightAssociative=!!e.rightAssociative,this.isLoop=!!e.isLoop,this.isAssign=!!e.isAssign,this.prefix=!!e.prefix,this.postfix=!!e.postfix,this.binop=null!=e.binop?e.binop:null,this.updateContext=null}}const F=new Map;function B(t,e={}){e.keyword=t;const s=$(t,e);return F.set(t,s),s}function R(t,e){return $(t,{beforeExpr:v,binop:e})}let j=-1;const U=[],_=[],z=[],H=[],q=[],V=[];function $(t,e={}){var s,i,r,a;return++j,_.push(t),z.push(null!=(s=e.binop)?s:-1),H.push(null!=(i=e.beforeExpr)&&i),q.push(null!=(r=e.startsExpr)&&r),V.push(null!=(a=e.prefix)&&a),U.push(new D(t,e)),j}function K(t,e={}){var s,i,r,a;return++j,F.set(t,j),_.push(t),z.push(null!=(s=e.binop)?s:-1),H.push(null!=(i=e.beforeExpr)&&i),q.push(null!=(r=e.startsExpr)&&r),V.push(null!=(a=e.prefix)&&a),U.push(new D("name",e)),j}const J={bracketL:$("[",{beforeExpr:v,startsExpr:N}),bracketHashL:$("#[",{beforeExpr:v,startsExpr:N}),bracketBarL:$("[|",{beforeExpr:v,startsExpr:N}),bracketR:$("]"),bracketBarR:$("|]"),braceL:$("{",{beforeExpr:v,startsExpr:N}),braceBarL:$("{|",{beforeExpr:v,startsExpr:N}),braceHashL:$("#{",{beforeExpr:v,startsExpr:N}),braceR:$("}"),braceBarR:$("|}"),parenL:$("(",{beforeExpr:v,startsExpr:N}),parenR:$(")"),comma:$(",",{beforeExpr:v}),semi:$(";",{beforeExpr:v}),colon:$(":",{beforeExpr:v}),doubleColon:$("::",{beforeExpr:v}),dot:$("."),question:$("?",{beforeExpr:v}),questionDot:$("?."),arrow:$("=>",{beforeExpr:v}),template:$("template"),ellipsis:$("...",{beforeExpr:v}),backQuote:$("`",{startsExpr:N}),dollarBraceL:$("${",{beforeExpr:v,startsExpr:N}),templateTail:$("...`",{startsExpr:N}),templateNonTail:$("...${",{beforeExpr:v,startsExpr:N}),at:$("@"),hash:$("#",{startsExpr:N}),interpreterDirective:$("#!..."),eq:$("=",{beforeExpr:v,isAssign:L}),assign:$("_=",{beforeExpr:v,isAssign:L}),slashAssign:$("_=",{beforeExpr:v,isAssign:L}),xorAssign:$("_=",{beforeExpr:v,isAssign:L}),moduloAssign:$("_=",{beforeExpr:v,isAssign:L}),incDec:$("++/--",{prefix:M,postfix:O,startsExpr:N}),bang:$("!",{beforeExpr:v,prefix:M,startsExpr:N}),tilde:$("~",{beforeExpr:v,prefix:M,startsExpr:N}),doubleCaret:$("^^",{startsExpr:N}),doubleAt:$("@@",{startsExpr:N}),pipeline:R("|>",0),nullishCoalescing:R("??",1),logicalOR:R("||",1),logicalAND:R("&&",2),bitwiseOR:R("|",3),bitwiseXOR:R("^",4),bitwiseAND:R("&",5),equality:R("==/!=/===/!==",6),lt:R("</>/<=/>=",7),gt:R("</>/<=/>=",7),relational:R("</>/<=/>=",7),bitShift:R("<</>>/>>>",8),bitShiftL:R("<</>>/>>>",8),bitShiftR:R("<</>>/>>>",8),plusMin:$("+/-",{beforeExpr:v,binop:9,prefix:M,startsExpr:N}),modulo:$("%",{binop:10,startsExpr:N}),star:$("*",{binop:10}),slash:R("/",10),exponent:$("**",{beforeExpr:v,binop:11,rightAssociative:!0}),_in:B("in",{beforeExpr:v,binop:7}),_instanceof:B("instanceof",{beforeExpr:v,binop:7}),_break:B("break"),_case:B("case",{beforeExpr:v}),_catch:B("catch"),_continue:B("continue"),_debugger:B("debugger"),_default:B("default",{beforeExpr:v}),_else:B("else",{beforeExpr:v}),_finally:B("finally"),_function:B("function",{startsExpr:N}),_if:B("if"),_return:B("return",{beforeExpr:v}),_switch:B("switch"),_throw:B("throw",{beforeExpr:v,prefix:M,startsExpr:N}),_try:B("try"),_var:B("var"),_const:B("const"),_with:B("with"),_new:B("new",{beforeExpr:v,startsExpr:N}),_this:B("this",{startsExpr:N}),_super:B("super",{startsExpr:N}),_class:B("class",{startsExpr:N}),_extends:B("extends",{beforeExpr:v}),_export:B("export"),_import:B("import",{startsExpr:N}),_null:B("null",{startsExpr:N}),_true:B("true",{startsExpr:N}),_false:B("false",{startsExpr:N}),_typeof:B("typeof",{beforeExpr:v,prefix:M,startsExpr:N}),_void:B("void",{beforeExpr:v,prefix:M,startsExpr:N}),_delete:B("delete",{beforeExpr:v,prefix:M,startsExpr:N}),_do:B("do",{isLoop:k,beforeExpr:v}),_for:B("for",{isLoop:k}),_while:B("while",{isLoop:k}),_as:K("as",{startsExpr:N}),_assert:K("assert",{startsExpr:N}),_async:K("async",{startsExpr:N}),_await:K("await",{startsExpr:N}),_defer:K("defer",{startsExpr:N}),_from:K("from",{startsExpr:N}),_get:K("get",{startsExpr:N}),_let:K("let",{startsExpr:N}),_meta:K("meta",{startsExpr:N}),_of:K("of",{startsExpr:N}),_sent:K("sent",{startsExpr:N}),_set:K("set",{startsExpr:N}),_source:K("source",{startsExpr:N}),_static:K("static",{startsExpr:N}),_using:K("using",{startsExpr:N}),_yield:K("yield",{startsExpr:N}),_asserts:K("asserts",{startsExpr:N}),_checks:K("checks",{startsExpr:N}),_exports:K("exports",{startsExpr:N}),_global:K("global",{startsExpr:N}),_implements:K("implements",{startsExpr:N}),_intrinsic:K("intrinsic",{startsExpr:N}),_infer:K("infer",{startsExpr:N}),_is:K("is",{startsExpr:N}),_mixins:K("mixins",{startsExpr:N}),_proto:K("proto",{startsExpr:N}),_require:K("require",{startsExpr:N}),_satisfies:K("satisfies",{startsExpr:N}),_keyof:K("keyof",{startsExpr:N}),_readonly:K("readonly",{startsExpr:N}),_unique:K("unique",{startsExpr:N}),_abstract:K("abstract",{startsExpr:N}),_declare:K("declare",{startsExpr:N}),_enum:K("enum",{startsExpr:N}),_module:K("module",{startsExpr:N}),_namespace:K("namespace",{startsExpr:N}),_interface:K("interface",{startsExpr:N}),_type:K("type",{startsExpr:N}),_opaque:K("opaque",{startsExpr:N}),name:$("name",{startsExpr:N}),placeholder:$("%%",{startsExpr:!0}),string:$("string",{startsExpr:N}),num:$("num",{startsExpr:N}),bigint:$("bigint",{startsExpr:N}),decimal:$("decimal",{startsExpr:N}),regexp:$("regexp",{startsExpr:N}),privateName:$("#name",{startsExpr:N}),eof:$("eof"),jsxName:$("jsxName"),jsxText:$("jsxText",{beforeExpr:!0}),jsxTagStart:$("jsxTagStart",{startsExpr:!0}),jsxTagEnd:$("jsxTagEnd")};function W(t){return t>=93&&t<=133}function X(t){return t<=92}function G(t){return t>=58&&t<=133}function Y(t){return t>=58&&t<=137}function Q(t){return H[t]}function Z(t){return q[t]}function tt(t){return t>=29&&t<=33}function et(t){return t>=129&&t<=131}function st(t){return t>=90&&t<=92}function it(t){return t>=58&&t<=92}function rt(t){return t>=39&&t<=59}function at(t){return 34===t}function nt(t){return V[t]}function ot(t){return t>=121&&t<=123}function ht(t){return t>=124&&t<=130}function ct(t){return _[t]}function pt(t){return z[t]}function lt(t){return 57===t}function ut(t){return t>=24&&t<=25}function dt(t){return U[t]}U[8].updateContext=t=>{t.pop()},U[5].updateContext=U[7].updateContext=U[23].updateContext=t=>{t.push(C.brace)},U[22].updateContext=t=>{t[t.length-1]===C.template?t.pop():t.push(C.template)},U[143].updateContext=t=>{t.push(C.j_expr,C.j_oTag)};let mt="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲊᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟍꟐꟑꟓꟕ-Ƛꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",ft="·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࢗ-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･";const yt=new RegExp("["+mt+"]"),xt=new RegExp("["+mt+ft+"]");mt=ft=null;const gt=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],Pt=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function bt(t,e){let s=65536;for(let i=0,r=e.length;i<r;i+=2){if(s+=e[i],s>t)return!1;if(s+=e[i+1],s>=t)return!0}return!1}function Tt(t){return t<65?36===t:t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&yt.test(String.fromCharCode(t)):bt(t,gt)))}function At(t){return t<48?36===t:t<58||!(t<65)&&(t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&xt.test(String.fromCharCode(t)):bt(t,gt)||bt(t,Pt))))}const St={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},Et=new Set(St.keyword),wt=new Set(St.strict),It=new Set(St.strictBind);function Ct(t,e){return e&&"await"===t||"enum"===t}function vt(t,e){return Ct(t,e)||wt.has(t)}function Nt(t){return It.has(t)}function kt(t,e){return vt(t,e)||Nt(t)}function Lt(t){return Et.has(t)}function Mt(t,e,s){return 64===t&&64===e&&Tt(s)}const Ot=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);function Dt(t){return Ot.has(t)}class Ft{constructor(t){this.flags=0,this.names=new Map,this.firstLexicalName="",this.flags=t}}class Bt{constructor(t,e){this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=t,this.inModule=e}get inTopLevel(){return(1&this.currentScope().flags)>0}get inFunction(){return(2&this.currentVarScopeFlags())>0}get allowSuper(){return(16&this.currentThisScopeFlags())>0}get allowDirectSuper(){return(32&this.currentThisScopeFlags())>0}get inClass(){return(64&this.currentThisScopeFlags())>0}get inClassAndNotInNonArrowFunction(){const t=this.currentThisScopeFlags();return(64&t)>0&&0===(2&t)}get inStaticBlock(){for(let t=this.scopeStack.length-1;;t--){const{flags:e}=this.scopeStack[t];if(128&e)return!0;if(451&e)return!1}}get inNonArrowFunction(){return(2&this.currentThisScopeFlags())>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(t){return new Ft(t)}enter(t){this.scopeStack.push(this.createScope(t))}exit(){const t=this.scopeStack.pop();return t.flags}treatFunctionsAsVarInScope(t){return!!(130&t.flags||!this.parser.inModule&&1&t.flags)}declareName(t,e,s){let i=this.currentScope();if(8&e||16&e){this.checkRedeclarationInScope(i,t,e,s);let r=i.names.get(t)||0;16&e?r|=4:(i.firstLexicalName||(i.firstLexicalName=t),r|=2),i.names.set(t,r),8&e&&this.maybeExportDefined(i,t)}else if(4&e)for(let r=this.scopeStack.length-1;r>=0;--r)if(i=this.scopeStack[r],this.checkRedeclarationInScope(i,t,e,s),i.names.set(t,1|(i.names.get(t)||0)),this.maybeExportDefined(i,t),387&i.flags)break;this.parser.inModule&&1&i.flags&&this.undefinedExports.delete(t)}maybeExportDefined(t,e){this.parser.inModule&&1&t.flags&&this.undefinedExports.delete(e)}checkRedeclarationInScope(t,e,s,i){this.isRedeclaredInScope(t,e,s)&&this.parser.raise(P.VarRedeclaration,i,{identifierName:e})}isRedeclaredInScope(t,e,s){if(!(1&s))return!1;if(8&s)return t.names.has(e);const i=t.names.get(e);return 16&s?(2&i)>0||!this.treatFunctionsAsVarInScope(t)&&(1&i)>0:(2&i)>0&&!(8&t.flags&&t.firstLexicalName===e)||!this.treatFunctionsAsVarInScope(t)&&(4&i)>0}checkLocalExport(t){const{name:e}=t,s=this.scopeStack[0];s.names.has(e)||this.undefinedExports.set(e,t.loc.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScopeFlags(){for(let t=this.scopeStack.length-1;;t--){const{flags:e}=this.scopeStack[t];if(387&e)return e}}currentThisScopeFlags(){for(let t=this.scopeStack.length-1;;t--){const{flags:e}=this.scopeStack[t];if(451&e&&!(4&e))return e}}}class Rt extends Ft{constructor(...t){super(...t),this.declareFunctions=new Set}}class jt extends Bt{createScope(t){return new Rt(t)}declareName(t,e,s){const i=this.currentScope();if(2048&e)return this.checkRedeclarationInScope(i,t,e,s),this.maybeExportDefined(i,t),void i.declareFunctions.add(t);super.declareName(t,e,s)}isRedeclaredInScope(t,e,s){if(super.isRedeclaredInScope(t,e,s))return!0;if(2048&s&&!t.declareFunctions.has(e)){const s=t.names.get(e);return(4&s)>0||(2&s)>0}return!1}checkLocalExport(t){this.scopeStack[0].declareFunctions.has(t.name)||super.checkLocalExport(t)}}const Ut=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),_t=g`flow`({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:({reservedType:t})=>`Cannot overwrite reserved type ${t}.`,DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:({memberName:t,enumName:e})=>`Boolean enum members need to be initialized. Use either \`${t} = true,\` or \`${t} = false,\` in enum \`${e}\`.`,EnumDuplicateMemberName:({memberName:t,enumName:e})=>`Enum member names need to be unique, but the name \`${t}\` has already been used before in enum \`${e}\`.`,EnumInconsistentMemberValues:({enumName:t})=>`Enum \`${t}\` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.`,EnumInvalidExplicitType:({invalidEnumType:t,enumName:e})=>`Enum type \`${t}\` is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${e}\`.`,EnumInvalidExplicitTypeUnknownSupplied:({enumName:t})=>`Supplied enum type is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${t}\`.`,EnumInvalidMemberInitializerPrimaryType:({enumName:t,memberName:e,explicitType:s})=>`Enum \`${t}\` has type \`${s}\`, so the initializer of \`${e}\` needs to be a ${s} literal.`,EnumInvalidMemberInitializerSymbolType:({enumName:t,memberName:e})=>`Symbol enum members cannot be initialized. Use \`${e},\` in enum \`${t}\`.`,EnumInvalidMemberInitializerUnknownType:({enumName:t,memberName:e})=>`The enum member initializer for \`${e}\` needs to be a literal (either a boolean, number, or string) in enum \`${t}\`.`,EnumInvalidMemberName:({enumName:t,memberName:e,suggestion:s})=>`Enum member names cannot start with lowercase 'a' through 'z'. Instead of using \`${e}\`, consider using \`${s}\`, in enum \`${t}\`.`,EnumNumberMemberNotInitialized:({enumName:t,memberName:e})=>`Number enum members need to be initialized, e.g. \`${e} = 1\` in enum \`${t}\`.`,EnumStringMemberInconsistentlyInitialized:({enumName:t})=>`String enum members need to consistently either all use initializers, or use no initializers, in enum \`${t}\`.`,GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:({reservedType:t})=>`Unexpected reserved type ${t}.`,UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:({unsupportedExportKind:t,suggestion:e})=>`\`declare export ${t}\` is not supported. Use \`${e}\` instead.`,UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function zt(t){return"DeclareExportAllDeclaration"===t.type||"DeclareExportDeclaration"===t.type&&(!t.declaration||"TypeAlias"!==t.declaration.type&&"InterfaceDeclaration"!==t.declaration.type)}function Ht(t){return"type"===t.importKind||"typeof"===t.importKind}const qt={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function Vt(t,e){const s=[],i=[];for(let r=0;r<t.length;r++)(e(t[r],r,t)?s:i).push(t[r]);return[s,i]}const $t=/\*?\s*@((?:no)?flow)\b/;var Kt=t=>class extends t{constructor(...t){super(...t),this.flowPragma=void 0}getScopeHandler(){return jt}shouldParseTypes(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}finishToken(t,e){134!==t&&13!==t&&28!==t&&void 0===this.flowPragma&&(this.flowPragma=null),super.finishToken(t,e)}addComment(t){if(void 0===this.flowPragma){const e=$t.exec(t.value);if(e)if("flow"===e[1])this.flowPragma="flow";else{if("noflow"!==e[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}else;}super.addComment(t)}flowParseTypeInitialiser(t){const e=this.state.inType;this.state.inType=!0,this.expect(t||14);const s=this.flowParseType();return this.state.inType=e,s}flowParsePredicate(){const t=this.startNode(),e=this.state.startLoc;return this.next(),this.expectContextual(110),this.state.lastTokStartLoc.index>e.index+1&&this.raise(_t.UnexpectedSpaceBetweenModuloChecks,e),this.eat(10)?(t.value=super.parseExpression(),this.expect(11),this.finishNode(t,"DeclaredPredicate")):this.finishNode(t,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){const t=this.state.inType;this.state.inType=!0,this.expect(14);let e=null,s=null;return this.match(54)?(this.state.inType=t,s=this.flowParsePredicate()):(e=this.flowParseType(),this.state.inType=t,this.match(54)&&(s=this.flowParsePredicate())),[e,s]}flowParseDeclareClass(t){return this.next(),this.flowParseInterfaceish(t,!0),this.finishNode(t,"DeclareClass")}flowParseDeclareFunction(t){this.next();const e=t.id=this.parseIdentifier(),s=this.startNode(),i=this.startNode();this.match(47)?s.typeParameters=this.flowParseTypeParameterDeclaration():s.typeParameters=null,this.expect(10);const r=this.flowParseFunctionTypeParams();return s.params=r.params,s.rest=r.rest,s.this=r._this,this.expect(11),[s.returnType,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),i.typeAnnotation=this.finishNode(s,"FunctionTypeAnnotation"),e.typeAnnotation=this.finishNode(i,"TypeAnnotation"),this.resetEndLocation(e),this.semicolon(),this.scope.declareName(t.id.name,2048,t.id.loc.start),this.finishNode(t,"DeclareFunction")}flowParseDeclare(t,e){return this.match(80)?this.flowParseDeclareClass(t):this.match(68)?this.flowParseDeclareFunction(t):this.match(74)?this.flowParseDeclareVariable(t):this.eatContextual(127)?this.match(16)?this.flowParseDeclareModuleExports(t):(e&&this.raise(_t.NestedDeclareModule,this.state.lastTokStartLoc),this.flowParseDeclareModule(t)):this.isContextual(130)?this.flowParseDeclareTypeAlias(t):this.isContextual(131)?this.flowParseDeclareOpaqueType(t):this.isContextual(129)?this.flowParseDeclareInterface(t):this.match(82)?this.flowParseDeclareExportDeclaration(t,e):void this.unexpected()}flowParseDeclareVariable(t){return this.next(),t.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(t.id.name,5,t.id.loc.start),this.semicolon(),this.finishNode(t,"DeclareVariable")}flowParseDeclareModule(t){this.scope.enter(0),this.match(134)?t.id=super.parseExprAtom():t.id=this.parseIdentifier();const e=t.body=this.startNode(),s=e.body=[];this.expect(5);while(!this.match(8)){let t=this.startNode();this.match(83)?(this.next(),this.isContextual(130)||this.match(87)||this.raise(_t.InvalidNonTypeImportInDeclareModule,this.state.lastTokStartLoc),super.parseImport(t)):(this.expectContextual(125,_t.UnsupportedStatementInDeclareModule),t=this.flowParseDeclare(t,!0)),s.push(t)}this.scope.exit(),this.expect(8),this.finishNode(e,"BlockStatement");let i=null,r=!1;return s.forEach(t=>{zt(t)?("CommonJS"===i&&this.raise(_t.AmbiguousDeclareModuleKind,t),i="ES"):"DeclareModuleExports"===t.type&&(r&&this.raise(_t.DuplicateDeclareModuleExports,t),"ES"===i&&this.raise(_t.AmbiguousDeclareModuleKind,t),i="CommonJS",r=!0)}),t.kind=i||"CommonJS",this.finishNode(t,"DeclareModule")}flowParseDeclareExportDeclaration(t,e){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?t.declaration=this.flowParseDeclare(this.startNode()):(t.declaration=this.flowParseType(),this.semicolon()),t.default=!0,this.finishNode(t,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(130)||this.isContextual(129))&&!e){const t=this.state.value;throw this.raise(_t.UnsupportedDeclareExportKind,this.state.startLoc,{unsupportedExportKind:t,suggestion:qt[t]})}return this.match(74)||this.match(68)||this.match(80)||this.isContextual(131)?(t.declaration=this.flowParseDeclare(this.startNode()),t.default=!1,this.finishNode(t,"DeclareExportDeclaration")):this.match(55)||this.match(5)||this.isContextual(129)||this.isContextual(130)||this.isContextual(131)?(t=this.parseExport(t,null),"ExportNamedDeclaration"===t.type?(t.default=!1,delete t.exportKind,this.castNodeTo(t,"DeclareExportDeclaration")):this.castNodeTo(t,"DeclareExportAllDeclaration")):void this.unexpected()}flowParseDeclareModuleExports(t){return this.next(),this.expectContextual(111),t.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(t,"DeclareModuleExports")}flowParseDeclareTypeAlias(t){this.next();const e=this.flowParseTypeAlias(t);return this.castNodeTo(e,"DeclareTypeAlias"),e}flowParseDeclareOpaqueType(t){this.next();const e=this.flowParseOpaqueType(t,!0);return this.castNodeTo(e,"DeclareOpaqueType"),e}flowParseDeclareInterface(t){return this.next(),this.flowParseInterfaceish(t,!1),this.finishNode(t,"DeclareInterface")}flowParseInterfaceish(t,e){if(t.id=this.flowParseRestrictedIdentifier(!e,!0),this.scope.declareName(t.id.name,e?17:8201,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.extends=[],this.eat(81))do{t.extends.push(this.flowParseInterfaceExtends())}while(!e&&this.eat(12));if(e){if(t.implements=[],t.mixins=[],this.eatContextual(117))do{t.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(12));if(this.eatContextual(113))do{t.implements.push(this.flowParseInterfaceExtends())}while(this.eat(12))}t.body=this.flowParseObjectType({allowStatic:e,allowExact:!1,allowSpread:!1,allowProto:e,allowInexact:!1})}flowParseInterfaceExtends(){const t=this.startNode();return t.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,this.finishNode(t,"InterfaceExtends")}flowParseInterface(t){return this.flowParseInterfaceish(t,!1),this.finishNode(t,"InterfaceDeclaration")}checkNotUnderscore(t){"_"===t&&this.raise(_t.UnexpectedReservedUnderscore,this.state.startLoc)}checkReservedType(t,e,s){Ut.has(t)&&this.raise(s?_t.AssignReservedType:_t.UnexpectedReservedType,e,{reservedType:t})}flowParseRestrictedIdentifier(t,e){return this.checkReservedType(this.state.value,this.state.startLoc,e),this.parseIdentifier(t)}flowParseTypeAlias(t){return t.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(t.id.name,8201,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(t,"TypeAlias")}flowParseOpaqueType(t,e){return this.expectContextual(130),t.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(t.id.name,8201,t.id.loc.start),this.match(47)?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.supertype=null,this.match(14)&&(t.supertype=this.flowParseTypeInitialiser(14)),t.impltype=null,e||(t.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(t,"OpaqueType")}flowParseTypeParameter(t=!1){const e=this.state.startLoc,s=this.startNode(),i=this.flowParseVariance(),r=this.flowParseTypeAnnotatableIdentifier();return s.name=r.name,s.variance=i,s.bound=r.typeAnnotation,this.match(29)?(this.eat(29),s.default=this.flowParseType()):t&&this.raise(_t.MissingTypeParamDefault,e),this.finishNode(s,"TypeParameter")}flowParseTypeParameterDeclaration(){const t=this.state.inType,e=this.startNode();e.params=[],this.state.inType=!0,this.match(47)||this.match(143)?this.next():this.unexpected();let s=!1;do{const t=this.flowParseTypeParameter(s);e.params.push(t),t.default&&(s=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=t,this.finishNode(e,"TypeParameterDeclaration")}flowInTopLevelContext(t){if(this.curContext()===C.brace)return t();{const e=this.state.context;this.state.context=[e[0]];try{return t()}finally{this.state.context=e}}}flowParseTypeParameterInstantiationInExpression(){if(47===this.reScan_lt())return this.flowParseTypeParameterInstantiation()}flowParseTypeParameterInstantiation(){const t=this.startNode(),e=this.state.inType;return this.state.inType=!0,t.params=[],this.flowInTopLevelContext(()=>{this.expect(47);const e=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!1;while(!this.match(48))t.params.push(this.flowParseType()),this.match(48)||this.expect(12);this.state.noAnonFunctionType=e}),this.state.inType=e,this.state.inType||this.curContext()!==C.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(t,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){if(47!==this.reScan_lt())return;const t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expect(47);while(!this.match(48))t.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}flowParseInterfaceType(){const t=this.startNode();if(this.expectContextual(129),t.extends=[],this.eat(81))do{t.extends.push(this.flowParseInterfaceExtends())}while(this.eat(12));return t.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(t,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(135)||this.match(134)?super.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(t,e,s){return t.static=e,14===this.lookahead().type?(t.id=this.flowParseObjectPropertyKey(),t.key=this.flowParseTypeInitialiser()):(t.id=null,t.key=this.flowParseType()),this.expect(3),t.value=this.flowParseTypeInitialiser(),t.variance=s,this.finishNode(t,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(t,e){return t.static=e,t.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(t.method=!0,t.optional=!1,t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.loc.start))):(t.method=!1,this.eat(17)&&(t.optional=!0),t.value=this.flowParseTypeInitialiser()),this.finishNode(t,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(t){t.params=[],t.rest=null,t.typeParameters=null,t.this=null,this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(t.this=this.flowParseFunctionTypeParam(!0),t.this.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))t.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(t.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),t.returnType=this.flowParseTypeInitialiser(),this.finishNode(t,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(t,e){const s=this.startNode();return t.static=e,t.value=this.flowParseObjectTypeMethodish(s),this.finishNode(t,"ObjectTypeCallProperty")}flowParseObjectType({allowStatic:t,allowExact:e,allowSpread:s,allowProto:i,allowInexact:r}){const a=this.state.inType;this.state.inType=!0;const n=this.startNode();let o,h;n.callProperties=[],n.properties=[],n.indexers=[],n.internalSlots=[];let c=!1;e&&this.match(6)?(this.expect(6),o=9,h=!0):(this.expect(5),o=8,h=!1),n.exact=h;while(!this.match(o)){let e=!1,a=null,o=null;const p=this.startNode();if(i&&this.isContextual(118)){const e=this.lookahead();14!==e.type&&17!==e.type&&(this.next(),a=this.state.startLoc,t=!1)}if(t&&this.isContextual(106)){const t=this.lookahead();14!==t.type&&17!==t.type&&(this.next(),e=!0)}const l=this.flowParseVariance();if(this.eat(0))null!=a&&this.unexpected(a),this.eat(0)?(l&&this.unexpected(l.loc.start),n.internalSlots.push(this.flowParseObjectTypeInternalSlot(p,e))):n.indexers.push(this.flowParseObjectTypeIndexer(p,e,l));else if(this.match(10)||this.match(47))null!=a&&this.unexpected(a),l&&this.unexpected(l.loc.start),n.callProperties.push(this.flowParseObjectTypeCallProperty(p,e));else{let t="init";if(this.isContextual(99)||this.isContextual(104)){const e=this.lookahead();Y(e.type)&&(t=this.state.value,this.next())}const i=this.flowParseObjectTypeProperty(p,e,a,l,t,s,null!=r?r:!h);null===i?(c=!0,o=this.state.lastTokStartLoc):n.properties.push(i)}this.flowObjectTypeSemicolon(),!o||this.match(8)||this.match(9)||this.raise(_t.UnexpectedExplicitInexactInObject,o)}this.expect(o),s&&(n.inexact=c);const p=this.finishNode(n,"ObjectTypeAnnotation");return this.state.inType=a,p}flowParseObjectTypeProperty(t,e,s,i,r,a,n){if(this.eat(21)){const e=this.match(12)||this.match(13)||this.match(8)||this.match(9);return e?(a?n||this.raise(_t.InexactInsideExact,this.state.lastTokStartLoc):this.raise(_t.InexactInsideNonObject,this.state.lastTokStartLoc),i&&this.raise(_t.InexactVariance,i),null):(a||this.raise(_t.UnexpectedSpreadType,this.state.lastTokStartLoc),null!=s&&this.unexpected(s),i&&this.raise(_t.SpreadVariance,i),t.argument=this.flowParseType(),this.finishNode(t,"ObjectTypeSpreadProperty"))}{t.key=this.flowParseObjectPropertyKey(),t.static=e,t.proto=null!=s,t.kind=r;let n=!1;return this.match(47)||this.match(10)?(t.method=!0,null!=s&&this.unexpected(s),i&&this.unexpected(i.loc.start),t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.loc.start)),"get"!==r&&"set"!==r||this.flowCheckGetterSetterParams(t),!a&&"constructor"===t.key.name&&t.value.this&&this.raise(_t.ThisParamBannedInConstructor,t.value.this)):("init"!==r&&this.unexpected(),t.method=!1,this.eat(17)&&(n=!0),t.value=this.flowParseTypeInitialiser(),t.variance=i),t.optional=n,this.finishNode(t,"ObjectTypeProperty")}}flowCheckGetterSetterParams(t){const e="get"===t.kind?0:1,s=t.value.params.length+(t.value.rest?1:0);t.value.this&&this.raise("get"===t.kind?_t.GetterMayNotHaveThisParam:_t.SetterMayNotHaveThisParam,t.value.this),s!==e&&this.raise("get"===t.kind?P.BadGetterArity:P.BadSetterArity,t),"set"===t.kind&&t.value.rest&&this.raise(P.BadSetterRestParameter,t)}flowObjectTypeSemicolon(){this.eat(13)||this.eat(12)||this.match(8)||this.match(9)||this.unexpected()}flowParseQualifiedTypeIdentifier(t,e){null!=t||(t=this.state.startLoc);let s=e||this.flowParseRestrictedIdentifier(!0);while(this.eat(16)){const e=this.startNodeAt(t);e.qualification=s,e.id=this.flowParseRestrictedIdentifier(!0),s=this.finishNode(e,"QualifiedTypeIdentifier")}return s}flowParseGenericType(t,e){const s=this.startNodeAt(t);return s.typeParameters=null,s.id=this.flowParseQualifiedTypeIdentifier(t,e),this.match(47)&&(s.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(s,"GenericTypeAnnotation")}flowParseTypeofType(){const t=this.startNode();return this.expect(87),t.argument=this.flowParsePrimaryType(),this.finishNode(t,"TypeofTypeAnnotation")}flowParseTupleType(){const t=this.startNode();t.types=[],this.expect(0);while(this.state.pos<this.length&&!this.match(3)){if(t.types.push(this.flowParseType()),this.match(3))break;this.expect(12)}return this.expect(3),this.finishNode(t,"TupleTypeAnnotation")}flowParseFunctionTypeParam(t){let e=null,s=!1,i=null;const r=this.startNode(),a=this.lookahead(),n=78===this.state.type;return 14===a.type||17===a.type?(n&&!t&&this.raise(_t.ThisParamMustBeFirst,r),e=this.parseIdentifier(n),this.eat(17)&&(s=!0,n&&this.raise(_t.ThisParamMayNotBeOptional,r)),i=this.flowParseTypeInitialiser()):i=this.flowParseType(),r.name=e,r.optional=s,r.typeAnnotation=i,this.finishNode(r,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(t){const e=this.startNodeAt(t.loc.start);return e.name=null,e.optional=!1,e.typeAnnotation=t,this.finishNode(e,"FunctionTypeParam")}flowParseFunctionTypeParams(t=[]){let e=null,s=null;this.match(78)&&(s=this.flowParseFunctionTypeParam(!0),s.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))t.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(e=this.flowParseFunctionTypeParam(!1)),{params:t,rest:e,_this:s}}flowIdentToTypeAnnotation(t,e,s){switch(s.name){case"any":return this.finishNode(e,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(e,"BooleanTypeAnnotation");case"mixed":return this.finishNode(e,"MixedTypeAnnotation");case"empty":return this.finishNode(e,"EmptyTypeAnnotation");case"number":return this.finishNode(e,"NumberTypeAnnotation");case"string":return this.finishNode(e,"StringTypeAnnotation");case"symbol":return this.finishNode(e,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(s.name),this.flowParseGenericType(t,s)}}flowParsePrimaryType(){const t=this.state.startLoc,e=this.startNode();let s,i,r=!1;const a=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,i=this.flowParseTupleType(),this.state.noAnonFunctionType=a,i;case 47:{const t=this.startNode();return t.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),s=this.flowParseFunctionTypeParams(),t.params=s.params,t.rest=s.rest,t.this=s._this,this.expect(11),this.expect(19),t.returnType=this.flowParseType(),this.finishNode(t,"FunctionTypeAnnotation")}case 10:{const t=this.startNode();if(this.next(),!this.match(11)&&!this.match(21))if(W(this.state.type)||this.match(78)){const t=this.lookahead().type;r=17!==t&&14!==t}else r=!0;if(r){if(this.state.noAnonFunctionType=!1,i=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&19===this.lookahead().type))return this.expect(11),i;this.eat(12)}return s=i?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(i)]):this.flowParseFunctionTypeParams(),t.params=s.params,t.rest=s.rest,t.this=s._this,this.expect(11),this.expect(19),t.returnType=this.flowParseType(),t.typeParameters=null,this.finishNode(t,"FunctionTypeAnnotation")}case 134:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return e.value=this.match(85),this.next(),this.finishNode(e,"BooleanLiteralTypeAnnotation");case 53:if("-"===this.state.value){if(this.next(),this.match(135))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",e);if(this.match(136))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",e);throw this.raise(_t.UnexpectedSubtractionOperand,this.state.startLoc)}return void this.unexpected();case 135:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 136:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(e,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(e,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(e,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(e,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if(it(this.state.type)){const t=ct(this.state.type);return this.next(),super.createIdentifier(e,t)}if(W(this.state.type))return this.isContextual(129)?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(t,e,this.parseIdentifier())}this.unexpected()}flowParsePostfixType(){const t=this.state.startLoc;let e=this.flowParsePrimaryType(),s=!1;while((this.match(0)||this.match(18))&&!this.canInsertSemicolon()){const i=this.startNodeAt(t),r=this.eat(18);s=s||r,this.expect(0),!r&&this.match(3)?(i.elementType=e,this.next(),e=this.finishNode(i,"ArrayTypeAnnotation")):(i.objectType=e,i.indexType=this.flowParseType(),this.expect(3),s?(i.optional=r,e=this.finishNode(i,"OptionalIndexedAccessType")):e=this.finishNode(i,"IndexedAccessType"))}return e}flowParsePrefixType(){const t=this.startNode();return this.eat(17)?(t.typeAnnotation=this.flowParsePrefixType(),this.finishNode(t,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){const t=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){const e=this.startNodeAt(t.loc.start);return e.params=[this.reinterpretTypeAsFunctionTypeParam(t)],e.rest=null,e.this=null,e.returnType=this.flowParseType(),e.typeParameters=null,this.finishNode(e,"FunctionTypeAnnotation")}return t}flowParseIntersectionType(){const t=this.startNode();this.eat(45);const e=this.flowParseAnonFunctionWithoutParens();t.types=[e];while(this.eat(45))t.types.push(this.flowParseAnonFunctionWithoutParens());return 1===t.types.length?e:this.finishNode(t,"IntersectionTypeAnnotation")}flowParseUnionType(){const t=this.startNode();this.eat(43);const e=this.flowParseIntersectionType();t.types=[e];while(this.eat(43))t.types.push(this.flowParseIntersectionType());return 1===t.types.length?e:this.finishNode(t,"UnionTypeAnnotation")}flowParseType(){const t=this.state.inType;this.state.inType=!0;const e=this.flowParseUnionType();return this.state.inType=t,e}flowParseTypeOrImplicitInstantiation(){if(132===this.state.type&&"_"===this.state.value){const t=this.state.startLoc,e=this.parseIdentifier();return this.flowParseGenericType(t,e)}return this.flowParseType()}flowParseTypeAnnotation(){const t=this.startNode();return t.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(t,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(t){const e=t?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e)),e}typeCastToParameter(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.loc.end),t.expression}flowParseVariance(){let t=null;return this.match(53)?(t=this.startNode(),"+"===this.state.value?t.kind="plus":t.kind="minus",this.next(),this.finishNode(t,"Variance")):t}parseFunctionBody(t,e,s=!1){e?this.forwardNoArrowParamsConversionAt(t,()=>super.parseFunctionBody(t,!0,s)):super.parseFunctionBody(t,!1,s)}parseFunctionBodyAndFinish(t,e,s=!1){if(this.match(14)){const e=this.startNode();[e.typeAnnotation,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),t.returnType=e.typeAnnotation?this.finishNode(e,"TypeAnnotation"):null}return super.parseFunctionBodyAndFinish(t,e,s)}parseStatementLike(t){if(this.state.strict&&this.isContextual(129)){const t=this.lookahead();if(G(t.type)){const t=this.startNode();return this.next(),this.flowParseInterface(t)}}else if(this.isContextual(126)){const t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}const e=super.parseStatementLike(t);return void 0!==this.flowPragma||this.isValidDirective(e)||(this.flowPragma=null),e}parseExpressionStatement(t,e,s){if("Identifier"===e.type)if("declare"===e.name){if(this.match(80)||W(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(t)}else if(W(this.state.type)){if("interface"===e.name)return this.flowParseInterface(t);if("type"===e.name)return this.flowParseTypeAlias(t);if("opaque"===e.name)return this.flowParseOpaqueType(t,!1)}return super.parseExpressionStatement(t,e,s)}shouldParseExportDeclaration(){const{type:t}=this.state;return 126===t||et(t)?!this.state.containsEsc:super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){const{type:t}=this.state;return 126===t||et(t)?this.state.containsEsc:super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.isContextual(126)){const t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}return super.parseExportDefaultExpression()}parseConditional(t,e,s){if(!this.match(17))return t;if(this.state.maybeInArrowParameters){const e=this.lookaheadCharCode();if(44===e||61===e||58===e||41===e)return this.setOptionalParametersError(s),t}this.expect(17);const i=this.state.clone(),r=this.state.noArrowAt,a=this.startNodeAt(e);let{consequent:n,failed:o}=this.tryParseConditionalConsequent(),[h,c]=this.getArrowLikeExpressions(n);if(o||c.length>0){const t=[...r];if(c.length>0){this.state=i,this.state.noArrowAt=t;for(let e=0;e<c.length;e++)t.push(c[e].start);({consequent:n,failed:o}=this.tryParseConditionalConsequent()),[h,c]=this.getArrowLikeExpressions(n)}o&&h.length>1&&this.raise(_t.AmbiguousConditionalArrow,i.startLoc),o&&1===h.length&&(this.state=i,t.push(h[0].start),this.state.noArrowAt=t,({consequent:n,failed:o}=this.tryParseConditionalConsequent()))}return this.getArrowLikeExpressions(n,!0),this.state.noArrowAt=r,this.expect(14),a.test=t,a.consequent=n,a.alternate=this.forwardNoArrowParamsConversionAt(a,()=>this.parseMaybeAssign(void 0,void 0)),this.finishNode(a,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);const t=this.parseMaybeAssignAllowIn(),e=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:t,failed:e}}getArrowLikeExpressions(t,e){const s=[t],i=[];while(0!==s.length){const t=s.pop();"ArrowFunctionExpression"===t.type&&"BlockStatement"!==t.body.type?(t.typeParameters||!t.returnType?this.finishArrowValidation(t):i.push(t),s.push(t.body)):"ConditionalExpression"===t.type&&(s.push(t.consequent),s.push(t.alternate))}return e?(i.forEach(t=>this.finishArrowValidation(t)),[i,[]]):Vt(i,t=>t.params.every(t=>this.isAssignable(t,!0)))}finishArrowValidation(t){var e;this.toAssignableList(t.params,null==(e=t.extra)?void 0:e.trailingCommaLoc,!1),this.scope.enter(6),super.checkParams(t,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(t,e){let s;return this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(t.start))?(this.state.noArrowParamsConversionAt.push(this.state.start),s=e(),this.state.noArrowParamsConversionAt.pop()):s=e(),s}parseParenItem(t,e){const s=super.parseParenItem(t,e);if(this.eat(17)&&(s.optional=!0,this.resetEndLocation(t)),this.match(14)){const t=this.startNodeAt(e);return t.expression=s,t.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(t,"TypeCastExpression")}return s}assertModuleNodeAllowed(t){"ImportDeclaration"===t.type&&("type"===t.importKind||"typeof"===t.importKind)||"ExportNamedDeclaration"===t.type&&"type"===t.exportKind||"ExportAllDeclaration"===t.type&&"type"===t.exportKind||super.assertModuleNodeAllowed(t)}parseExportDeclaration(t){if(this.isContextual(130)){t.exportKind="type";const e=this.startNode();return this.next(),this.match(5)?(t.specifiers=this.parseExportSpecifiers(!0),super.parseExportFrom(t),null):this.flowParseTypeAlias(e)}if(this.isContextual(131)){t.exportKind="type";const e=this.startNode();return this.next(),this.flowParseOpaqueType(e,!1)}if(this.isContextual(129)){t.exportKind="type";const e=this.startNode();return this.next(),this.flowParseInterface(e)}if(this.isContextual(126)){t.exportKind="value";const e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return super.parseExportDeclaration(t)}eatExportStar(t){return!!super.eatExportStar(t)||!(!this.isContextual(130)||55!==this.lookahead().type)&&(t.exportKind="type",this.next(),this.next(),!0)}maybeParseExportNamespaceSpecifier(t){const{startLoc:e}=this.state,s=super.maybeParseExportNamespaceSpecifier(t);return s&&"type"===t.exportKind&&this.unexpected(e),s}parseClassId(t,e,s){super.parseClassId(t,e,s),this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(t,e,s){const{startLoc:i}=this.state;if(this.isContextual(125)){if(super.parseClassMemberFromModifier(t,e))return;e.declare=!0}super.parseClassMember(t,e,s),e.declare&&("ClassProperty"!==e.type&&"ClassPrivateProperty"!==e.type&&"PropertyDefinition"!==e.type?this.raise(_t.DeclareClassElement,i):e.value&&this.raise(_t.DeclareClassFieldInitializer,e.value))}isIterator(t){return"iterator"===t||"asyncIterator"===t}readIterator(){const t=super.readWord1(),e="@@"+t;this.isIterator(t)&&this.state.inType||this.raise(P.InvalidIdentifier,this.state.curPosition(),{identifierName:e}),this.finishToken(132,e)}getTokenFromCode(t){const e=this.input.charCodeAt(this.state.pos+1);123===t&&124===e?this.finishOp(6,2):!this.state.inType||62!==t&&60!==t?this.state.inType&&63===t?46===e?this.finishOp(18,2):this.finishOp(17,1):Mt(t,e,this.input.charCodeAt(this.state.pos+2))?(this.state.pos+=2,this.readIterator()):super.getTokenFromCode(t):this.finishOp(62===t?48:47,1)}isAssignable(t,e){return"TypeCastExpression"===t.type?this.isAssignable(t.expression,e):super.isAssignable(t,e)}toAssignable(t,e=!1){e||"AssignmentExpression"!==t.type||"TypeCastExpression"!==t.left.type||(t.left=this.typeCastToParameter(t.left)),super.toAssignable(t,e)}toAssignableList(t,e,s){for(let i=0;i<t.length;i++){const e=t[i];"TypeCastExpression"===(null==e?void 0:e.type)&&(t[i]=this.typeCastToParameter(e))}super.toAssignableList(t,e,s)}toReferencedList(t,e){for(let i=0;i<t.length;i++){var s;const r=t[i];!r||"TypeCastExpression"!==r.type||null!=(s=r.extra)&&s.parenthesized||!(t.length>1)&&e||this.raise(_t.TypeCastInPattern,r.typeAnnotation)}return t}parseArrayLike(t,e,s,i){const r=super.parseArrayLike(t,e,s,i);return e&&!this.state.maybeInArrowParameters&&this.toReferencedList(r.elements),r}isValidLVal(t,e,s){return"TypeCastExpression"===t||super.isValidLVal(t,e,s)}parseClassProperty(t){return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(t)}parseClassPrivateProperty(t){return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(t)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(14)||super.isClassProperty()}isNonstaticConstructor(t){return!this.match(14)&&super.isNonstaticConstructor(t)}pushClassMethod(t,e,s,i,r,a){if(e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(t,e,s,i,r,a),e.params&&r){const t=e.params;t.length>0&&this.isThisParam(t[0])&&this.raise(_t.ThisParamBannedInConstructor,e)}else if("MethodDefinition"===e.type&&r&&e.value.params){const t=e.value.params;t.length>0&&this.isThisParam(t[0])&&this.raise(_t.ThisParamBannedInConstructor,e)}}pushClassPrivateMethod(t,e,s,i){e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(t,e,s,i)}parseClassSuper(t){if(super.parseClassSuper(t),t.superClass&&(this.match(47)||this.match(51))&&(t.superTypeParameters=this.flowParseTypeParameterInstantiationInExpression()),this.isContextual(113)){this.next();const e=t.implements=[];do{const t=this.startNode();t.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,e.push(this.finishNode(t,"ClassImplements"))}while(this.eat(12))}}checkGetterSetterParams(t){super.checkGetterSetterParams(t);const e=this.getObjectOrClassMethodParams(t);if(e.length>0){const s=e[0];this.isThisParam(s)&&"get"===t.kind?this.raise(_t.GetterMayNotHaveThisParam,s):this.isThisParam(s)&&this.raise(_t.SetterMayNotHaveThisParam,s)}}parsePropertyNamePrefixOperator(t){t.variance=this.flowParseVariance()}parseObjPropValue(t,e,s,i,r,a,n){let o;t.variance&&this.unexpected(t.variance.loc.start),delete t.variance,this.match(47)&&!a&&(o=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());const h=super.parseObjPropValue(t,e,s,i,r,a,n);return o&&((h.value||h).typeParameters=o),h}parseFunctionParamType(t){return this.eat(17)&&("Identifier"!==t.type&&this.raise(_t.PatternIsOptional,t),this.isThisParam(t)&&this.raise(_t.ThisParamMayNotBeOptional,t),t.optional=!0),this.match(14)?t.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(t)&&this.raise(_t.ThisParamAnnotationRequired,t),this.match(29)&&this.isThisParam(t)&&this.raise(_t.ThisParamNoDefault,t),this.resetEndLocation(t),t}parseMaybeDefault(t,e){const s=super.parseMaybeDefault(t,e);return"AssignmentPattern"===s.type&&s.typeAnnotation&&s.right.start<s.typeAnnotation.start&&this.raise(_t.TypeBeforeInitializer,s.typeAnnotation),s}checkImportReflection(t){super.checkImportReflection(t),t.module&&"value"!==t.importKind&&this.raise(_t.ImportReflectionHasImportType,t.specifiers[0].loc.start)}parseImportSpecifierLocal(t,e,s){e.local=Ht(t)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),t.specifiers.push(this.finishImportSpecifier(e,s))}isPotentialImportPhase(t){if(super.isPotentialImportPhase(t))return!0;if(this.isContextual(130)){if(!t)return!0;const e=this.lookaheadCharCode();return 123===e||42===e}return!t&&this.isContextual(87)}applyImportPhase(t,e,s,i){if(super.applyImportPhase(t,e,s,i),e){if(!s&&this.match(65))return;t.exportKind="type"===s?s:"value"}else"type"===s&&this.match(55)&&this.unexpected(),t.importKind="type"===s||"typeof"===s?s:"value"}parseImportSpecifier(t,e,s,i,r){const a=t.imported;let n=null;"Identifier"===a.type&&("type"===a.name?n="type":"typeof"===a.name&&(n="typeof"));let o=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){const e=this.parseIdentifier(!0);null===n||G(this.state.type)?(t.imported=a,t.importKind=null,t.local=this.parseIdentifier()):(t.imported=e,t.importKind=n,t.local=this.cloneIdentifier(e))}else{if(null!==n&&G(this.state.type))t.imported=this.parseIdentifier(!0),t.importKind=n;else{if(e)throw this.raise(P.ImportBindingIsString,t,{importName:a.value});t.imported=a,t.importKind=null}this.eatContextual(93)?t.local=this.parseIdentifier():(o=!0,t.local=this.cloneIdentifier(t.imported))}const h=Ht(t);return s&&h&&this.raise(_t.ImportTypeShorthandOnlyInPureImport,t),(s||h)&&this.checkReservedType(t.local.name,t.local.loc.start,!0),!o||s||h||this.checkReservedWord(t.local.name,t.loc.start,!0,!0),this.finishImportSpecifier(t,"ImportSpecifier")}parseBindingAtom(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return super.parseBindingAtom()}}parseFunctionParams(t,e){const s=t.kind;"get"!==s&&"set"!==s&&this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(t,e)}parseVarId(t,e){super.parseVarId(t,e),this.match(14)&&(t.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t.id))}parseAsyncArrowFromCallExpression(t,e){if(this.match(14)){const e=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,t.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=e}return super.parseAsyncArrowFromCallExpression(t,e)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}parseMaybeAssign(t,e){var s;let i,r=null;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(r=this.state.clone(),i=this.tryParse(()=>super.parseMaybeAssign(t,e),r),!i.error)return i.node;const{context:s}=this.state,a=s[s.length-1];a!==C.j_oTag&&a!==C.j_expr||s.pop()}if(null!=(s=i)&&s.error||this.match(47)){var a,n;let s;r=r||this.state.clone();const o=this.tryParse(i=>{var r;s=this.flowParseTypeParameterDeclaration();const a=this.forwardNoArrowParamsConversionAt(s,()=>{const i=super.parseMaybeAssign(t,e);return this.resetStartLocationFromNode(i,s),i});null!=(r=a.extra)&&r.parenthesized&&i();const n=this.maybeUnwrapTypeCastExpression(a);return"ArrowFunctionExpression"!==n.type&&i(),n.typeParameters=s,this.resetStartLocationFromNode(n,s),a},r);let h=null;if(o.node&&"ArrowFunctionExpression"===this.maybeUnwrapTypeCastExpression(o.node).type){if(!o.error&&!o.aborted)return o.node.async&&this.raise(_t.UnexpectedTypeParameterBeforeAsyncArrowFunction,s),o.node;h=o.node}if(null!=(a=i)&&a.node)return this.state=i.failState,i.node;if(h)return this.state=o.failState,h;if(null!=(n=i)&&n.thrown)throw i.error;if(o.thrown)throw o.error;throw this.raise(_t.UnexpectedTokenAfterTypeParameter,s)}return super.parseMaybeAssign(t,e)}parseArrow(t){if(this.match(14)){const e=this.tryParse(()=>{const e=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;const s=this.startNode();return[s.typeAnnotation,t.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=e,this.canInsertSemicolon()&&this.unexpected(),this.match(19)||this.unexpected(),s});if(e.thrown)return null;e.error&&(this.state=e.failState),t.returnType=e.node.typeAnnotation?this.finishNode(e.node,"TypeAnnotation"):null}return super.parseArrow(t)}shouldParseArrow(t){return this.match(14)||super.shouldParseArrow(t)}setArrowFunctionParameters(t,e){this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(t.start))?t.params=e:super.setArrowFunctionParameters(t,e)}checkParams(t,e,s,i=!0){if(!s||!this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(t.start))){for(let e=0;e<t.params.length;e++)this.isThisParam(t.params[e])&&e>0&&this.raise(_t.ThisParamMustBeFirst,t.params[e]);super.checkParams(t,e,s,i)}}parseParenAndDistinguishExpression(t){return super.parseParenAndDistinguishExpression(t&&!this.state.noArrowAt.includes(this.sourceToOffsetPos(this.state.start)))}parseSubscripts(t,e,s){if("Identifier"===t.type&&"async"===t.name&&this.state.noArrowAt.includes(e.index)){this.next();const s=this.startNodeAt(e);s.callee=t,s.arguments=super.parseCallExpressionArguments(11),t=this.finishNode(s,"CallExpression")}else if("Identifier"===t.type&&"async"===t.name&&this.match(47)){const i=this.state.clone(),r=this.tryParse(t=>this.parseAsyncArrowWithTypeParameters(e)||t(),i);if(!r.error&&!r.aborted)return r.node;const a=this.tryParse(()=>super.parseSubscripts(t,e,s),i);if(a.node&&!a.error)return a.node;if(r.node)return this.state=r.failState,r.node;if(a.node)return this.state=a.failState,a.node;throw r.error||a.error}return super.parseSubscripts(t,e,s)}parseSubscript(t,e,s,i){if(this.match(18)&&this.isLookaheadToken_lt()){if(i.optionalChainMember=!0,s)return i.stop=!0,t;this.next();const r=this.startNodeAt(e);return r.callee=t,r.typeArguments=this.flowParseTypeParameterInstantiationInExpression(),this.expect(10),r.arguments=this.parseCallExpressionArguments(11),r.optional=!0,this.finishCallExpression(r,!0)}if(!s&&this.shouldParseTypes()&&(this.match(47)||this.match(51))){const s=this.startNodeAt(e);s.callee=t;const r=this.tryParse(()=>(s.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(10),s.arguments=super.parseCallExpressionArguments(11),i.optionalChainMember&&(s.optional=!1),this.finishCallExpression(s,i.optionalChainMember)));if(r.node)return r.error&&(this.state=r.failState),r.node}return super.parseSubscript(t,e,s,i)}parseNewCallee(t){super.parseNewCallee(t);let e=null;this.shouldParseTypes()&&this.match(47)&&(e=this.tryParse(()=>this.flowParseTypeParameterInstantiationCallOrNew()).node),t.typeArguments=e}parseAsyncArrowWithTypeParameters(t){const e=this.startNodeAt(t);if(this.parseFunctionParams(e,!1),this.parseArrow(e))return super.parseArrowExpression(e,void 0,!0)}readToken_mult_modulo(t){const e=this.input.charCodeAt(this.state.pos+1);if(42===t&&47===e&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();super.readToken_mult_modulo(t)}readToken_pipe_amp(t){const e=this.input.charCodeAt(this.state.pos+1);124!==t||125!==e?super.readToken_pipe_amp(t):this.finishOp(9,2)}parseTopLevel(t,e){const s=super.parseTopLevel(t,e);return this.state.hasFlowComment&&this.raise(_t.UnterminatedFlowComment,this.state.curPosition()),s}skipBlockComment(){if(!this.hasPlugin("flowComments")||!this.skipFlowComment())return super.skipBlockComment(this.state.hasFlowComment?"*-/":"*/");{if(this.state.hasFlowComment)throw this.raise(_t.NestedFlowComment,this.state.startLoc);this.hasFlowCommentCompletion();const t=this.skipFlowComment();t&&(this.state.pos+=t,this.state.hasFlowComment=!0)}}skipFlowComment(){const{pos:t}=this.state;let e=2;while([32,9].includes(this.input.charCodeAt(t+e)))e++;const s=this.input.charCodeAt(e+t),i=this.input.charCodeAt(e+t+1);return 58===s&&58===i?e+2:"flow-include"===this.input.slice(e+t,e+t+12)?e+12:58===s&&58!==i&&e}hasFlowCommentCompletion(){const t=this.input.indexOf("*/",this.state.pos);if(-1===t)throw this.raise(P.UnterminatedComment,this.state.curPosition())}flowEnumErrorBooleanMemberNotInitialized(t,{enumName:e,memberName:s}){this.raise(_t.EnumBooleanMemberNotInitialized,t,{memberName:s,enumName:e})}flowEnumErrorInvalidMemberInitializer(t,e){return this.raise(e.explicitType?"symbol"===e.explicitType?_t.EnumInvalidMemberInitializerSymbolType:_t.EnumInvalidMemberInitializerPrimaryType:_t.EnumInvalidMemberInitializerUnknownType,t,e)}flowEnumErrorNumberMemberNotInitialized(t,e){this.raise(_t.EnumNumberMemberNotInitialized,t,e)}flowEnumErrorStringMemberInconsistentlyInitialized(t,e){this.raise(_t.EnumStringMemberInconsistentlyInitialized,t,e)}flowEnumMemberInit(){const t=this.state.startLoc,e=()=>this.match(12)||this.match(8);switch(this.state.type){case 135:{const s=this.parseNumericLiteral(this.state.value);return e()?{type:"number",loc:s.loc.start,value:s}:{type:"invalid",loc:t}}case 134:{const s=this.parseStringLiteral(this.state.value);return e()?{type:"string",loc:s.loc.start,value:s}:{type:"invalid",loc:t}}case 85:case 86:{const s=this.parseBooleanLiteral(this.match(85));return e()?{type:"boolean",loc:s.loc.start,value:s}:{type:"invalid",loc:t}}default:return{type:"invalid",loc:t}}}flowEnumMemberRaw(){const t=this.state.startLoc,e=this.parseIdentifier(!0),s=this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:t};return{id:e,init:s}}flowEnumCheckExplicitTypeMismatch(t,e,s){const{explicitType:i}=e;null!==i&&i!==s&&this.flowEnumErrorInvalidMemberInitializer(t,e)}flowEnumMembers({enumName:t,explicitType:e}){const s=new Set,i={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]};let r=!1;while(!this.match(8)){if(this.eat(21)){r=!0;break}const a=this.startNode(),{id:n,init:o}=this.flowEnumMemberRaw(),h=n.name;if(""===h)continue;/^[a-z]/.test(h)&&this.raise(_t.EnumInvalidMemberName,n,{memberName:h,suggestion:h[0].toUpperCase()+h.slice(1),enumName:t}),s.has(h)&&this.raise(_t.EnumDuplicateMemberName,n,{memberName:h,enumName:t}),s.add(h);const c={enumName:t,explicitType:e,memberName:h};switch(a.id=n,o.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(o.loc,c,"boolean"),a.init=o.value,i.booleanMembers.push(this.finishNode(a,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(o.loc,c,"number"),a.init=o.value,i.numberMembers.push(this.finishNode(a,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(o.loc,c,"string"),a.init=o.value,i.stringMembers.push(this.finishNode(a,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(o.loc,c);case"none":switch(e){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(o.loc,c);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(o.loc,c);break;default:i.defaultedMembers.push(this.finishNode(a,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}return{members:i,hasUnknownMembers:r}}flowEnumStringMembers(t,e,{enumName:s}){if(0===t.length)return e;if(0===e.length)return t;if(e.length>t.length){for(const e of t)this.flowEnumErrorStringMemberInconsistentlyInitialized(e,{enumName:s});return e}for(const i of e)this.flowEnumErrorStringMemberInconsistentlyInitialized(i,{enumName:s});return t}flowEnumParseExplicitType({enumName:t}){if(!this.eatContextual(102))return null;if(!W(this.state.type))throw this.raise(_t.EnumInvalidExplicitTypeUnknownSupplied,this.state.startLoc,{enumName:t});const{value:e}=this.state;return this.next(),"boolean"!==e&&"number"!==e&&"string"!==e&&"symbol"!==e&&this.raise(_t.EnumInvalidExplicitType,this.state.startLoc,{enumName:t,invalidEnumType:e}),e}flowEnumBody(t,e){const s=e.name,i=e.loc.start,r=this.flowEnumParseExplicitType({enumName:s});this.expect(5);const{members:a,hasUnknownMembers:n}=this.flowEnumMembers({enumName:s,explicitType:r});switch(t.hasUnknownMembers=n,r){case"boolean":return t.explicitType=!0,t.members=a.booleanMembers,this.expect(8),this.finishNode(t,"EnumBooleanBody");case"number":return t.explicitType=!0,t.members=a.numberMembers,this.expect(8),this.finishNode(t,"EnumNumberBody");case"string":return t.explicitType=!0,t.members=this.flowEnumStringMembers(a.stringMembers,a.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(t,"EnumStringBody");case"symbol":return t.members=a.defaultedMembers,this.expect(8),this.finishNode(t,"EnumSymbolBody");default:{const e=()=>(t.members=[],this.expect(8),this.finishNode(t,"EnumStringBody"));t.explicitType=!1;const r=a.booleanMembers.length,n=a.numberMembers.length,o=a.stringMembers.length,h=a.defaultedMembers.length;if(r||n||o||h){if(r||n){if(!n&&!o&&r>=h){for(const t of a.defaultedMembers)this.flowEnumErrorBooleanMemberNotInitialized(t.loc.start,{enumName:s,memberName:t.id.name});return t.members=a.booleanMembers,this.expect(8),this.finishNode(t,"EnumBooleanBody")}if(!r&&!o&&n>=h){for(const t of a.defaultedMembers)this.flowEnumErrorNumberMemberNotInitialized(t.loc.start,{enumName:s,memberName:t.id.name});return t.members=a.numberMembers,this.expect(8),this.finishNode(t,"EnumNumberBody")}return this.raise(_t.EnumInconsistentMemberValues,i,{enumName:s}),e()}return t.members=this.flowEnumStringMembers(a.stringMembers,a.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(t,"EnumStringBody")}return e()}}}flowParseEnumDeclaration(t){const e=this.parseIdentifier();return t.id=e,t.body=this.flowEnumBody(this.startNode(),e),this.finishNode(t,"EnumDeclaration")}jsxParseOpeningElementAfterName(t){return this.shouldParseTypes()&&(this.match(47)||this.match(51))&&(t.typeArguments=this.flowParseTypeParameterInstantiationInExpression()),super.jsxParseOpeningElementAfterName(t)}isLookaheadToken_lt(){const t=this.nextTokenStart();if(60===this.input.charCodeAt(t)){const e=this.input.charCodeAt(t+1);return 60!==e&&61!==e}return!1}reScan_lt_gt(){const{type:t}=this.state;47===t?(this.state.pos-=1,this.readToken_lt()):48===t&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){const{type:t}=this.state;return 51===t?(this.state.pos-=2,this.finishOp(47,1),47):t}maybeUnwrapTypeCastExpression(t){return"TypeCastExpression"===t.type?t.expression:t}};const Jt={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},Wt=/\r\n|[\r\n\u2028\u2029]/,Xt=new RegExp(Wt.source,"g");function Gt(t){switch(t){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}function Yt(t,e,s){for(let i=e;i<s;i++)if(Gt(t.charCodeAt(i)))return!0;return!1}const Qt=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Zt=/(?:[^\S\n\r\u2028\u2029]|\/\/.*|\/\*.*?\*\/)*/g;function te(t){switch(t){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}const ee=g`jsx`({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:({openingTagName:t})=>`Expected corresponding JSX closing tag for <${t}>.`,MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:({unexpected:t,HTMLEntity:e})=>`Unexpected token \`${t}\`. Did you mean \`${e}\` or \`{'${t}'}\`?`,UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function se(t){return!!t&&("JSXOpeningFragment"===t.type||"JSXClosingFragment"===t.type)}function ie(t){if("JSXIdentifier"===t.type)return t.name;if("JSXNamespacedName"===t.type)return t.namespace.name+":"+t.name.name;if("JSXMemberExpression"===t.type)return ie(t.object)+"."+ie(t.property);throw new Error("Node had unexpected type: "+t.type)}var re=t=>class extends t{jsxReadToken(){let t="",e=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(ee.UnterminatedJsxContent,this.state.startLoc);const s=this.input.charCodeAt(this.state.pos);switch(s){case 60:case 123:return this.state.pos===this.state.start?void(60===s&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(143)):super.getTokenFromCode(s)):(t+=this.input.slice(e,this.state.pos),void this.finishToken(142,t));case 38:t+=this.input.slice(e,this.state.pos),t+=this.jsxReadEntity(),e=this.state.pos;break;case 62:case 125:default:Gt(s)?(t+=this.input.slice(e,this.state.pos),t+=this.jsxReadNewLine(!0),e=this.state.pos):++this.state.pos}}}jsxReadNewLine(t){const e=this.input.charCodeAt(this.state.pos);let s;return++this.state.pos,13===e&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,s=t?"\n":"\r\n"):s=String.fromCharCode(e),++this.state.curLine,this.state.lineStart=this.state.pos,s}jsxReadString(t){let e="",s=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(P.UnterminatedString,this.state.startLoc);const i=this.input.charCodeAt(this.state.pos);if(i===t)break;38===i?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadEntity(),s=this.state.pos):Gt(i)?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadNewLine(!1),s=this.state.pos):++this.state.pos}e+=this.input.slice(s,this.state.pos++),this.finishToken(134,e)}jsxReadEntity(){const t=++this.state.pos;if(35===this.codePointAtPos(this.state.pos)){++this.state.pos;let t=10;120===this.codePointAtPos(this.state.pos)&&(t=16,++this.state.pos);const e=this.readInt(t,void 0,!1,"bail");if(null!==e&&59===this.codePointAtPos(this.state.pos))return++this.state.pos,String.fromCodePoint(e)}else{let e=0,s=!1;while(e++<10&&this.state.pos<this.length&&!(s=59===this.codePointAtPos(this.state.pos)))++this.state.pos;if(s){const e=this.input.slice(t,this.state.pos),s=Jt[e];if(++this.state.pos,s)return s}}return this.state.pos=t,"&"}jsxReadWord(){let t;const e=this.state.pos;do{t=this.input.charCodeAt(++this.state.pos)}while(At(t)||45===t);this.finishToken(141,this.input.slice(e,this.state.pos))}jsxParseIdentifier(){const t=this.startNode();return this.match(141)?t.name=this.state.value:it(this.state.type)?t.name=ct(this.state.type):this.unexpected(),this.next(),this.finishNode(t,"JSXIdentifier")}jsxParseNamespacedName(){const t=this.state.startLoc,e=this.jsxParseIdentifier();if(!this.eat(14))return e;const s=this.startNodeAt(t);return s.namespace=e,s.name=this.jsxParseIdentifier(),this.finishNode(s,"JSXNamespacedName")}jsxParseElementName(){const t=this.state.startLoc;let e=this.jsxParseNamespacedName();if("JSXNamespacedName"===e.type)return e;while(this.eat(16)){const s=this.startNodeAt(t);s.object=e,s.property=this.jsxParseIdentifier(),e=this.finishNode(s,"JSXMemberExpression")}return e}jsxParseAttributeValue(){let t;switch(this.state.type){case 5:return t=this.startNode(),this.setContext(C.brace),this.next(),t=this.jsxParseExpressionContainer(t,C.j_oTag),"JSXEmptyExpression"===t.expression.type&&this.raise(ee.AttributeIsEmpty,t),t;case 143:case 134:return this.parseExprAtom();default:throw this.raise(ee.UnsupportedJsxValue,this.state.startLoc)}}jsxParseEmptyExpression(){const t=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(t,"JSXEmptyExpression",this.state.startLoc)}jsxParseSpreadChild(t){return this.next(),t.expression=this.parseExpression(),this.setContext(C.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXSpreadChild")}jsxParseExpressionContainer(t,e){if(this.match(8))t.expression=this.jsxParseEmptyExpression();else{const e=this.parseExpression();t.expression=e}return this.setContext(e),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXExpressionContainer")}jsxParseAttribute(){const t=this.startNode();return this.match(5)?(this.setContext(C.brace),this.next(),this.expect(21),t.argument=this.parseMaybeAssignAllowIn(),this.setContext(C.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(t,"JSXSpreadAttribute")):(t.name=this.jsxParseNamespacedName(),t.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(t,"JSXAttribute"))}jsxParseOpeningElementAt(t){const e=this.startNodeAt(t);return this.eat(144)?this.finishNode(e,"JSXOpeningFragment"):(e.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(e))}jsxParseOpeningElementAfterName(t){const e=[];while(!this.match(56)&&!this.match(144))e.push(this.jsxParseAttribute());return t.attributes=e,t.selfClosing=this.eat(56),this.expect(144),this.finishNode(t,"JSXOpeningElement")}jsxParseClosingElementAt(t){const e=this.startNodeAt(t);return this.eat(144)?this.finishNode(e,"JSXClosingFragment"):(e.name=this.jsxParseElementName(),this.expect(144),this.finishNode(e,"JSXClosingElement"))}jsxParseElementAt(t){const e=this.startNodeAt(t),s=[],i=this.jsxParseOpeningElementAt(t);let r=null;if(!i.selfClosing){t:for(;;)switch(this.state.type){case 143:if(t=this.state.startLoc,this.next(),this.eat(56)){r=this.jsxParseClosingElementAt(t);break t}s.push(this.jsxParseElementAt(t));break;case 142:s.push(this.parseLiteral(this.state.value,"JSXText"));break;case 5:{const t=this.startNode();this.setContext(C.brace),this.next(),this.match(21)?s.push(this.jsxParseSpreadChild(t)):s.push(this.jsxParseExpressionContainer(t,C.j_expr));break}default:this.unexpected()}se(i)&&!se(r)&&null!==r?this.raise(ee.MissingClosingTagFragment,r):!se(i)&&se(r)?this.raise(ee.MissingClosingTagElement,r,{openingTagName:ie(i.name)}):se(i)||se(r)||ie(r.name)!==ie(i.name)&&this.raise(ee.MissingClosingTagElement,r,{openingTagName:ie(i.name)})}if(se(i)?(e.openingFragment=i,e.closingFragment=r):(e.openingElement=i,e.closingElement=r),e.children=s,this.match(47))throw this.raise(ee.UnwrappedAdjacentJSXElements,this.state.startLoc);return se(i)?this.finishNode(e,"JSXFragment"):this.finishNode(e,"JSXElement")}jsxParseElement(){const t=this.state.startLoc;return this.next(),this.jsxParseElementAt(t)}setContext(t){const{context:e}=this.state;e[e.length-1]=t}parseExprAtom(t){return this.match(143)?this.jsxParseElement():this.match(47)&&33!==this.input.charCodeAt(this.state.pos)?(this.replaceToken(143),this.jsxParseElement()):super.parseExprAtom(t)}skipSpace(){const t=this.curContext();t.preserveSpace||super.skipSpace()}getTokenFromCode(t){const e=this.curContext();if(e!==C.j_expr){if(e===C.j_oTag||e===C.j_cTag){if(Tt(t))return void this.jsxReadWord();if(62===t)return++this.state.pos,void this.finishToken(144);if((34===t||39===t)&&e===C.j_oTag)return void this.jsxReadString(t)}if(60===t&&this.state.canStartJSXElement&&33!==this.input.charCodeAt(this.state.pos+1))return++this.state.pos,void this.finishToken(143);super.getTokenFromCode(t)}else this.jsxReadToken()}updateContext(t){const{context:e,type:s}=this.state;if(56===s&&143===t)e.splice(-2,2,C.j_cTag),this.state.canStartJSXElement=!1;else if(143===s)e.push(C.j_oTag);else if(144===s){const s=e[e.length-1];s===C.j_oTag&&56===t||s===C.j_cTag?(e.pop(),this.state.canStartJSXElement=e[e.length-1]===C.j_expr):(this.setContext(C.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=Q(s)}};class ae extends Ft{constructor(...t){super(...t),this.tsNames=new Map}}class ne extends Bt{constructor(...t){super(...t),this.importsStack=[]}createScope(t){return this.importsStack.push(new Set),new ae(t)}enter(t){256===t&&this.importsStack.push(new Set),super.enter(t)}exit(){const t=super.exit();return 256===t&&this.importsStack.pop(),t}hasImport(t,e){const s=this.importsStack.length;if(this.importsStack[s-1].has(t))return!0;if(!e&&s>1)for(let i=0;i<s-1;i++)if(this.importsStack[i].has(t))return!0;return!1}declareName(t,e,s){if(4096&e)return this.hasImport(t,!0)&&this.parser.raise(P.VarRedeclaration,s,{identifierName:t}),void this.importsStack[this.importsStack.length-1].add(t);const i=this.currentScope();let r=i.tsNames.get(t)||0;if(1024&e)return this.maybeExportDefined(i,t),void i.tsNames.set(t,16|r);super.declareName(t,e,s),2&e&&(1&e||(this.checkRedeclarationInScope(i,t,e,s),this.maybeExportDefined(i,t)),r|=1),256&e&&(r|=2),512&e&&(r|=4),128&e&&(r|=8),r&&i.tsNames.set(t,r)}isRedeclaredInScope(t,e,s){const i=t.tsNames.get(e);if((2&i)>0){if(256&s){const t=!!(512&s),e=(4&i)>0;return t!==e}return!0}return 128&s&&(8&i)>0?!!(2&t.names.get(e))&&!!(1&s):!!(2&s&&(1&i)>0)||super.isRedeclaredInScope(t,e,s)}checkLocalExport(t){const{name:e}=t;if(this.hasImport(e))return;const s=this.scopeStack.length;for(let i=s-1;i>=0;i--){const t=this.scopeStack[i],s=t.tsNames.get(e);if((1&s)>0||(16&s)>0)return}super.checkLocalExport(t)}}class oe{constructor(){this.stacks=[]}enter(t){this.stacks.push(t)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(2&this.currentFlags())>0}get hasYield(){return(1&this.currentFlags())>0}get hasReturn(){return(4&this.currentFlags())>0}get hasIn(){return(8&this.currentFlags())>0}}function he(t,e){return(t?2:0)|(e?1:0)}class ce{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}sourceToOffsetPos(t){return t+this.startIndex}offsetToSourcePos(t){return t-this.startIndex}hasPlugin(t){if("string"===typeof t)return this.plugins.has(t);{const[e,s]=t;if(!this.hasPlugin(e))return!1;const i=this.plugins.get(e);for(const t of Object.keys(s))if((null==i?void 0:i[t])!==s[t])return!1;return!0}}getPluginOption(t,e){var s;return null==(s=this.plugins.get(t))?void 0:s[e]}}function pe(t,e){void 0===t.trailingComments?t.trailingComments=e:t.trailingComments.unshift(...e)}function le(t,e){void 0===t.leadingComments?t.leadingComments=e:t.leadingComments.unshift(...e)}function ue(t,e){void 0===t.innerComments?t.innerComments=e:t.innerComments.unshift(...e)}function de(t,e,s){let i=null,r=e.length;while(null===i&&r>0)i=e[--r];null===i||i.start>s.start?ue(t,s.comments):pe(i,s.comments)}class me extends ce{addComment(t){this.filename&&(t.loc.filename=this.filename);const{commentsLen:e}=this.state;this.comments.length!==e&&(this.comments.length=e),this.comments.push(t),this.state.commentsLen++}processComment(t){const{commentStack:e}=this.state,s=e.length;if(0===s)return;let i=s-1;const r=e[i];r.start===t.end&&(r.leadingNode=t,i--);const{start:a}=t;for(;i>=0;i--){const s=e[i],r=s.end;if(!(r>a)){r===a&&(s.trailingNode=t);break}s.containingNode=t,this.finalizeComment(s),e.splice(i,1)}}finalizeComment(t){const{comments:e}=t;if(null!==t.leadingNode||null!==t.trailingNode)null!==t.leadingNode&&pe(t.leadingNode,e),null!==t.trailingNode&&le(t.trailingNode,e);else{const{containingNode:s,start:i}=t;if(44===this.input.charCodeAt(this.offsetToSourcePos(i)-1))switch(s.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":de(s,s.properties,t);break;case"CallExpression":case"OptionalCallExpression":de(s,s.arguments,t);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":de(s,s.params,t);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":de(s,s.elements,t);break;case"ExportNamedDeclaration":case"ImportDeclaration":de(s,s.specifiers,t);break;case"TSEnumDeclaration":de(s,s.members,t);break;case"TSEnumBody":de(s,s.members,t);break;default:ue(s,e)}else ue(s,e)}}finalizeRemainingComments(){const{commentStack:t}=this.state;for(let e=t.length-1;e>=0;e--)this.finalizeComment(t[e]);this.state.commentStack=[]}resetPreviousNodeTrailingComments(t){const{commentStack:e}=this.state,{length:s}=e;if(0===s)return;const i=e[s-1];i.leadingNode===t&&(i.leadingNode=null)}resetPreviousIdentifierLeadingComments(t){const{commentStack:e}=this.state,{length:s}=e;0!==s&&(e[s-1].trailingNode===t?e[s-1].trailingNode=null:s>=2&&e[s-2].trailingNode===t&&(e[s-2].trailingNode=null))}takeSurroundingComments(t,e,s){const{commentStack:i}=this.state,r=i.length;if(0===r)return;let a=r-1;for(;a>=0;a--){const r=i[a],n=r.end,o=r.start;if(o===s)r.leadingNode=t;else if(n===e)r.trailingNode=t;else if(n<e)break}}}class fe{constructor(){this.flags=1024,this.startIndex=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.labels=[],this.commentsLen=0,this.commentStack=[],this.pos=0,this.type=140,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.context=[C.brace],this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}get strict(){return(1&this.flags)>0}set strict(t){t?this.flags|=1:this.flags&=-2}init({strictMode:t,sourceType:e,startIndex:s,startLine:i,startColumn:a}){this.strict=!1!==t&&(!0===t||"module"===e),this.startIndex=s,this.curLine=i,this.lineStart=-a,this.startLoc=this.endLoc=new r(i,a,s)}get maybeInArrowParameters(){return(2&this.flags)>0}set maybeInArrowParameters(t){t?this.flags|=2:this.flags&=-3}get inType(){return(4&this.flags)>0}set inType(t){t?this.flags|=4:this.flags&=-5}get noAnonFunctionType(){return(8&this.flags)>0}set noAnonFunctionType(t){t?this.flags|=8:this.flags&=-9}get hasFlowComment(){return(16&this.flags)>0}set hasFlowComment(t){t?this.flags|=16:this.flags&=-17}get isAmbientContext(){return(32&this.flags)>0}set isAmbientContext(t){t?this.flags|=32:this.flags&=-33}get inAbstractClass(){return(64&this.flags)>0}set inAbstractClass(t){t?this.flags|=64:this.flags&=-65}get inDisallowConditionalTypesContext(){return(128&this.flags)>0}set inDisallowConditionalTypesContext(t){t?this.flags|=128:this.flags&=-129}get soloAwait(){return(256&this.flags)>0}set soloAwait(t){t?this.flags|=256:this.flags&=-257}get inFSharpPipelineDirectBody(){return(512&this.flags)>0}set inFSharpPipelineDirectBody(t){t?this.flags|=512:this.flags&=-513}get canStartJSXElement(){return(1024&this.flags)>0}set canStartJSXElement(t){t?this.flags|=1024:this.flags&=-1025}get containsEsc(){return(2048&this.flags)>0}set containsEsc(t){t?this.flags|=2048:this.flags&=-2049}get hasTopLevelAwait(){return(4096&this.flags)>0}set hasTopLevelAwait(t){t?this.flags|=4096:this.flags&=-4097}curPosition(){return new r(this.curLine,this.pos-this.lineStart,this.pos+this.startIndex)}clone(){const t=new fe;return t.flags=this.flags,t.startIndex=this.startIndex,t.curLine=this.curLine,t.lineStart=this.lineStart,t.startLoc=this.startLoc,t.endLoc=this.endLoc,t.errors=this.errors.slice(),t.potentialArrowAt=this.potentialArrowAt,t.noArrowAt=this.noArrowAt.slice(),t.noArrowParamsConversionAt=this.noArrowParamsConversionAt.slice(),t.topicContext=this.topicContext,t.labels=this.labels.slice(),t.commentsLen=this.commentsLen,t.commentStack=this.commentStack.slice(),t.pos=this.pos,t.type=this.type,t.value=this.value,t.start=this.start,t.end=this.end,t.lastTokEndLoc=this.lastTokEndLoc,t.lastTokStartLoc=this.lastTokStartLoc,t.context=this.context.slice(),t.firstInvalidTemplateEscapePos=this.firstInvalidTemplateEscapePos,t.strictErrors=this.strictErrors,t.tokensLength=this.tokensLength,t}}var ye=function(t){return t>=48&&t<=57};const xe={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},ge={bin:t=>48===t||49===t,oct:t=>t>=48&&t<=55,dec:t=>t>=48&&t<=57,hex:t=>t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102};function Pe(t,e,s,i,r,a){const n=s,o=i,h=r;let c="",p=null,l=s;const{length:u}=e;for(;;){if(s>=u){a.unterminated(n,o,h),c+=e.slice(l,s);break}const d=e.charCodeAt(s);if(be(t,d,e,s)){c+=e.slice(l,s);break}if(92===d){c+=e.slice(l,s);const n=Te(e,s,i,r,"template"===t,a);null!==n.ch||p?c+=n.ch:p={pos:s,lineStart:i,curLine:r},({pos:s,lineStart:i,curLine:r}=n),l=s}else 8232===d||8233===d?(++s,++r,i=s):10===d||13===d?"template"===t?(c+=e.slice(l,s)+"\n",++s,13===d&&10===e.charCodeAt(s)&&++s,++r,l=i=s):a.unterminated(n,o,h):++s}return{pos:s,str:c,firstInvalidLoc:p,lineStart:i,curLine:r,containsInvalid:!!p}}function be(t,e,s,i){return"template"===t?96===e||36===e&&123===s.charCodeAt(i+1):e===("double"===t?34:39)}function Te(t,e,s,i,r,a){const n=!r;e++;const o=t=>({pos:e,ch:t,lineStart:s,curLine:i}),h=t.charCodeAt(e++);switch(h){case 110:return o("\n");case 114:return o("\r");case 120:{let r;return({code:r,pos:e}=Ae(t,e,s,i,2,!1,n,a)),o(null===r?null:String.fromCharCode(r))}case 117:{let r;return({code:r,pos:e}=Ee(t,e,s,i,n,a)),o(null===r?null:String.fromCodePoint(r))}case 116:return o("\t");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:10===t.charCodeAt(e)&&++e;case 10:s=e,++i;case 8232:case 8233:return o("");case 56:case 57:if(r)return o(null);a.strictNumericEscape(e-1,s,i);default:if(h>=48&&h<=55){const n=e-1,h=/^[0-7]+/.exec(t.slice(n,e+2));let c=h[0],p=parseInt(c,8);p>255&&(c=c.slice(0,-1),p=parseInt(c,8)),e+=c.length-1;const l=t.charCodeAt(e);if("0"!==c||56===l||57===l){if(r)return o(null);a.strictNumericEscape(n,s,i)}return o(String.fromCharCode(p))}return o(String.fromCharCode(h))}}function Ae(t,e,s,i,r,a,n,o){const h=e;let c;return({n:c,pos:e}=Se(t,e,s,i,16,r,a,!1,o,!n)),null===c&&(n?o.invalidEscapeSequence(h,s,i):e=h-1),{code:c,pos:e}}function Se(t,e,s,i,r,a,n,o,h,c){const p=e,l=16===r?xe.hex:xe.decBinOct,u=16===r?ge.hex:10===r?ge.dec:8===r?ge.oct:ge.bin;let d=!1,m=0;for(let f=0,y=null==a?1/0:a;f<y;++f){const a=t.charCodeAt(e);let p;if(95!==a||"bail"===o){if(p=a>=97?a-97+10:a>=65?a-65+10:ye(a)?a-48:1/0,p>=r){if(p<=9&&c)return{n:null,pos:e};if(p<=9&&h.invalidDigit(e,s,i,r))p=0;else{if(!n)break;p=0,d=!0}}++e,m=m*r+p}else{const r=t.charCodeAt(e-1),a=t.charCodeAt(e+1);if(o){if(Number.isNaN(a)||!u(a)||l.has(r)||l.has(a)){if(c)return{n:null,pos:e};h.unexpectedNumericSeparator(e,s,i)}}else{if(c)return{n:null,pos:e};h.numericSeparatorInEscapeSequence(e,s,i)}++e}}return e===p||null!=a&&e-p!==a||d?{n:null,pos:e}:{n:m,pos:e}}function Ee(t,e,s,i,r,a){const n=t.charCodeAt(e);let o;if(123===n){if(++e,({code:o,pos:e}=Ae(t,e,s,i,t.indexOf("}",e)-e,!0,r,a)),++e,null!==o&&o>1114111){if(!r)return{code:null,pos:e};a.invalidCodePoint(e,s,i)}}else({code:o,pos:e}=Ae(t,e,s,i,4,!1,r,a));return{code:o,pos:e}}function we(t,e,s){return new r(s,t-e,t)}const Ie=new Set([103,109,115,105,121,117,100,118]);class Ce{constructor(t){const e=t.startIndex||0;this.type=t.type,this.value=t.value,this.start=e+t.start,this.end=e+t.end,this.loc=new a(t.startLoc,t.endLoc)}}class ve extends me{constructor(t,e){super(),this.isLookahead=void 0,this.tokens=[],this.errorHandlers_readInt={invalidDigit:(t,e,s,i)=>!!(2048&this.optionFlags)&&(this.raise(P.InvalidDigit,we(t,e,s),{radix:i}),!0),numericSeparatorInEscapeSequence:this.errorBuilder(P.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:this.errorBuilder(P.UnexpectedNumericSeparator)},this.errorHandlers_readCodePoint=Object.assign({},this.errorHandlers_readInt,{invalidEscapeSequence:this.errorBuilder(P.InvalidEscapeSequence),invalidCodePoint:this.errorBuilder(P.InvalidCodePoint)}),this.errorHandlers_readStringContents_string=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:(t,e,s)=>{this.recordStrictModeErrors(P.StrictNumericEscape,we(t,e,s))},unterminated:(t,e,s)=>{throw this.raise(P.UnterminatedString,we(t-1,e,s))}}),this.errorHandlers_readStringContents_template=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:this.errorBuilder(P.StrictNumericEscape),unterminated:(t,e,s)=>{throw this.raise(P.UnterminatedTemplate,we(t,e,s))}}),this.state=new fe,this.state.init(t),this.input=e,this.length=e.length,this.comments=[],this.isLookahead=!1}pushToken(t){this.tokens.length=this.state.tokensLength,this.tokens.push(t),++this.state.tokensLength}next(){this.checkKeywordEscapes(),256&this.optionFlags&&this.pushToken(new Ce(this.state)),this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(t){return!!this.match(t)&&(this.next(),!0)}match(t){return this.state.type===t}createLookaheadState(t){return{pos:t.pos,value:null,type:t.type,start:t.start,end:t.end,context:[this.curContext()],inType:t.inType,startLoc:t.startLoc,lastTokEndLoc:t.lastTokEndLoc,curLine:t.curLine,lineStart:t.lineStart,curPosition:t.curPosition}}lookahead(){const t=this.state;this.state=this.createLookaheadState(t),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;const e=this.state;return this.state=t,e}nextTokenStart(){return this.nextTokenStartSince(this.state.pos)}nextTokenStartSince(t){return Qt.lastIndex=t,Qt.test(this.input)?Qt.lastIndex:t}lookaheadCharCode(){return this.lookaheadCharCodeSince(this.state.pos)}lookaheadCharCodeSince(t){return this.input.charCodeAt(this.nextTokenStartSince(t))}nextTokenInLineStart(){return this.nextTokenInLineStartSince(this.state.pos)}nextTokenInLineStartSince(t){return Zt.lastIndex=t,Zt.test(this.input)?Zt.lastIndex:t}lookaheadInLineCharCode(){return this.input.charCodeAt(this.nextTokenInLineStart())}codePointAtPos(t){let e=this.input.charCodeAt(t);if(55296===(64512&e)&&++t<this.input.length){const s=this.input.charCodeAt(t);56320===(64512&s)&&(e=65536+((1023&e)<<10)+(1023&s))}return e}setStrict(t){this.state.strict=t,t&&(this.state.strictErrors.forEach(([t,e])=>this.raise(t,e)),this.state.strictErrors.clear())}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length?this.finishToken(140):this.getTokenFromCode(this.codePointAtPos(this.state.pos))}skipBlockComment(t){let e;this.isLookahead||(e=this.state.curPosition());const s=this.state.pos,i=this.input.indexOf(t,s+2);if(-1===i)throw this.raise(P.UnterminatedComment,this.state.curPosition());this.state.pos=i+t.length,Xt.lastIndex=s+2;while(Xt.test(this.input)&&Xt.lastIndex<=i)++this.state.curLine,this.state.lineStart=Xt.lastIndex;if(this.isLookahead)return;const r={type:"CommentBlock",value:this.input.slice(s+2,i),start:this.sourceToOffsetPos(s),end:this.sourceToOffsetPos(i+t.length),loc:new a(e,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(r),r}skipLineComment(t){const e=this.state.pos;let s;this.isLookahead||(s=this.state.curPosition());let i=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)while(!Gt(i)&&++this.state.pos<this.length)i=this.input.charCodeAt(this.state.pos);if(this.isLookahead)return;const r=this.state.pos,n=this.input.slice(e+t,r),o={type:"CommentLine",value:n,start:this.sourceToOffsetPos(e),end:this.sourceToOffsetPos(r),loc:new a(s,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(o),o}skipSpace(){const t=this.state.pos,e=4096&this.optionFlags?[]:null;t:while(this.state.pos<this.length){const s=this.input.charCodeAt(this.state.pos);switch(s){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:{const t=this.skipBlockComment("*/");void 0!==t&&(this.addComment(t),null==e||e.push(t));break}case 47:{const t=this.skipLineComment(2);void 0!==t&&(this.addComment(t),null==e||e.push(t));break}default:break t}break;default:if(te(s))++this.state.pos;else if(45===s&&!this.inModule&&8192&this.optionFlags){const s=this.state.pos;if(45!==this.input.charCodeAt(s+1)||62!==this.input.charCodeAt(s+2)||!(0===t||this.state.lineStart>t))break t;{const t=this.skipLineComment(3);void 0!==t&&(this.addComment(t),null==e||e.push(t))}}else{if(60!==s||this.inModule||!(8192&this.optionFlags))break t;{const t=this.state.pos;if(33!==this.input.charCodeAt(t+1)||45!==this.input.charCodeAt(t+2)||45!==this.input.charCodeAt(t+3))break t;{const t=this.skipLineComment(4);void 0!==t&&(this.addComment(t),null==e||e.push(t))}}}}}if((null==e?void 0:e.length)>0){const s=this.state.pos,i={start:this.sourceToOffsetPos(t),end:this.sourceToOffsetPos(s),comments:e,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(i)}}finishToken(t,e){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();const s=this.state.type;this.state.type=t,this.state.value=e,this.isLookahead||this.updateContext(s)}replaceToken(t){this.state.type=t,this.updateContext()}readToken_numberSign(){if(0===this.state.pos&&this.readToken_interpreter())return;const t=this.state.pos+1,e=this.codePointAtPos(t);if(e>=48&&e<=57)throw this.raise(P.UnexpectedDigitAfterHash,this.state.curPosition());if(123===e||91===e&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),"bar"===this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(123===e?P.RecordExpressionHashIncorrectStartSyntaxType:P.TupleExpressionHashIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,123===e?this.finishToken(7):this.finishToken(1)}else Tt(e)?(++this.state.pos,this.finishToken(139,this.readWord1(e))):92===e?(++this.state.pos,this.finishToken(139,this.readWord1())):this.finishOp(27,1)}readToken_dot(){const t=this.input.charCodeAt(this.state.pos+1);t>=48&&t<=57?this.readNumber(!0):46===t&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}readToken_slash(){const t=this.input.charCodeAt(this.state.pos+1);61===t?this.finishOp(31,2):this.finishOp(56,1)}readToken_interpreter(){if(0!==this.state.pos||this.length<2)return!1;let t=this.input.charCodeAt(this.state.pos+1);if(33!==t)return!1;const e=this.state.pos;this.state.pos+=1;while(!Gt(t)&&++this.state.pos<this.length)t=this.input.charCodeAt(this.state.pos);const s=this.input.slice(e+2,this.state.pos);return this.finishToken(28,s),!0}readToken_mult_modulo(t){let e=42===t?55:54,s=1,i=this.input.charCodeAt(this.state.pos+1);42===t&&42===i&&(s++,i=this.input.charCodeAt(this.state.pos+2),e=57),61!==i||this.state.inType||(s++,e=37===t?33:30),this.finishOp(e,s)}readToken_pipe_amp(t){const e=this.input.charCodeAt(this.state.pos+1);if(e!==t){if(124===t){if(62===e)return void this.finishOp(39,2);if(this.hasPlugin("recordAndTuple")&&125===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(P.RecordExpressionBarIncorrectEndSyntaxType,this.state.curPosition());return this.state.pos+=2,void this.finishToken(9)}if(this.hasPlugin("recordAndTuple")&&93===e){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(P.TupleExpressionBarIncorrectEndSyntaxType,this.state.curPosition());return this.state.pos+=2,void this.finishToken(4)}}61!==e?this.finishOp(124===t?43:45,1):this.finishOp(30,2)}else 61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(30,3):this.finishOp(124===t?41:42,2)}readToken_caret(){const t=this.input.charCodeAt(this.state.pos+1);if(61!==t||this.state.inType)if(94===t&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])){this.finishOp(37,2);const t=this.input.codePointAt(this.state.pos);94===t&&this.unexpected()}else this.finishOp(44,1);else this.finishOp(32,2)}readToken_atSign(){const t=this.input.charCodeAt(this.state.pos+1);64===t&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}readToken_plus_min(t){const e=this.input.charCodeAt(this.state.pos+1);e!==t?61===e?this.finishOp(30,2):this.finishOp(53,1):this.finishOp(34,2)}readToken_lt(){const{pos:t}=this.state,e=this.input.charCodeAt(t+1);if(60===e)return 61===this.input.charCodeAt(t+2)?void this.finishOp(30,3):void this.finishOp(51,2);61!==e?this.finishOp(47,1):this.finishOp(49,2)}readToken_gt(){const{pos:t}=this.state,e=this.input.charCodeAt(t+1);if(62===e){const e=62===this.input.charCodeAt(t+2)?3:2;return 61===this.input.charCodeAt(t+e)?void this.finishOp(30,e+1):void this.finishOp(52,e)}61!==e?this.finishOp(48,1):this.finishOp(49,2)}readToken_eq_excl(t){const e=this.input.charCodeAt(this.state.pos+1);if(61!==e)return 61===t&&62===e?(this.state.pos+=2,void this.finishToken(19)):void this.finishOp(61===t?29:35,1);this.finishOp(46,61===this.input.charCodeAt(this.state.pos+2)?3:2)}readToken_question(){const t=this.input.charCodeAt(this.state.pos+1),e=this.input.charCodeAt(this.state.pos+2);63===t?61===e?this.finishOp(30,3):this.finishOp(40,2):46!==t||e>=48&&e<=57?(++this.state.pos,this.finishToken(17)):(this.state.pos+=2,this.finishToken(18))}getTokenFromCode(t){switch(t){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(10);case 41:return++this.state.pos,void this.finishToken(11);case 59:return++this.state.pos,void this.finishToken(13);case 44:return++this.state.pos,void this.finishToken(12);case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(P.TupleExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:return++this.state.pos,void this.finishToken(3);case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(P.RecordExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:return++this.state.pos,void this.finishToken(8);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(15,2):(++this.state.pos,this.finishToken(14)));case 63:return void this.readToken_question();case 96:return void this.readTemplateToken();case 48:{const t=this.input.charCodeAt(this.state.pos+1);if(120===t||88===t)return void this.readRadixNumber(16);if(111===t||79===t)return void this.readRadixNumber(8);if(98===t||66===t)return void this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(t);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(t);case 124:case 38:return void this.readToken_pipe_amp(t);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(t);case 60:return void this.readToken_lt();case 62:return void this.readToken_gt();case 61:case 33:return void this.readToken_eq_excl(t);case 126:return void this.finishOp(36,1);case 64:return void this.readToken_atSign();case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(Tt(t))return void this.readWord(t)}throw this.raise(P.InvalidOrUnexpectedToken,this.state.curPosition(),{unexpected:String.fromCodePoint(t)})}finishOp(t,e){const s=this.input.slice(this.state.pos,this.state.pos+e);this.state.pos+=e,this.finishToken(t,s)}readRegexp(){const t=this.state.startLoc,e=this.state.start+1;let s,i,{pos:r}=this.state;for(;;++r){if(r>=this.length)throw this.raise(P.UnterminatedRegExp,n(t,1));const e=this.input.charCodeAt(r);if(Gt(e))throw this.raise(P.UnterminatedRegExp,n(t,1));if(s)s=!1;else{if(91===e)i=!0;else if(93===e&&i)i=!1;else if(47===e&&!i)break;s=92===e}}const a=this.input.slice(e,r);++r;let o="";const h=()=>n(t,r+2-e);while(r<this.length){const t=this.codePointAtPos(r),e=String.fromCharCode(t);if(Ie.has(t))118===t?o.includes("u")&&this.raise(P.IncompatibleRegExpUVFlags,h()):117===t&&o.includes("v")&&this.raise(P.IncompatibleRegExpUVFlags,h()),o.includes(e)&&this.raise(P.DuplicateRegExpFlags,h());else{if(!At(t)&&92!==t)break;this.raise(P.MalformedRegExpFlags,h())}++r,o+=e}this.state.pos=r,this.finishToken(138,{pattern:a,flags:o})}readInt(t,e,s=!1,i=!0){const{n:r,pos:a}=Se(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,e,s,i,this.errorHandlers_readInt,!1);return this.state.pos=a,r}readRadixNumber(t){const e=this.state.pos,s=this.state.curPosition();let i=!1;this.state.pos+=2;const r=this.readInt(t);null==r&&this.raise(P.InvalidDigit,n(s,2),{radix:t});const a=this.input.charCodeAt(this.state.pos);if(110===a)++this.state.pos,i=!0;else if(109===a)throw this.raise(P.InvalidDecimal,s);if(Tt(this.codePointAtPos(this.state.pos)))throw this.raise(P.NumberIdentifier,this.state.curPosition());if(i){const t=this.input.slice(e,this.state.pos).replace(/[_n]/g,"");this.finishToken(136,t)}else this.finishToken(135,r)}readNumber(t){const e=this.state.pos,s=this.state.curPosition();let i=!1,r=!1,a=!1,o=!1;t||null!==this.readInt(10)||this.raise(P.InvalidNumber,this.state.curPosition());const h=this.state.pos-e>=2&&48===this.input.charCodeAt(e);if(h){const t=this.input.slice(e,this.state.pos);if(this.recordStrictModeErrors(P.StrictOctalLiteral,s),!this.state.strict){const e=t.indexOf("_");e>0&&this.raise(P.ZeroDigitNumericSeparator,n(s,e))}o=h&&!/[89]/.test(t)}let c=this.input.charCodeAt(this.state.pos);if(46!==c||o||(++this.state.pos,this.readInt(10),i=!0,c=this.input.charCodeAt(this.state.pos)),69!==c&&101!==c||o||(c=this.input.charCodeAt(++this.state.pos),43!==c&&45!==c||++this.state.pos,null===this.readInt(10)&&this.raise(P.InvalidOrMissingExponent,s),i=!0,a=!0,c=this.input.charCodeAt(this.state.pos)),110===c&&((i||h)&&this.raise(P.InvalidBigIntLiteral,s),++this.state.pos,r=!0),109===c){this.expectPlugin("decimal",this.state.curPosition()),(a||h)&&this.raise(P.InvalidDecimal,s),++this.state.pos;var p=!0}if(Tt(this.codePointAtPos(this.state.pos)))throw this.raise(P.NumberIdentifier,this.state.curPosition());const l=this.input.slice(e,this.state.pos).replace(/[_mn]/g,"");if(r)return void this.finishToken(136,l);if(p)return void this.finishToken(137,l);const u=o?parseInt(l,8):parseFloat(l);this.finishToken(135,u)}readCodePoint(t){const{code:e,pos:s}=Ee(this.input,this.state.pos,this.state.lineStart,this.state.curLine,t,this.errorHandlers_readCodePoint);return this.state.pos=s,e}readString(t){const{str:e,pos:s,curLine:i,lineStart:r}=Pe(34===t?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string);this.state.pos=s+1,this.state.lineStart=r,this.state.curLine=i,this.finishToken(134,e)}readTemplateContinuation(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}readTemplateToken(){const t=this.input[this.state.pos],{str:e,firstInvalidLoc:s,pos:i,curLine:a,lineStart:n}=Pe("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template);this.state.pos=i+1,this.state.lineStart=n,this.state.curLine=a,s&&(this.state.firstInvalidTemplateEscapePos=new r(s.curLine,s.pos-s.lineStart,this.sourceToOffsetPos(s.pos))),96===this.input.codePointAt(i)?this.finishToken(24,s?null:t+e+"`"):(this.state.pos++,this.finishToken(25,s?null:t+e+"${"))}recordStrictModeErrors(t,e){const s=e.index;this.state.strict&&!this.state.strictErrors.has(s)?this.raise(t,e):this.state.strictErrors.set(s,[t,e])}readWord1(t){this.state.containsEsc=!1;let e="";const s=this.state.pos;let i=this.state.pos;void 0!==t&&(this.state.pos+=t<=65535?1:2);while(this.state.pos<this.length){const t=this.codePointAtPos(this.state.pos);if(At(t))this.state.pos+=t<=65535?1:2;else{if(92!==t)break;{this.state.containsEsc=!0,e+=this.input.slice(i,this.state.pos);const t=this.state.curPosition(),r=this.state.pos===s?Tt:At;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(P.MissingUnicodeEscape,this.state.curPosition()),i=this.state.pos-1;continue}++this.state.pos;const a=this.readCodePoint(!0);null!==a&&(r(a)||this.raise(P.EscapedCharNotAnIdentifier,t),e+=String.fromCodePoint(a)),i=this.state.pos}}}return e+this.input.slice(i,this.state.pos)}readWord(t){const e=this.readWord1(t),s=F.get(e);void 0!==s?this.finishToken(s,ct(s)):this.finishToken(132,e)}checkKeywordEscapes(){const{type:t}=this.state;it(t)&&this.state.containsEsc&&this.raise(P.InvalidEscapedReservedWord,this.state.startLoc,{reservedWord:ct(t)})}raise(t,e,s={}){const i=e instanceof r?e:e.loc.start,a=t(i,s);if(!(2048&this.optionFlags))throw a;return this.isLookahead||this.state.errors.push(a),a}raiseOverwrite(t,e,s={}){const i=e instanceof r?e:e.loc.start,a=i.index,n=this.state.errors;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.loc.index===a)return n[r]=t(i,s);if(e.loc.index<a)break}return this.raise(t,e,s)}updateContext(t){}unexpected(t,e){throw this.raise(P.UnexpectedToken,null!=t?t:this.state.startLoc,{expected:e?ct(e):null})}expectPlugin(t,e){if(this.hasPlugin(t))return!0;throw this.raise(P.MissingPlugin,null!=e?e:this.state.startLoc,{missingPlugin:[t]})}expectOnePlugin(t){if(!t.some(t=>this.hasPlugin(t)))throw this.raise(P.MissingOneOfPlugins,this.state.startLoc,{missingPlugin:t})}errorBuilder(t){return(e,s,i)=>{this.raise(t,we(e,s,i))}}}class Ne{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}}class ke{constructor(t){this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=t}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new Ne)}exit(){const t=this.stack.pop(),e=this.current();for(const[s,i]of Array.from(t.undefinedPrivateNames))e?e.undefinedPrivateNames.has(s)||e.undefinedPrivateNames.set(s,i):this.parser.raise(P.InvalidPrivateFieldResolution,i,{identifierName:s})}declarePrivateName(t,e,s){const{privateNames:i,loneAccessors:r,undefinedPrivateNames:a}=this.current();let n=i.has(t);if(3&e){const s=n&&r.get(t);if(s){const i=4&s,a=4&e,o=3&s,h=3&e;n=o===h||i!==a,n||r.delete(t)}else n||r.set(t,e)}n&&this.parser.raise(P.PrivateNameRedeclaration,s,{identifierName:t}),i.add(t),a.delete(t)}usePrivateName(t,e){let s;for(s of this.stack)if(s.privateNames.has(t))return;s?s.undefinedPrivateNames.set(t,e):this.parser.raise(P.InvalidPrivateFieldResolution,e,{identifierName:t})}}class Le{constructor(t=0){this.type=t}canBeArrowParameterDeclaration(){return 2===this.type||1===this.type}isCertainlyParameterDeclaration(){return 3===this.type}}class Me extends Le{constructor(t){super(t),this.declarationErrors=new Map}recordDeclarationError(t,e){const s=e.index;this.declarationErrors.set(s,[t,e])}clearDeclarationError(t){this.declarationErrors.delete(t)}iterateErrors(t){this.declarationErrors.forEach(t)}}class Oe{constructor(t){this.parser=void 0,this.stack=[new Le],this.parser=t}enter(t){this.stack.push(t)}exit(){this.stack.pop()}recordParameterInitializerError(t,e){const s=e.loc.start,{stack:i}=this;let r=i.length-1,a=i[r];while(!a.isCertainlyParameterDeclaration()){if(!a.canBeArrowParameterDeclaration())return;a.recordDeclarationError(t,s),a=i[--r]}this.parser.raise(t,s)}recordArrowParameterBindingError(t,e){const{stack:s}=this,i=s[s.length-1],r=e.loc.start;if(i.isCertainlyParameterDeclaration())this.parser.raise(t,r);else{if(!i.canBeArrowParameterDeclaration())return;i.recordDeclarationError(t,r)}}recordAsyncArrowParametersError(t){const{stack:e}=this;let s=e.length-1,i=e[s];while(i.canBeArrowParameterDeclaration())2===i.type&&i.recordDeclarationError(P.AwaitBindingIdentifier,t),i=e[--s]}validateAsPattern(){const{stack:t}=this,e=t[t.length-1];e.canBeArrowParameterDeclaration()&&e.iterateErrors(([e,s])=>{this.parser.raise(e,s);let i=t.length-2,r=t[i];while(r.canBeArrowParameterDeclaration())r.clearDeclarationError(s.index),r=t[--i]})}}function De(){return new Le(3)}function Fe(){return new Me(1)}function Be(){return new Me(2)}function Re(){return new Le}class je extends ve{addExtra(t,e,s,i=!0){if(!t)return;let{extra:r}=t;null==r&&(r={},t.extra=r),i?r[e]=s:Object.defineProperty(r,e,{enumerable:i,value:s})}isContextual(t){return this.state.type===t&&!this.state.containsEsc}isUnparsedContextual(t,e){const s=t+e.length;if(this.input.slice(t,s)===e){const t=this.input.charCodeAt(s);return!(At(t)||55296===(64512&t))}return!1}isLookaheadContextual(t){const e=this.nextTokenStart();return this.isUnparsedContextual(e,t)}eatContextual(t){return!!this.isContextual(t)&&(this.next(),!0)}expectContextual(t,e){if(!this.eatContextual(t)){if(null!=e)throw this.raise(e,this.state.startLoc);this.unexpected(null,t)}}canInsertSemicolon(){return this.match(140)||this.match(8)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return Yt(this.input,this.offsetToSourcePos(this.state.lastTokEndLoc.index),this.state.start)}hasFollowingLineBreak(){return Yt(this.input,this.state.end,this.nextTokenStart())}isLineTerminator(){return this.eat(13)||this.canInsertSemicolon()}semicolon(t=!0){(t?this.isLineTerminator():this.eat(13))||this.raise(P.MissingSemicolon,this.state.lastTokEndLoc)}expect(t,e){this.eat(t)||this.unexpected(e,t)}tryParse(t,e=this.state.clone()){const s={node:null};try{const i=t((t=null)=>{throw s.node=t,s});if(this.state.errors.length>e.errors.length){const t=this.state;return this.state=e,this.state.tokensLength=t.tokensLength,{node:i,error:t.errors[e.errors.length],thrown:!1,aborted:!1,failState:t}}return{node:i,error:null,thrown:!1,aborted:!1,failState:null}}catch(i){const t=this.state;if(this.state=e,i instanceof SyntaxError)return{node:null,error:i,thrown:!0,aborted:!1,failState:t};if(i===s)return{node:s.node,error:null,thrown:!1,aborted:!0,failState:t};throw i}}checkExpressionErrors(t,e){if(!t)return!1;const{shorthandAssignLoc:s,doubleProtoLoc:i,privateKeyLoc:r,optionalParametersLoc:a}=t,n=!!s||!!i||!!a||!!r;if(!e)return n;null!=s&&this.raise(P.InvalidCoverInitializedName,s),null!=i&&this.raise(P.DuplicateProto,i),null!=r&&this.raise(P.UnexpectedPrivateField,r),null!=a&&this.unexpected(a)}isLiteralPropertyName(){return Y(this.state.type)}isPrivateName(t){return"PrivateName"===t.type}getPrivateNameSV(t){return t.id.name}hasPropertyAsPrivateName(t){return("MemberExpression"===t.type||"OptionalMemberExpression"===t.type)&&this.isPrivateName(t.property)}isObjectProperty(t){return"ObjectProperty"===t.type}isObjectMethod(t){return"ObjectMethod"===t.type}initializeScopes(t="module"===this.options.sourceType){const e=this.state.labels;this.state.labels=[];const s=this.exportedIdentifiers;this.exportedIdentifiers=new Set;const i=this.inModule;this.inModule=t;const r=this.scope,a=this.getScopeHandler();this.scope=new a(this,t);const n=this.prodParam;this.prodParam=new oe;const o=this.classScope;this.classScope=new ke(this);const h=this.expressionScope;return this.expressionScope=new Oe(this),()=>{this.state.labels=e,this.exportedIdentifiers=s,this.inModule=i,this.scope=r,this.prodParam=n,this.classScope=o,this.expressionScope=h}}enterInitialScopes(){let t=0;this.inModule&&(t|=2),32&this.optionFlags&&(t|=1),this.scope.enter(1),this.prodParam.enter(t)}checkDestructuringPrivate(t){const{privateKeyLoc:e}=t;null!==e&&this.expectPlugin("destructuringPrivate",e)}}class Ue{constructor(){this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null}}class _e{constructor(t,e,s){this.type="",this.start=e,this.end=0,this.loc=new a(s),128&(null==t?void 0:t.optionFlags)&&(this.range=[e,0]),null!=t&&t.filename&&(this.loc.filename=t.filename)}}const ze=_e.prototype;ze.__clone=function(){const t=new _e(void 0,this.start,this.loc.start),e=Object.keys(this);for(let s=0,i=e.length;s<i;s++){const i=e[s];"leadingComments"!==i&&"trailingComments"!==i&&"innerComments"!==i&&(t[i]=this[i])}return t};class He extends je{startNode(){const t=this.state.startLoc;return new _e(this,t.index,t)}startNodeAt(t){return new _e(this,t.index,t)}startNodeAtNode(t){return this.startNodeAt(t.loc.start)}finishNode(t,e){return this.finishNodeAt(t,e,this.state.lastTokEndLoc)}finishNodeAt(t,e,s){return t.type=e,t.end=s.index,t.loc.end=s,128&this.optionFlags&&(t.range[1]=s.index),4096&this.optionFlags&&this.processComment(t),t}resetStartLocation(t,e){t.start=e.index,t.loc.start=e,128&this.optionFlags&&(t.range[0]=e.index)}resetEndLocation(t,e=this.state.lastTokEndLoc){t.end=e.index,t.loc.end=e,128&this.optionFlags&&(t.range[1]=e.index)}resetStartLocationFromNode(t,e){this.resetStartLocation(t,e.loc.start)}castNodeTo(t,e){return t.type=e,t}cloneIdentifier(t){const{type:e,start:s,end:i,loc:r,range:a,name:n}=t,o=Object.create(ze);return o.type=e,o.start=s,o.end=i,o.loc=r,o.range=a,o.name=n,t.extra&&(o.extra=t.extra),o}cloneStringLiteral(t){const{type:e,start:s,end:i,loc:r,range:a,extra:n}=t,o=Object.create(ze);return o.type=e,o.start=s,o.end=i,o.loc=r,o.range=a,o.extra=n,o.value=t.value,o}}const qe=t=>"ParenthesizedExpression"===t.type?qe(t.expression):t;class Ve extends He{toAssignable(t,e=!1){var s,i;let r=void 0;switch(("ParenthesizedExpression"===t.type||null!=(s=t.extra)&&s.parenthesized)&&(r=qe(t),e?"Identifier"===r.type?this.expressionScope.recordArrowParameterBindingError(P.InvalidParenthesizedAssignment,t):"MemberExpression"===r.type||this.isOptionalMemberExpression(r)||this.raise(P.InvalidParenthesizedAssignment,t):this.raise(P.InvalidParenthesizedAssignment,t)),t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":this.castNodeTo(t,"ObjectPattern");for(let s=0,i=t.properties.length,r=i-1;s<i;s++){var a;const i=t.properties[s],n=s===r;this.toAssignableObjectExpressionProp(i,n,e),n&&"RestElement"===i.type&&null!=(a=t.extra)&&a.trailingCommaLoc&&this.raise(P.RestTrailingComma,t.extra.trailingCommaLoc)}break;case"ObjectProperty":{const{key:s,value:i}=t;this.isPrivateName(s)&&this.classScope.usePrivateName(this.getPrivateNameSV(s),s.loc.start),this.toAssignable(i,e);break}case"SpreadElement":throw new Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":this.castNodeTo(t,"ArrayPattern"),this.toAssignableList(t.elements,null==(i=t.extra)?void 0:i.trailingCommaLoc,e);break;case"AssignmentExpression":"="!==t.operator&&this.raise(P.MissingEqInAssignment,t.left.loc.end),this.castNodeTo(t,"AssignmentPattern"),delete t.operator,this.toAssignable(t.left,e);break;case"ParenthesizedExpression":this.toAssignable(r,e);break}}toAssignableObjectExpressionProp(t,e,s){if("ObjectMethod"===t.type)this.raise("get"===t.kind||"set"===t.kind?P.PatternHasAccessor:P.PatternHasMethod,t.key);else if("SpreadElement"===t.type){this.castNodeTo(t,"RestElement");const i=t.argument;this.checkToRestConversion(i,!1),this.toAssignable(i,s),e||this.raise(P.RestTrailingComma,t)}else this.toAssignable(t,s)}toAssignableList(t,e,s){const i=t.length-1;for(let r=0;r<=i;r++){const a=t[r];a&&(this.toAssignableListItem(t,r,s),"RestElement"===a.type&&(r<i?this.raise(P.RestTrailingComma,a):e&&this.raise(P.RestTrailingComma,e)))}}toAssignableListItem(t,e,s){const i=t[e];if("SpreadElement"===i.type){this.castNodeTo(i,"RestElement");const t=i.argument;this.checkToRestConversion(t,!0),this.toAssignable(t,s)}else this.toAssignable(i,s)}isAssignable(t,e){switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":return!0;case"ObjectExpression":{const e=t.properties.length-1;return t.properties.every((t,s)=>"ObjectMethod"!==t.type&&(s===e||"SpreadElement"!==t.type)&&this.isAssignable(t))}case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every(t=>null===t||this.isAssignable(t));case"AssignmentExpression":return"="===t.operator;case"ParenthesizedExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!e;default:return!1}}toReferencedList(t,e){return t}toReferencedListDeep(t,e){this.toReferencedList(t,e);for(const s of t)"ArrayExpression"===(null==s?void 0:s.type)&&this.toReferencedListDeep(s.elements)}parseSpread(t){const e=this.startNode();return this.next(),e.argument=this.parseMaybeAssignAllowIn(t,void 0),this.finishNode(e,"SpreadElement")}parseRestBinding(){const t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}parseBindingAtom(){switch(this.state.type){case 0:{const t=this.startNode();return this.next(),t.elements=this.parseBindingList(3,93,1),this.finishNode(t,"ArrayPattern")}case 5:return this.parseObjectLike(8,!0)}return this.parseIdentifier()}parseBindingList(t,e,s){const i=1&s,r=[];let a=!0;while(!this.eat(t))if(a?a=!1:this.expect(12),i&&this.match(12))r.push(null);else{if(this.eat(t))break;if(this.match(21)){let i=this.parseRestBinding();if((this.hasPlugin("flow")||2&s)&&(i=this.parseFunctionParamType(i)),r.push(i),!this.checkCommaAfterRest(e)){this.expect(t);break}}else{const t=[];if(2&s){this.match(26)&&this.hasPlugin("decorators")&&this.raise(P.UnsupportedParameterDecorator,this.state.startLoc);while(this.match(26))t.push(this.parseDecorator())}r.push(this.parseBindingElement(s,t))}}return r}parseBindingRestProperty(t){return this.next(),t.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(t,"RestElement")}parseBindingProperty(){const{type:t,startLoc:e}=this.state;if(21===t)return this.parseBindingRestProperty(this.startNode());const s=this.startNode();return 139===t?(this.expectPlugin("destructuringPrivate",e),this.classScope.usePrivateName(this.state.value,e),s.key=this.parsePrivateName()):this.parsePropertyName(s),s.method=!1,this.parseObjPropValue(s,e,!1,!1,!0,!1)}parseBindingElement(t,e){const s=this.parseMaybeDefault();(this.hasPlugin("flow")||2&t)&&this.parseFunctionParamType(s),e.length&&(s.decorators=e,this.resetStartLocationFromNode(s,e[0]));const i=this.parseMaybeDefault(s.loc.start,s);return i}parseFunctionParamType(t){return t}parseMaybeDefault(t,e){if(null!=t||(t=this.state.startLoc),e=null!=e?e:this.parseBindingAtom(),!this.eat(29))return e;const s=this.startNodeAt(t);return s.left=e,s.right=this.parseMaybeAssignAllowIn(),this.finishNode(s,"AssignmentPattern")}isValidLVal(t,e,s){switch(t){case"AssignmentPattern":return"left";case"RestElement":return"argument";case"ObjectProperty":return"value";case"ParenthesizedExpression":return"expression";case"ArrayPattern":return"elements";case"ObjectPattern":return"properties"}return!1}isOptionalMemberExpression(t){return"OptionalMemberExpression"===t.type}checkLVal(t,e,s=64,i=!1,r=!1,a=!1){var n;const o=t.type;if(this.isObjectMethod(t))return;const h=this.isOptionalMemberExpression(t);if(h||"MemberExpression"===o)return h&&(this.expectPlugin("optionalChainingAssign",t.loc.start),"AssignmentExpression"!==e.type&&this.raise(P.InvalidLhsOptionalChaining,t,{ancestor:e})),void(64!==s&&this.raise(P.InvalidPropertyBindingPattern,t));if("Identifier"===o){this.checkIdentifier(t,s,r);const{name:e}=t;return void(i&&(i.has(e)?this.raise(P.ParamDupe,t):i.add(e)))}const c=this.isValidLVal(o,!(a||null!=(n=t.extra)&&n.parenthesized)&&"AssignmentExpression"===e.type,s);if(!0===c)return;if(!1===c){const i=64===s?P.InvalidLhs:P.InvalidLhsBinding;return void this.raise(i,t,{ancestor:e})}let p,l;"string"===typeof c?(p=c,l="ParenthesizedExpression"===o):[p,l]=c;const u="ArrayPattern"===o||"ObjectPattern"===o?{type:o}:e,d=t[p];if(Array.isArray(d))for(const m of d)m&&this.checkLVal(m,u,s,i,r,l);else d&&this.checkLVal(d,u,s,i,r,l)}checkIdentifier(t,e,s=!1){this.state.strict&&(s?kt(t.name,this.inModule):Nt(t.name))&&(64===e?this.raise(P.StrictEvalArguments,t,{referenceName:t.name}):this.raise(P.StrictEvalArgumentsBinding,t,{bindingName:t.name})),8192&e&&"let"===t.name&&this.raise(P.LetInLexicalBinding,t),64&e||this.declareNameFromIdentifier(t,e)}declareNameFromIdentifier(t,e){this.scope.declareName(t.name,e,t.loc.start)}checkToRestConversion(t,e){switch(t.type){case"ParenthesizedExpression":this.checkToRestConversion(t.expression,e);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(e)break;default:this.raise(P.InvalidRestAssignmentPattern,t)}}checkCommaAfterRest(t){return!!this.match(12)&&(this.raise(this.lookaheadCharCode()===t?P.RestTrailingComma:P.ElementAfterRest,this.state.startLoc),!0)}}function $e(t){if(null==t)throw new Error(`Unexpected ${t} value.`);return t}function Ke(t){if(!t)throw new Error("Assert fail")}const Je=g`typescript`({AbstractMethodHasImplementation:({methodName:t})=>`Method '${t}' cannot have an implementation because it is marked abstract.`,AbstractPropertyHasInitializer:({propertyName:t})=>`Property '${t}' cannot have an initializer because it is marked abstract.`,AccessorCannotBeOptional:"An 'accessor' property cannot be declared optional.",AccessorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccessorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:({kind:t})=>`'declare' is not allowed in ${t}ters.`,DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:({modifier:t})=>"Accessibility modifier already seen.",DuplicateModifier:({modifier:t})=>`Duplicate modifier: '${t}'.`,EmptyHeritageClauseType:({token:t})=>`'${t}' list cannot be empty.`,EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:({modifiers:t})=>`'${t[0]}' modifier cannot be used with '${t[1]}' modifier.`,IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:({modifier:t})=>`Index signatures cannot have an accessibility modifier ('${t}').`,IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidHeritageClauseType:({token:t})=>`'${t}' list can only include identifiers or qualified-names with optional type arguments.`,InvalidModifierOnTypeMember:({modifier:t})=>`'${t}' modifier cannot appear on a type member.`,InvalidModifierOnTypeParameter:({modifier:t})=>`'${t}' modifier cannot appear on a type parameter.`,InvalidModifierOnTypeParameterPositions:({modifier:t})=>`'${t}' modifier can only appear on a type parameter of a class, interface or type alias.`,InvalidModifiersOrder:({orderedModifiers:t})=>`'${t[0]}' modifier must precede '${t[1]}' modifier.`,InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:({modifier:t})=>`Private elements cannot have an accessibility modifier ('${t}').`,ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccessorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccessorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccessorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:({typeParameterName:t})=>`Single type parameter ${t} should have a trailing comma. Example usage: <${t},>.`,StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TupleOptionalAfterType:"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:({type:t})=>`Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ${t}.`});function We(t){switch(t){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}function Xe(t){return"private"===t||"public"===t||"protected"===t}function Ge(t){return"in"===t||"out"===t}var Ye=t=>class extends t{constructor(...t){super(...t),this.tsParseInOutModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out"],disallowedModifiers:["const","public","private","protected","readonly","declare","abstract","override"],errorTemplate:Je.InvalidModifierOnTypeParameter}),this.tsParseConstModifier=this.tsParseModifiers.bind(this,{allowedModifiers:["const"],disallowedModifiers:["in","out"],errorTemplate:Je.InvalidModifierOnTypeParameterPositions}),this.tsParseInOutConstModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out","const"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:Je.InvalidModifierOnTypeParameter})}getScopeHandler(){return ne}tsIsIdentifier(){return W(this.state.type)}tsTokenCanFollowModifier(){return this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(139)||this.isLiteralPropertyName()}tsNextTokenOnSameLineAndCanFollowModifier(){return this.next(),!this.hasPrecedingLineBreak()&&this.tsTokenCanFollowModifier()}tsNextTokenCanFollowModifier(){return this.match(106)?(this.next(),this.tsTokenCanFollowModifier()):this.tsNextTokenOnSameLineAndCanFollowModifier()}tsParseModifier(t,e){if(!W(this.state.type)&&58!==this.state.type&&75!==this.state.type)return;const s=this.state.value;if(t.includes(s)){if(e&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return s}}tsParseModifiers({allowedModifiers:t,disallowedModifiers:e,stopOnStartOfClassStaticBlock:s,errorTemplate:i=Je.InvalidModifierOnTypeMember},r){const a=(t,e,s,i)=>{e===s&&r[i]&&this.raise(Je.InvalidModifiersOrder,t,{orderedModifiers:[s,i]})},n=(t,e,s,i)=>{(r[s]&&e===i||r[i]&&e===s)&&this.raise(Je.IncompatibleModifiers,t,{modifiers:[s,i]})};for(;;){const{startLoc:o}=this.state,h=this.tsParseModifier(t.concat(null!=e?e:[]),s);if(!h)break;Xe(h)?r.accessibility?this.raise(Je.DuplicateAccessibilityModifier,o,{modifier:h}):(a(o,h,h,"override"),a(o,h,h,"static"),a(o,h,h,"readonly"),r.accessibility=h):Ge(h)?(r[h]&&this.raise(Je.DuplicateModifier,o,{modifier:h}),r[h]=!0,a(o,h,"in","out")):(hasOwnProperty.call(r,h)?this.raise(Je.DuplicateModifier,o,{modifier:h}):(a(o,h,"static","readonly"),a(o,h,"static","override"),a(o,h,"override","readonly"),a(o,h,"abstract","override"),n(o,h,"declare","override"),n(o,h,"static","abstract")),r[h]=!0),null!=e&&e.includes(h)&&this.raise(i,o,{modifier:h})}}tsIsListTerminator(t){switch(t){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}}tsParseList(t,e){const s=[];while(!this.tsIsListTerminator(t))s.push(e());return s}tsParseDelimitedList(t,e,s){return $e(this.tsParseDelimitedListWorker(t,e,!0,s))}tsParseDelimitedListWorker(t,e,s,i){const r=[];let a=-1;for(;;){if(this.tsIsListTerminator(t))break;a=-1;const i=e();if(null==i)return;if(r.push(i),!this.eat(12)){if(this.tsIsListTerminator(t))break;return void(s&&this.expect(12))}a=this.state.lastTokStartLoc.index}return i&&(i.value=a),r}tsParseBracketedList(t,e,s,i,r){i||(s?this.expect(0):this.expect(47));const a=this.tsParseDelimitedList(t,e,r);return s?this.expect(3):this.expect(48),a}tsParseImportType(){const t=this.startNode();return this.expect(83),this.expect(10),this.match(134)?t.argument=this.parseStringLiteral(this.state.value):(this.raise(Je.UnsupportedImportTypeArgument,this.state.startLoc),t.argument=super.parseExprAtom()),this.eat(12)?t.options=this.tsParseImportTypeOptions():t.options=null,this.expect(11),this.eat(16)&&(t.qualifier=this.tsParseEntityName(3)),this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSImportType")}tsParseImportTypeOptions(){const t=this.startNode();this.expect(5);const e=this.startNode();return this.isContextual(76)?(e.method=!1,e.key=this.parseIdentifier(!0),e.computed=!1,e.shorthand=!1):this.unexpected(null,76),this.expect(14),e.value=this.tsParseImportTypeWithPropertyValue(),t.properties=[this.finishObjectProperty(e)],this.expect(8),this.finishNode(t,"ObjectExpression")}tsParseImportTypeWithPropertyValue(){const t=this.startNode(),e=[];this.expect(5);while(!this.match(8)){const t=this.state.type;W(t)||134===t?e.push(super.parsePropertyDefinition(null)):this.unexpected(),this.eat(12)}return t.properties=e,this.next(),this.finishNode(t,"ObjectExpression")}tsParseEntityName(t){let e;if(1&t&&this.match(78))if(2&t)e=this.parseIdentifier(!0);else{const t=this.startNode();this.next(),e=this.finishNode(t,"ThisExpression")}else e=this.parseIdentifier(!!(1&t));while(this.eat(16)){const s=this.startNodeAtNode(e);s.left=e,s.right=this.parseIdentifier(!!(1&t)),e=this.finishNode(s,"TSQualifiedName")}return e}tsParseTypeReference(){const t=this.startNode();return t.typeName=this.tsParseEntityName(1),!this.hasPrecedingLineBreak()&&this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeReference")}tsParseThisTypePredicate(t){this.next();const e=this.startNodeAtNode(t);return e.parameterName=t,e.typeAnnotation=this.tsParseTypeAnnotation(!1),e.asserts=!1,this.finishNode(e,"TSTypePredicate")}tsParseThisTypeNode(){const t=this.startNode();return this.next(),this.finishNode(t,"TSThisType")}tsParseTypeQuery(){const t=this.startNode();return this.expect(87),this.match(83)?t.exprName=this.tsParseImportType():t.exprName=this.tsParseEntityName(3),!this.hasPrecedingLineBreak()&&this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeQuery")}tsParseTypeParameter(t){const e=this.startNode();return t(e),e.name=this.tsParseTypeParameterName(),e.constraint=this.tsEatThenParseType(81),e.default=this.tsEatThenParseType(29),this.finishNode(e,"TSTypeParameter")}tsTryParseTypeParameters(t){if(this.match(47))return this.tsParseTypeParameters(t)}tsParseTypeParameters(t){const e=this.startNode();this.match(47)||this.match(143)?this.next():this.unexpected();const s={value:-1};return e.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,t),!1,!0,s),0===e.params.length&&this.raise(Je.EmptyTypeParameters,e),-1!==s.value&&this.addExtra(e,"trailingComma",s.value),this.finishNode(e,"TSTypeParameterDeclaration")}tsFillSignature(t,e){const s=19===t,i="parameters",r="typeAnnotation";e.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier),this.expect(10),e[i]=this.tsParseBindingListForSignature(),(s||this.match(t))&&(e[r]=this.tsParseTypeOrTypePredicateAnnotation(t))}tsParseBindingListForSignature(){const t=super.parseBindingList(11,41,2);for(const e of t){const{type:t}=e;"AssignmentPattern"!==t&&"TSParameterProperty"!==t||this.raise(Je.UnsupportedSignatureParameterKind,e,{type:t})}return t}tsParseTypeMemberSemicolon(){this.eat(12)||this.isLineTerminator()||this.expect(13)}tsParseSignatureMember(t,e){return this.tsFillSignature(14,e),this.tsParseTypeMemberSemicolon(),this.finishNode(e,t)}tsIsUnambiguouslyIndexSignature(){return this.next(),!!W(this.state.type)&&(this.next(),this.match(14))}tsTryParseIndexSignature(t){if(!this.match(0)||!this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this)))return;this.expect(0);const e=this.parseIdentifier();e.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(e),this.expect(3),t.parameters=[e];const s=this.tsTryParseTypeAnnotation();return s&&(t.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSIndexSignature")}tsParsePropertyOrMethodSignature(t,e){if(this.eat(17)&&(t.optional=!0),this.match(10)||this.match(47)){e&&this.raise(Je.ReadonlyForMethodSignature,t);const s=t;s.kind&&this.match(47)&&this.raise(Je.AccessorCannotHaveTypeParameters,this.state.curPosition()),this.tsFillSignature(14,s),this.tsParseTypeMemberSemicolon();const i="parameters",r="typeAnnotation";if("get"===s.kind)s[i].length>0&&(this.raise(P.BadGetterArity,this.state.curPosition()),this.isThisParam(s[i][0])&&this.raise(Je.AccessorCannotDeclareThisParameter,this.state.curPosition()));else if("set"===s.kind){if(1!==s[i].length)this.raise(P.BadSetterArity,this.state.curPosition());else{const t=s[i][0];this.isThisParam(t)&&this.raise(Je.AccessorCannotDeclareThisParameter,this.state.curPosition()),"Identifier"===t.type&&t.optional&&this.raise(Je.SetAccessorCannotHaveOptionalParameter,this.state.curPosition()),"RestElement"===t.type&&this.raise(Je.SetAccessorCannotHaveRestParameter,this.state.curPosition())}s[r]&&this.raise(Je.SetAccessorCannotHaveReturnType,s[r])}else s.kind="method";return this.finishNode(s,"TSMethodSignature")}{const s=t;e&&(s.readonly=!0);const i=this.tsTryParseTypeAnnotation();return i&&(s.typeAnnotation=i),this.tsParseTypeMemberSemicolon(),this.finishNode(s,"TSPropertySignature")}}tsParseTypeMember(){const t=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",t);if(this.match(77)){const e=this.startNode();return this.next(),this.match(10)||this.match(47)?this.tsParseSignatureMember("TSConstructSignatureDeclaration",t):(t.key=this.createIdentifier(e,"new"),this.tsParsePropertyOrMethodSignature(t,!1))}this.tsParseModifiers({allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]},t);const e=this.tsTryParseIndexSignature(t);return e||(super.parsePropertyName(t),t.computed||"Identifier"!==t.key.type||"get"!==t.key.name&&"set"!==t.key.name||!this.tsTokenCanFollowModifier()||(t.kind=t.key.name,super.parsePropertyName(t),this.match(10)||this.match(47)||this.unexpected(null,10)),this.tsParsePropertyOrMethodSignature(t,!!t.readonly))}tsParseTypeLiteral(){const t=this.startNode();return t.members=this.tsParseObjectTypeMembers(),this.finishNode(t,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(5);const t=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),t}tsIsStartOfMappedType(){return this.next(),this.eat(53)?this.isContextual(122):(this.isContextual(122)&&this.next(),!!this.match(0)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(58))))}tsParseMappedType(){const t=this.startNode();this.expect(5),this.match(53)?(t.readonly=this.state.value,this.next(),this.expectContextual(122)):this.eatContextual(122)&&(t.readonly=!0),this.expect(0);{const e=this.startNode();e.name=this.tsParseTypeParameterName(),e.constraint=this.tsExpectThenParseType(58),t.typeParameter=this.finishNode(e,"TSTypeParameter")}return t.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(t.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(t.optional=!0),t.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(t,"TSMappedType")}tsParseTupleType(){const t=this.startNode();t.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let e=!1;return t.elementTypes.forEach(t=>{const{type:s}=t;!e||"TSRestType"===s||"TSOptionalType"===s||"TSNamedTupleMember"===s&&t.optional||this.raise(Je.OptionalTypeBeforeRequired,t),e||(e="TSNamedTupleMember"===s&&t.optional||"TSOptionalType"===s)}),this.finishNode(t,"TSTupleType")}tsParseTupleElementType(){const t=this.state.startLoc,e=this.eat(21),{startLoc:s}=this.state;let i,r,a,n;const o=G(this.state.type),h=o?this.lookaheadCharCode():null;if(58===h)i=!0,a=!1,r=this.parseIdentifier(!0),this.expect(14),n=this.tsParseType();else if(63===h){a=!0;const t=this.state.value,e=this.tsParseNonArrayType();58===this.lookaheadCharCode()?(i=!0,r=this.createIdentifier(this.startNodeAt(s),t),this.expect(17),this.expect(14),n=this.tsParseType()):(i=!1,n=e,this.expect(17))}else n=this.tsParseType(),a=this.eat(17),i=this.eat(14);if(i){let t;r?(t=this.startNodeAt(s),t.optional=a,t.label=r,t.elementType=n,this.eat(17)&&(t.optional=!0,this.raise(Je.TupleOptionalAfterType,this.state.lastTokStartLoc))):(t=this.startNodeAt(s),t.optional=a,this.raise(Je.InvalidTupleMemberLabel,n),t.label=n,t.elementType=this.tsParseType()),n=this.finishNode(t,"TSNamedTupleMember")}else if(a){const t=this.startNodeAt(s);t.typeAnnotation=n,n=this.finishNode(t,"TSOptionalType")}if(e){const e=this.startNodeAt(t);e.typeAnnotation=n,n=this.finishNode(e,"TSRestType")}return n}tsParseParenthesizedType(){const t=this.startNode();return this.expect(10),t.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(t,"TSParenthesizedType")}tsParseFunctionOrConstructorType(t,e){const s=this.startNode();return"TSConstructorType"===t&&(s.abstract=!!e,e&&this.next(),this.next()),this.tsInAllowConditionalTypesContext(()=>this.tsFillSignature(19,s)),this.finishNode(s,t)}tsParseLiteralTypeNode(){const t=this.startNode();switch(this.state.type){case 135:case 136:case 134:case 85:case 86:t.literal=super.parseExprAtom();break;default:this.unexpected()}return this.finishNode(t,"TSLiteralType")}tsParseTemplateLiteralType(){{const t=this.startNode();return t.literal=super.parseTemplate(!1),this.finishNode(t,"TSLiteralType")}}parseTemplateSubstitution(){return this.state.inType?this.tsParseType():super.parseTemplateSubstitution()}tsParseThisTypeOrThisTypePredicate(){const t=this.tsParseThisTypeNode();return this.isContextual(116)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(t):t}tsParseNonArrayType(){switch(this.state.type){case 134:case 135:case 136:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if("-"===this.state.value){const t=this.startNode(),e=this.lookahead();return 135!==e.type&&136!==e.type&&this.unexpected(),t.literal=this.parseMaybeUnary(),this.finishNode(t,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:{const{type:t}=this.state;if(W(t)||88===t||84===t){const e=88===t?"TSVoidKeyword":84===t?"TSNullKeyword":We(this.state.value);if(void 0!==e&&46!==this.lookaheadCharCode()){const t=this.startNode();return this.next(),this.finishNode(t,e)}return this.tsParseTypeReference()}}}this.unexpected()}tsParseArrayTypeOrHigher(){const{startLoc:t}=this.state;let e=this.tsParseNonArrayType();while(!this.hasPrecedingLineBreak()&&this.eat(0))if(this.match(3)){const s=this.startNodeAt(t);s.elementType=e,this.expect(3),e=this.finishNode(s,"TSArrayType")}else{const s=this.startNodeAt(t);s.objectType=e,s.indexType=this.tsParseType(),this.expect(3),e=this.finishNode(s,"TSIndexedAccessType")}return e}tsParseTypeOperator(){const t=this.startNode(),e=this.state.value;return this.next(),t.operator=e,t.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===e&&this.tsCheckTypeAnnotationForReadOnly(t),this.finishNode(t,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(t){switch(t.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(Je.UnexpectedReadonly,t)}}tsParseInferType(){const t=this.startNode();this.expectContextual(115);const e=this.startNode();return e.name=this.tsParseTypeParameterName(),e.constraint=this.tsTryParse(()=>this.tsParseConstraintForInferType()),t.typeParameter=this.finishNode(e,"TSTypeParameter"),this.finishNode(t,"TSInferType")}tsParseConstraintForInferType(){if(this.eat(81)){const t=this.tsInDisallowConditionalTypesContext(()=>this.tsParseType());if(this.state.inDisallowConditionalTypesContext||!this.match(17))return t}}tsParseTypeOperatorOrHigher(){const t=ot(this.state.type)&&!this.state.containsEsc;return t?this.tsParseTypeOperator():this.isContextual(115)?this.tsParseInferType():this.tsInAllowConditionalTypesContext(()=>this.tsParseArrayTypeOrHigher())}tsParseUnionOrIntersectionType(t,e,s){const i=this.startNode(),r=this.eat(s),a=[];do{a.push(e())}while(this.eat(s));return 1!==a.length||r?(i.types=a,this.finishNode(i,t)):a[0]}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}tsIsStartOfFunctionType(){return!!this.match(47)||this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(W(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){const{errors:e}=this.state,s=e.length;try{return this.parseObjectLike(8,!0),e.length===s}catch(t){return!1}}if(this.match(0)){this.next();const{errors:t}=this.state,s=t.length;try{return super.parseBindingList(3,93,1),t.length===s}catch(e){return!1}}return!1}tsIsUnambiguouslyStartOfFunctionType(){if(this.next(),this.match(11)||this.match(21))return!0;if(this.tsSkipParameterStart()){if(this.match(14)||this.match(12)||this.match(17)||this.match(29))return!0;if(this.match(11)&&(this.next(),this.match(19)))return!0}return!1}tsParseTypeOrTypePredicateAnnotation(t){return this.tsInType(()=>{const e=this.startNode();this.expect(t);const s=this.startNode(),i=!!this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(i&&this.match(78)){let t=this.tsParseThisTypeOrThisTypePredicate();return"TSThisType"===t.type?(s.parameterName=t,s.asserts=!0,s.typeAnnotation=null,t=this.finishNode(s,"TSTypePredicate")):(this.resetStartLocationFromNode(t,s),t.asserts=!0),e.typeAnnotation=t,this.finishNode(e,"TSTypeAnnotation")}const r=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!r)return i?(s.parameterName=this.parseIdentifier(),s.asserts=i,s.typeAnnotation=null,e.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(e,"TSTypeAnnotation")):this.tsParseTypeAnnotation(!1,e);const a=this.tsParseTypeAnnotation(!1);return s.parameterName=r,s.typeAnnotation=a,s.asserts=i,e.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(e,"TSTypeAnnotation")})}tsTryParseTypeOrTypePredicateAnnotation(){if(this.match(14))return this.tsParseTypeOrTypePredicateAnnotation(14)}tsTryParseTypeAnnotation(){if(this.match(14))return this.tsParseTypeAnnotation()}tsTryParseType(){return this.tsEatThenParseType(14)}tsParseTypePredicatePrefix(){const t=this.parseIdentifier();if(this.isContextual(116)&&!this.hasPrecedingLineBreak())return this.next(),t}tsParseTypePredicateAsserts(){if(109!==this.state.type)return!1;const t=this.state.containsEsc;return this.next(),!(!W(this.state.type)&&!this.match(78))&&(t&&this.raise(P.InvalidEscapedReservedWord,this.state.lastTokStartLoc,{reservedWord:"asserts"}),!0)}tsParseTypeAnnotation(t=!0,e=this.startNode()){return this.tsInType(()=>{t&&this.expect(14),e.typeAnnotation=this.tsParseType()}),this.finishNode(e,"TSTypeAnnotation")}tsParseType(){Ke(this.state.inType);const t=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return t;const e=this.startNodeAtNode(t);return e.checkType=t,e.extendsType=this.tsInDisallowConditionalTypesContext(()=>this.tsParseNonConditionalType()),this.expect(17),e.trueType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.expect(14),e.falseType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.finishNode(e,"TSConditionalType")}isAbstractConstructorSignature(){return this.isContextual(124)&&77===this.lookahead().type}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(Je.ReservedTypeAssertion,this.state.startLoc);const t=this.startNode();return t.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?this.tsParseTypeReference():this.tsParseType())),this.expect(48),t.expression=this.parseMaybeUnary(),this.finishNode(t,"TSTypeAssertion")}tsParseHeritageClause(t){const e=this.state.startLoc,s=this.tsParseDelimitedList("HeritageClauseElement",()=>{{const t=this.startNode();return t.expression=this.tsParseEntityName(3),this.match(47)&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSExpressionWithTypeArguments")}});return s.length||this.raise(Je.EmptyHeritageClauseType,e,{token:t}),s}tsParseInterfaceDeclaration(t,e={}){if(this.hasFollowingLineBreak())return null;this.expectContextual(129),e.declare&&(t.declare=!0),W(this.state.type)?(t.id=this.parseIdentifier(),this.checkIdentifier(t.id,130)):(t.id=null,this.raise(Je.MissingInterfaceName,this.state.startLoc)),t.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers),this.eat(81)&&(t.extends=this.tsParseHeritageClause("extends"));const s=this.startNode();return s.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),t.body=this.finishNode(s,"TSInterfaceBody"),this.finishNode(t,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(t){return t.id=this.parseIdentifier(),this.checkIdentifier(t.id,2),t.typeAnnotation=this.tsInType(()=>{if(t.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutModifiers),this.expect(29),this.isContextual(114)&&16!==this.lookahead().type){const t=this.startNode();return this.next(),this.finishNode(t,"TSIntrinsicKeyword")}return this.tsParseType()}),this.semicolon(),this.finishNode(t,"TSTypeAliasDeclaration")}tsInTopLevelContext(t){if(this.curContext()===C.brace)return t();{const e=this.state.context;this.state.context=[e[0]];try{return t()}finally{this.state.context=e}}}tsInType(t){const e=this.state.inType;this.state.inType=!0;try{return t()}finally{this.state.inType=e}}tsInDisallowConditionalTypesContext(t){const e=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return t()}finally{this.state.inDisallowConditionalTypesContext=e}}tsInAllowConditionalTypesContext(t){const e=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return t()}finally{this.state.inDisallowConditionalTypesContext=e}}tsEatThenParseType(t){if(this.match(t))return this.tsNextThenParseType()}tsExpectThenParseType(t){return this.tsInType(()=>(this.expect(t),this.tsParseType()))}tsNextThenParseType(){return this.tsInType(()=>(this.next(),this.tsParseType()))}tsParseEnumMember(){const t=this.startNode();return t.id=this.match(134)?super.parseStringLiteral(this.state.value):this.parseIdentifier(!0),this.eat(29)&&(t.initializer=super.parseMaybeAssignAllowIn()),this.finishNode(t,"TSEnumMember")}tsParseEnumDeclaration(t,e={}){return e.const&&(t.const=!0),e.declare&&(t.declare=!0),this.expectContextual(126),t.id=this.parseIdentifier(),this.checkIdentifier(t.id,t.const?8971:8459),this.expect(5),t.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(t,"TSEnumDeclaration")}tsParseEnumBody(){const t=this.startNode();return this.expect(5),t.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(t,"TSEnumBody")}tsParseModuleBlock(){const t=this.startNode();return this.scope.enter(0),this.expect(5),super.parseBlockOrModuleBlockBody(t.body=[],void 0,!0,8),this.scope.exit(),this.finishNode(t,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(t,e=!1){if(t.id=this.parseIdentifier(),e||this.checkIdentifier(t.id,1024),this.eat(16)){const e=this.startNode();this.tsParseModuleOrNamespaceDeclaration(e,!0),t.body=e}else this.scope.enter(256),this.prodParam.enter(0),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(t,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(t){return this.isContextual(112)?(t.kind="global",t.global=!0,t.id=this.parseIdentifier()):this.match(134)?(t.kind="module",t.id=super.parseStringLiteral(this.state.value)):this.unexpected(),this.match(5)?(this.scope.enter(256),this.prodParam.enter(0),t.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(t,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(t,e,s){t.isExport=s||!1,t.id=e||this.parseIdentifier(),this.checkIdentifier(t.id,4096),this.expect(29);const i=this.tsParseModuleReference();return"type"===t.importKind&&"TSExternalModuleReference"!==i.type&&this.raise(Je.ImportAliasHasImportType,i),t.moduleReference=i,this.semicolon(),this.finishNode(t,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual(119)&&40===this.lookaheadCharCode()}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(0)}tsParseExternalModuleReference(){const t=this.startNode();return this.expectContextual(119),this.expect(10),this.match(134)||this.unexpected(),t.expression=super.parseExprAtom(),this.expect(11),this.sawUnambiguousESM=!0,this.finishNode(t,"TSExternalModuleReference")}tsLookAhead(t){const e=this.state.clone(),s=t();return this.state=e,s}tsTryParseAndCatch(t){const e=this.tryParse(e=>t()||e());if(!e.aborted&&e.node)return e.error&&(this.state=e.failState),e.node}tsTryParse(t){const e=this.state.clone(),s=t();if(void 0!==s&&!1!==s)return s;this.state=e}tsTryParseDeclare(t){if(this.isLineTerminator())return;let e,s=this.state.type;return this.isContextual(100)&&(s=74,e="let"),this.tsInAmbientContext(()=>{switch(s){case 68:return t.declare=!0,super.parseFunctionStatement(t,!1,!1);case 80:return t.declare=!0,this.parseClass(t,!0,!1);case 126:return this.tsParseEnumDeclaration(t,{declare:!0});case 112:return this.tsParseAmbientExternalModuleDeclaration(t);case 75:case 74:return this.match(75)&&this.isLookaheadContextual("enum")?(this.expect(75),this.tsParseEnumDeclaration(t,{const:!0,declare:!0})):(t.declare=!0,this.parseVarStatement(t,e||this.state.value,!0));case 129:{const e=this.tsParseInterfaceDeclaration(t,{declare:!0});if(e)return e}default:if(W(s))return this.tsParseDeclaration(t,this.state.value,!0,null)}})}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}tsParseExpressionStatement(t,e,s){switch(e.name){case"declare":{const e=this.tsTryParseDeclare(t);return e&&(e.declare=!0),e}case"global":if(this.match(5)){this.scope.enter(256),this.prodParam.enter(0);const s=t;return s.kind="global",t.global=!0,s.id=e,s.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(s,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(t,e.name,!1,s)}}tsParseDeclaration(t,e,s,i){switch(e){case"abstract":if(this.tsCheckLineTerminator(s)&&(this.match(80)||W(this.state.type)))return this.tsParseAbstractDeclaration(t,i);break;case"module":if(this.tsCheckLineTerminator(s)){if(this.match(134))return this.tsParseAmbientExternalModuleDeclaration(t);if(W(this.state.type))return t.kind="module",this.tsParseModuleOrNamespaceDeclaration(t)}break;case"namespace":if(this.tsCheckLineTerminator(s)&&W(this.state.type))return t.kind="namespace",this.tsParseModuleOrNamespaceDeclaration(t);break;case"type":if(this.tsCheckLineTerminator(s)&&W(this.state.type))return this.tsParseTypeAliasDeclaration(t);break}}tsCheckLineTerminator(t){return t?!this.hasFollowingLineBreak()&&(this.next(),!0):!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(t){if(!this.match(47))return;const e=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;const s=this.tsTryParseAndCatch(()=>{const e=this.startNodeAt(t);return e.typeParameters=this.tsParseTypeParameters(this.tsParseConstModifier),super.parseFunctionParams(e),e.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(19),e});return this.state.maybeInArrowParameters=e,s?super.parseArrowExpression(s,null,!0):void 0}tsParseTypeArgumentsInExpression(){if(47===this.reScan_lt())return this.tsParseTypeArguments()}tsParseTypeArguments(){const t=this.startNode();return t.params=this.tsInType(()=>this.tsInTopLevelContext(()=>(this.expect(47),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))),0===t.params.length?this.raise(Je.EmptyTypeArguments,t):this.state.inType||this.curContext()!==C.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(t,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){return ht(this.state.type)}isExportDefaultSpecifier(){return!this.tsIsDeclarationStart()&&super.isExportDefaultSpecifier()}parseBindingElement(t,e){const s=e.length?e[0].loc.start:this.state.startLoc,i={};this.tsParseModifiers({allowedModifiers:["public","private","protected","override","readonly"]},i);const r=i.accessibility,a=i.override,n=i.readonly;4&t||!(r||n||a)||this.raise(Je.UnexpectedParameterModifier,s);const o=this.parseMaybeDefault();2&t&&this.parseFunctionParamType(o);const h=this.parseMaybeDefault(o.loc.start,o);if(r||n||a){const t=this.startNodeAt(s);return e.length&&(t.decorators=e),r&&(t.accessibility=r),n&&(t.readonly=n),a&&(t.override=a),"Identifier"!==h.type&&"AssignmentPattern"!==h.type&&this.raise(Je.UnsupportedParameterPropertyKind,t),t.parameter=h,this.finishNode(t,"TSParameterProperty")}return e.length&&(o.decorators=e),h}isSimpleParameter(t){return"TSParameterProperty"===t.type&&super.isSimpleParameter(t.parameter)||super.isSimpleParameter(t)}tsDisallowOptionalPattern(t){for(const e of t.params)"Identifier"!==e.type&&e.optional&&!this.state.isAmbientContext&&this.raise(Je.PatternIsOptional,e)}setArrowFunctionParameters(t,e,s){super.setArrowFunctionParameters(t,e,s),this.tsDisallowOptionalPattern(t)}parseFunctionBodyAndFinish(t,e,s=!1){this.match(14)&&(t.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));const i="FunctionDeclaration"===e?"TSDeclareFunction":"ClassMethod"===e||"ClassPrivateMethod"===e?"TSDeclareMethod":void 0;return i&&!this.match(5)&&this.isLineTerminator()?this.finishNode(t,i):"TSDeclareFunction"===i&&this.state.isAmbientContext&&(this.raise(Je.DeclareFunctionHasImplementation,t),t.declare)?super.parseFunctionBodyAndFinish(t,i,s):(this.tsDisallowOptionalPattern(t),super.parseFunctionBodyAndFinish(t,e,s))}registerFunctionStatementId(t){!t.body&&t.id?this.checkIdentifier(t.id,1024):super.registerFunctionStatementId(t)}tsCheckForInvalidTypeCasts(t){t.forEach(t=>{"TSTypeCastExpression"===(null==t?void 0:t.type)&&this.raise(Je.UnexpectedTypeAnnotation,t.typeAnnotation)})}toReferencedList(t,e){return this.tsCheckForInvalidTypeCasts(t),t}parseArrayLike(t,e,s,i){const r=super.parseArrayLike(t,e,s,i);return"ArrayExpression"===r.type&&this.tsCheckForInvalidTypeCasts(r.elements),r}parseSubscript(t,e,s,i){if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();const s=this.startNodeAt(e);return s.expression=t,this.finishNode(s,"TSNonNullExpression")}let r=!1;if(this.match(18)&&60===this.lookaheadCharCode()){if(s)return i.stop=!0,t;i.optionalChainMember=r=!0,this.next()}if(this.match(47)||this.match(51)){let a;const n=this.tsTryParseAndCatch(()=>{if(!s&&this.atPossibleAsyncArrow(t)){const t=this.tsTryParseGenericAsyncArrowFunction(e);if(t)return t}const n=this.tsParseTypeArgumentsInExpression();if(!n)return;if(r&&!this.match(10))return void(a=this.state.curPosition());if(ut(this.state.type)){const s=super.parseTaggedTemplateExpression(t,e,i);return s.typeParameters=n,s}if(!s&&this.eat(10)){const s=this.startNodeAt(e);return s.callee=t,s.arguments=this.parseCallExpressionArguments(11),this.tsCheckForInvalidTypeCasts(s.arguments),s.typeParameters=n,i.optionalChainMember&&(s.optional=r),this.finishCallExpression(s,i.optionalChainMember)}const o=this.state.type;if(48===o||52===o||10!==o&&Z(o)&&!this.hasPrecedingLineBreak())return;const h=this.startNodeAt(e);return h.expression=t,h.typeParameters=n,this.finishNode(h,"TSInstantiationExpression")});if(a&&this.unexpected(a,10),n)return"TSInstantiationExpression"===n.type&&((this.match(16)||this.match(18)&&40!==this.lookaheadCharCode())&&this.raise(Je.InvalidPropertyAccessAfterInstantiationExpression,this.state.startLoc),this.match(16)||this.match(18)||(n.expression=super.stopParseSubscript(t,i))),n}return super.parseSubscript(t,e,s,i)}parseNewCallee(t){var e;super.parseNewCallee(t);const{callee:s}=t;"TSInstantiationExpression"!==s.type||null!=(e=s.extra)&&e.parenthesized||(t.typeParameters=s.typeParameters,t.callee=s.expression)}parseExprOp(t,e,s){let i;if(pt(58)>s&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(i=this.isContextual(120)))){const r=this.startNodeAt(e);return r.expression=t,r.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?(i&&this.raise(P.UnexpectedKeyword,this.state.startLoc,{keyword:"const"}),this.tsParseTypeReference()):this.tsParseType())),this.finishNode(r,i?"TSSatisfiesExpression":"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(r,e,s)}return super.parseExprOp(t,e,s)}checkReservedWord(t,e,s,i){this.state.isAmbientContext||super.checkReservedWord(t,e,s,i)}checkImportReflection(t){super.checkImportReflection(t),t.module&&"value"!==t.importKind&&this.raise(Je.ImportReflectionHasImportType,t.specifiers[0].loc.start)}checkDuplicateExports(){}isPotentialImportPhase(t){if(super.isPotentialImportPhase(t))return!0;if(this.isContextual(130)){const e=this.lookaheadCharCode();return t?123===e||42===e:61!==e}return!t&&this.isContextual(87)}applyImportPhase(t,e,s,i){super.applyImportPhase(t,e,s,i),e?t.exportKind="type"===s?"type":"value":t.importKind="type"===s||"typeof"===s?s:"value"}parseImport(t){if(this.match(134))return t.importKind="value",super.parseImport(t);let e;if(W(this.state.type)&&61===this.lookaheadCharCode())return t.importKind="value",this.tsParseImportEqualsDeclaration(t);if(this.isContextual(130)){const s=this.parseMaybeImportPhase(t,!1);if(61===this.lookaheadCharCode())return this.tsParseImportEqualsDeclaration(t,s);e=super.parseImportSpecifiersAndAfter(t,s)}else e=super.parseImport(t);return"type"===e.importKind&&e.specifiers.length>1&&"ImportDefaultSpecifier"===e.specifiers[0].type&&this.raise(Je.TypeImportCannotSpecifyDefaultAndNamed,e),e}parseExport(t,e){if(this.match(83)){const e=t;this.next();let s=null;this.isContextual(130)&&this.isPotentialImportPhase(!1)?s=this.parseMaybeImportPhase(e,!1):e.importKind="value";const i=this.tsParseImportEqualsDeclaration(e,s,!0);return i}if(this.eat(29)){const e=t;return e.expression=super.parseExpression(),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(e,"TSExportAssignment")}if(this.eatContextual(93)){const e=t;return this.expectContextual(128),e.id=this.parseIdentifier(),this.semicolon(),this.finishNode(e,"TSNamespaceExportDeclaration")}return super.parseExport(t,e)}isAbstractClass(){return this.isContextual(124)&&80===this.lookahead().type}parseExportDefaultExpression(){if(this.isAbstractClass()){const t=this.startNode();return this.next(),t.abstract=!0,this.parseClass(t,!0,!0)}if(this.match(129)){const t=this.tsParseInterfaceDeclaration(this.startNode());if(t)return t}return super.parseExportDefaultExpression()}parseVarStatement(t,e,s=!1){const{isAmbientContext:i}=this.state,r=super.parseVarStatement(t,e,s||i);if(!i)return r;for(const{id:a,init:n}of r.declarations)n&&("const"!==e||a.typeAnnotation?this.raise(Je.InitializerNotAllowedInAmbientContext,n):Ze(n,this.hasPlugin("estree"))||this.raise(Je.ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference,n));return r}parseStatementContent(t,e){if(this.match(75)&&this.isLookaheadContextual("enum")){const t=this.startNode();return this.expect(75),this.tsParseEnumDeclaration(t,{const:!0})}if(this.isContextual(126))return this.tsParseEnumDeclaration(this.startNode());if(this.isContextual(129)){const t=this.tsParseInterfaceDeclaration(this.startNode());if(t)return t}return super.parseStatementContent(t,e)}parseAccessModifier(){return this.tsParseModifier(["public","protected","private"])}tsHasSomeModifiers(t,e){return e.some(e=>Xe(e)?t.accessibility===e:!!t[e])}tsIsStartOfStaticBlocks(){return this.isContextual(106)&&123===this.lookaheadCharCode()}parseClassMember(t,e,s){const i=["declare","private","public","protected","override","abstract","readonly","static"];this.tsParseModifiers({allowedModifiers:i,disallowedModifiers:["in","out"],stopOnStartOfClassStaticBlock:!0,errorTemplate:Je.InvalidModifierOnTypeParameterPositions},e);const r=()=>{this.tsIsStartOfStaticBlocks()?(this.next(),this.next(),this.tsHasSomeModifiers(e,i)&&this.raise(Je.StaticBlockCannotHaveModifier,this.state.curPosition()),super.parseClassStaticBlock(t,e)):this.parseClassMemberWithIsStatic(t,e,s,!!e.static)};e.declare?this.tsInAmbientContext(r):r()}parseClassMemberWithIsStatic(t,e,s,i){const r=this.tsTryParseIndexSignature(e);if(r)return t.body.push(r),e.abstract&&this.raise(Je.IndexSignatureHasAbstract,e),e.accessibility&&this.raise(Je.IndexSignatureHasAccessibility,e,{modifier:e.accessibility}),e.declare&&this.raise(Je.IndexSignatureHasDeclare,e),void(e.override&&this.raise(Je.IndexSignatureHasOverride,e));!this.state.inAbstractClass&&e.abstract&&this.raise(Je.NonAbstractClassHasAbstractMethod,e),e.override&&(s.hadSuperClass||this.raise(Je.OverrideNotInSubClass,e)),super.parseClassMemberWithIsStatic(t,e,s,i)}parsePostMemberNameModifiers(t){const e=this.eat(17);e&&(t.optional=!0),t.readonly&&this.match(10)&&this.raise(Je.ClassMethodHasReadonly,t),t.declare&&this.match(10)&&this.raise(Je.ClassMethodHasDeclare,t)}parseExpressionStatement(t,e,s){const i="Identifier"===e.type?this.tsParseExpressionStatement(t,e,s):void 0;return i||super.parseExpressionStatement(t,e,s)}shouldParseExportDeclaration(){return!!this.tsIsDeclarationStart()||super.shouldParseExportDeclaration()}parseConditional(t,e,s){if(!this.match(17))return t;if(this.state.maybeInArrowParameters){const e=this.lookaheadCharCode();if(44===e||61===e||58===e||41===e)return this.setOptionalParametersError(s),t}return super.parseConditional(t,e,s)}parseParenItem(t,e){const s=super.parseParenItem(t,e);if(this.eat(17)&&(s.optional=!0,this.resetEndLocation(t)),this.match(14)){const s=this.startNodeAt(e);return s.expression=t,s.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(s,"TSTypeCastExpression")}return t}parseExportDeclaration(t){if(!this.state.isAmbientContext&&this.isContextual(125))return this.tsInAmbientContext(()=>this.parseExportDeclaration(t));const e=this.state.startLoc,s=this.eatContextual(125);if(s&&(this.isContextual(125)||!this.shouldParseExportDeclaration()))throw this.raise(Je.ExpectedAmbientAfterExportDeclare,this.state.startLoc);const i=W(this.state.type),r=i&&this.tsTryParseExportDeclaration()||super.parseExportDeclaration(t);return r?(("TSInterfaceDeclaration"===r.type||"TSTypeAliasDeclaration"===r.type||s)&&(t.exportKind="type"),s&&"TSImportEqualsDeclaration"!==r.type&&(this.resetStartLocation(r,e),r.declare=!0),r):null}parseClassId(t,e,s,i){if((!e||s)&&this.isContextual(113))return;super.parseClassId(t,e,s,t.declare?1024:8331);const r=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);r&&(t.typeParameters=r)}parseClassPropertyAnnotation(t){t.optional||(this.eat(35)?t.definite=!0:this.eat(17)&&(t.optional=!0));const e=this.tsTryParseTypeAnnotation();e&&(t.typeAnnotation=e)}parseClassProperty(t){if(this.parseClassPropertyAnnotation(t),this.state.isAmbientContext&&(!t.readonly||t.typeAnnotation)&&this.match(29)&&this.raise(Je.DeclareClassFieldHasInitializer,this.state.startLoc),t.abstract&&this.match(29)){const{key:e}=t;this.raise(Je.AbstractPropertyHasInitializer,this.state.startLoc,{propertyName:"Identifier"!==e.type||t.computed?`[${this.input.slice(this.offsetToSourcePos(e.start),this.offsetToSourcePos(e.end))}]`:e.name})}return super.parseClassProperty(t)}parseClassPrivateProperty(t){return t.abstract&&this.raise(Je.PrivateElementHasAbstract,t),t.accessibility&&this.raise(Je.PrivateElementHasAccessibility,t,{modifier:t.accessibility}),this.parseClassPropertyAnnotation(t),super.parseClassPrivateProperty(t)}parseClassAccessorProperty(t){return this.parseClassPropertyAnnotation(t),t.optional&&this.raise(Je.AccessorCannotBeOptional,t),super.parseClassAccessorProperty(t)}pushClassMethod(t,e,s,i,r,a){const n=this.tsTryParseTypeParameters(this.tsParseConstModifier);n&&r&&this.raise(Je.ConstructorHasTypeParameters,n);const{declare:o=!1,kind:h}=e;!o||"get"!==h&&"set"!==h||this.raise(Je.DeclareAccessor,e,{kind:h}),n&&(e.typeParameters=n),super.pushClassMethod(t,e,s,i,r,a)}pushClassPrivateMethod(t,e,s,i){const r=this.tsTryParseTypeParameters(this.tsParseConstModifier);r&&(e.typeParameters=r),super.pushClassPrivateMethod(t,e,s,i)}declareClassPrivateMethodInScope(t,e){"TSDeclareMethod"!==t.type&&("MethodDefinition"===t.type&&null==t.value.body||super.declareClassPrivateMethodInScope(t,e))}parseClassSuper(t){super.parseClassSuper(t),t.superClass&&(this.match(47)||this.match(51))&&(t.superTypeParameters=this.tsParseTypeArgumentsInExpression()),this.eatContextual(113)&&(t.implements=this.tsParseHeritageClause("implements"))}parseObjPropValue(t,e,s,i,r,a,n){const o=this.tsTryParseTypeParameters(this.tsParseConstModifier);return o&&(t.typeParameters=o),super.parseObjPropValue(t,e,s,i,r,a,n)}parseFunctionParams(t,e){const s=this.tsTryParseTypeParameters(this.tsParseConstModifier);s&&(t.typeParameters=s),super.parseFunctionParams(t,e)}parseVarId(t,e){super.parseVarId(t,e),"Identifier"===t.id.type&&!this.hasPrecedingLineBreak()&&this.eat(35)&&(t.definite=!0);const s=this.tsTryParseTypeAnnotation();s&&(t.id.typeAnnotation=s,this.resetEndLocation(t.id))}parseAsyncArrowFromCallExpression(t,e){return this.match(14)&&(t.returnType=this.tsParseTypeAnnotation()),super.parseAsyncArrowFromCallExpression(t,e)}parseMaybeAssign(t,e){var s,i,r,a,n;let o,h,c,p;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(o=this.state.clone(),h=this.tryParse(()=>super.parseMaybeAssign(t,e),o),!h.error)return h.node;const{context:s}=this.state,i=s[s.length-1];i!==C.j_oTag&&i!==C.j_expr||s.pop()}if((null==(s=h)||!s.error)&&!this.match(47))return super.parseMaybeAssign(t,e);o&&o!==this.state||(o=this.state.clone());const l=this.tryParse(s=>{var i,r;p=this.tsParseTypeParameters(this.tsParseConstModifier);const a=super.parseMaybeAssign(t,e);return("ArrowFunctionExpression"!==a.type||null!=(i=a.extra)&&i.parenthesized)&&s(),0!==(null==(r=p)?void 0:r.params.length)&&this.resetStartLocationFromNode(a,p),a.typeParameters=p,a},o);if(!l.error&&!l.aborted)return p&&this.reportReservedArrowTypeParam(p),l.node;if(!h&&(Ke(!this.hasPlugin("jsx")),c=this.tryParse(()=>super.parseMaybeAssign(t,e),o),!c.error))return c.node;if(null!=(i=h)&&i.node)return this.state=h.failState,h.node;if(l.node)return this.state=l.failState,p&&this.reportReservedArrowTypeParam(p),l.node;if(null!=(r=c)&&r.node)return this.state=c.failState,c.node;throw(null==(a=h)?void 0:a.error)||l.error||(null==(n=c)?void 0:n.error)}reportReservedArrowTypeParam(t){var e;1!==t.params.length||t.params[0].constraint||null!=(e=t.extra)&&e.trailingComma||!this.getPluginOption("typescript","disallowAmbiguousJSXLike")||this.raise(Je.ReservedArrowTypeParam,t)}parseMaybeUnary(t,e){return!this.hasPlugin("jsx")&&this.match(47)?this.tsParseTypeAssertion():super.parseMaybeUnary(t,e)}parseArrow(t){if(this.match(14)){const e=this.tryParse(t=>{const e=this.tsParseTypeOrTypePredicateAnnotation(14);return!this.canInsertSemicolon()&&this.match(19)||t(),e});if(e.aborted)return;e.thrown||(e.error&&(this.state=e.failState),t.returnType=e.node)}return super.parseArrow(t)}parseFunctionParamType(t){this.eat(17)&&(t.optional=!0);const e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e),this.resetEndLocation(t),t}isAssignable(t,e){switch(t.type){case"TSTypeCastExpression":return this.isAssignable(t.expression,e);case"TSParameterProperty":return!0;default:return super.isAssignable(t,e)}}toAssignable(t,e=!1){switch(t.type){case"ParenthesizedExpression":this.toAssignableParenthesizedExpression(t,e);break;case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":e?this.expressionScope.recordArrowParameterBindingError(Je.UnexpectedTypeCastInParameter,t):this.raise(Je.UnexpectedTypeCastInParameter,t),this.toAssignable(t.expression,e);break;case"AssignmentExpression":e||"TSTypeCastExpression"!==t.left.type||(t.left=this.typeCastToParameter(t.left));default:super.toAssignable(t,e)}}toAssignableParenthesizedExpression(t,e){switch(t.expression.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":this.toAssignable(t.expression,e);break;default:super.toAssignable(t,e)}}checkToRestConversion(t,e){switch(t.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":this.checkToRestConversion(t.expression,!1);break;default:super.checkToRestConversion(t,e)}}isValidLVal(t,e,s){switch(t){case"TSTypeCastExpression":return!0;case"TSParameterProperty":return"parameter";case"TSNonNullExpression":return"expression";case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":return(64!==s||!e)&&["expression",!0];default:return super.isValidLVal(t,e,s)}}parseBindingAtom(){return 78===this.state.type?this.parseIdentifier(!0):super.parseBindingAtom()}parseMaybeDecoratorArguments(t,e){if(this.match(47)||this.match(51)){const s=this.tsParseTypeArgumentsInExpression();if(this.match(10)){const i=super.parseMaybeDecoratorArguments(t,e);return i.typeParameters=s,i}this.unexpected(null,10)}return super.parseMaybeDecoratorArguments(t,e)}checkCommaAfterRest(t){return this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===t?(this.next(),!1):super.checkCommaAfterRest(t)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(35)||this.match(14)||super.isClassProperty()}parseMaybeDefault(t,e){const s=super.parseMaybeDefault(t,e);return"AssignmentPattern"===s.type&&s.typeAnnotation&&s.right.start<s.typeAnnotation.start&&this.raise(Je.TypeAnnotationAfterAssign,s.typeAnnotation),s}getTokenFromCode(t){if(this.state.inType){if(62===t)return void this.finishOp(48,1);if(60===t)return void this.finishOp(47,1)}super.getTokenFromCode(t)}reScan_lt_gt(){const{type:t}=this.state;47===t?(this.state.pos-=1,this.readToken_lt()):48===t&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){const{type:t}=this.state;return 51===t?(this.state.pos-=2,this.finishOp(47,1),47):t}toAssignableListItem(t,e,s){const i=t[e];"TSTypeCastExpression"===i.type&&(t[e]=this.typeCastToParameter(i)),super.toAssignableListItem(t,e,s)}typeCastToParameter(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.loc.end),t.expression}shouldParseArrow(t){return this.match(14)?t.every(t=>this.isAssignable(t,!0)):super.shouldParseArrow(t)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}canHaveLeadingDecorator(){return super.canHaveLeadingDecorator()||this.isAbstractClass()}jsxParseOpeningElementAfterName(t){if(this.match(47)||this.match(51)){const e=this.tsTryParseAndCatch(()=>this.tsParseTypeArgumentsInExpression());e&&(t.typeParameters=e)}return super.jsxParseOpeningElementAfterName(t)}getGetterSetterExpectedParamCount(t){const e=super.getGetterSetterExpectedParamCount(t),s=this.getObjectOrClassMethodParams(t),i=s[0],r=i&&this.isThisParam(i);return r?e+1:e}parseCatchClauseParam(){const t=super.parseCatchClauseParam(),e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e,this.resetEndLocation(t)),t}tsInAmbientContext(t){const{isAmbientContext:e,strict:s}=this.state;this.state.isAmbientContext=!0,this.state.strict=!1;try{return t()}finally{this.state.isAmbientContext=e,this.state.strict=s}}parseClass(t,e,s){const i=this.state.inAbstractClass;this.state.inAbstractClass=!!t.abstract;try{return super.parseClass(t,e,s)}finally{this.state.inAbstractClass=i}}tsParseAbstractDeclaration(t,e){if(this.match(80))return t.abstract=!0,this.maybeTakeDecorators(e,this.parseClass(t,!0,!1));if(this.isContextual(129)){if(!this.hasFollowingLineBreak())return t.abstract=!0,this.raise(Je.NonClassMethodPropertyHasAbstractModifer,t),this.tsParseInterfaceDeclaration(t)}else this.unexpected(null,80)}parseMethod(t,e,s,i,r,a,n){const o=super.parseMethod(t,e,s,i,r,a,n);if(o.abstract||"TSAbstractMethodDefinition"===o.type){const t=this.hasPlugin("estree"),e=t?o.value:o;if(e.body){const{key:t}=o;this.raise(Je.AbstractMethodHasImplementation,o,{methodName:"Identifier"!==t.type||o.computed?`[${this.input.slice(this.offsetToSourcePos(t.start),this.offsetToSourcePos(t.end))}]`:t.name})}}return o}tsParseTypeParameterName(){const t=this.parseIdentifier();return t.name}shouldParseAsAmbientContext(){return!!this.getPluginOption("typescript","dts")}parse(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.parse()}getExpression(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.getExpression()}parseExportSpecifier(t,e,s,i){return!e&&i?(this.parseTypeOnlyImportExportSpecifier(t,!1,s),this.finishNode(t,"ExportSpecifier")):(t.exportKind="value",super.parseExportSpecifier(t,e,s,i))}parseImportSpecifier(t,e,s,i,r){return!e&&i?(this.parseTypeOnlyImportExportSpecifier(t,!0,s),this.finishNode(t,"ImportSpecifier")):(t.importKind="value",super.parseImportSpecifier(t,e,s,i,s?4098:4096))}parseTypeOnlyImportExportSpecifier(t,e,s){const i=e?"imported":"local",r=e?"local":"exported";let a,n=t[i],o=!1,h=!0;const c=n.loc.start;if(this.isContextual(93)){const t=this.parseIdentifier();if(this.isContextual(93)){const s=this.parseIdentifier();G(this.state.type)?(o=!0,n=t,a=e?this.parseIdentifier():this.parseModuleExportName(),h=!1):(a=s,h=!1)}else G(this.state.type)?(h=!1,a=e?this.parseIdentifier():this.parseModuleExportName()):(o=!0,n=t)}else G(this.state.type)&&(o=!0,e?(n=this.parseIdentifier(!0),this.isContextual(93)||this.checkReservedWord(n.name,n.loc.start,!0,!0)):n=this.parseModuleExportName());o&&s&&this.raise(e?Je.TypeModifierIsUsedInTypeImports:Je.TypeModifierIsUsedInTypeExports,c),t[i]=n,t[r]=a;const p=e?"importKind":"exportKind";t[p]=o?"type":"value",h&&this.eatContextual(93)&&(t[r]=e?this.parseIdentifier():this.parseModuleExportName()),t[r]||(t[r]=this.cloneIdentifier(t[i])),e&&this.checkIdentifier(t[r],o?4098:4096)}fillOptionalPropertiesForTSESLint(t){switch(t.type){case"ExpressionStatement":return void(null!=t.directive||(t.directive=void 0));case"RestElement":t.value=void 0;case"Identifier":case"ArrayPattern":case"AssignmentPattern":case"ObjectPattern":return null!=t.decorators||(t.decorators=[]),null!=t.optional||(t.optional=!1),void(null!=t.typeAnnotation||(t.typeAnnotation=void 0));case"TSParameterProperty":return null!=t.accessibility||(t.accessibility=void 0),null!=t.decorators||(t.decorators=[]),null!=t.override||(t.override=!1),null!=t.readonly||(t.readonly=!1),void(null!=t.static||(t.static=!1));case"TSEmptyBodyFunctionExpression":t.body=null;case"TSDeclareFunction":case"FunctionDeclaration":case"FunctionExpression":case"ClassMethod":case"ClassPrivateMethod":return null!=t.declare||(t.declare=!1),null!=t.returnType||(t.returnType=void 0),void(null!=t.typeParameters||(t.typeParameters=void 0));case"Property":return void(null!=t.optional||(t.optional=!1));case"TSMethodSignature":case"TSPropertySignature":null!=t.optional||(t.optional=!1);case"TSIndexSignature":return null!=t.accessibility||(t.accessibility=void 0),null!=t.readonly||(t.readonly=!1),void(null!=t.static||(t.static=!1));case"TSAbstractPropertyDefinition":case"PropertyDefinition":case"TSAbstractAccessorProperty":case"AccessorProperty":null!=t.declare||(t.declare=!1),null!=t.definite||(t.definite=!1),null!=t.readonly||(t.readonly=!1),null!=t.typeAnnotation||(t.typeAnnotation=void 0);case"TSAbstractMethodDefinition":case"MethodDefinition":return null!=t.accessibility||(t.accessibility=void 0),null!=t.decorators||(t.decorators=[]),null!=t.override||(t.override=!1),void(null!=t.optional||(t.optional=!1));case"ClassExpression":null!=t.id||(t.id=null);case"ClassDeclaration":return null!=t.abstract||(t.abstract=!1),null!=t.declare||(t.declare=!1),null!=t.decorators||(t.decorators=[]),null!=t.implements||(t.implements=[]),null!=t.superTypeArguments||(t.superTypeArguments=void 0),void(null!=t.typeParameters||(t.typeParameters=void 0));case"TSTypeAliasDeclaration":case"VariableDeclaration":return void(null!=t.declare||(t.declare=!1));case"VariableDeclarator":return void(null!=t.definite||(t.definite=!1));case"TSEnumDeclaration":return null!=t.const||(t.const=!1),void(null!=t.declare||(t.declare=!1));case"TSEnumMember":return void(null!=t.computed||(t.computed=!1));case"TSImportType":return null!=t.qualifier||(t.qualifier=null),void(null!=t.options||(t.options=null));case"TSInterfaceDeclaration":return null!=t.declare||(t.declare=!1),void(null!=t.extends||(t.extends=[]));case"TSModuleDeclaration":return null!=t.declare||(t.declare=!1),void(null!=t.global||(t.global="global"===t.kind));case"TSTypeParameter":return null!=t.const||(t.const=!1),null!=t.in||(t.in=!1),void(null!=t.out||(t.out=!1))}}};function Qe(t){if("MemberExpression"!==t.type)return!1;const{computed:e,property:s}=t;return(!e||"StringLiteral"===s.type||!("TemplateLiteral"!==s.type||s.expressions.length>0))&&ss(t.object)}function Ze(t,e){var s;const{type:i}=t;if(null!=(s=t.extra)&&s.parenthesized)return!1;if(e){if("Literal"===i){const{value:e}=t;if("string"===typeof e||"boolean"===typeof e)return!0}}else if("StringLiteral"===i||"BooleanLiteral"===i)return!0;return!(!ts(t,e)&&!es(t,e))||("TemplateLiteral"===i&&0===t.expressions.length||!!Qe(t))}function ts(t,e){return e?"Literal"===t.type&&("number"===typeof t.value||"bigint"in t):"NumericLiteral"===t.type||"BigIntLiteral"===t.type}function es(t,e){if("UnaryExpression"===t.type){const{operator:s,argument:i}=t;if("-"===s&&ts(i,e))return!0}return!1}function ss(t){return"Identifier"===t.type||"MemberExpression"===t.type&&!t.computed&&ss(t.object)}const is=g`placeholders`({ClassNameIsRequired:"A class name is required.",UnexpectedSpace:"Unexpected space in placeholder."});var rs=t=>class extends t{parsePlaceholder(t){if(this.match(133)){const e=this.startNode();return this.next(),this.assertNoSpace(),e.name=super.parseIdentifier(!0),this.assertNoSpace(),this.expect(133),this.finishPlaceholder(e,t)}}finishPlaceholder(t,e){let s=t;return s.expectedNode&&s.type||(s=this.finishNode(s,"Placeholder")),s.expectedNode=e,s}getTokenFromCode(t){37===t&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(133,2):super.getTokenFromCode(t)}parseExprAtom(t){return this.parsePlaceholder("Expression")||super.parseExprAtom(t)}parseIdentifier(t){return this.parsePlaceholder("Identifier")||super.parseIdentifier(t)}checkReservedWord(t,e,s,i){void 0!==t&&super.checkReservedWord(t,e,s,i)}cloneIdentifier(t){const e=super.cloneIdentifier(t);return"Placeholder"===e.type&&(e.expectedNode=t.expectedNode),e}cloneStringLiteral(t){return"Placeholder"===t.type?this.cloneIdentifier(t):super.cloneStringLiteral(t)}parseBindingAtom(){return this.parsePlaceholder("Pattern")||super.parseBindingAtom()}isValidLVal(t,e,s){return"Placeholder"===t||super.isValidLVal(t,e,s)}toAssignable(t,e){t&&"Placeholder"===t.type&&"Expression"===t.expectedNode?t.expectedNode="Pattern":super.toAssignable(t,e)}chStartsBindingIdentifier(t,e){if(super.chStartsBindingIdentifier(t,e))return!0;const s=this.lookahead();return 133===s.type}verifyBreakContinue(t,e){t.label&&"Placeholder"===t.label.type||super.verifyBreakContinue(t,e)}parseExpressionStatement(t,e){var s;if("Placeholder"!==e.type||null!=(s=e.extra)&&s.parenthesized)return super.parseExpressionStatement(t,e);if(this.match(14)){const s=t;return s.label=this.finishPlaceholder(e,"Identifier"),this.next(),s.body=super.parseStatementOrSloppyAnnexBFunctionDeclaration(),this.finishNode(s,"LabeledStatement")}this.semicolon();const i=t;return i.name=e.name,this.finishPlaceholder(i,"Statement")}parseBlock(t,e,s){return this.parsePlaceholder("BlockStatement")||super.parseBlock(t,e,s)}parseFunctionId(t){return this.parsePlaceholder("Identifier")||super.parseFunctionId(t)}parseClass(t,e,s){const i=e?"ClassDeclaration":"ClassExpression";this.next();const r=this.state.strict,a=this.parsePlaceholder("Identifier");if(a){if(!(this.match(81)||this.match(133)||this.match(5))){if(s||!e)return t.id=null,t.body=this.finishPlaceholder(a,"ClassBody"),this.finishNode(t,i);throw this.raise(is.ClassNameIsRequired,this.state.startLoc)}t.id=a}else this.parseClassId(t,e,s);return super.parseClassSuper(t),t.body=this.parsePlaceholder("ClassBody")||super.parseClassBody(!!t.superClass,r),this.finishNode(t,i)}parseExport(t,e){const s=this.parsePlaceholder("Identifier");if(!s)return super.parseExport(t,e);const i=t;if(!this.isContextual(98)&&!this.match(12))return i.specifiers=[],i.source=null,i.declaration=this.finishPlaceholder(s,"Declaration"),this.finishNode(i,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");const r=this.startNode();return r.exported=s,i.specifiers=[this.finishNode(r,"ExportDefaultSpecifier")],super.parseExport(i,e)}isExportDefaultSpecifier(){if(this.match(65)){const t=this.nextTokenStart();if(this.isUnparsedContextual(t,"from")&&this.input.startsWith(ct(133),this.nextTokenStartSince(t+4)))return!0}return super.isExportDefaultSpecifier()}maybeParseExportDefaultSpecifier(t,e){var s;return!(null==(s=t.specifiers)||!s.length)||super.maybeParseExportDefaultSpecifier(t,e)}checkExport(t){const{specifiers:e}=t;null!=e&&e.length&&(t.specifiers=e.filter(t=>"Placeholder"===t.exported.type)),super.checkExport(t),t.specifiers=e}parseImport(t){const e=this.parsePlaceholder("Identifier");if(!e)return super.parseImport(t);if(t.specifiers=[],!this.isContextual(98)&&!this.match(12))return t.source=this.finishPlaceholder(e,"StringLiteral"),this.semicolon(),this.finishNode(t,"ImportDeclaration");const s=this.startNodeAtNode(e);if(s.local=e,t.specifiers.push(this.finishNode(s,"ImportDefaultSpecifier")),this.eat(12)){const e=this.maybeParseStarImportSpecifier(t);e||this.parseNamedImportSpecifiers(t)}return this.expectContextual(98),t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}parseImportSource(){return this.parsePlaceholder("StringLiteral")||super.parseImportSource()}assertNoSpace(){this.state.start>this.offsetToSourcePos(this.state.lastTokEndLoc.index)&&this.raise(is.UnexpectedSpace,this.state.lastTokEndLoc)}},as=t=>class extends t{parseV8Intrinsic(){if(this.match(54)){const t=this.state.startLoc,e=this.startNode();if(this.next(),W(this.state.type)){const t=this.parseIdentifierName(),s=this.createIdentifier(e,t);if(this.castNodeTo(s,"V8IntrinsicIdentifier"),this.match(10))return s}this.unexpected(t)}}parseExprAtom(t){return this.parseV8Intrinsic()||super.parseExprAtom(t)}};const ns=["minimal","fsharp","hack","smart"],os=["^^","@@","^","%","#"];function hs(t){if(t.has("decorators")){if(t.has("decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");const e=t.get("decorators").decoratorsBeforeExport;if(null!=e&&"boolean"!==typeof e)throw new Error("'decoratorsBeforeExport' must be a boolean, if specified.");const s=t.get("decorators").allowCallParenthesized;if(null!=s&&"boolean"!==typeof s)throw new Error("'allowCallParenthesized' must be a boolean.")}if(t.has("flow")&&t.has("typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(t.has("placeholders")&&t.has("v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(t.has("pipelineOperator")){var e;const i=t.get("pipelineOperator").proposal;if(!ns.includes(i)){const t=ns.map(t=>`"${t}"`).join(", ");throw new Error(`"pipelineOperator" requires "proposal" option whose value must be one of: ${t}.`)}if("hack"===i){if(t.has("placeholders"))throw new Error("Cannot combine placeholders plugin and Hack-style pipes.");if(t.has("v8intrinsic"))throw new Error("Cannot combine v8intrinsic plugin and Hack-style pipes.");const e=t.get("pipelineOperator").topicToken;if(!os.includes(e)){const t=os.map(t=>`"${t}"`).join(", ");throw new Error(`"pipelineOperator" in "proposal": "hack" mode also requires a "topicToken" option whose value must be one of: ${t}.`)}var s;if("#"===e&&"hash"===(null==(s=t.get("recordAndTuple"))?void 0:s.syntaxType))throw new Error(`Plugin conflict between \`["pipelineOperator", { proposal: "hack", topicToken: "#" }]\` and \`${JSON.stringify(["recordAndTuple",t.get("recordAndTuple")])}\`.`)}else if("smart"===i&&"hash"===(null==(e=t.get("recordAndTuple"))?void 0:e.syntaxType))throw new Error(`Plugin conflict between \`["pipelineOperator", { proposal: "smart" }]\` and \`${JSON.stringify(["recordAndTuple",t.get("recordAndTuple")])}\`.`)}if(t.has("moduleAttributes")){if(t.has("deprecatedImportAssert")||t.has("importAssertions"))throw new Error("Cannot combine importAssertions, deprecatedImportAssert and moduleAttributes plugins.");const e=t.get("moduleAttributes").version;if("may-2020"!==e)throw new Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(t.has("importAssertions")&&t.has("deprecatedImportAssert"))throw new Error("Cannot combine importAssertions and deprecatedImportAssert plugins.");if(!t.has("deprecatedImportAssert")&&t.has("importAttributes")&&t.get("importAttributes").deprecatedAssertSyntax&&t.set("deprecatedImportAssert",{}),t.has("recordAndTuple")){const e=t.get("recordAndTuple").syntaxType;if(null!=e){const t=["hash","bar"];if(!t.includes(e))throw new Error("The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: "+t.map(t=>`'${t}'`).join(", "))}}if(t.has("asyncDoExpressions")&&!t.has("doExpressions")){const t=new Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw t.missingPlugins="doExpressions",t}if(t.has("optionalChainingAssign")&&"2023-07"!==t.get("optionalChainingAssign").version)throw new Error("The 'optionalChainingAssign' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is '2023-07'.")}const cs={estree:w,jsx:re,flow:Kt,typescript:Ye,v8intrinsic:as,placeholders:rs},ps=Object.keys(cs);class ls extends Ve{checkProto(t,e,s,i){if("SpreadElement"===t.type||this.isObjectMethod(t)||t.computed||t.shorthand)return s;const r=t.key,a="Identifier"===r.type?r.name:r.value;return"__proto__"===a?e?(this.raise(P.RecordNoProto,r),!0):(s&&(i?null===i.doubleProtoLoc&&(i.doubleProtoLoc=r.loc.start):this.raise(P.DuplicateProto,r)),!0):s}shouldExitDescending(t,e){return"ArrowFunctionExpression"===t.type&&this.offsetToSourcePos(t.start)===e}getExpression(){this.enterInitialScopes(),this.nextToken();const t=this.parseExpression();return this.match(140)||this.unexpected(),this.finalizeRemainingComments(),t.comments=this.comments,t.errors=this.state.errors,256&this.optionFlags&&(t.tokens=this.tokens),t}parseExpression(t,e){return t?this.disallowInAnd(()=>this.parseExpressionBase(e)):this.allowInAnd(()=>this.parseExpressionBase(e))}parseExpressionBase(t){const e=this.state.startLoc,s=this.parseMaybeAssign(t);if(this.match(12)){const i=this.startNodeAt(e);i.expressions=[s];while(this.eat(12))i.expressions.push(this.parseMaybeAssign(t));return this.toReferencedList(i.expressions),this.finishNode(i,"SequenceExpression")}return s}parseMaybeAssignDisallowIn(t,e){return this.disallowInAnd(()=>this.parseMaybeAssign(t,e))}parseMaybeAssignAllowIn(t,e){return this.allowInAnd(()=>this.parseMaybeAssign(t,e))}setOptionalParametersError(t){t.optionalParametersLoc=this.state.startLoc}parseMaybeAssign(t,e){const s=this.state.startLoc,i=this.isContextual(108);if(i&&this.prodParam.hasYield){this.next();let t=this.parseYield(s);return e&&(t=e.call(this,t,s)),t}let r;t?r=!1:(t=new Ue,r=!0);const{type:a}=this.state;(10===a||W(a))&&(this.state.potentialArrowAt=this.state.start);let n=this.parseMaybeConditional(t);if(e&&(n=e.call(this,n,s)),tt(this.state.type)){const e=this.startNodeAt(s),i=this.state.value;if(e.operator=i,this.match(29)){this.toAssignable(n,!0),e.left=n;const i=s.index;null!=t.doubleProtoLoc&&t.doubleProtoLoc.index>=i&&(t.doubleProtoLoc=null),null!=t.shorthandAssignLoc&&t.shorthandAssignLoc.index>=i&&(t.shorthandAssignLoc=null),null!=t.privateKeyLoc&&t.privateKeyLoc.index>=i&&(this.checkDestructuringPrivate(t),t.privateKeyLoc=null)}else e.left=n;return this.next(),e.right=this.parseMaybeAssign(),this.checkLVal(n,this.finishNode(e,"AssignmentExpression")),e}if(r&&this.checkExpressionErrors(t,!0),i){const{type:t}=this.state,e=this.hasPlugin("v8intrinsic")?Z(t):Z(t)&&!this.match(54);if(e&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(P.YieldNotInGeneratorFunction,s),this.parseYield(s)}return n}parseMaybeConditional(t){const e=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseExprOps(t);return this.shouldExitDescending(i,s)?i:this.parseConditional(i,e,t)}parseConditional(t,e,s){if(this.eat(17)){const s=this.startNodeAt(e);return s.test=t,s.consequent=this.parseMaybeAssignAllowIn(),this.expect(14),s.alternate=this.parseMaybeAssign(),this.finishNode(s,"ConditionalExpression")}return t}parseMaybeUnaryOrPrivate(t){return this.match(139)?this.parsePrivateName():this.parseMaybeUnary(t)}parseExprOps(t){const e=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseMaybeUnaryOrPrivate(t);return this.shouldExitDescending(i,s)?i:this.parseExprOp(i,e,-1)}parseExprOp(t,e,s){if(this.isPrivateName(t)){const e=this.getPrivateNameSV(t);(s>=pt(58)||!this.prodParam.hasIn||!this.match(58))&&this.raise(P.PrivateInExpectedIn,t,{identifierName:e}),this.classScope.usePrivateName(e,t.loc.start)}const i=this.state.type;if(rt(i)&&(this.prodParam.hasIn||!this.match(58))){let r=pt(i);if(r>s){if(39===i){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return t;this.checkPipelineAtInfixOperator(t,e)}const a=this.startNodeAt(e);a.left=t,a.operator=this.state.value;const n=41===i||42===i,o=40===i;if(o&&(r=pt(42)),this.next(),39===i&&this.hasPlugin(["pipelineOperator",{proposal:"minimal"}])&&96===this.state.type&&this.prodParam.hasAwait)throw this.raise(P.UnexpectedAwaitAfterPipelineBody,this.state.startLoc);a.right=this.parseExprOpRightExpr(i,r);const h=this.finishNode(a,n||o?"LogicalExpression":"BinaryExpression"),c=this.state.type;if(o&&(41===c||42===c)||n&&40===c)throw this.raise(P.MixingCoalesceWithLogical,this.state.startLoc);return this.parseExprOp(h,e,s)}}return t}parseExprOpRightExpr(t,e){const s=this.state.startLoc;switch(t){case 39:switch(this.getPluginOption("pipelineOperator","proposal")){case"hack":return this.withTopicBindingContext(()=>this.parseHackPipeBody());case"fsharp":return this.withSoloAwaitPermittingContext(()=>this.parseFSharpPipelineBody(e))}if("smart"===this.getPluginOption("pipelineOperator","proposal"))return this.withTopicBindingContext(()=>{if(this.prodParam.hasYield&&this.isContextual(108))throw this.raise(P.PipeBodyIsTighter,this.state.startLoc);return this.parseSmartPipelineBodyInStyle(this.parseExprOpBaseRightExpr(t,e),s)});default:return this.parseExprOpBaseRightExpr(t,e)}}parseExprOpBaseRightExpr(t,e){const s=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),s,lt(t)?e-1:e)}parseHackPipeBody(){var t;const{startLoc:e}=this.state,s=this.parseMaybeAssign(),i=d.has(s.type);return!i||null!=(t=s.extra)&&t.parenthesized||this.raise(P.PipeUnparenthesizedBody,e,{type:s.type}),this.topicReferenceWasUsedInCurrentContext()||this.raise(P.PipeTopicUnused,e),s}checkExponentialAfterUnary(t){this.match(57)&&this.raise(P.UnexpectedTokenUnaryExponentiation,t.argument)}parseMaybeUnary(t,e){const s=this.state.startLoc,i=this.isContextual(96);if(i&&this.recordAwaitIfAllowed()){this.next();const t=this.parseAwait(s);return e||this.checkExponentialAfterUnary(t),t}const r=this.match(34),a=this.startNode();if(nt(this.state.type)){a.operator=this.state.value,a.prefix=!0,this.match(72)&&this.expectPlugin("throwExpressions");const s=this.match(89);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(t,!0),this.state.strict&&s){const t=a.argument;"Identifier"===t.type?this.raise(P.StrictDelete,a):this.hasPropertyAsPrivateName(t)&&this.raise(P.DeletePrivateField,a)}if(!r)return e||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}const n=this.parseUpdate(a,r,t);if(i){const{type:t}=this.state,e=this.hasPlugin("v8intrinsic")?Z(t):Z(t)&&!this.match(54);if(e&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(P.AwaitNotInAsyncContext,s),this.parseAwait(s)}return n}parseUpdate(t,e,s){if(e){const e=t;return this.checkLVal(e.argument,this.finishNode(e,"UpdateExpression")),t}const i=this.state.startLoc;let r=this.parseExprSubscripts(s);if(this.checkExpressionErrors(s,!1))return r;while(at(this.state.type)&&!this.canInsertSemicolon()){const t=this.startNodeAt(i);t.operator=this.state.value,t.prefix=!1,t.argument=r,this.next(),this.checkLVal(r,r=this.finishNode(t,"UpdateExpression"))}return r}parseExprSubscripts(t){const e=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseExprAtom(t);return this.shouldExitDescending(i,s)?i:this.parseSubscripts(i,e)}parseSubscripts(t,e,s){const i={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(t),stop:!1};do{t=this.parseSubscript(t,e,s,i),i.maybeAsyncArrow=!1}while(!i.stop);return t}parseSubscript(t,e,s,i){const{type:r}=this.state;if(!s&&15===r)return this.parseBind(t,e,s,i);if(ut(r))return this.parseTaggedTemplateExpression(t,e,i);let a=!1;if(18===r){if(s&&(this.raise(P.OptionalChainingNoNew,this.state.startLoc),40===this.lookaheadCharCode()))return this.stopParseSubscript(t,i);i.optionalChainMember=a=!0,this.next()}if(!s&&this.match(10))return this.parseCoverCallAndAsyncArrowHead(t,e,i,a);{const s=this.eat(0);return s||a||this.eat(16)?this.parseMember(t,e,i,s,a):this.stopParseSubscript(t,i)}}stopParseSubscript(t,e){return e.stop=!0,t}parseMember(t,e,s,i,r){const a=this.startNodeAt(e);return a.object=t,a.computed=i,i?(a.property=this.parseExpression(),this.expect(3)):this.match(139)?("Super"===t.type&&this.raise(P.SuperPrivateField,e),this.classScope.usePrivateName(this.state.value,this.state.startLoc),a.property=this.parsePrivateName()):a.property=this.parseIdentifier(!0),s.optionalChainMember?(a.optional=r,this.finishNode(a,"OptionalMemberExpression")):this.finishNode(a,"MemberExpression")}parseBind(t,e,s,i){const r=this.startNodeAt(e);return r.object=t,this.next(),r.callee=this.parseNoCallExpr(),i.stop=!0,this.parseSubscripts(this.finishNode(r,"BindExpression"),e,s)}parseCoverCallAndAsyncArrowHead(t,e,s,i){const r=this.state.maybeInArrowParameters;let a=null;this.state.maybeInArrowParameters=!0,this.next();const n=this.startNodeAt(e);n.callee=t;const{maybeAsyncArrow:o,optionalChainMember:h}=s;o&&(this.expressionScope.enter(Be()),a=new Ue),h&&(n.optional=i),n.arguments=i?this.parseCallExpressionArguments(11):this.parseCallExpressionArguments(11,"Super"!==t.type,n,a);let c=this.finishCallExpression(n,h);return o&&this.shouldParseAsyncArrow()&&!i?(s.stop=!0,this.checkDestructuringPrivate(a),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),c=this.parseAsyncArrowFromCallExpression(this.startNodeAt(e),c)):(o&&(this.checkExpressionErrors(a,!0),this.expressionScope.exit()),this.toReferencedArguments(c)),this.state.maybeInArrowParameters=r,c}toReferencedArguments(t,e){this.toReferencedListDeep(t.arguments,e)}parseTaggedTemplateExpression(t,e,s){const i=this.startNodeAt(e);return i.tag=t,i.quasi=this.parseTemplate(!0),s.optionalChainMember&&this.raise(P.OptionalChainingNoTemplate,e),this.finishNode(i,"TaggedTemplateExpression")}atPossibleAsyncArrow(t){return"Identifier"===t.type&&"async"===t.name&&this.state.lastTokEndLoc.index===t.end&&!this.canInsertSemicolon()&&t.end-t.start===5&&this.offsetToSourcePos(t.start)===this.state.potentialArrowAt}finishCallExpression(t,e){if("Import"===t.callee.type)if(0===t.arguments.length||t.arguments.length>2)this.raise(P.ImportCallArity,t);else for(const s of t.arguments)"SpreadElement"===s.type&&this.raise(P.ImportCallSpreadArgument,s);return this.finishNode(t,e?"OptionalCallExpression":"CallExpression")}parseCallExpressionArguments(t,e,s,i){const r=[];let a=!0;const n=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;while(!this.eat(t)){if(a)a=!1;else if(this.expect(12),this.match(t)){s&&this.addTrailingCommaExtraToNode(s),this.next();break}r.push(this.parseExprListItem(!1,i,e))}return this.state.inFSharpPipelineDirectBody=n,r}shouldParseAsyncArrow(){return this.match(19)&&!this.canInsertSemicolon()}parseAsyncArrowFromCallExpression(t,e){var s;return this.resetPreviousNodeTrailingComments(e),this.expect(19),this.parseArrowExpression(t,e.arguments,!0,null==(s=e.extra)?void 0:s.trailingCommaLoc),e.innerComments&&ue(t,e.innerComments),e.callee.trailingComments&&ue(t,e.callee.trailingComments),t}parseNoCallExpr(){const t=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),t,!0)}parseExprAtom(t){let e,s=null;const{type:i}=this.state;switch(i){case 79:return this.parseSuper();case 83:return e=this.startNode(),this.next(),this.match(16)?this.parseImportMetaProperty(e):this.match(10)?512&this.optionFlags?this.parseImportCall(e):this.finishNode(e,"Import"):(this.raise(P.UnsupportedImport,this.state.lastTokStartLoc),this.finishNode(e,"Import"));case 78:return e=this.startNode(),this.next(),this.finishNode(e,"ThisExpression");case 90:return this.parseDo(this.startNode(),!1);case 56:case 31:return this.readRegexp(),this.parseRegExpLiteral(this.state.value);case 135:return this.parseNumericLiteral(this.state.value);case 136:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(!0);case 86:return this.parseBooleanLiteral(!1);case 10:{const t=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(t)}case 0:return this.parseArrayLike(3,!0,!1,t);case 5:return this.parseObjectLike(8,!1,!1,t);case 68:return this.parseFunctionOrFunctionSent();case 26:s=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(s,this.startNode()),!1);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(!1);case 15:{e=this.startNode(),this.next(),e.object=null;const t=e.callee=this.parseNoCallExpr();if("MemberExpression"===t.type)return this.finishNode(e,"BindExpression");throw this.raise(P.UnsupportedBind,t)}case 139:return this.raise(P.PrivateInExpectedIn,this.state.startLoc,{identifierName:this.state.value}),this.parsePrivateName();case 33:return this.parseTopicReferenceThenEqualsSign(54,"%");case 32:return this.parseTopicReferenceThenEqualsSign(44,"^");case 37:case 38:return this.parseTopicReference("hack");case 44:case 54:case 27:{const t=this.getPluginOption("pipelineOperator","proposal");if(t)return this.parseTopicReference(t);this.unexpected();break}case 47:{const t=this.input.codePointAt(this.nextTokenStart());Tt(t)||62===t?this.expectOnePlugin(["jsx","flow","typescript"]):this.unexpected();break}default:if(137===i)return this.parseDecimalLiteral(this.state.value);if(2===i||1===i)return this.parseArrayLike(2===this.state.type?4:3,!1,!0);if(6===i||7===i)return this.parseObjectLike(6===this.state.type?9:8,!1,!0);if(W(i)){if(this.isContextual(127)&&123===this.lookaheadInLineCharCode())return this.parseModuleExpression();const t=this.state.potentialArrowAt===this.state.start,e=this.state.containsEsc,s=this.parseIdentifier();if(!e&&"async"===s.name&&!this.canInsertSemicolon()){const{type:t}=this.state;if(68===t)return this.resetPreviousNodeTrailingComments(s),this.next(),this.parseAsyncFunctionExpression(this.startNodeAtNode(s));if(W(t))return 61===this.lookaheadCharCode()?this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(s)):s;if(90===t)return this.resetPreviousNodeTrailingComments(s),this.parseDo(this.startNodeAtNode(s),!0)}return t&&this.match(19)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(this.startNodeAtNode(s),[s],!1)):s}this.unexpected()}}parseTopicReferenceThenEqualsSign(t,e){const s=this.getPluginOption("pipelineOperator","proposal");if(s)return this.state.type=t,this.state.value=e,this.state.pos--,this.state.end--,this.state.endLoc=n(this.state.endLoc,-1),this.parseTopicReference(s);this.unexpected()}parseTopicReference(t){const e=this.startNode(),s=this.state.startLoc,i=this.state.type;return this.next(),this.finishTopicReference(e,s,t,i)}finishTopicReference(t,e,s,i){if(this.testTopicReferenceConfiguration(s,e,i))return"hack"===s?(this.topicReferenceIsAllowedInCurrentContext()||this.raise(P.PipeTopicUnbound,e),this.registerTopicReference(),this.finishNode(t,"TopicReference")):(this.topicReferenceIsAllowedInCurrentContext()||this.raise(P.PrimaryTopicNotAllowed,e),this.registerTopicReference(),this.finishNode(t,"PipelinePrimaryTopicReference"));throw this.raise(P.PipeTopicUnconfiguredToken,e,{token:ct(i)})}testTopicReferenceConfiguration(t,e,s){switch(t){case"hack":return this.hasPlugin(["pipelineOperator",{topicToken:ct(s)}]);case"smart":return 27===s;default:throw this.raise(P.PipeTopicRequiresHackPipes,e)}}parseAsyncArrowUnaryFunction(t){this.prodParam.enter(he(!0,this.prodParam.hasYield));const e=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(P.LineTerminatorBeforeArrow,this.state.curPosition()),this.expect(19),this.parseArrowExpression(t,e,!0)}parseDo(t,e){this.expectPlugin("doExpressions"),e&&this.expectPlugin("asyncDoExpressions"),t.async=e,this.next();const s=this.state.labels;return this.state.labels=[],e?(this.prodParam.enter(2),t.body=this.parseBlock(),this.prodParam.exit()):t.body=this.parseBlock(),this.state.labels=s,this.finishNode(t,"DoExpression")}parseSuper(){const t=this.startNode();return this.next(),!this.match(10)||this.scope.allowDirectSuper||16&this.optionFlags?this.scope.allowSuper||16&this.optionFlags||this.raise(P.UnexpectedSuper,t):this.raise(P.SuperNotAllowed,t),this.match(10)||this.match(0)||this.match(16)||this.raise(P.UnsupportedSuper,t),this.finishNode(t,"Super")}parsePrivateName(){const t=this.startNode(),e=this.startNodeAt(n(this.state.startLoc,1)),s=this.state.value;return this.next(),t.id=this.createIdentifier(e,s),this.finishNode(t,"PrivateName")}parseFunctionOrFunctionSent(){const t=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(16)){const e=this.createIdentifier(this.startNodeAtNode(t),"function");return this.next(),this.match(103)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected(),this.parseMetaProperty(t,e,"sent")}return this.parseFunction(t)}parseMetaProperty(t,e,s){t.meta=e;const i=this.state.containsEsc;return t.property=this.parseIdentifier(!0),(t.property.name!==s||i)&&this.raise(P.UnsupportedMetaProperty,t.property,{target:e.name,onlyValidPropertyName:s}),this.finishNode(t,"MetaProperty")}parseImportMetaProperty(t){const e=this.createIdentifier(this.startNodeAtNode(t),"import");if(this.next(),this.isContextual(101))this.inModule||this.raise(P.ImportMetaOutsideModule,e),this.sawUnambiguousESM=!0;else if(this.isContextual(105)||this.isContextual(97)){const e=this.isContextual(105);if(this.expectPlugin(e?"sourcePhaseImports":"deferredImportEvaluation"),!(512&this.optionFlags))throw this.raise(P.DynamicImportPhaseRequiresImportExpressions,this.state.startLoc,{phase:this.state.value});return this.next(),t.phase=e?"source":"defer",this.parseImportCall(t)}return this.parseMetaProperty(t,e,"meta")}parseLiteralAtNode(t,e,s){return this.addExtra(s,"rawValue",t),this.addExtra(s,"raw",this.input.slice(this.offsetToSourcePos(s.start),this.state.end)),s.value=t,this.next(),this.finishNode(s,e)}parseLiteral(t,e){const s=this.startNode();return this.parseLiteralAtNode(t,e,s)}parseStringLiteral(t){return this.parseLiteral(t,"StringLiteral")}parseNumericLiteral(t){return this.parseLiteral(t,"NumericLiteral")}parseBigIntLiteral(t){return this.parseLiteral(t,"BigIntLiteral")}parseDecimalLiteral(t){return this.parseLiteral(t,"DecimalLiteral")}parseRegExpLiteral(t){const e=this.startNode();return this.addExtra(e,"raw",this.input.slice(this.offsetToSourcePos(e.start),this.state.end)),e.pattern=t.pattern,e.flags=t.flags,this.next(),this.finishNode(e,"RegExpLiteral")}parseBooleanLiteral(t){const e=this.startNode();return e.value=t,this.next(),this.finishNode(e,"BooleanLiteral")}parseNullLiteral(){const t=this.startNode();return this.next(),this.finishNode(t,"NullLiteral")}parseParenAndDistinguishExpression(t){const e=this.state.startLoc;let s;this.next(),this.expressionScope.enter(Fe());const i=this.state.maybeInArrowParameters,r=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;const a=this.state.startLoc,n=[],o=new Ue;let h,c,p=!0;while(!this.match(11)){if(p)p=!1;else if(this.expect(12,null===o.optionalParametersLoc?null:o.optionalParametersLoc),this.match(11)){c=this.state.startLoc;break}if(this.match(21)){const t=this.state.startLoc;if(h=this.state.startLoc,n.push(this.parseParenItem(this.parseRestBinding(),t)),!this.checkCommaAfterRest(41))break}else n.push(this.parseMaybeAssignAllowIn(o,this.parseParenItem))}const l=this.state.lastTokEndLoc;this.expect(11),this.state.maybeInArrowParameters=i,this.state.inFSharpPipelineDirectBody=r;let u=this.startNodeAt(e);return t&&this.shouldParseArrow(n)&&(u=this.parseArrow(u))?(this.checkDestructuringPrivate(o),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(u,n,!1),u):(this.expressionScope.exit(),n.length||this.unexpected(this.state.lastTokStartLoc),c&&this.unexpected(c),h&&this.unexpected(h),this.checkExpressionErrors(o,!0),this.toReferencedListDeep(n,!0),n.length>1?(s=this.startNodeAt(a),s.expressions=n,this.finishNode(s,"SequenceExpression"),this.resetEndLocation(s,l)):s=n[0],this.wrapParenthesis(e,s))}wrapParenthesis(t,e){if(!(1024&this.optionFlags))return this.addExtra(e,"parenthesized",!0),this.addExtra(e,"parenStart",t.index),this.takeSurroundingComments(e,t.index,this.state.lastTokEndLoc.index),e;const s=this.startNodeAt(t);return s.expression=e,this.finishNode(s,"ParenthesizedExpression")}shouldParseArrow(t){return!this.canInsertSemicolon()}parseArrow(t){if(this.eat(19))return t}parseParenItem(t,e){return t}parseNewOrNewTarget(){const t=this.startNode();if(this.next(),this.match(16)){const e=this.createIdentifier(this.startNodeAtNode(t),"new");this.next();const s=this.parseMetaProperty(t,e,"target");return this.scope.inNonArrowFunction||this.scope.inClass||4&this.optionFlags||this.raise(P.UnexpectedNewTarget,s),s}return this.parseNew(t)}parseNew(t){if(this.parseNewCallee(t),this.eat(10)){const e=this.parseExprList(11);this.toReferencedList(e),t.arguments=e}else t.arguments=[];return this.finishNode(t,"NewExpression")}parseNewCallee(t){const e=this.match(83),s=this.parseNoCallExpr();t.callee=s,!e||"Import"!==s.type&&"ImportExpression"!==s.type||this.raise(P.ImportCallNotNewExpression,s)}parseTemplateElement(t){const{start:e,startLoc:s,end:i,value:r}=this.state,a=e+1,o=this.startNodeAt(n(s,1));null===r&&(t||this.raise(P.InvalidEscapeSequenceTemplate,n(this.state.firstInvalidTemplateEscapePos,1)));const h=this.match(24),c=h?-1:-2,p=i+c;o.value={raw:this.input.slice(a,p).replace(/\r\n?/g,"\n"),cooked:null===r?null:r.slice(1,c)},o.tail=h,this.next();const l=this.finishNode(o,"TemplateElement");return this.resetEndLocation(l,n(this.state.lastTokEndLoc,c)),l}parseTemplate(t){const e=this.startNode();let s=this.parseTemplateElement(t);const i=[s],r=[];while(!s.tail)r.push(this.parseTemplateSubstitution()),this.readTemplateContinuation(),i.push(s=this.parseTemplateElement(t));return e.expressions=r,e.quasis=i,this.finishNode(e,"TemplateLiteral")}parseTemplateSubstitution(){return this.parseExpression()}parseObjectLike(t,e,s,i){s&&this.expectPlugin("recordAndTuple");const r=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;let a=!1,n=!0;const o=this.startNode();o.properties=[],this.next();while(!this.match(t)){if(n)n=!1;else if(this.expect(12),this.match(t)){this.addTrailingCommaExtraToNode(o);break}let r;e?r=this.parseBindingProperty():(r=this.parsePropertyDefinition(i),a=this.checkProto(r,s,a,i)),s&&!this.isObjectProperty(r)&&"SpreadElement"!==r.type&&this.raise(P.InvalidRecordProperty,r),r.shorthand&&this.addExtra(r,"shorthand",!0),o.properties.push(r)}this.next(),this.state.inFSharpPipelineDirectBody=r;let h="ObjectExpression";return e?h="ObjectPattern":s&&(h="RecordExpression"),this.finishNode(o,h)}addTrailingCommaExtraToNode(t){this.addExtra(t,"trailingComma",this.state.lastTokStartLoc.index),this.addExtra(t,"trailingCommaLoc",this.state.lastTokStartLoc,!1)}maybeAsyncOrAccessorProp(t){return!t.computed&&"Identifier"===t.key.type&&(this.isLiteralPropertyName()||this.match(0)||this.match(55))}parsePropertyDefinition(t){let e=[];if(this.match(26)){this.hasPlugin("decorators")&&this.raise(P.UnsupportedPropertyDecorator,this.state.startLoc);while(this.match(26))e.push(this.parseDecorator())}const s=this.startNode();let i,r=!1,a=!1;if(this.match(21))return e.length&&this.unexpected(),this.parseSpread();e.length&&(s.decorators=e,e=[]),s.method=!1,t&&(i=this.state.startLoc);let n=this.eat(55);this.parsePropertyNamePrefixOperator(s);const o=this.state.containsEsc;if(this.parsePropertyName(s,t),!n&&!o&&this.maybeAsyncOrAccessorProp(s)){const{key:t}=s,e=t.name;"async"!==e||this.hasPrecedingLineBreak()||(r=!0,this.resetPreviousNodeTrailingComments(t),n=this.eat(55),this.parsePropertyName(s)),"get"!==e&&"set"!==e||(a=!0,this.resetPreviousNodeTrailingComments(t),s.kind=e,this.match(55)&&(n=!0,this.raise(P.AccessorIsGenerator,this.state.curPosition(),{kind:e}),this.next()),this.parsePropertyName(s))}return this.parseObjPropValue(s,i,n,r,!1,a,t)}getGetterSetterExpectedParamCount(t){return"get"===t.kind?0:1}getObjectOrClassMethodParams(t){return t.params}checkGetterSetterParams(t){var e;const s=this.getGetterSetterExpectedParamCount(t),i=this.getObjectOrClassMethodParams(t);i.length!==s&&this.raise("get"===t.kind?P.BadGetterArity:P.BadSetterArity,t),"set"===t.kind&&"RestElement"===(null==(e=i[i.length-1])?void 0:e.type)&&this.raise(P.BadSetterRestParameter,t)}parseObjectMethod(t,e,s,i,r){if(r){const s=this.parseMethod(t,e,!1,!1,!1,"ObjectMethod");return this.checkGetterSetterParams(s),s}if(s||e||this.match(10))return i&&this.unexpected(),t.kind="method",t.method=!0,this.parseMethod(t,e,s,!1,!1,"ObjectMethod")}parseObjectProperty(t,e,s,i){if(t.shorthand=!1,this.eat(14))return t.value=s?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowIn(i),this.finishObjectProperty(t);if(!t.computed&&"Identifier"===t.key.type){if(this.checkReservedWord(t.key.name,t.key.loc.start,!0,!1),s)t.value=this.parseMaybeDefault(e,this.cloneIdentifier(t.key));else if(this.match(29)){const s=this.state.startLoc;null!=i?null===i.shorthandAssignLoc&&(i.shorthandAssignLoc=s):this.raise(P.InvalidCoverInitializedName,s),t.value=this.parseMaybeDefault(e,this.cloneIdentifier(t.key))}else t.value=this.cloneIdentifier(t.key);return t.shorthand=!0,this.finishObjectProperty(t)}}finishObjectProperty(t){return this.finishNode(t,"ObjectProperty")}parseObjPropValue(t,e,s,i,r,a,n){const o=this.parseObjectMethod(t,s,i,r,a)||this.parseObjectProperty(t,e,r,n);return o||this.unexpected(),o}parsePropertyName(t,e){if(this.eat(0))t.computed=!0,t.key=this.parseMaybeAssignAllowIn(),this.expect(3);else{const{type:s,value:i}=this.state;let r;if(G(s))r=this.parseIdentifier(!0);else switch(s){case 135:r=this.parseNumericLiteral(i);break;case 134:r=this.parseStringLiteral(i);break;case 136:r=this.parseBigIntLiteral(i);break;case 139:{const t=this.state.startLoc;null!=e?null===e.privateKeyLoc&&(e.privateKeyLoc=t):this.raise(P.UnexpectedPrivateField,t),r=this.parsePrivateName();break}default:if(137===s){r=this.parseDecimalLiteral(i);break}this.unexpected()}t.key=r,139!==s&&(t.computed=!1)}}initFunction(t,e){t.id=null,t.generator=!1,t.async=e}parseMethod(t,e,s,i,r,a,n=!1){this.initFunction(t,s),t.generator=e,this.scope.enter(18|(n?64:0)|(r?32:0)),this.prodParam.enter(he(s,t.generator)),this.parseFunctionParams(t,i);const o=this.parseFunctionBodyAndFinish(t,a,!0);return this.prodParam.exit(),this.scope.exit(),o}parseArrayLike(t,e,s,i){s&&this.expectPlugin("recordAndTuple");const r=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;const a=this.startNode();return this.next(),a.elements=this.parseExprList(t,!s,i,a),this.state.inFSharpPipelineDirectBody=r,this.finishNode(a,s?"TupleExpression":"ArrayExpression")}parseArrowExpression(t,e,s,i){this.scope.enter(6);let r=he(s,!1);!this.match(5)&&this.prodParam.hasIn&&(r|=8),this.prodParam.enter(r),this.initFunction(t,s);const a=this.state.maybeInArrowParameters;return e&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(t,e,i)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(t,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=a,this.finishNode(t,"ArrowFunctionExpression")}setArrowFunctionParameters(t,e,s){this.toAssignableList(e,s,!1),t.params=e}parseFunctionBodyAndFinish(t,e,s=!1){return this.parseFunctionBody(t,!1,s),this.finishNode(t,e)}parseFunctionBody(t,e,s=!1){const i=e&&!this.match(5);if(this.expressionScope.enter(Re()),i)t.body=this.parseMaybeAssign(),this.checkParams(t,!1,e,!1);else{const i=this.state.strict,r=this.state.labels;this.state.labels=[],this.prodParam.enter(4|this.prodParam.currentFlags()),t.body=this.parseBlock(!0,!1,r=>{const a=!this.isSimpleParamList(t.params);r&&a&&this.raise(P.IllegalLanguageModeDirective,"method"!==t.kind&&"constructor"!==t.kind||!t.key?t:t.key.loc.end);const n=!i&&this.state.strict;this.checkParams(t,!this.state.strict&&!e&&!s&&!a,e,n),this.state.strict&&t.id&&this.checkIdentifier(t.id,65,n)}),this.prodParam.exit(),this.state.labels=r}this.expressionScope.exit()}isSimpleParameter(t){return"Identifier"===t.type}isSimpleParamList(t){for(let e=0,s=t.length;e<s;e++)if(!this.isSimpleParameter(t[e]))return!1;return!0}checkParams(t,e,s,i=!0){const r=!e&&new Set,a={type:"FormalParameters"};for(const n of t.params)this.checkLVal(n,a,5,r,i)}parseExprList(t,e,s,i){const r=[];let a=!0;while(!this.eat(t)){if(a)a=!1;else if(this.expect(12),this.match(t)){i&&this.addTrailingCommaExtraToNode(i),this.next();break}r.push(this.parseExprListItem(e,s))}return r}parseExprListItem(t,e,s){let i;if(this.match(12))t||this.raise(P.UnexpectedToken,this.state.curPosition(),{unexpected:","}),i=null;else if(this.match(21)){const t=this.state.startLoc;i=this.parseParenItem(this.parseSpread(e),t)}else if(this.match(17)){this.expectPlugin("partialApplication"),s||this.raise(P.UnexpectedArgumentPlaceholder,this.state.startLoc);const t=this.startNode();this.next(),i=this.finishNode(t,"ArgumentPlaceholder")}else i=this.parseMaybeAssignAllowIn(e,this.parseParenItem);return i}parseIdentifier(t){const e=this.startNode(),s=this.parseIdentifierName(t);return this.createIdentifier(e,s)}createIdentifier(t,e){return t.name=e,t.loc.identifierName=e,this.finishNode(t,"Identifier")}parseIdentifierName(t){let e;const{startLoc:s,type:i}=this.state;G(i)?e=this.state.value:this.unexpected();const r=X(i);return t?r&&this.replaceToken(132):this.checkReservedWord(e,s,r,!1),this.next(),e}checkReservedWord(t,e,s,i){if(t.length>10)return;if(!Dt(t))return;if(s&&Lt(t))return void this.raise(P.UnexpectedKeyword,e,{keyword:t});const r=this.state.strict?i?kt:vt:Ct;if(r(t,this.inModule))this.raise(P.UnexpectedReservedWord,e,{reservedWord:t});else if("yield"===t){if(this.prodParam.hasYield)return void this.raise(P.YieldBindingIdentifier,e)}else if("await"===t){if(this.prodParam.hasAwait)return void this.raise(P.AwaitBindingIdentifier,e);if(this.scope.inStaticBlock)return void this.raise(P.AwaitBindingIdentifierInStaticBlock,e);this.expressionScope.recordAsyncArrowParametersError(e)}else if("arguments"===t&&this.scope.inClassAndNotInNonArrowFunction)return void this.raise(P.ArgumentsInClass,e)}recordAwaitIfAllowed(){const t=this.prodParam.hasAwait||1&this.optionFlags&&!this.scope.inFunction;return t&&!this.scope.inFunction&&(this.state.hasTopLevelAwait=!0),t}parseAwait(t){const e=this.startNodeAt(t);return this.expressionScope.recordParameterInitializerError(P.AwaitExpressionFormalParameter,e),this.eat(55)&&this.raise(P.ObsoleteAwaitStar,e),this.scope.inFunction||1&this.optionFlags||(this.isAmbiguousPrefixOrIdentifier()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(e.argument=this.parseMaybeUnary(null,!0)),this.finishNode(e,"AwaitExpression")}isAmbiguousPrefixOrIdentifier(){if(this.hasPrecedingLineBreak())return!0;const{type:t}=this.state;return 53===t||10===t||0===t||ut(t)||102===t&&!this.state.containsEsc||138===t||56===t||this.hasPlugin("v8intrinsic")&&54===t}parseYield(t){const e=this.startNodeAt(t);this.expressionScope.recordParameterInitializerError(P.YieldInParameter,e);let s=!1,i=null;if(!this.hasPrecedingLineBreak())switch(s=this.eat(55),this.state.type){case 13:case 140:case 8:case 11:case 3:case 9:case 14:case 12:if(!s)break;default:i=this.parseMaybeAssign()}return e.delegate=s,e.argument=i,this.finishNode(e,"YieldExpression")}parseImportCall(t){if(this.next(),t.source=this.parseMaybeAssignAllowIn(),t.options=null,this.eat(12)&&!this.match(11)&&(t.options=this.parseMaybeAssignAllowIn(),this.eat(12)&&!this.match(11))){do{this.parseMaybeAssignAllowIn()}while(this.eat(12)&&!this.match(11));this.raise(P.ImportCallArity,t)}return this.expect(11),this.finishNode(t,"ImportExpression")}checkPipelineAtInfixOperator(t,e){this.hasPlugin(["pipelineOperator",{proposal:"smart"}])&&"SequenceExpression"===t.type&&this.raise(P.PipelineHeadSequenceExpression,e)}parseSmartPipelineBodyInStyle(t,e){if(this.isSimpleReference(t)){const s=this.startNodeAt(e);return s.callee=t,this.finishNode(s,"PipelineBareFunction")}{const s=this.startNodeAt(e);return this.checkSmartPipeTopicBodyEarlyErrors(e),s.expression=t,this.finishNode(s,"PipelineTopicExpression")}}isSimpleReference(t){switch(t.type){case"MemberExpression":return!t.computed&&this.isSimpleReference(t.object);case"Identifier":return!0;default:return!1}}checkSmartPipeTopicBodyEarlyErrors(t){if(this.match(19))throw this.raise(P.PipelineBodyNoArrow,this.state.startLoc);this.topicReferenceWasUsedInCurrentContext()||this.raise(P.PipelineTopicUnused,t)}withTopicBindingContext(t){const e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}withSmartMixTopicForbiddingContext(t){if(!this.hasPlugin(["pipelineOperator",{proposal:"smart"}]))return t();{const e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}}withSoloAwaitPermittingContext(t){const e=this.state.soloAwait;this.state.soloAwait=!0;try{return t()}finally{this.state.soloAwait=e}}allowInAnd(t){const e=this.prodParam.currentFlags(),s=8&~e;if(s){this.prodParam.enter(8|e);try{return t()}finally{this.prodParam.exit()}}return t()}disallowInAnd(t){const e=this.prodParam.currentFlags(),s=8&e;if(s){this.prodParam.enter(-9&e);try{return t()}finally{this.prodParam.exit()}}return t()}registerTopicReference(){this.state.topicContext.maxTopicIndex=0}topicReferenceIsAllowedInCurrentContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1}topicReferenceWasUsedInCurrentContext(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}parseFSharpPipelineBody(t){const e=this.state.startLoc;this.state.potentialArrowAt=this.state.start;const s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;const i=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),e,t);return this.state.inFSharpPipelineDirectBody=s,i}parseModuleExpression(){this.expectPlugin("moduleBlocks");const t=this.startNode();this.next(),this.match(5)||this.unexpected(null,5);const e=this.startNodeAt(this.state.endLoc);this.next();const s=this.initializeScopes(!0);this.enterInitialScopes();try{t.body=this.parseProgram(e,8,"module")}finally{s()}return this.finishNode(t,"ModuleExpression")}parsePropertyNamePrefixOperator(t){}}const us={kind:1},ds={kind:2},ms=/[\uD800-\uDFFF]/u,fs=/in(?:stanceof)?/y;function ys(t,e,s){for(let i=0;i<t.length;i++){const r=t[i],{type:a}=r;if("number"===typeof a){if(139===a){const{loc:e,start:s,value:a,end:o}=r,h=s+1,c=n(e.start,1);t.splice(i,1,new Ce({type:dt(27),value:"#",start:s,end:h,startLoc:e.start,endLoc:c}),new Ce({type:dt(132),value:a,start:h,end:o,startLoc:c,endLoc:e.end})),i++;continue}if(ut(a)){const{loc:o,start:h,value:c,end:p}=r,l=h+1,u=n(o.start,1);let d,m,f,y,x;d=96===e.charCodeAt(h-s)?new Ce({type:dt(22),value:"`",start:h,end:l,startLoc:o.start,endLoc:u}):new Ce({type:dt(8),value:"}",start:h,end:l,startLoc:o.start,endLoc:u}),24===a?(f=p-1,y=n(o.end,-1),m=null===c?null:c.slice(1,-1),x=new Ce({type:dt(22),value:"`",start:f,end:p,startLoc:y,endLoc:o.end})):(f=p-2,y=n(o.end,-2),m=null===c?null:c.slice(1,-2),x=new Ce({type:dt(23),value:"${",start:f,end:p,startLoc:y,endLoc:o.end})),t.splice(i,1,d,new Ce({type:dt(20),value:m,start:l,end:f,startLoc:u,endLoc:y}),x),i+=2;continue}r.type=dt(a)}}return t}class xs extends ls{parseTopLevel(t,e){return t.program=this.parseProgram(e),t.comments=this.comments,256&this.optionFlags&&(t.tokens=ys(this.tokens,this.input,this.startIndex)),this.finishNode(t,"File")}parseProgram(t,e=140,s=this.options.sourceType){if(t.sourceType=s,t.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(t,!0,!0,e),this.inModule){if(!(64&this.optionFlags)&&this.scope.undefinedExports.size>0)for(const[t,e]of Array.from(this.scope.undefinedExports))this.raise(P.ModuleExportUndefined,e,{localName:t});this.addExtra(t,"topLevelAwait",this.state.hasTopLevelAwait)}let i;return i=140===e?this.finishNode(t,"Program"):this.finishNodeAt(t,"Program",n(this.state.startLoc,-1)),i}stmtToDirective(t){const e=this.castNodeTo(t,"Directive"),s=this.castNodeTo(t.expression,"DirectiveLiteral"),i=s.value,r=this.input.slice(this.offsetToSourcePos(s.start),this.offsetToSourcePos(s.end)),a=s.value=r.slice(1,-1);return this.addExtra(s,"raw",r),this.addExtra(s,"rawValue",a),this.addExtra(s,"expressionValue",i),e.value=s,delete t.expression,e}parseInterpreterDirective(){if(!this.match(28))return null;const t=this.startNode();return t.value=this.state.value,this.next(),this.finishNode(t,"InterpreterDirective")}isLet(){return!!this.isContextual(100)&&this.hasFollowingBindingAtom()}chStartsBindingIdentifier(t,e){if(Tt(t)){if(fs.lastIndex=e,fs.test(this.input)){const t=this.codePointAtPos(fs.lastIndex);if(!At(t)&&92!==t)return!1}return!0}return 92===t}chStartsBindingPattern(t){return 91===t||123===t}hasFollowingBindingAtom(){const t=this.nextTokenStart(),e=this.codePointAtPos(t);return this.chStartsBindingPattern(e)||this.chStartsBindingIdentifier(e,t)}hasInLineFollowingBindingIdentifierOrBrace(){const t=this.nextTokenInLineStart(),e=this.codePointAtPos(t);return 123===e||this.chStartsBindingIdentifier(e,t)}allowsForUsing(){const{type:t,containsEsc:e,end:s}=this.lookahead();if(102===t&&!e){const t=this.lookaheadCharCodeSince(s);if(61!==t&&58!==t&&59!==t)return!1}return!(!W(t)||this.hasFollowingLineBreak())&&(this.expectPlugin("explicitResourceManagement"),!0)}startsAwaitUsing(){let t=this.nextTokenInLineStart();if(this.isUnparsedContextual(t,"using")){t=this.nextTokenInLineStartSince(t+5);const e=this.codePointAtPos(t);if(this.chStartsBindingIdentifier(e,t))return this.expectPlugin("explicitResourceManagement"),!0}return!1}parseModuleItem(){return this.parseStatementLike(15)}parseStatementListItem(){return this.parseStatementLike(6|(!this.options.annexB||this.state.strict?0:8))}parseStatementOrSloppyAnnexBFunctionDeclaration(t=!1){let e=0;return this.options.annexB&&!this.state.strict&&(e|=4,t&&(e|=8)),this.parseStatementLike(e)}parseStatement(){return this.parseStatementLike(0)}parseStatementLike(t){let e=null;return this.match(26)&&(e=this.parseDecorators(!0)),this.parseStatementContent(t,e)}parseStatementContent(t,e){const s=this.state.type,i=this.startNode(),r=!!(2&t),a=!!(4&t),n=1&t;switch(s){case 60:return this.parseBreakContinueStatement(i,!0);case 63:return this.parseBreakContinueStatement(i,!1);case 64:return this.parseDebuggerStatement(i);case 90:return this.parseDoWhileStatement(i);case 91:return this.parseForStatement(i);case 68:if(46===this.lookaheadCharCode())break;return a||this.raise(this.state.strict?P.StrictFunction:this.options.annexB?P.SloppyFunctionAnnexB:P.SloppyFunction,this.state.startLoc),this.parseFunctionStatement(i,!1,!r&&a);case 80:return r||this.unexpected(),this.parseClass(this.maybeTakeDecorators(e,i),!0);case 69:return this.parseIfStatement(i);case 70:return this.parseReturnStatement(i);case 71:return this.parseSwitchStatement(i);case 72:return this.parseThrowStatement(i);case 73:return this.parseTryStatement(i);case 96:if(!this.state.containsEsc&&this.startsAwaitUsing())return this.recordAwaitIfAllowed()?r||this.raise(P.UnexpectedLexicalDeclaration,i):this.raise(P.AwaitUsingNotInAsyncContext,i),this.next(),this.parseVarStatement(i,"await using");break;case 107:if(this.state.containsEsc||!this.hasInLineFollowingBindingIdentifierOrBrace())break;return this.expectPlugin("explicitResourceManagement"),!this.scope.inModule&&this.scope.inTopLevel?this.raise(P.UnexpectedUsingDeclaration,this.state.startLoc):r||this.raise(P.UnexpectedLexicalDeclaration,this.state.startLoc),this.parseVarStatement(i,"using");case 100:{if(this.state.containsEsc)break;const t=this.nextTokenStart(),e=this.codePointAtPos(t);if(91!==e){if(!r&&this.hasFollowingLineBreak())break;if(!this.chStartsBindingIdentifier(e,t)&&123!==e)break}}case 75:r||this.raise(P.UnexpectedLexicalDeclaration,this.state.startLoc);case 74:{const t=this.state.value;return this.parseVarStatement(i,t)}case 92:return this.parseWhileStatement(i);case 76:return this.parseWithStatement(i);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(i);case 83:{const t=this.lookaheadCharCode();if(40===t||46===t)break}case 82:{let t;return 8&this.optionFlags||n||this.raise(P.UnexpectedImportExport,this.state.startLoc),this.next(),t=83===s?this.parseImport(i):this.parseExport(i,e),this.assertModuleNodeAllowed(t),t}default:if(this.isAsyncFunction())return r||this.raise(P.AsyncFunctionInSingleStatementContext,this.state.startLoc),this.next(),this.parseFunctionStatement(i,!0,!r&&a)}const o=this.state.value,h=this.parseExpression();return W(s)&&"Identifier"===h.type&&this.eat(14)?this.parseLabeledStatement(i,o,h,t):this.parseExpressionStatement(i,h,e)}assertModuleNodeAllowed(t){8&this.optionFlags||this.inModule||this.raise(P.ImportOutsideModule,t)}decoratorsEnabledBeforeExport(){return!!this.hasPlugin("decorators-legacy")||this.hasPlugin("decorators")&&!1!==this.getPluginOption("decorators","decoratorsBeforeExport")}maybeTakeDecorators(t,e,s){var i;t&&(null!=(i=e.decorators)&&i.length?("boolean"!==typeof this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(P.DecoratorsBeforeAfterExport,e.decorators[0]),e.decorators.unshift(...t)):e.decorators=t,this.resetStartLocationFromNode(e,t[0]),s&&this.resetStartLocationFromNode(s,e));return e}canHaveLeadingDecorator(){return this.match(80)}parseDecorators(t){const e=[];do{e.push(this.parseDecorator())}while(this.match(26));if(this.match(82))t||this.unexpected(),this.decoratorsEnabledBeforeExport()||this.raise(P.DecoratorExportClass,this.state.startLoc);else if(!this.canHaveLeadingDecorator())throw this.raise(P.UnexpectedLeadingDecorator,this.state.startLoc);return e}parseDecorator(){this.expectOnePlugin(["decorators","decorators-legacy"]);const t=this.startNode();if(this.next(),this.hasPlugin("decorators")){const e=this.state.startLoc;let s;if(this.match(10)){const e=this.state.startLoc;this.next(),s=this.parseExpression(),this.expect(11),s=this.wrapParenthesis(e,s);const i=this.state.startLoc;t.expression=this.parseMaybeDecoratorArguments(s,e),!1===this.getPluginOption("decorators","allowCallParenthesized")&&t.expression!==s&&this.raise(P.DecoratorArgumentsOutsideParentheses,i)}else{s=this.parseIdentifier(!1);while(this.eat(16)){const t=this.startNodeAt(e);t.object=s,this.match(139)?(this.classScope.usePrivateName(this.state.value,this.state.startLoc),t.property=this.parsePrivateName()):t.property=this.parseIdentifier(!0),t.computed=!1,s=this.finishNode(t,"MemberExpression")}t.expression=this.parseMaybeDecoratorArguments(s,e)}}else t.expression=this.parseExprSubscripts();return this.finishNode(t,"Decorator")}parseMaybeDecoratorArguments(t,e){if(this.eat(10)){const s=this.startNodeAt(e);return s.callee=t,s.arguments=this.parseCallExpressionArguments(11),this.toReferencedList(s.arguments),this.finishNode(s,"CallExpression")}return t}parseBreakContinueStatement(t,e){return this.next(),this.isLineTerminator()?t.label=null:(t.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(t,e),this.finishNode(t,e?"BreakStatement":"ContinueStatement")}verifyBreakContinue(t,e){let s;for(s=0;s<this.state.labels.length;++s){const i=this.state.labels[s];if(null==t.label||i.name===t.label.name){if(null!=i.kind&&(e||1===i.kind))break;if(t.label&&e)break}}if(s===this.state.labels.length){const s=e?"BreakStatement":"ContinueStatement";this.raise(P.IllegalBreakContinue,t,{type:s})}}parseDebuggerStatement(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")}parseHeaderExpression(){this.expect(10);const t=this.parseExpression();return this.expect(11),t}parseDoWhileStatement(t){return this.next(),this.state.labels.push(us),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.expect(92),t.test=this.parseHeaderExpression(),this.eat(13),this.finishNode(t,"DoWhileStatement")}parseForStatement(t){this.next(),this.state.labels.push(us);let e=null;if(this.isContextual(96)&&this.recordAwaitIfAllowed()&&(e=this.state.startLoc,this.next()),this.scope.enter(0),this.expect(10),this.match(13))return null!==e&&this.unexpected(e),this.parseFor(t,null);const s=this.isContextual(100);{const i=this.isContextual(96)&&this.startsAwaitUsing(),r=i||this.isContextual(107)&&this.allowsForUsing(),a=s&&this.hasFollowingBindingAtom()||r;if(this.match(74)||this.match(75)||a){const s=this.startNode();let a;i?(a="await using",this.recordAwaitIfAllowed()||this.raise(P.AwaitUsingNotInAsyncContext,this.state.startLoc),this.next()):a=this.state.value,this.next(),this.parseVar(s,!0,a);const n=this.finishNode(s,"VariableDeclaration"),o=this.match(58);return o&&r&&this.raise(P.ForInUsing,n),(o||this.isContextual(102))&&1===n.declarations.length?this.parseForIn(t,n,e):(null!==e&&this.unexpected(e),this.parseFor(t,n))}}const i=this.isContextual(95),r=new Ue,a=this.parseExpression(!0,r),n=this.isContextual(102);if(n&&(s&&this.raise(P.ForOfLet,a),null===e&&i&&"Identifier"===a.type&&this.raise(P.ForOfAsync,a)),n||this.match(58)){this.checkDestructuringPrivate(r),this.toAssignable(a,!0);const s=n?"ForOfStatement":"ForInStatement";return this.checkLVal(a,{type:s}),this.parseForIn(t,a,e)}return this.checkExpressionErrors(r,!0),null!==e&&this.unexpected(e),this.parseFor(t,a)}parseFunctionStatement(t,e,s){return this.next(),this.parseFunction(t,1|(s?2:0)|(e?8:0))}parseIfStatement(t){return this.next(),t.test=this.parseHeaderExpression(),t.consequent=this.parseStatementOrSloppyAnnexBFunctionDeclaration(),t.alternate=this.eat(66)?this.parseStatementOrSloppyAnnexBFunctionDeclaration():null,this.finishNode(t,"IfStatement")}parseReturnStatement(t){return this.prodParam.hasReturn||2&this.optionFlags||this.raise(P.IllegalReturn,this.state.startLoc),this.next(),this.isLineTerminator()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")}parseSwitchStatement(t){this.next(),t.discriminant=this.parseHeaderExpression();const e=t.cases=[];let s,i;for(this.expect(5),this.state.labels.push(ds),this.scope.enter(0);!this.match(8);)if(this.match(61)||this.match(65)){const t=this.match(61);s&&this.finishNode(s,"SwitchCase"),e.push(s=this.startNode()),s.consequent=[],this.next(),t?s.test=this.parseExpression():(i&&this.raise(P.MultipleDefaultsInSwitch,this.state.lastTokStartLoc),i=!0,s.test=null),this.expect(14)}else s?s.consequent.push(this.parseStatementListItem()):this.unexpected();return this.scope.exit(),s&&this.finishNode(s,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(t,"SwitchStatement")}parseThrowStatement(t){return this.next(),this.hasPrecedingLineBreak()&&this.raise(P.NewlineAfterThrow,this.state.lastTokEndLoc),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")}parseCatchClauseParam(){const t=this.parseBindingAtom();return this.scope.enter(this.options.annexB&&"Identifier"===t.type?8:0),this.checkLVal(t,{type:"CatchClause"},9),t}parseTryStatement(t){if(this.next(),t.block=this.parseBlock(),t.handler=null,this.match(62)){const e=this.startNode();this.next(),this.match(10)?(this.expect(10),e.param=this.parseCatchClauseParam(),this.expect(11)):(e.param=null,this.scope.enter(0)),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseBlock(!1,!1)),this.scope.exit(),t.handler=this.finishNode(e,"CatchClause")}return t.finalizer=this.eat(67)?this.parseBlock():null,t.handler||t.finalizer||this.raise(P.NoCatchOrFinally,t),this.finishNode(t,"TryStatement")}parseVarStatement(t,e,s=!1){return this.next(),this.parseVar(t,!1,e,s),this.semicolon(),this.finishNode(t,"VariableDeclaration")}parseWhileStatement(t){return this.next(),t.test=this.parseHeaderExpression(),this.state.labels.push(us),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.finishNode(t,"WhileStatement")}parseWithStatement(t){return this.state.strict&&this.raise(P.StrictWith,this.state.startLoc),this.next(),t.object=this.parseHeaderExpression(),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.finishNode(t,"WithStatement")}parseEmptyStatement(t){return this.next(),this.finishNode(t,"EmptyStatement")}parseLabeledStatement(t,e,s,i){for(const a of this.state.labels)a.name===e&&this.raise(P.LabelRedeclaration,s,{labelName:e});const r=st(this.state.type)?1:this.match(71)?2:null;for(let a=this.state.labels.length-1;a>=0;a--){const e=this.state.labels[a];if(e.statementStart!==t.start)break;e.statementStart=this.sourceToOffsetPos(this.state.start),e.kind=r}return this.state.labels.push({name:e,kind:r,statementStart:this.sourceToOffsetPos(this.state.start)}),t.body=8&i?this.parseStatementOrSloppyAnnexBFunctionDeclaration(!0):this.parseStatement(),this.state.labels.pop(),t.label=s,this.finishNode(t,"LabeledStatement")}parseExpressionStatement(t,e,s){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")}parseBlock(t=!1,e=!0,s){const i=this.startNode();return t&&this.state.strictErrors.clear(),this.expect(5),e&&this.scope.enter(0),this.parseBlockBody(i,t,!1,8,s),e&&this.scope.exit(),this.finishNode(i,"BlockStatement")}isValidDirective(t){return"ExpressionStatement"===t.type&&"StringLiteral"===t.expression.type&&!t.expression.extra.parenthesized}parseBlockBody(t,e,s,i,r){const a=t.body=[],n=t.directives=[];this.parseBlockOrModuleBlockBody(a,e?n:void 0,s,i,r)}parseBlockOrModuleBlockBody(t,e,s,i,r){const a=this.state.strict;let n=!1,o=!1;while(!this.match(i)){const i=s?this.parseModuleItem():this.parseStatementListItem();if(e&&!o){if(this.isValidDirective(i)){const t=this.stmtToDirective(i);e.push(t),n||"use strict"!==t.value.value||(n=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}t.push(i)}null==r||r.call(this,n),a||this.setStrict(!1),this.next()}parseFor(t,e){return t.init=e,this.semicolon(!1),t.test=this.match(13)?null:this.parseExpression(),this.semicolon(!1),t.update=this.match(11)?null:this.parseExpression(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,"ForStatement")}parseForIn(t,e,s){const i=this.match(58);return this.next(),i?null!==s&&this.unexpected(s):t.await=null!==s,"VariableDeclaration"!==e.type||null==e.declarations[0].init||i&&this.options.annexB&&!this.state.strict&&"var"===e.kind&&"Identifier"===e.declarations[0].id.type||this.raise(P.ForInOfLoopInitializer,e,{type:i?"ForInStatement":"ForOfStatement"}),"AssignmentPattern"===e.type&&this.raise(P.InvalidLhs,e,{ancestor:{type:"ForStatement"}}),t.left=e,t.right=i?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(11),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,i?"ForInStatement":"ForOfStatement")}parseVar(t,e,s,i=!1){const r=t.declarations=[];for(t.kind=s;;){const t=this.startNode();if(this.parseVarId(t,s),t.init=this.eat(29)?e?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():null,null!==t.init||i||("Identifier"===t.id.type||e&&(this.match(58)||this.isContextual(102))?"const"!==s&&"using"!==s&&"await using"!==s||this.match(58)||this.isContextual(102)||this.raise(P.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:s}):this.raise(P.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:"destructuring"})),r.push(this.finishNode(t,"VariableDeclarator")),!this.eat(12))break}return t}parseVarId(t,e){const s=this.parseBindingAtom();"using"!==e&&"await using"!==e||"ArrayPattern"!==s.type&&"ObjectPattern"!==s.type||this.raise(P.UsingDeclarationHasBindingPattern,s.loc.start),this.checkLVal(s,{type:"VariableDeclarator"},"var"===e?5:8201),t.id=s}parseAsyncFunctionExpression(t){return this.parseFunction(t,8)}parseFunction(t,e=0){const s=2&e,i=!!(1&e),r=i&&!(4&e),a=!!(8&e);this.initFunction(t,a),this.match(55)&&(s&&this.raise(P.GeneratorInSingleStatementContext,this.state.startLoc),this.next(),t.generator=!0),i&&(t.id=this.parseFunctionId(r));const n=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(2),this.prodParam.enter(he(a,t.generator)),i||(t.id=this.parseFunctionId()),this.parseFunctionParams(t,!1),this.withSmartMixTopicForbiddingContext(()=>{this.parseFunctionBodyAndFinish(t,i?"FunctionDeclaration":"FunctionExpression")}),this.prodParam.exit(),this.scope.exit(),i&&!s&&this.registerFunctionStatementId(t),this.state.maybeInArrowParameters=n,t}parseFunctionId(t){return t||W(this.state.type)?this.parseIdentifier():null}parseFunctionParams(t,e){this.expect(10),this.expressionScope.enter(De()),t.params=this.parseBindingList(11,41,2|(e?4:0)),this.expressionScope.exit()}registerFunctionStatementId(t){t.id&&this.scope.declareName(t.id.name,!this.options.annexB||this.state.strict||t.generator||t.async?this.scope.treatFunctionsAsVar?5:8201:17,t.id.loc.start)}parseClass(t,e,s){this.next();const i=this.state.strict;return this.state.strict=!0,this.parseClassId(t,e,s),this.parseClassSuper(t),t.body=this.parseClassBody(!!t.superClass,i),this.finishNode(t,e?"ClassDeclaration":"ClassExpression")}isClassProperty(){return this.match(29)||this.match(13)||this.match(8)}isClassMethod(){return this.match(10)}nameIsConstructor(t){return"Identifier"===t.type&&"constructor"===t.name||"StringLiteral"===t.type&&"constructor"===t.value}isNonstaticConstructor(t){return!t.computed&&!t.static&&this.nameIsConstructor(t.key)}parseClassBody(t,e){this.classScope.enter();const s={hadConstructor:!1,hadSuperClass:t};let i=[];const r=this.startNode();if(r.body=[],this.expect(5),this.withSmartMixTopicForbiddingContext(()=>{while(!this.match(8)){if(this.eat(13)){if(i.length>0)throw this.raise(P.DecoratorSemicolon,this.state.lastTokEndLoc);continue}if(this.match(26)){i.push(this.parseDecorator());continue}const t=this.startNode();i.length&&(t.decorators=i,this.resetStartLocationFromNode(t,i[0]),i=[]),this.parseClassMember(r,t,s),"constructor"===t.kind&&t.decorators&&t.decorators.length>0&&this.raise(P.DecoratorConstructor,t)}}),this.state.strict=e,this.next(),i.length)throw this.raise(P.TrailingDecorator,this.state.startLoc);return this.classScope.exit(),this.finishNode(r,"ClassBody")}parseClassMemberFromModifier(t,e){const s=this.parseIdentifier(!0);if(this.isClassMethod()){const i=e;return i.kind="method",i.computed=!1,i.key=s,i.static=!1,this.pushClassMethod(t,i,!1,!1,!1,!1),!0}if(this.isClassProperty()){const i=e;return i.computed=!1,i.key=s,i.static=!1,t.body.push(this.parseClassProperty(i)),!0}return this.resetPreviousNodeTrailingComments(s),!1}parseClassMember(t,e,s){const i=this.isContextual(106);if(i){if(this.parseClassMemberFromModifier(t,e))return;if(this.eat(5))return void this.parseClassStaticBlock(t,e)}this.parseClassMemberWithIsStatic(t,e,s,i)}parseClassMemberWithIsStatic(t,e,s,i){const r=e,a=e,n=e,o=e,h=e,c=r,p=r;if(e.static=i,this.parsePropertyNamePrefixOperator(e),this.eat(55)){c.kind="method";const e=this.match(139);return this.parseClassElementName(c),e?void this.pushClassPrivateMethod(t,a,!0,!1):(this.isNonstaticConstructor(r)&&this.raise(P.ConstructorIsGenerator,r.key),void this.pushClassMethod(t,r,!0,!1,!1,!1))}const l=!this.state.containsEsc&&W(this.state.type),u=this.parseClassElementName(e),d=l?u.name:null,m=this.isPrivateName(u),f=this.state.startLoc;if(this.parsePostMemberNameModifiers(p),this.isClassMethod()){if(c.kind="method",m)return void this.pushClassPrivateMethod(t,a,!1,!1);const i=this.isNonstaticConstructor(r);let n=!1;i&&(r.kind="constructor",s.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(P.DuplicateConstructor,u),i&&this.hasPlugin("typescript")&&e.override&&this.raise(P.OverrideOnConstructor,u),s.hadConstructor=!0,n=s.hadSuperClass),this.pushClassMethod(t,r,!1,!1,i,n)}else if(this.isClassProperty())m?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,n);else if("async"!==d||this.isLineTerminator())if("get"!==d&&"set"!==d||this.match(55)&&this.isLineTerminator())if("accessor"!==d||this.isLineTerminator())this.isLineTerminator()?m?this.pushClassPrivateProperty(t,o):this.pushClassProperty(t,n):this.unexpected();else{this.expectPlugin("decoratorAutoAccessors"),this.resetPreviousNodeTrailingComments(u);const e=this.match(139);this.parseClassElementName(n),this.pushClassAccessorProperty(t,h,e)}else{this.resetPreviousNodeTrailingComments(u),c.kind=d;const e=this.match(139);this.parseClassElementName(r),e?this.pushClassPrivateMethod(t,a,!1,!1):(this.isNonstaticConstructor(r)&&this.raise(P.ConstructorIsAccessor,r.key),this.pushClassMethod(t,r,!1,!1,!1,!1)),this.checkGetterSetterParams(r)}else{this.resetPreviousNodeTrailingComments(u);const e=this.eat(55);p.optional&&this.unexpected(f),c.kind="method";const s=this.match(139);this.parseClassElementName(c),this.parsePostMemberNameModifiers(p),s?this.pushClassPrivateMethod(t,a,e,!0):(this.isNonstaticConstructor(r)&&this.raise(P.ConstructorIsAsync,r.key),this.pushClassMethod(t,r,e,!0,!1,!1))}}parseClassElementName(t){const{type:e,value:s}=this.state;if(132!==e&&134!==e||!t.static||"prototype"!==s||this.raise(P.StaticPrototype,this.state.startLoc),139===e){"constructor"===s&&this.raise(P.ConstructorClassPrivateField,this.state.startLoc);const e=this.parsePrivateName();return t.key=e,e}return this.parsePropertyName(t),t.key}parseClassStaticBlock(t,e){var s;this.scope.enter(208);const i=this.state.labels;this.state.labels=[],this.prodParam.enter(0);const r=e.body=[];this.parseBlockOrModuleBlockBody(r,void 0,!1,8),this.prodParam.exit(),this.scope.exit(),this.state.labels=i,t.body.push(this.finishNode(e,"StaticBlock")),null!=(s=e.decorators)&&s.length&&this.raise(P.DecoratorStaticBlock,e)}pushClassProperty(t,e){!e.computed&&this.nameIsConstructor(e.key)&&this.raise(P.ConstructorClassField,e.key),t.body.push(this.parseClassProperty(e))}pushClassPrivateProperty(t,e){const s=this.parseClassPrivateProperty(e);t.body.push(s),this.classScope.declarePrivateName(this.getPrivateNameSV(s.key),0,s.key.loc.start)}pushClassAccessorProperty(t,e,s){s||e.computed||!this.nameIsConstructor(e.key)||this.raise(P.ConstructorClassField,e.key);const i=this.parseClassAccessorProperty(e);t.body.push(i),s&&this.classScope.declarePrivateName(this.getPrivateNameSV(i.key),0,i.key.loc.start)}pushClassMethod(t,e,s,i,r,a){t.body.push(this.parseMethod(e,s,i,r,a,"ClassMethod",!0))}pushClassPrivateMethod(t,e,s,i){const r=this.parseMethod(e,s,i,!1,!1,"ClassPrivateMethod",!0);t.body.push(r);const a="get"===r.kind?r.static?6:2:"set"===r.kind?r.static?5:1:0;this.declareClassPrivateMethodInScope(r,a)}declareClassPrivateMethodInScope(t,e){this.classScope.declarePrivateName(this.getPrivateNameSV(t.key),e,t.key.loc.start)}parsePostMemberNameModifiers(t){}parseClassPrivateProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassPrivateProperty")}parseClassProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassProperty")}parseClassAccessorProperty(t){return this.parseInitializer(t),this.semicolon(),this.finishNode(t,"ClassAccessorProperty")}parseInitializer(t){this.scope.enter(80),this.expressionScope.enter(Re()),this.prodParam.enter(0),t.value=this.eat(29)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}parseClassId(t,e,s,i=8331){if(W(this.state.type))t.id=this.parseIdentifier(),e&&this.declareNameFromIdentifier(t.id,i);else{if(!s&&e)throw this.raise(P.MissingClassName,this.state.startLoc);t.id=null}}parseClassSuper(t){t.superClass=this.eat(81)?this.parseExprSubscripts():null}parseExport(t,e){const s=this.parseMaybeImportPhase(t,!0),i=this.maybeParseExportDefaultSpecifier(t,s),r=!i||this.eat(12),a=r&&this.eatExportStar(t),n=a&&this.maybeParseExportNamespaceSpecifier(t),o=r&&(!n||this.eat(12)),h=i||a;if(a&&!n){if(i&&this.unexpected(),e)throw this.raise(P.UnsupportedDecoratorExport,t);return this.parseExportFrom(t,!0),this.sawUnambiguousESM=!0,this.finishNode(t,"ExportAllDeclaration")}const c=this.maybeParseExportNamedSpecifiers(t);let p;if(i&&r&&!a&&!c&&this.unexpected(null,5),n&&o&&this.unexpected(null,98),h||c){if(p=!1,e)throw this.raise(P.UnsupportedDecoratorExport,t);this.parseExportFrom(t,h)}else p=this.maybeParseExportDeclaration(t);if(h||c||p){var l;const s=t;if(this.checkExport(s,!0,!1,!!s.source),"ClassDeclaration"===(null==(l=s.declaration)?void 0:l.type))this.maybeTakeDecorators(e,s.declaration,s);else if(e)throw this.raise(P.UnsupportedDecoratorExport,t);return this.sawUnambiguousESM=!0,this.finishNode(s,"ExportNamedDeclaration")}if(this.eat(65)){const s=t,i=this.parseExportDefaultExpression();if(s.declaration=i,"ClassDeclaration"===i.type)this.maybeTakeDecorators(e,i,s);else if(e)throw this.raise(P.UnsupportedDecoratorExport,t);return this.checkExport(s,!0,!0),this.sawUnambiguousESM=!0,this.finishNode(s,"ExportDefaultDeclaration")}this.unexpected(null,5)}eatExportStar(t){return this.eat(55)}maybeParseExportDefaultSpecifier(t,e){if(e||this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom",null==e?void 0:e.loc.start);const s=e||this.parseIdentifier(!0),i=this.startNodeAtNode(s);return i.exported=s,t.specifiers=[this.finishNode(i,"ExportDefaultSpecifier")],!0}return!1}maybeParseExportNamespaceSpecifier(t){if(this.isContextual(93)){var e;null!=(e=t).specifiers||(e.specifiers=[]);const s=this.startNodeAt(this.state.lastTokStartLoc);return this.next(),s.exported=this.parseModuleExportName(),t.specifiers.push(this.finishNode(s,"ExportNamespaceSpecifier")),!0}return!1}maybeParseExportNamedSpecifiers(t){if(this.match(5)){const e=t;e.specifiers||(e.specifiers=[]);const s="type"===e.exportKind;return e.specifiers.push(...this.parseExportSpecifiers(s)),e.source=null,this.hasPlugin("importAssertions")?e.assertions=[]:e.attributes=[],e.declaration=null,!0}return!1}maybeParseExportDeclaration(t){return!!this.shouldParseExportDeclaration()&&(t.specifiers=[],t.source=null,this.hasPlugin("importAssertions")?t.assertions=[]:t.attributes=[],t.declaration=this.parseExportDeclaration(t),!0)}isAsyncFunction(){if(!this.isContextual(95))return!1;const t=this.nextTokenInLineStart();return this.isUnparsedContextual(t,"function")}parseExportDefaultExpression(){const t=this.startNode();if(this.match(68))return this.next(),this.parseFunction(t,5);if(this.isAsyncFunction())return this.next(),this.next(),this.parseFunction(t,13);if(this.match(80))return this.parseClass(t,!0,!0);if(this.match(26))return this.hasPlugin("decorators")&&!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(P.DecoratorBeforeExport,this.state.startLoc),this.parseClass(this.maybeTakeDecorators(this.parseDecorators(!1),this.startNode()),!0,!0);if(this.match(75)||this.match(74)||this.isLet())throw this.raise(P.UnsupportedDefaultExport,this.state.startLoc);const e=this.parseMaybeAssignAllowIn();return this.semicolon(),e}parseExportDeclaration(t){if(this.match(80)){const t=this.parseClass(this.startNode(),!0,!1);return t}return this.parseStatementListItem()}isExportDefaultSpecifier(){const{type:t}=this.state;if(W(t)){if(95===t&&!this.state.containsEsc||100===t)return!1;if((130===t||129===t)&&!this.state.containsEsc){const{type:t}=this.lookahead();if(W(t)&&98!==t||5===t)return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(65))return!1;const e=this.nextTokenStart(),s=this.isUnparsedContextual(e,"from");if(44===this.input.charCodeAt(e)||W(this.state.type)&&s)return!0;if(this.match(65)&&s){const t=this.input.charCodeAt(this.nextTokenStartSince(e+4));return 34===t||39===t}return!1}parseExportFrom(t,e){this.eatContextual(98)?(t.source=this.parseImportSource(),this.checkExport(t),this.maybeParseImportAttributes(t),this.checkJSONModuleImport(t)):e&&this.unexpected(),this.semicolon()}shouldParseExportDeclaration(){const{type:t}=this.state;return 26===t&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))?(!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(P.DecoratorBeforeExport,this.state.startLoc),!0):this.isContextual(107)||this.isContextual(96)&&this.startsAwaitUsing()?(this.raise(P.UsingDeclarationExport,this.state.startLoc),!0):74===t||75===t||68===t||80===t||this.isLet()||this.isAsyncFunction()}checkExport(t,e,s,i){var r;if(e)if(s){if(this.checkDuplicateExports(t,"default"),this.hasPlugin("exportDefaultFrom")){var a;const e=t.declaration;"Identifier"!==e.type||"from"!==e.name||e.end-e.start!==4||null!=(a=e.extra)&&a.parenthesized||this.raise(P.ExportDefaultFromAsIdentifier,e)}}else if(null!=(r=t.specifiers)&&r.length)for(const n of t.specifiers){const{exported:t}=n,e="Identifier"===t.type?t.name:t.value;if(this.checkDuplicateExports(n,e),!i&&n.local){const{local:t}=n;"Identifier"!==t.type?this.raise(P.ExportBindingIsString,n,{localName:t.value,exportName:e}):(this.checkReservedWord(t.name,t.loc.start,!0,!1),this.scope.checkLocalExport(t))}}else if(t.declaration){const e=t.declaration;if("FunctionDeclaration"===e.type||"ClassDeclaration"===e.type){const{id:s}=e;if(!s)throw new Error("Assertion failure");this.checkDuplicateExports(t,s.name)}else if("VariableDeclaration"===e.type)for(const t of e.declarations)this.checkDeclaration(t.id)}}checkDeclaration(t){if("Identifier"===t.type)this.checkDuplicateExports(t,t.name);else if("ObjectPattern"===t.type)for(const e of t.properties)this.checkDeclaration(e);else if("ArrayPattern"===t.type)for(const e of t.elements)e&&this.checkDeclaration(e);else"ObjectProperty"===t.type?this.checkDeclaration(t.value):"RestElement"===t.type?this.checkDeclaration(t.argument):"AssignmentPattern"===t.type&&this.checkDeclaration(t.left)}checkDuplicateExports(t,e){this.exportedIdentifiers.has(e)&&("default"===e?this.raise(P.DuplicateDefaultExport,t):this.raise(P.DuplicateExport,t,{exportName:e})),this.exportedIdentifiers.add(e)}parseExportSpecifiers(t){const e=[];let s=!0;this.expect(5);while(!this.eat(8)){if(s)s=!1;else if(this.expect(12),this.eat(8))break;const i=this.isContextual(130),r=this.match(134),a=this.startNode();a.local=this.parseModuleExportName(),e.push(this.parseExportSpecifier(a,r,t,i))}return e}parseExportSpecifier(t,e,s,i){return this.eatContextual(93)?t.exported=this.parseModuleExportName():e?t.exported=this.cloneStringLiteral(t.local):t.exported||(t.exported=this.cloneIdentifier(t.local)),this.finishNode(t,"ExportSpecifier")}parseModuleExportName(){if(this.match(134)){const t=this.parseStringLiteral(this.state.value),e=ms.exec(t.value);return e&&this.raise(P.ModuleExportNameHasLoneSurrogate,t,{surrogateCharCode:e[0].charCodeAt(0)}),t}return this.parseIdentifier(!0)}isJSONModuleImport(t){return null!=t.assertions&&t.assertions.some(({key:t,value:e})=>"json"===e.value&&("Identifier"===t.type?"type"===t.name:"type"===t.value))}checkImportReflection(t){const{specifiers:e}=t,s=1===e.length?e[0].type:null;if("source"===t.phase)"ImportDefaultSpecifier"!==s&&this.raise(P.SourcePhaseImportRequiresDefault,e[0].loc.start);else if("defer"===t.phase)"ImportNamespaceSpecifier"!==s&&this.raise(P.DeferImportRequiresNamespace,e[0].loc.start);else if(t.module){var i;"ImportDefaultSpecifier"!==s&&this.raise(P.ImportReflectionNotBinding,e[0].loc.start),(null==(i=t.assertions)?void 0:i.length)>0&&this.raise(P.ImportReflectionHasAssertion,e[0].loc.start)}}checkJSONModuleImport(t){if(this.isJSONModuleImport(t)&&"ExportAllDeclaration"!==t.type){const{specifiers:e}=t;if(null!=e){const t=e.find(t=>{let e;if("ExportSpecifier"===t.type?e=t.local:"ImportSpecifier"===t.type&&(e=t.imported),void 0!==e)return"Identifier"===e.type?"default"!==e.name:"default"!==e.value});void 0!==t&&this.raise(P.ImportJSONBindingNotDefault,t.loc.start)}}}isPotentialImportPhase(t){return!t&&(this.isContextual(105)||this.isContextual(97)||this.isContextual(127))}applyImportPhase(t,e,s,i){e||("module"===s?(this.expectPlugin("importReflection",i),t.module=!0):this.hasPlugin("importReflection")&&(t.module=!1),"source"===s?(this.expectPlugin("sourcePhaseImports",i),t.phase="source"):"defer"===s?(this.expectPlugin("deferredImportEvaluation",i),t.phase="defer"):this.hasPlugin("sourcePhaseImports")&&(t.phase=null))}parseMaybeImportPhase(t,e){if(!this.isPotentialImportPhase(e))return this.applyImportPhase(t,e,null),null;const s=this.parseIdentifier(!0),{type:i}=this.state,r=G(i)?98!==i||102===this.lookaheadCharCode():12!==i;return r?(this.resetPreviousIdentifierLeadingComments(s),this.applyImportPhase(t,e,s.name,s.loc.start),null):(this.applyImportPhase(t,e,null),s)}isPrecedingIdImportPhase(t){const{type:e}=this.state;return W(e)?98!==e||102===this.lookaheadCharCode():12!==e}parseImport(t){return this.match(134)?this.parseImportSourceAndAttributes(t):this.parseImportSpecifiersAndAfter(t,this.parseMaybeImportPhase(t,!1))}parseImportSpecifiersAndAfter(t,e){t.specifiers=[];const s=this.maybeParseDefaultImportSpecifier(t,e),i=!s||this.eat(12),r=i&&this.maybeParseStarImportSpecifier(t);return i&&!r&&this.parseNamedImportSpecifiers(t),this.expectContextual(98),this.parseImportSourceAndAttributes(t)}parseImportSourceAndAttributes(t){return null!=t.specifiers||(t.specifiers=[]),t.source=this.parseImportSource(),this.maybeParseImportAttributes(t),this.checkImportReflection(t),this.checkJSONModuleImport(t),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(t,"ImportDeclaration")}parseImportSource(){return this.match(134)||this.unexpected(),this.parseExprAtom()}parseImportSpecifierLocal(t,e,s){e.local=this.parseIdentifier(),t.specifiers.push(this.finishImportSpecifier(e,s))}finishImportSpecifier(t,e,s=8201){return this.checkLVal(t.local,{type:e},s),this.finishNode(t,e)}parseImportAttributes(){this.expect(5);const t=[],e=new Set;do{if(this.match(8))break;const s=this.startNode(),i=this.state.value;if(e.has(i)&&this.raise(P.ModuleAttributesWithDuplicateKeys,this.state.startLoc,{key:i}),e.add(i),this.match(134)?s.key=this.parseStringLiteral(i):s.key=this.parseIdentifier(!0),this.expect(14),!this.match(134))throw this.raise(P.ModuleAttributeInvalidValue,this.state.startLoc);s.value=this.parseStringLiteral(this.state.value),t.push(this.finishNode(s,"ImportAttribute"))}while(this.eat(12));return this.expect(8),t}parseModuleAttributes(){const t=[],e=new Set;do{const s=this.startNode();if(s.key=this.parseIdentifier(!0),"type"!==s.key.name&&this.raise(P.ModuleAttributeDifferentFromType,s.key),e.has(s.key.name)&&this.raise(P.ModuleAttributesWithDuplicateKeys,s.key,{key:s.key.name}),e.add(s.key.name),this.expect(14),!this.match(134))throw this.raise(P.ModuleAttributeInvalidValue,this.state.startLoc);s.value=this.parseStringLiteral(this.state.value),t.push(this.finishNode(s,"ImportAttribute"))}while(this.eat(12));return t}maybeParseImportAttributes(t){let e;var s=!1;if(this.match(76)){if(this.hasPrecedingLineBreak()&&40===this.lookaheadCharCode())return;this.next(),this.hasPlugin("moduleAttributes")?(e=this.parseModuleAttributes(),this.addExtra(t,"deprecatedWithLegacySyntax",!0)):e=this.parseImportAttributes(),s=!0}else this.isContextual(94)&&!this.hasPrecedingLineBreak()?(this.hasPlugin("deprecatedImportAssert")||this.hasPlugin("importAssertions")||this.raise(P.ImportAttributesUseAssert,this.state.startLoc),this.hasPlugin("importAssertions")||this.addExtra(t,"deprecatedAssertSyntax",!0),this.next(),e=this.parseImportAttributes()):e=[];!s&&this.hasPlugin("importAssertions")?t.assertions=e:t.attributes=e}maybeParseDefaultImportSpecifier(t,e){if(e){const s=this.startNodeAtNode(e);return s.local=e,t.specifiers.push(this.finishImportSpecifier(s,"ImportDefaultSpecifier")),!0}return!!G(this.state.type)&&(this.parseImportSpecifierLocal(t,this.startNode(),"ImportDefaultSpecifier"),!0)}maybeParseStarImportSpecifier(t){if(this.match(55)){const e=this.startNode();return this.next(),this.expectContextual(93),this.parseImportSpecifierLocal(t,e,"ImportNamespaceSpecifier"),!0}return!1}parseNamedImportSpecifiers(t){let e=!0;this.expect(5);while(!this.eat(8)){if(e)e=!1;else{if(this.eat(14))throw this.raise(P.DestructureNamedImport,this.state.startLoc);if(this.expect(12),this.eat(8))break}const s=this.startNode(),i=this.match(134),r=this.isContextual(130);s.imported=this.parseModuleExportName();const a=this.parseImportSpecifier(s,i,"type"===t.importKind||"typeof"===t.importKind,r,void 0);t.specifiers.push(a)}}parseImportSpecifier(t,e,s,i,r){if(this.eatContextual(93))t.local=this.parseIdentifier();else{const{imported:s}=t;if(e)throw this.raise(P.ImportBindingIsString,t,{importName:s.value});this.checkReservedWord(s.name,t.loc.start,!0,!0),t.local||(t.local=this.cloneIdentifier(s))}return this.finishImportSpecifier(t,"ImportSpecifier",r)}isThisParam(t){return"Identifier"===t.type&&"this"===t.name}}class gs extends xs{constructor(t,e,s){t=T(t),super(t,e),this.options=t,this.initializeScopes(),this.plugins=s,this.filename=t.sourceFilename,this.startIndex=t.startIndex;let i=0;t.allowAwaitOutsideFunction&&(i|=1),t.allowReturnOutsideFunction&&(i|=2),t.allowImportExportEverywhere&&(i|=8),t.allowSuperOutsideMethod&&(i|=16),t.allowUndeclaredExports&&(i|=64),t.allowNewTargetOutsideFunction&&(i|=4),t.allowYieldOutsideFunction&&(i|=32),t.ranges&&(i|=128),t.tokens&&(i|=256),t.createImportExpressions&&(i|=512),t.createParenthesizedExpressions&&(i|=1024),t.errorRecovery&&(i|=2048),t.attachComment&&(i|=4096),t.annexB&&(i|=8192),this.optionFlags=i}getScopeHandler(){return Bt}parse(){this.enterInitialScopes();const t=this.startNode(),e=this.startNode();return this.nextToken(),t.errors=null,this.parseTopLevel(t,e),t.errors=this.state.errors,t.comments.length=this.state.commentsLen,t}}function Ps(t,e){var s;if("unambiguous"!==(null==(s=e)?void 0:s.sourceType))return Ss(e,t).parse();e=Object.assign({},e);try{e.sourceType="module";const s=Ss(e,t),r=s.parse();if(s.sawUnambiguousESM)return r;if(s.ambiguousScriptDifferentAst)try{return e.sourceType="script",Ss(e,t).parse()}catch(i){}else r.program.sourceType="script";return r}catch(r){try{return e.sourceType="script",Ss(e,t).parse()}catch(a){}throw r}}function bs(t,e){const s=Ss(e,t);return s.options.strictMode&&(s.state.strict=!0),s.getExpression()}function Ts(t){const e={};for(const s of Object.keys(t))e[s]=dt(t[s]);return e}const As=Ts(J);function Ss(t,e){let s=gs;const i=new Map;if(null!=t&&t.plugins){for(const e of t.plugins){let t,s;"string"===typeof e?t=e:[t,s]=e,i.has(t)||i.set(t,s||{})}hs(i),s=ws(i)}return new s(t,e,i)}const Es=new Map;function ws(t){const e=[];for(const r of ps)t.has(r)&&e.push(r);const s=e.join("|");let i=Es.get(s);if(!i){i=gs;for(const t of e)i=cs[t](i);Es.set(s,i)}return i}e.parse=Ps,e.parseExpression=bs,e.tokTypes=As},"28a0":function(t,e){"function"===typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var s=function(){};s.prototype=e.prototype,t.prototype=new s,t.prototype.constructor=t}},2909:function(t,e,s){"use strict";s.d(e,"a",(function(){return h}));var i=s("6b75");function r(t){if(Array.isArray(t))return Object(i["a"])(t)}s("a4d3"),s("e01a"),s("d28b"),s("a630"),s("d3b7"),s("3ca3"),s("ddb0");function a(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}var n=s("06c5");function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t){return r(t)||a(t)||Object(n["a"])(t)||o()}},3022:function(t,e,s){(function(t){var i=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),s={},i=0;i<e.length;i++)s[e[i]]=Object.getOwnPropertyDescriptor(t,e[i]);return s},r=/%[sdj%]/g;e.format=function(t){if(!A(t)){for(var e=[],s=0;s<arguments.length;s++)e.push(o(arguments[s]));return e.join(" ")}s=1;for(var i=arguments,a=i.length,n=String(t).replace(r,(function(t){if("%%"===t)return"%";if(s>=a)return t;switch(t){case"%s":return String(i[s++]);case"%d":return Number(i[s++]);case"%j":try{return JSON.stringify(i[s++])}catch(e){return"[Circular]"}default:return t}})),h=i[s];s<a;h=i[++s])P(h)||!I(h)?n+=" "+h:n+=" "+o(h);return n},e.deprecate=function(s,i){if("undefined"!==typeof t&&!0===t.noDeprecation)return s;if("undefined"===typeof t)return function(){return e.deprecate(s,i).apply(this,arguments)};var r=!1;function a(){if(!r){if(t.throwDeprecation)throw new Error(i);t.traceDeprecation?console.trace(i):console.error(i),r=!0}return s.apply(this,arguments)}return a};var a,n={};function o(t,s){var i={seen:[],stylize:c};return arguments.length>=3&&(i.depth=arguments[2]),arguments.length>=4&&(i.colors=arguments[3]),g(s)?i.showHidden=s:s&&e._extend(i,s),E(i.showHidden)&&(i.showHidden=!1),E(i.depth)&&(i.depth=2),E(i.colors)&&(i.colors=!1),E(i.customInspect)&&(i.customInspect=!0),i.colors&&(i.stylize=h),l(i,t,i.depth)}function h(t,e){var s=o.styles[e];return s?"["+o.colors[s][0]+"m"+t+"["+o.colors[s][1]+"m":t}function c(t,e){return t}function p(t){var e={};return t.forEach((function(t,s){e[t]=!0})),e}function l(t,s,i){if(t.customInspect&&s&&N(s.inspect)&&s.inspect!==e.inspect&&(!s.constructor||s.constructor.prototype!==s)){var r=s.inspect(i,t);return A(r)||(r=l(t,r,i)),r}var a=u(t,s);if(a)return a;var n=Object.keys(s),o=p(n);if(t.showHidden&&(n=Object.getOwnPropertyNames(s)),v(s)&&(n.indexOf("message")>=0||n.indexOf("description")>=0))return d(s);if(0===n.length){if(N(s)){var h=s.name?": "+s.name:"";return t.stylize("[Function"+h+"]","special")}if(w(s))return t.stylize(RegExp.prototype.toString.call(s),"regexp");if(C(s))return t.stylize(Date.prototype.toString.call(s),"date");if(v(s))return d(s)}var c,g="",P=!1,b=["{","}"];if(x(s)&&(P=!0,b=["[","]"]),N(s)){var T=s.name?": "+s.name:"";g=" [Function"+T+"]"}return w(s)&&(g=" "+RegExp.prototype.toString.call(s)),C(s)&&(g=" "+Date.prototype.toUTCString.call(s)),v(s)&&(g=" "+d(s)),0!==n.length||P&&0!=s.length?i<0?w(s)?t.stylize(RegExp.prototype.toString.call(s),"regexp"):t.stylize("[Object]","special"):(t.seen.push(s),c=P?m(t,s,i,o,n):n.map((function(e){return f(t,s,i,o,e,P)})),t.seen.pop(),y(c,g,b)):b[0]+g+b[1]}function u(t,e){if(E(e))return t.stylize("undefined","undefined");if(A(e)){var s="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(s,"string")}return T(e)?t.stylize(""+e,"number"):g(e)?t.stylize(""+e,"boolean"):P(e)?t.stylize("null","null"):void 0}function d(t){return"["+Error.prototype.toString.call(t)+"]"}function m(t,e,s,i,r){for(var a=[],n=0,o=e.length;n<o;++n)F(e,String(n))?a.push(f(t,e,s,i,String(n),!0)):a.push("");return r.forEach((function(r){r.match(/^\d+$/)||a.push(f(t,e,s,i,r,!0))})),a}function f(t,e,s,i,r,a){var n,o,h;if(h=Object.getOwnPropertyDescriptor(e,r)||{value:e[r]},h.get?o=h.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):h.set&&(o=t.stylize("[Setter]","special")),F(i,r)||(n="["+r+"]"),o||(t.seen.indexOf(h.value)<0?(o=P(s)?l(t,h.value,null):l(t,h.value,s-1),o.indexOf("\n")>-1&&(o=a?o.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+o.split("\n").map((function(t){return"   "+t})).join("\n"))):o=t.stylize("[Circular]","special")),E(n)){if(a&&r.match(/^\d+$/))return o;n=JSON.stringify(""+r),n.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(n=n.substr(1,n.length-2),n=t.stylize(n,"name")):(n=n.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),n=t.stylize(n,"string"))}return n+": "+o}function y(t,e,s){var i=t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return i>60?s[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+s[1]:s[0]+e+" "+t.join(", ")+" "+s[1]}function x(t){return Array.isArray(t)}function g(t){return"boolean"===typeof t}function P(t){return null===t}function b(t){return null==t}function T(t){return"number"===typeof t}function A(t){return"string"===typeof t}function S(t){return"symbol"===typeof t}function E(t){return void 0===t}function w(t){return I(t)&&"[object RegExp]"===L(t)}function I(t){return"object"===typeof t&&null!==t}function C(t){return I(t)&&"[object Date]"===L(t)}function v(t){return I(t)&&("[object Error]"===L(t)||t instanceof Error)}function N(t){return"function"===typeof t}function k(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function L(t){return Object.prototype.toString.call(t)}function M(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(s){if(E(a)&&(a=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/",BASE_URL:"/"}).NODE_DEBUG||""),s=s.toUpperCase(),!n[s])if(new RegExp("\\b"+s+"\\b","i").test(a)){var i=t.pid;n[s]=function(){var t=e.format.apply(e,arguments);console.error("%s %d: %s",s,i,t)}}else n[s]=function(){};return n[s]},e.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=x,e.isBoolean=g,e.isNull=P,e.isNullOrUndefined=b,e.isNumber=T,e.isString=A,e.isSymbol=S,e.isUndefined=E,e.isRegExp=w,e.isObject=I,e.isDate=C,e.isError=v,e.isFunction=N,e.isPrimitive=k,e.isBuffer=s("d60a");var O=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function D(){var t=new Date,e=[M(t.getHours()),M(t.getMinutes()),M(t.getSeconds())].join(":");return[t.getDate(),O[t.getMonth()],e].join(" ")}function F(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",D(),e.format.apply(e,arguments))},e.inherits=s("28a0"),e._extend=function(t,e){if(!e||!I(e))return t;var s=Object.keys(e),i=s.length;while(i--)t[s[i]]=e[s[i]];return t};var B="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function R(t,e){if(!t){var s=new Error("Promise was rejected with a falsy value");s.reason=t,t=s}return e(t)}function j(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function s(){for(var s=[],i=0;i<arguments.length;i++)s.push(arguments[i]);var r=s.pop();if("function"!==typeof r)throw new TypeError("The last argument must be of type Function");var a=this,n=function(){return r.apply(a,arguments)};e.apply(this,s).then((function(e){t.nextTick(n,null,e)}),(function(e){t.nextTick(R,e,n)}))}return Object.setPrototypeOf(s,Object.getPrototypeOf(e)),Object.defineProperties(s,i(e)),s}e.promisify=function(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');if(B&&t[B]){var e=t[B];if("function"!==typeof e)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,B,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,s,i=new Promise((function(t,i){e=t,s=i})),r=[],a=0;a<arguments.length;a++)r.push(arguments[a]);r.push((function(t,i){t?s(t):e(i)}));try{t.apply(this,r)}catch(n){s(n)}return i}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),B&&Object.defineProperty(e,B,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,i(t))},e.promisify.custom=B,e.callbackify=j}).call(this,s("4362"))},"4df4":function(t,e,s){"use strict";var i=s("0366"),r=s("7b0b"),a=s("9bdd"),n=s("e95a"),o=s("50c4"),h=s("8418"),c=s("35a1");t.exports=function(t){var e,s,p,l,u,d,m=r(t),f="function"==typeof this?this:Array,y=arguments.length,x=y>1?arguments[1]:void 0,g=void 0!==x,P=c(m),b=0;if(g&&(x=i(x,y>2?arguments[2]:void 0,2)),void 0==P||f==Array&&n(P))for(e=o(m.length),s=new f(e);e>b;b++)d=g?x(m[b],b):m[b],h(s,b,d);else for(l=P.call(m),u=l.next,s=new f;!(p=u.call(l)).done;b++)d=g?a(l,x,[p.value,b],!0):p.value,h(s,b,d);return s.length=b,s}},"6b75":function(t,e,s){"use strict";function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var s=0,i=Array(e);s<e;s++)i[s]=t[s];return i}s.d(e,"a",(function(){return i}))},"8a79":function(t,e,s){"use strict";var i=s("23e7"),r=s("06cf").f,a=s("50c4"),n=s("5a34"),o=s("1d80"),h=s("ab13"),c=s("c430"),p="".endsWith,l=Math.min,u=h("endsWith"),d=!c&&!u&&!!function(){var t=r(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!d&&!u},{endsWith:function(t){var e=String(o(this));n(t);var s=arguments.length>1?arguments[1]:void 0,i=a(e.length),r=void 0===s?i:l(a(s),i),h=String(t);return p?p.call(e,h,r):e.slice(r-h.length,r)===h}})},"9a9a":function(t,e,s){"use strict";var i=s("23e7"),r=s("2266"),a=s("1c0b"),n=s("825a");i({target:"Iterator",proto:!0,real:!0},{some:function(t){return n(this),a(t),r(this,(function(e){if(t(e))return r.stop()}),void 0,!1,!0).stopped}})},"9d4a":function(t,e,s){"use strict";var i=s("23e7"),r=s("2266"),a=s("1c0b"),n=s("825a");i({target:"Iterator",proto:!0,real:!0},{reduce:function(t){n(this),a(t);var e=arguments.length<2,s=e?void 0:arguments[1];if(r(this,(function(i){e?(e=!1,s=i):s=t(s,i)}),void 0,!1,!0),e)throw TypeError("Reduce of empty iterator with no initial value");return s}})},a630:function(t,e,s){var i=s("23e7"),r=s("4df4"),a=s("1c7e"),n=!a((function(t){Array.from(t)}));i({target:"Array",stat:!0,forced:n},{from:r})},b311:function(t,e,s){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(e,s){t.exports=s()})(0,(function(){return function(){var t={686:function(t,e,s){"use strict";s.d(e,{default:function(){return M}});var i=s(279),r=s.n(i),a=s(370),n=s.n(a),o=s(817),h=s.n(o);function c(t){try{return document.execCommand(t)}catch(e){return!1}}var p=function(t){var e=h()(t);return c("cut"),e},l=p;function u(t){var e="rtl"===document.documentElement.getAttribute("dir"),s=document.createElement("textarea");s.style.fontSize="12pt",s.style.border="0",s.style.padding="0",s.style.margin="0",s.style.position="absolute",s.style[e?"right":"left"]="-9999px";var i=window.pageYOffset||document.documentElement.scrollTop;return s.style.top="".concat(i,"px"),s.setAttribute("readonly",""),s.value=t,s}var d=function(t,e){var s=u(t);e.container.appendChild(s);var i=h()(s);return c("copy"),s.remove(),i},m=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},s="";return"string"===typeof t?s=d(t,e):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===t||void 0===t?void 0:t.type)?s=d(t.value,e):(s=h()(t),c("copy")),s},f=m;function y(t){return y="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}var x=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,s=void 0===e?"copy":e,i=t.container,r=t.target,a=t.text;if("copy"!==s&&"cut"!==s)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==r){if(!r||"object"!==y(r)||1!==r.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===s&&r.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===s&&(r.hasAttribute("readonly")||r.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return a?f(a,{container:i}):r?"cut"===s?l(r):f(r,{container:i}):void 0},g=x;function P(t){return P="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function T(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function A(t,e,s){return e&&T(t.prototype,e),s&&T(t,s),t}function S(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&E(t,e)}function E(t,e){return E=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},E(t,e)}function w(t){var e=v();return function(){var s,i=N(t);if(e){var r=N(this).constructor;s=Reflect.construct(i,arguments,r)}else s=i.apply(this,arguments);return I(this,s)}}function I(t,e){return!e||"object"!==P(e)&&"function"!==typeof e?C(t):e}function C(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function v(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function N(t){return N=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},N(t)}function k(t,e){var s="data-clipboard-".concat(t);if(e.hasAttribute(s))return e.getAttribute(s)}var L=function(t){S(s,t);var e=w(s);function s(t,i){var r;return b(this,s),r=e.call(this),r.resolveOptions(i),r.listenClick(t),r}return A(s,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===P(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=n()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget,s=this.action(e)||"copy",i=g({action:s,container:this.container,target:this.target(e),text:this.text(e)});this.emit(i?"success":"error",{action:s,text:i,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(t){return k("action",t)}},{key:"defaultTarget",value:function(t){var e=k("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return k("text",t)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return f(t,e)}},{key:"cut",value:function(t){return l(t)}},{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,s=!!document.queryCommandSupported;return e.forEach((function(t){s=s&&!!document.queryCommandSupported(t)})),s}}]),s}(r()),M=L},828:function(t){var e=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var s=Element.prototype;s.matches=s.matchesSelector||s.mozMatchesSelector||s.msMatchesSelector||s.oMatchesSelector||s.webkitMatchesSelector}function i(t,s){while(t&&t.nodeType!==e){if("function"===typeof t.matches&&t.matches(s))return t;t=t.parentNode}}t.exports=i},438:function(t,e,s){var i=s(828);function r(t,e,s,i,r){var a=n.apply(this,arguments);return t.addEventListener(s,a,r),{destroy:function(){t.removeEventListener(s,a,r)}}}function a(t,e,s,i,a){return"function"===typeof t.addEventListener?r.apply(null,arguments):"function"===typeof s?r.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return r(t,e,s,i,a)})))}function n(t,e,s,r){return function(s){s.delegateTarget=i(s.target,e),s.delegateTarget&&r.call(t,s)}}t.exports=a},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var s=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===s||"[object HTMLCollection]"===s)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},370:function(t,e,s){var i=s(879),r=s(438);function a(t,e,s){if(!t&&!e&&!s)throw new Error("Missing required arguments");if(!i.string(e))throw new TypeError("Second argument must be a String");if(!i.fn(s))throw new TypeError("Third argument must be a Function");if(i.node(t))return n(t,e,s);if(i.nodeList(t))return o(t,e,s);if(i.string(t))return h(t,e,s);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function n(t,e,s){return t.addEventListener(e,s),{destroy:function(){t.removeEventListener(e,s)}}}function o(t,e,s){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,s)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,s)}))}}}function h(t,e,s){return r(document.body,t,e,s)}t.exports=a},817:function(t){function e(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var s=t.hasAttribute("readonly");s||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),s||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var i=window.getSelection(),r=document.createRange();r.selectNodeContents(t),i.removeAllRanges(),i.addRange(r),e=i.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,s){var i=this.e||(this.e={});return(i[t]||(i[t]=[])).push({fn:e,ctx:s}),this},once:function(t,e,s){var i=this;function r(){i.off(t,r),e.apply(s,arguments)}return r._=e,this.on(t,r,s)},emit:function(t){var e=[].slice.call(arguments,1),s=((this.e||(this.e={}))[t]||[]).slice(),i=0,r=s.length;for(i;i<r;i++)s[i].fn.apply(s[i].ctx,e);return this},off:function(t,e){var s=this.e||(this.e={}),i=s[t],r=[];if(i&&e)for(var a=0,n=i.length;a<n;a++)i[a].fn!==e&&i[a].fn._!==e&&r.push(i[a]);return r.length?s[t]=r:delete s[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function s(i){if(e[i])return e[i].exports;var r=e[i]={exports:{}};return t[i](r,r.exports,s),r.exports}return function(){s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,{a:e}),e}}(),function(){s.d=function(t,e){for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}}(),function(){s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),s(686)}().default}))},c740:function(t,e,s){"use strict";var i=s("23e7"),r=s("b727").findIndex,a=s("44d2"),n=s("ae40"),o="findIndex",h=!0,c=n(o);o in[]&&Array(1)[o]((function(){h=!1})),i({target:"Array",proto:!0,forced:h||!c},{findIndex:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),a(o)},d58f:function(t,e,s){var i=s("1c0b"),r=s("7b0b"),a=s("44ad"),n=s("50c4"),o=function(t){return function(e,s,o,h){i(s);var c=r(e),p=a(c),l=n(c.length),u=t?l-1:0,d=t?-1:1;if(o<2)while(1){if(u in p){h=p[u],u+=d;break}if(u+=d,t?u<0:l<=u)throw TypeError("Reduce of empty array with no initial value")}for(;t?u>=0:l>u;u+=d)u in p&&(h=s(h,p[u],u,c));return h}};t.exports={left:o(!1),right:o(!0)}},d60a:function(t,e){t.exports=function(t){return t&&"object"===typeof t&&"function"===typeof t.copy&&"function"===typeof t.fill&&"function"===typeof t.readUInt8}}}]);