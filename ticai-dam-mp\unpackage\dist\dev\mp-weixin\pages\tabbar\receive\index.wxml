<view class="container"><view data-event-opts="{{[['tap',[['receiveScan',['$event']]]]]}}" style="text-align:center;" bindtap="__e"><uni-icons vue-id="c1ea8ad0-1" type="scan" size="80" bind:__l="__l"></uni-icons></view><uni-forms class="vue-ref" vue-id="c1ea8ad0-2" modelValue="{{dataModel}}" rules="{{formRules}}" border="{{true}}" label-align="right" label-width="80" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('c1ea8ad0-3')+','+('c1ea8ad0-2')}}" label="终端号" required="{{true}}" name="sn" bind:__l="__l" vue-slots="{{['default']}}"><uni-row style=";" vue-id="{{('c1ea8ad0-4')+','+('c1ea8ad0-3')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('c1ea8ad0-5')+','+('c1ea8ad0-4')}}" span="{{17}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-6')+','+('c1ea8ad0-5')}}" type="text" size="mini" placeholder="请输入终端号" value="{{sn}}" data-event-opts="{{[['^input',[['__set_model',['','sn','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('c1ea8ad0-7')+','+('c1ea8ad0-4')}}" span="{{7}}" bind:__l="__l" vue-slots="{{['default']}}"><button class="ceshi" type="primary" plain="true" data-event-opts="{{[['tap',[['searchSn',['$event']]]]]}}" bindtap="__e">核查</button></uni-col></uni-row></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-8')+','+('c1ea8ad0-2')}}" label="资产编码" name="no" bind:__l="__l" vue-slots="{{['default']}}"><uni-row style=";" vue-id="{{('c1ea8ad0-9')+','+('c1ea8ad0-8')}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-col vue-id="{{('c1ea8ad0-10')+','+('c1ea8ad0-9')}}" span="{{17}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-11')+','+('c1ea8ad0-10')}}" type="text" size="mini" placeholder="请输入资产编码搜索资产信息" value="{{no}}" data-event-opts="{{[['^input',[['__set_model',['','no','$event',[]]]]]]}}" bind:__l="__l"></uni-easyinput></uni-col><uni-col vue-id="{{('c1ea8ad0-12')+','+('c1ea8ad0-9')}}" span="{{7}}" bind:__l="__l" vue-slots="{{['default']}}"><button class="ceshi" type="primary" plain="true" data-event-opts="{{[['tap',[['searchNo',['$event']]]]]}}" bindtap="__e">搜索</button></uni-col></uni-row></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-13')+','+('c1ea8ad0-2')}}" label="网点编码" required="{{true}}" name="locationCode" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-14')+','+('c1ea8ad0-13')}}" type="text" value="{{dataModel.locationCode}}" data-event-opts="{{[['^input',[['__set_model',['$0','locationCode','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item style="display:none;" vue-id="{{('c1ea8ad0-15')+','+('c1ea8ad0-2')}}" label="网点Id" name="location" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-16')+','+('c1ea8ad0-15')}}" type="text" value="{{dataModel.location}}" data-event-opts="{{[['^input',[['__set_model',['$0','location','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-17')+','+('c1ea8ad0-2')}}" label="网点用户" required="{{true}}" name="locationName" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-18')+','+('c1ea8ad0-17')}}" type="text" value="{{dataModel.locationName}}" data-event-opts="{{[['^input',[['__set_model',['$0','locationName','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-19')+','+('c1ea8ad0-2')}}" label="网点地址" required="{{true}}" name="locationAddress" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-20')+','+('c1ea8ad0-19')}}" type="text" value="{{dataModel.locationAddress}}" data-event-opts="{{[['^input',[['__set_model',['$0','locationAddress','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-21')+','+('c1ea8ad0-2')}}" label="联系人" required="{{true}}" name="contact" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-22')+','+('c1ea8ad0-21')}}" type="text" value="{{dataModel.contact}}" data-event-opts="{{[['^input',[['__set_model',['$0','contact','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('c1ea8ad0-23')+','+('c1ea8ad0-2')}}" label="联系电话" required="{{true}}" name="phone" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('c1ea8ad0-24')+','+('c1ea8ad0-23')}}" type="text" value="{{dataModel.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['dataModel']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><text class="caption">资产领用列表：</text><view class="list-block"><block wx:if="{{$root.g0}}"><view><block wx:for="{{doneList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item"><view class="row"><view class="label">资产编码：</view><view class="text">{{item.no}}</view></view><view class="row"><view class="label">资产名称：</view><view class="text">{{item.name}}</view></view></view></block></view></block><view class="test"></view><view class="rec"><button class="but" type="primary" data-event-opts="{{[['tap',[['saveReceive',['$event']]]]]}}" bindtap="__e">提交领用</button></view></view></view>