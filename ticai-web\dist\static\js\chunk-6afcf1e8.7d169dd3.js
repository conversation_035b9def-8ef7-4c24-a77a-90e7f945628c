(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6afcf1e8"],{"31c2":function(t,e,a){"use strict";a("9256")},5281:function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.visible?a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{visible:t.visible,title:"选择资产",width:"1000px","append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"60px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"检索："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入编码、名称",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"类型："}},[a("chosen",{staticStyle:{width:"120px"},attrs:{clearable:"",path:"/am/asset/type/list","value-field":"code","label-field":"name",placeholder:"请选择类型"},model:{value:t.qform.mold,callback:function(e){t.$set(t.qform,"mold",e)},expression:"qform.mold"}})],1),a("el-form-item",{attrs:{label:"部门/区域：","label-width":"100px"}},[a("dept-region-chosen",{staticStyle:{width:"130px"},attrs:{simple:!1,clearable:"",placeholder:"请选择部门/区域"},model:{value:t.regions,callback:function(e){t.regions=e},expression:"regions"}})],1),a("el-form-item",{attrs:{label:"终端号:"}},[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请输入终端号",autocomplete:"off"},model:{value:t.qform.sn,callback:function(e){t.$set(t.qform,"sn",e)},expression:"qform.sn"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/page",stripe:"",border:"","highlight-current-row":"","row-class-name":t.getRowClassName},on:{"selection-change":t.selectionChange,"row-click":t.clickRow}},[t.multiple?a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center",selectable:t.isSelectable}}):a("el-table-column",{attrs:{label:"",width:"45",fixed:"left",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.selectedItem&&t.selectedItem.id===e.row.id?a("el-link",{attrs:{underline:!1,type:"primary"}},[a("svg-icon",{attrs:{"icon-class":"ic_radio"}})],1):t.isSelectable(e.row)?a("el-link",{attrs:{underline:!1}},[a("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1):a("el-link",{staticStyle:{color:"#ccc"},attrs:{underline:!1}},[a("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1)]}}],null,!1,2496327718)}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"150",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getAssetStatusType(e.row),size:"mini","disable-transitions":""}},[t._v(t._s(t.getAssetStatusText(e.row)))])]}}],null,!1,2633502830)})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确 定")])],1)])],1):t._e()},i=[],s=(a("ac1f"),a("841c"),a("6ecd")),n=a("92cc"),o=a("69db"),r=a("576f"),c={components:{PageTable:s["a"],DeptRegionChosen:n["a"],Chosen:o["a"]},props:{multiple:{type:Boolean,default:!1}},data:function(){return{visible:!1,qform:{keyword:null,ignoreId:null},tag:null,regions:[],statusOptions:[],selectedItem:null,items:[],allowDamaged:!1}},watch:{regions:function(t){this.qform.region=t.length>1?t[1]:null,this.qform.dept=t.length>0?t[0]:null}},methods:{getAssetStatusType:function(t){return Object(r["c"])(t.status)},getAssetStatusText:function(t){return Object(r["b"])(t.status)},isSelectable:function(t){return!!this.allowDamaged||"7"!==t.status},getRowClassName:function(t){var e=t.row;return this.allowDamaged?"":"7"===e.status?"disabled-row":""},search:function(){this.$refs.grid.search(this.qform)},show:function(t,e){var a=this;t||(t={}),this.tag=e,this.qform.ignoreId=t.ignoreId,this.qform.status=t.status,this.qform.statusList=t.statusList,this.qform.location=t.location,this.qform.type=t.type,this.qform.ignoreDept=t.ignoreDept,this.allowDamaged=t.allowDamaged||!1,this.selectedItem=null,this.items=[],this.visible=!0,this.$refs.grid?this.$refs.grid.search(this.qform,!0):this.$nextTick((function(){return a.$refs.grid.search(a.qform,!0)}))},clickRow:function(t){this.multiple||(this.isSelectable(t)?this.selectedItem&&this.selectedItem.id===t.id?this.selectedItem=null:this.selectedItem=t:this.$message.warning("已损坏状态的资产不可选择"))},selectionChange:function(t){this.multiple&&(this.items=t||[])},confirm:function(){this.$emit("selected",this.multiple?this.items:this.selectedItem,this.tag),this.visible=!1}}},d=c,m=(a("31c2"),a("2877")),u=Object(m["a"])(d,l,i,!1,null,"3d300f83",null);e["a"]=u.exports},"576f":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return n}));var l={1:"闲置中",2:"使用中",3:"已借用",5:"维修中",6:"已锁定",7:"已损坏",8:"已报废"};function i(t){var e=[];for(var a in l)e.push({value:a,text:l[a]});return e}function s(t){switch(t){case"1":return"success";case"2":return"primary";case"3":return"warning";case"5":return"danger";case"6":return"secondary";case"7":return"warning";case"8":return"info"}return""}function n(t){return l[t]||""}},"69db":function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-select",t._g(t._b({attrs:{loading:t.loading},on:{change:t.changeMe}},"el-select",t.$attrs,!1),t.$listeners),[t.all?a("el-option",{attrs:{label:t.all,value:""}}):t._e(),t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})}))],2)},i=[],s=(a("d3b7"),a("ac1f"),a("00b4"),a("0643"),a("4e3e"),a("159b"),a("b775")),n={name:"Chosen",props:{path:{type:String,default:null},option:{type:Array,default:function(){return[]}},valueField:{type:String,default:"value"},labelField:{type:String,default:"text"},all:{type:String,default:null},isNode:{type:Boolean,default:!1}},data:function(){return{loading:!1,options:[]}},watch:{path:function(){this.load()}},mounted:function(){this.load()},methods:{load:function(){var t=this;this.path?/^\//.test(this.path)?(this.loading=!0,Object(s["a"])({url:this.path}).then((function(e){if(t.loading=!1,t.isNode){var a=[];e.forEach((function(t){return a.push({value:t.id,text:t.label,tag:t})})),t.options=a}else if(t.valueField||t.labelField){var l=[];e.forEach((function(e){l.push({value:e[t.valueField||"value"],text:e[t.labelField||"text"],tag:e})})),t.options=l}else t.options=e})).catch((function(e){t.loading=!1,console.log(e)}))):this.options=this.$store.getters.dict[this.path]:this.options=this.option},changeMe:function(t){if(null==t)this.$emit("changeItem",null);else for(var e=0;e<this.options.length;e++)if(this.options[e].value===t){this.$emit("changeItem",t,this.options[e].tag);break}}}},o=n,r=a("2877"),c=Object(r["a"])(o,l,i,!1,null,null,null);e["a"]=c.exports},9256:function(t,e,a){},"92cc":function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-cascader",t._g(t._b({attrs:{options:t.options,props:t.props},on:{change:t.changeMe}},"el-cascader",t.$attrs,!1),t.$listeners))},i=[],s=a("b775"),n={name:"DeptRegionChosen",props:{anyNode:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},simple:{type:Boolean,default:!0}},data:function(){return{options:[],props:{checkStrictly:this.anyNode,multiple:this.multiple,value:"id"}}},mounted:function(){this.load()},methods:{load:function(){var t=this;Object(s["a"])("/sys/dept/tree").then((function(e){e.length&&(t.options=e)}))},changeMe:function(t){this.simple?this.$emit("input",t&&t.length?t[t.length-1]:null):this.$emit("input",t),this.$emit("selected",t)}}},o=n,r=a("2877"),c=Object(r["a"])(o,l,i,!1,null,null,null);e["a"]=c.exports},"96e4":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:t.apply}},[t._v("维修登记申请")]),a("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=t.items.length,size:"mini",icon:"el-icon-document"},on:{click:t.preview}},[t._v("查看详情")]),a("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=t.items.length||"2"!==t.items[0].status,type:"success",size:"mini",icon:"el-icon-s-check"},on:{click:t.finish}},[t._v("完成维修登记")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.download}},[t._v("选中导出")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:t.exportQuery}},[t._v("根据条件导出")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入申请单号、说明",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"申请人："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"申请人姓名、编码",autocomplete:"off"},model:{value:t.qform.userKey,callback:function(e){t.$set(t.qform,"userKey",e)},expression:"qform.userKey"}})],1),a("el-form-item",{attrs:{label:"当前状态："}},[a("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:t.qform.status,callback:function(e){t.$set(t.qform,"status",e)},expression:"qform.status"}},t._l(t.statusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"申请时间："}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.qdate,callback:function(e){t.qdate=e},expression:"qdate"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/maintain/page",query:t.qform,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),a("el-table-column",{attrs:{label:"维修登记单号",prop:"no",width:"140",align:"center",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewItem(e.row)}}},[t._v(t._s(e.row.no))])]}}])}),a("el-table-column",{attrs:{label:"申请部门",prop:"deptName",width:"120","header-align":"center"}}),a("el-table-column",{attrs:{label:"申请人",prop:"userName",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"申请时间",prop:"time",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"预计完成时间",prop:"planTime",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"实际完成时间",prop:"realTime",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"实际维修费用",prop:"realAmount",width:"110",align:"center"}}),a("el-table-column",{attrs:{label:"维修登记说明",prop:"memo","min-width":"200","header-align":"center"}}),a("el-table-column",{attrs:{label:"明细数量",prop:"assetCount",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"small",hit:"",type:t.getStatusType(e.row),"disable-transitions":""}},[t._v(t._s(t.getStatusText(e.row)))])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"完成维修登记",width:"500px",visible:t.billVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.billVisible=e}}},[a("el-form",{ref:"billform",attrs:{model:t.billdata,rules:t.billRules,"label-width":"140px"}},[a("el-form-item",{attrs:{label:"申请单号："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.billdata.no,callback:function(e){t.$set(t.billdata,"no",e)},expression:"billdata.no"}})],1),a("el-form-item",{attrs:{label:"预计完成时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.billdata.planTime,callback:function(e){t.$set(t.billdata,"planTime",e)},expression:"billdata.planTime"}})],1),a("el-form-item",{attrs:{label:"实际完成时间：",prop:"realTime"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择实际完成时间","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.billdata.realTime,callback:function(e){t.$set(t.billdata,"realTime",e)},expression:"billdata.realTime"}})],1),a("el-form-item",{attrs:{label:"实际维修费用：",prop:"realAmount"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:t.billdata.realAmount,callback:function(e){t.$set(t.billdata,"realAmount",e)},expression:"billdata.realAmount"}},[a("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),a("el-form-item",{attrs:{label:"完成说明：",prop:"memo"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",maxlength:"200","show-word-limit":"",clearable:"",placeholder:"请输入完成说明"},model:{value:t.billdata.memo,callback:function(e){t.$set(t.billdata,"memo",e)},expression:"billdata.memo"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.billVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)],1),a("maintain-apply",{ref:"apply",on:{success:t.search}}),a("maintain-view",{ref:"view"})],1)},i=[],s=(a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"维修登记申请",width:"900px",visible:t.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("el-form",{ref:"dataform",attrs:{size:"small","label-width":"140px",model:t.form,rules:t.formRules}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"申请日期：",prop:"time"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择申请日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.form.time,callback:function(e){t.$set(t.form,"time",e)},expression:"form.time"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"预计完成日期：",prop:"planTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择预计完成日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.form.planTime,callback:function(e){t.$set(t.form,"planTime",e)},expression:"form.planTime"}})],1)],1)],1),a("el-form-item",{attrs:{label:"维修登记说明：",prop:"memo"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",maxlength:"200","show-word-limit":"",clearable:"",placeholder:"请输入维修登记说明"},model:{value:t.form.memo,callback:function(e){t.$set(t.form,"memo",e)},expression:"form.memo"}})],1)],1),a("upload-file",{attrs:{simple:"",multiple:"",type:"WX"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}}),a("div",{staticStyle:{"margin-top":"20px"}},[t._v("维修资产明细：")]),a("el-divider"),a("el-table",{attrs:{data:t.assetList,size:"small",border:""}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"150"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getAssetStatusType(e.row),size:"small"}},[t._v(t._s(t.getAssetStatusText(e.row)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),t.removeAsset(e.index)}}},[t._v("移除")])]}}])},[a("template",{slot:"header"},[a("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.addAsset}},[t._v("新 增")])],1)],2)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)],1),a("asset-chosen",{ref:"asset",attrs:{multiple:""},on:{selected:t.selectedAsset}})],1)},o=[],r=(a("a434"),a("ed08")),c=a("576f"),d=a("5281"),m=a("660a"),u={components:{AssetChosen:d["a"],UploadFile:m["a"]},data:function(){return{visible:!1,currDate:Object(r["b"])(),form:{},formRules:{time:[{required:!0,message:"请选择申请日期",trigger:"blur"}],planTime:[{required:!0,message:"请选择预计完成时间",trigger:"blur"}]},assetList:[],fileList:[]}},methods:{getAssetStatusType:function(t){return Object(c["c"])(t.status)},getAssetStatusText:function(t){return Object(c["b"])(t.status)},show:function(){this.visible=!0,this.form={time:Object(r["b"])()},this.assetList=[],this.fileList=[]},addAsset:function(){this.$refs.asset.show({status:"1"})},selectedAsset:function(t){var e=this,a={};this.assetList.forEach((function(t){a[t.id]=!0})),t.forEach((function(t){a[t.id]||e.assetList.push(t)}))},removeAsset:function(t){var e=this;this.$confirm("确定要移除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.assetList.splice(t,1)})).catch((function(){}))},save:function(){var t=this;this.$refs.dataform.validate((function(e){if(e){var a=[];if(t.assetList.forEach((function(t){return a.push({asset:t.id,dept:t.dept,region:t.region,location:t.location,useDept:t.userDept,useUser:t.useUser,assetStatus:t.status})})),!a.length)return t.$message.warning("请选择要维修登记的资产");t.form.assetList=a,t.form.fileList=t.fileList,t.$http({url:"/am/asset/maintain/apply",data:t.form}).then((function(e){e.code>0&&(t.visible=!1,t.$message.success("提交成功"),t.$emit("success"))}))}}))}}},p=u,f=a("2877"),h=Object(f["a"])(p,n,o,!1,null,null,null),b=h.exports,v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"维修登记申请信息",width:"900px",visible:t.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("el-form",{ref:"dataform",attrs:{size:"small","label-width":"140px",model:t.form}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"申请部门："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.deptName,callback:function(e){t.$set(t.form,"deptName",e)},expression:"form.deptName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"申请人："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.userName,callback:function(e){t.$set(t.form,"userName",e)},expression:"form.userName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"申请时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.time,callback:function(e){t.$set(t.form,"time",e)},expression:"form.time"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"预计完成时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.planTime,callback:function(e){t.$set(t.form,"planTime",e)},expression:"form.planTime"}})],1)],1)],1),a("el-form-item",{attrs:{label:"申请说明："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{type:"textarea",readonly:""},model:{value:t.form.memo,callback:function(e){t.$set(t.form,"memo",e)},expression:"form.memo"}})],1),t.form.realTime||t.form.fmemo?[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际完成时间："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.realTime,callback:function(e){t.$set(t.form,"realTime",e)},expression:"form.realTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际维修费用："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{readonly:""},model:{value:t.form.realAmount,callback:function(e){t.$set(t.form,"realAmount",e)},expression:"form.realAmount"}},[a("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1)],1)],1),a("el-form-item",{attrs:{label:"完成维修说明："}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},attrs:{type:"textarea",readonly:""},model:{value:t.form.fmemo,callback:function(e){t.$set(t.form,"fmemo",e)},expression:"form.fmemo"}})],1)]:t._e()],2),t.fileList.length?a("upload-file",{attrs:{multiple:"",disabled:""},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}}):t._e(),a("div",{staticStyle:{"margin-top":"10px"}},[t._v("维修登记资产明细：")]),a("el-divider"),a("el-table",{attrs:{data:t.assetList,size:"small",border:""}},[a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewAsset(e.row.id)}}},[t._v(t._s(e.row.no))])]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewAsset(e.row.id)}}},[t._v(t._s(e.row.name))])]}}])}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getAssetStatusType(e.row),size:"small"}},[t._v(t._s(t.getAssetStatusText(e.row)))])]}}])})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("关 闭")])],1)],1),a("asset-view",{ref:"assetView"})],1)},g=[],w=a("c21a"),y={components:{AssetView:w["a"],UploadFile:m["a"]},data:function(){return{visible:!1,form:{},regionText:"",assetList:[],fileList:[],attachContext:this.$store.getters.attachContext}},methods:{getAssetStatusType:function(t){return Object(c["c"])(t.status)},getAssetStatusText:function(t){return Object(c["b"])(t.status)},show:function(t){this.visible=!0,this.form=t,this.assetList=t.details||[],this.fileList=t.fileList||[]},viewAsset:function(t){this.$refs.assetView.show(t)}}},x=y,k=Object(f["a"])(x,v,g,!1,null,null,null),S=k.exports,$=a("5c96"),_={1:"审核中",2:"待完成",3:"已完成",5:"已拒绝",9:"已作废"},C={components:{PageTable:s["a"],maintainApply:b,maintainView:S},data:function(){return{fullscreenLoading:!1,statusOptions:[],qform:{keyword:null},qdate:[],items:[],billVisible:!1,billdata:{},billRules:{realTime:[{required:!0,message:"请输入实际完成时间",trigger:"blur"}],realAmount:[{required:!0,message:"请输入实际维修费用",trigger:"blur"}]}}},watch:{qdate:function(t){this.qform.begin=t&&2===t.length?t[0]:null,this.qform.end=t&&2===t.length?t[1]:null}},mounted:function(){var t=this;Object.keys(_).forEach((function(e){t.statusOptions.push({value:e,text:_[e]})})),this.search()},methods:{getStatusType:function(t){switch(t.status){case"1":return"";case"2":return"warnning";case"3":return"success";case"5":return"danger";case"9":return"info"}return""},getStatusText:function(t){return _[t.status]||""},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(t){this.items=t||[]},apply:function(){this.$refs.apply.show()},preview:function(){1===this.items.length&&this.viewItem(this.items[0])},viewItem:function(t){var e=this;this.fullscreenLoading=!0,this.$http("/am/asset/maintain/get/"+t.id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&e.$refs.view.show(t.data)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")}))},finish:function(){var t=JSON.stringify(this.items[0]);this.billdata=JSON.parse(t),this.billdata.memo="",this.billVisible=!0},save:function(){var t=this;this.$refs.billform.validate((function(e){e&&t.$http({url:"/am/asset/maintain/finish",data:t.billdata}).then((function(e){e.code>0&&(t.billVisible=!1,t.$message.success("完成维修登记成功"),t.search())}))}))},print:function(){},download:function(){var t=this;if(0===this.items.length)return this.$message.warning("至少要选择一条维修登记记录");var e=[];this.items.forEach((function(t){return e.push(t.id)}));var a=$["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/am/asset/maintain/exportBatch",data:e,responseType:"blob"}).then((function(e){a.close(),t.$saveAs(e,"资产维修登记明细.xlsx")})).catch((function(e){a.close(),t.$message.error("导出生成出错:"+e)}))},exportQuery:function(){var t=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=$["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});t.$jasper({url:"/am/asset/maintain/exportAll",data:t.qform,responseType:"blob"}).then((function(a){e.close(),t.$saveAs(a,"资产维修登记明细.xlsx")})).catch((function(a){e.close(),t.$message.error("导出生成出错:"+a)}))}))}}},N=C,A=Object(f["a"])(N,l,i,!1,null,null,null);e["default"]=A.exports},c21a:function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"},{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"custom-class":"dialog-tab dialog-full",width:"1180px",visible:t.visible,"append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[t._v("资产详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":t.activeIndex,mode:"horizontal"},on:{select:t.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[t._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[t._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[t._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[t._v("附件信息")]),a("el-menu-item",{attrs:{index:"5"}},[t._v("地理位置")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-form",{ref:"baseform",attrs:{model:t.data,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.typeName,callback:function(e){t.$set(t.data,"typeName",e)},expression:"data.typeName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.no,callback:function(e){t.$set(t.data,"no",e)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.name,callback:function(e){t.$set(t.data,"name",e)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.deptName,callback:function(e){t.$set(t.data,"deptName",e)},expression:"data.deptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.regionName,callback:function(e){t.$set(t.data,"regionName",e)},expression:"data.regionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.sn,callback:function(e){t.$set(t.data,"sn",e)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.maDate,callback:function(e){t.$set(t.data,"maDate",e)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.guDate,callback:function(e){t.$set(t.data,"guDate",e)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.productDate,callback:function(e){t.$set(t.data,"productDate",e)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.takeDate,callback:function(e){t.$set(t.data,"takeDate",e)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boDate,callback:function(e){t.$set(t.data,"boDate",e)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boAmount,callback:function(e){t.$set(t.data,"boAmount",e)},expression:"data.boAmount"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.expiryMonth,callback:function(e){t.$set(t.data,"expiryMonth",e)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[t._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.financeDate,callback:function(e){t.$set(t.data,"financeDate",e)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.value,callback:function(e){t.$set(t.data,"value",e)},expression:"data.value"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.spec,callback:function(e){t.$set(t.data,"spec",e)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.brand,callback:function(e){t.$set(t.data,"brand",e)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.manu,callback:function(e){t.$set(t.data,"manu",e)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.invoice,callback:function(e){t.$set(t.data,"invoice",e)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.seller,callback:function(e){t.$set(t.data,"seller",e)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.voucher,callback:function(e){t.$set(t.data,"voucher",e)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.price,callback:function(e){t.$set(t.data,"price",e)},expression:"data.price"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.contract,callback:function(e){t.$set(t.data,"contract",e)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.memo,callback:function(e){t.$set(t.data,"memo",e)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDeptName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useDeptName,callback:function(e){t.$set(t.data,"useDeptName",e)},expression:"data.useDeptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUserName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useUserName,callback:function(e){t.$set(t.data,"useUserName",e)},expression:"data.useUserName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最后打印：",prop:"printTime"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.printTime,callback:function(e){t.$set(t.data,"printTime",e)},expression:"data.printTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原存放地址：",prop:"fromAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromAddr,callback:function(e){t.$set(t.data,"fromAddr",e)},expression:"data.fromAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"定位地址：",prop:"locAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locAddr,callback:function(e){t.$set(t.data,"locAddr",e)},expression:"data.locAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原备注：",prop:"fromMemo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromMemo,callback:function(e){t.$set(t.data,"fromMemo",e)},expression:"data.fromMemo"}})],1)],1)],1),t.data.location?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用网点：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationName,callback:function(e){t.$set(t.data,"locationName",e)},expression:"data.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点联系人：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationContact,callback:function(e){t.$set(t.data,"locationContact",e)},expression:"data.locationContact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点电话：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationPhone,callback:function(e){t.$set(t.data,"locationPhone",e)},expression:"data.locationPhone"}})],1)],1)],1):t._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},t._l(t.fields,(function(t,e){return a("element-view-item",{key:t.renderKey,attrs:{"current-item":t,index:e}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-table",{attrs:{data:t.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:t.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:t.colRelDescr}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===t.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("upload-file",{attrs:{multiple:"",disabled:"",type:"ASSET"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===t.activeIndex,expression:"activeIndex === '5'"}],staticStyle:{"min-height":"500px"}},[a("div",{ref:"map",style:{"min-height":"500px",height:"100%"}},[null==t.lnglat?a("div",{staticStyle:{"text-align":"center","line-height":"50px","font-size":"24px"}},[t._v("该资产还未定位")]):t._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===t.activeIndex,expression:"activeIndex === '6'"}],staticStyle:{"min-height":"500px"}},[a("div",{staticStyle:{width:"400px",height:"400px",margin:"45px auto"}},[a("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.imgQr||t.defaultQr}})])])],2):t._e()],1)},i=[],s=(a("d81d"),a("b0c0"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("1cfe")),n=a("660a"),o={components:{ElementViewItem:s["a"],UploadFile:n["a"]},data:function(){return{visible:!1,loading:!1,activeIndex:"1",data:{},regionText:"",latlng:"",fields:[],relList:[],fileList:[],attachContext:this.$store.getters.attachContext,omap:null,map:{center:null,zoom:15,satellite:!1,markers:[]},currPoint:null,lnglat:null,infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)}),defaultQr:"/images/default_qr.png",imgQr:null,address:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{show:function(t){var e=this;this.visible=!0,"string"===typeof t?(this.loading=!0,this.$http("/am/asset/get/"+t).then((function(t){e.loading=!1,t.code>0&&t.data?e.showItem(t.data):e.visible=!1})).catch((function(){e.loading=!1,e.$message.error("网络超时"),e.visible=!1}))):this.showItem(t)},showItem:function(t){this.regionText=(t.regionName||"")+"/"+(t.locationName||""),this.latlng=t.lat&&t.lng?t.lat+", "+t.lng:"",this.activeIndex="1",this.data=t,this.fields=t.attr?JSON.parse(t.attr):[];var e=[];t.relList&&t.relList.forEach((function(t){switch(t.type){case"1":t.the?e.push({id:t.id,type:"2",rel:t.the,name:t.name}):e.push({id:t.id,type:"1",rel:t.rel,name:t.name});break;case"2":t.the?e.push({id:t.id,type:"4",rel:t.the,name:t.name}):e.push({id:t.id,type:"3",rel:t.rel,name:t.name})}})),this.relList=e,this.fileList=t.fileList||[],this.lnglat=t.lng&&t.lng?new window.AMap.LngLat(t.lng,t.lat):null,this.lnglat&&null==this.omap&&this.initMap()},handleSelectMenu:function(t){this.activeIndex=t,"6"===t&&this.data&&this.data.id&&(this.imgQr=this.defaultQr,this.imgQr="/api/am/asset/qrcode/"+this.data.id)},colRelType:function(t){switch(t.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(t){switch(t.type){case"1":return"当前资产包含【"+t.name+"】";case"2":return"当前资产属于【"+t.name+"】";case"3":return"当前资产安装了【"+t.name+"】";case"4":return"当前资产运行与【"+t.name+"】"}return""},initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete)}))},mapComplete:function(t){var e=this;this.currPoint=new window.AMap.Marker({position:this.lnglat,label:{direction:"top",content:this.data.name},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.omap.setCenter(this.lnglat),window.AMap.plugin("AMap.Geocoder",(function(){var t=new window.AMap.Geocoder({city:"全国"});t.getAddress([e.data.lng,e.data.lat],(function(t,a){"complete"===t&&"OK"===a.info&&a.regeocode&&(e.address=a.regeocode.formattedAddress)}))})),this.currPoint.on("click",(function(t){var a='<div style="padding:20px 15px;">';a+="<div>经度："+e.data.lng+"</div>",a+="<div>纬度："+e.data.lat+"</div>",a+="<div>定位地址："+e.address+"</div>",a+="</div>",e.infoWindow.setContent(a),e.infoWindow.open(e.omap,e.lnglat)}))}}},r=o,c=a("2877"),d=Object(c["a"])(r,l,i,!1,null,"4dd0b724",null);e["a"]=d.exports}}]);