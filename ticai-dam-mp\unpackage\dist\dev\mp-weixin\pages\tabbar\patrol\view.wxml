<view class="container"><view class="form-view"><view class="form-view-item"><label>巡检单号</label><text>{{task.no}}</text></view><view class="form-view-item"><label>巡检名称</label><text>{{task.planName}}</text></view><view class="form-view-item"><label>计划时间</label><text>{{task.timeText}}</text></view><view class="form-view-item"><label>巡检结论</label><text>{{task.result==='1'?'正常':'异常'}}</text></view><view class="form-view-item"><label>结论描述</label><text>{{task.memo}}</text></view></view><uni-file-picker bind:input="__e" vue-id="452d6414-1" title="附件" readonly="{{true}}" limit="{{6}}" fileMediatype="image" mode="grid" value="{{imageValue}}" data-event-opts="{{[['^input',[['__set_model',['','imageValue','$event',[]]]]]]}}" bind:__l="__l"></uni-file-picker></view>