(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b160d1ee"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"69db":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-select",t._g(t._b({attrs:{loading:t.loading},on:{change:t.changeMe}},"el-select",t.$attrs,!1),t.$listeners),[t.all?a("el-option",{attrs:{label:t.all,value:""}}):t._e(),t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})}))],2)},n=[],r=(a("d3b7"),a("ac1f"),a("00b4"),a("0643"),a("4e3e"),a("159b"),a("b775")),s={name:"Chosen",props:{path:{type:String,default:null},option:{type:Array,default:function(){return[]}},valueField:{type:String,default:"value"},labelField:{type:String,default:"text"},all:{type:String,default:null},isNode:{type:Boolean,default:!1}},data:function(){return{loading:!1,options:[]}},watch:{path:function(){this.load()}},mounted:function(){this.load()},methods:{load:function(){var t=this;this.path?/^\//.test(this.path)?(this.loading=!0,Object(r["a"])({url:this.path}).then((function(e){if(t.loading=!1,t.isNode){var a=[];e.forEach((function(t){return a.push({value:t.id,text:t.label,tag:t})})),t.options=a}else if(t.valueField||t.labelField){var i=[];e.forEach((function(e){i.push({value:e[t.valueField||"value"],text:e[t.labelField||"text"],tag:e})})),t.options=i}else t.options=e})).catch((function(e){t.loading=!1,console.log(e)}))):this.options=this.$store.getters.dict[this.path]:this.options=this.option},changeMe:function(t){if(null==t)this.$emit("changeItem",null);else for(var e=0;e<this.options.length;e++)if(this.options[e].value===t){this.$emit("changeItem",t,this.options[e].tag);break}}}},o=s,l=a("2877"),c=Object(l["a"])(o,i,n,!1,null,null,null);e["a"]=c.exports},"6e56":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-thumb"},on:{click:t.handle}},[t._v("申领")]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-share"},on:{click:t.send}},[t._v("指派给")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:t.batchRemove}},[t._v("批量作废")])],1)],1),a("div",{staticClass:"search",staticStyle:{width:"350px"}},[a("el-button-group",{staticStyle:{"margin-left":"30px"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入单号、名称关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"巡检人："}},[a("el-input",{staticStyle:{width:"130px"},attrs:{clearable:"",placeholder:"工号或姓名",autocomplete:"off"},model:{value:t.qform.userName,callback:function(e){t.$set(t.qform,"userName",e)},expression:"qform.userName"}})],1),a("el-form-item",{attrs:{label:"任务状态："}},[a("chosen",{staticStyle:{width:"120px"},attrs:{option:t.execStatusOption,clearable:""},model:{value:t.qform.execStatus,callback:function(e){t.$set(t.qform,"execStatus",e)},expression:"qform.execStatus"}})],1),a("el-form-item",{attrs:{label:"任务阶段："}},[a("chosen",{staticStyle:{width:"120px"},attrs:{option:t.periodStatusOption,clearable:""},model:{value:t.qform.periodStatus,callback:function(e){t.$set(t.qform,"periodStatus",e)},expression:"qform.periodStatus"}})],1),a("el-form-item",{attrs:{label:"任务时间："}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"截止日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:t.qdate,callback:function(e){t.qdate=e},expression:"qdate"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/patrol/task/page",query:t.qform,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40",align:"center"}}),a("el-table-column",{attrs:{label:"任务单号",prop:"no",width:"130",align:"center"}}),a("el-table-column",{attrs:{label:"计划名称",prop:"planName","min-width":"120"}}),a("el-table-column",{attrs:{label:"巡检对象",prop:"planType",width:"80",align:"center",formatter:t.colType}}),a("el-table-column",{attrs:{label:"巡检人",prop:"euserName",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(t._s(a.euserName||"待指派"))]}}])}),a("el-table-column",{attrs:{label:"计划起止时间",prop:"startTime",width:"160",align:"center",formatter:t.colTime}}),a("el-table-column",{attrs:{label:"任务阶段",prop:"periodStatus",width:"85",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("el-tag",{attrs:{type:t.getPeriodStatusType(i.periodStatus),size:"mini"}},[t._v(t._s(t.getPeriodStatusText(i.periodStatus)))])]}}])}),a("el-table-column",{attrs:{label:"执行状态",prop:"execStatus",width:"85",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("el-tag",{attrs:{type:t.getExecStatusType(i.execStatus),size:"mini"}},[t._v(t._s(t.getExecStatusText(i)))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"150","header-align":"center",align:"right",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[i.no&&i.euser?a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-share"},on:{click:function(e){return e.stopPropagation(),t.trnas(i)}}},[t._v("转交")]):t._e(),/1|2/.test(i.execStatus)?a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(e){return e.stopPropagation(),t.remove(i)}}},[t._v("作废")]):t._e()]}}])})],1)],1)])},n=[],r=(a("ac1f"),a("5319"),a("841c"),a("ed08")),s=a("6ecd"),o=a("69db"),l={0:"待申领",1:"待执行",2:"已执行",3:"已结束",5:"异常",8:"已作废"},c={1:"未开始",2:"即将开始",3:"已开始",4:"已结束"},u={components:{PageTable:s["a"],Chosen:o["a"]},data:function(){return{fullscreenLoading:!1,qform:{},execStatusOption:Object(r["h"])(l),periodStatusOption:Object(r["h"])(c),qdate:[],items:[]}},watch:{qdate:function(t){this.qform.begin=t[0],this.qform.end=t[1]}},mounted:function(){this.search()},methods:{colType:function(t,e,a){return{1:"路线",2:"点位",3:"资产"}[a]},getPeriodStatusText:function(t){return c[t]||""},getPeriodStatusType:function(t){return{1:"info",2:"warning",3:"primary",4:"success"}[t]},getExecStatusText:function(t){return"1"===t.execStatus&&null==t.euser?"待申领":l[t.execStatus]||""},getExecStatusType:function(t){return{1:"info",2:"primary",3:"success",5:"waring",8:"danger"}[t]},colTime:function(t){if(t.startTime){var e=t.startTime.length<11?t.startTime+" 00:00":t.startTime.replace(/:00$/,""),a=t.endTime?t.endTime.replace(/(^.+\s)|(:00$)/g,""):"00:00";return e+"~"+a}},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(t){this.items=t||[]},add:function(){this.$refs.detail.show()},edit:function(t){var e=this;this.fullscreenLoading=!0,this.$http("/am/patrol/task/get/"+t.id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&console.log(t.data)})).catch((function(){e.fullscreenLoading=!1}))},remove:function(t){var e=this;this.$confirm("确定要作废这个任务吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http("/am/patrol/task/remove/"+t.id).then((function(t){t.code>0&&(e.$message.success("发布成功"),e.search())}))})).catch((function(){}))},handle:function(){if(0===this.items.length)return this.$message.warning("请选择记录")},send:function(){if(0===this.items.length)return this.$message.warning("请选择记录")},batchRemove:function(){if(0===this.items.length)return this.$message.warning("请选择记录")}}},h=u,p=a("2877"),d=Object(p["a"])(h,i,n,!1,null,null,null);e["default"]=d.exports},"6ecd":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[t.total>1?a("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),a("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},n=[],r=a("53ca"),s=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),o={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var a=this;if(this.path){var i={pageNumber:1},n=Object(r["a"])(t);"undefined"===n?i.pageNumber=1:"number"===n?i.pageNumber=t:"object"===n?(this.params=t,"number"===typeof e&&(i.pageNumber=e),"boolean"===typeof e&&this.empty()):i.pageNumber=t.pageNumber,this.pi=i.pageNumber,this.paging&&(this.params.pageNumber=i.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(s["a"])({url:this.path,data:this.params}).then((function(t){a.loading=!1,a.paging?a.renderPage(t):a.renderList(t.rows?t.rows:t),a.$emit("loaded",t)})).catch((function(t){a.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var a=[],i=0;i<e.length;i++)e[i][t]&&a.push(e[i][t]);return a}}},l=o,c=(a("b2d4"),a("2877")),u=Object(c["a"])(l,i,n,!1,null,"bdcc19d8",null);e["a"]=u.exports},"841c":function(t,e,a){"use strict";var i=a("d784"),n=a("825a"),r=a("1d80"),s=a("129f"),o=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=r(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var r=n(t),l=String(this),c=r.lastIndex;s(c,0)||(r.lastIndex=0);var u=o(r,l);return s(r.lastIndex,c)||(r.lastIndex=c),null===u?-1:u.index}]}))},ac65:function(t,e,a){},b2d4:function(t,e,a){"use strict";a("ac65")}}]);