{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\rp\\asset\\sn.vue", "mtime": 1752649761445}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnBhZC1zdGFydC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zZWFyY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBQYWdlVGFibGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1BhZ2VUYWJsZS52dWUnOwppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCBUcmVlQm94IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9UcmVlQm94LnZ1ZSc7CmltcG9ydCBEZXRhaWxWaWV3IGZyb20gJy4vRGV0YWlsVmlldy52dWUnOwp2YXIgc3RhdHVzID0gewogICcxJzogJ+WcqOiBjCcsCiAgJzUnOiAn56a76IGMJywKICAnOCc6ICfnpoHnlKgnCn07CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBQYWdlVGFibGU6IFBhZ2VUYWJsZSwKICAgIFRyZWVCb3g6IFRyZWVCb3gsCiAgICBEZXRhaWxWaWV3OiBEZXRhaWxWaWV3CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZm9ybUxhYmVsV2lkdGg6ICcxMDBweCcsCiAgICAgIHFmb3JtOiB7CiAgICAgICAga2V5d29yZDogbnVsbCwKICAgICAgICB0aW1lQmVnaW46IG51bGwsCiAgICAgICAgdGltZUVuZDogbnVsbCwKICAgICAgICBkYXRhVHlwZTogbnVsbAogICAgICB9LAogICAgICBkYXRlUmFuZ2U6IG51bGwsCiAgICAgIGl0ZW1zOiBbXSwKICAgICAgZGVwdFRyZWU6IFtdLAogICAgICByZWdpb25PcHRpb25zOiBbXSwKICAgICAgZnVsbHNjcmVlbkxvYWRpbmc6IGZhbHNlCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMubG9hZERlcHRUcmVlKCk7CiAgICB0aGlzLmxvYWRSZWdpb25PcHRpb25zKCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5zZWFyY2goKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGxvYWREZXB0VHJlZTogZnVuY3Rpb24gbG9hZERlcHRUcmVlKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLiRodHRwKCcvc3lzL2RlcHQvdHJlZUJ5VHlwZS8xJykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMuZGVwdFRyZWUgPSByZXM7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy4kYWxlcnQoJ+WKoOi9veacuuaehOagkeWHuumUmScpOwogICAgICB9KTsKICAgIH0sCiAgICBsb2FkUmVnaW9uT3B0aW9uczogZnVuY3Rpb24gbG9hZFJlZ2lvbk9wdGlvbnMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLiRodHRwKCcvYW0vcmVnaW9uL2xpc3QnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczIucmVnaW9uT3B0aW9ucyA9IHJlcyB8fCBbXTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi4kYWxlcnQoJ+WKoOi9veWMuuWfn+WIl+ihqOWHuumUmScpOwogICAgICB9KTsKICAgIH0sCiAgICBjb2xHZW5kZXI6IGZ1bmN0aW9uIGNvbEdlbmRlcihfLCBfXywgdikgewogICAgICByZXR1cm4gdiA9PT0gJzEnID8gJ+eUtycgOiB2ID09PSAnMicgPyAn5aWzJyA6ICcnOwogICAgfSwKICAgIGZuU3RhdHVzOiBmdW5jdGlvbiBmblN0YXR1cyhfLCBfXywgdikgewogICAgICByZXR1cm4gc3RhdHVzW3ZdIHx8ICcnOwogICAgfSwKICAgIGhhbmRsZURhdGVSYW5nZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlRGF0ZVJhbmdlQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5xZm9ybS50aW1lQmVnaW4gPSB2YWxbMF07CiAgICAgICAgdGhpcy5xZm9ybS50aW1lRW5kID0gdmFsWzFdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucWZvcm0udGltZUJlZ2luID0gbnVsbDsKICAgICAgICB0aGlzLnFmb3JtLnRpbWVFbmQgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgZm9ybWF0RGF0ZVRpbWU6IGZ1bmN0aW9uIGZvcm1hdERhdGVUaW1lKGRhdGVUaW1lKSB7CiAgICAgIGlmICghZGF0ZVRpbWUpIHJldHVybiAnJzsKICAgICAgdmFyIGRhdGUgPSBuZXcgRGF0ZShkYXRlVGltZSk7CiAgICAgIHZhciB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICB2YXIgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgdmFyIGRheSA9IFN0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgdmFyIGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgdmFyIG1pbnV0ZXMgPSBTdHJpbmcoZGF0ZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIHZhciBzZWNvbmRzID0gU3RyaW5nKGRhdGUuZ2V0U2Vjb25kcygpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICByZXR1cm4gIiIuY29uY2F0KHllYXIsICItIikuY29uY2F0KG1vbnRoLCAiLSIpLmNvbmNhdChkYXksICIgIikuY29uY2F0KGhvdXJzLCAiOiIpLmNvbmNhdChtaW51dGVzLCAiOiIpLmNvbmNhdChzZWNvbmRzKTsKICAgIH0sCiAgICBzZWFyY2g6IGZ1bmN0aW9uIHNlYXJjaCgpIHsKICAgICAgdGhpcy4kcmVmcy5ncmlkLnNlYXJjaCh0aGlzLnFmb3JtKTsKICAgIH0sCiAgICBzZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIHNlbGVjdGlvbkNoYW5nZSh2KSB7CiAgICAgIHRoaXMuaXRlbXMgPSB2IHx8IFtdOwogICAgfSwKICAgIGRvd25sb2FkOiBmdW5jdGlvbiBkb3dubG9hZCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIGlmICh0aGlzLml0ZW1zLmxlbmd0aCA9PT0gMCkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Iez5bCR6KaB6YCJ5oup5LiA5p2h6LWE5Lqn6K6w5b2VJyk7CiAgICAgIHZhciBpZHMgPSBbXTsKICAgICAgdGhpcy5pdGVtcy5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgcmV0dXJuIGlkcy5wdXNoKHIuaWQpOwogICAgICB9KTsKICAgICAgdmFyIGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICBmdWxsc2NyZWVuOiB0cnVlLAogICAgICAgIHRleHQ6ICfmraPlnKjlr7zlh7rmlofku7bvvIzor7fogJDlv4PnrYnlvoUuLi4nCiAgICAgIH0pOwogICAgICB0aGlzLiRqYXNwZXIoewogICAgICAgIHVybDogJy9ycC9leHBvcnRCYXRjaCcsCiAgICAgICAgZGF0YTogaWRzLAogICAgICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKGJsb2IpIHsKICAgICAgICBsb2FkSW5zdC5jbG9zZSgpOwogICAgICAgIF90aGlzMy4kc2F2ZUFzKGJsb2IsICfotYTkuqfnu4jnq6/mnLrnu5HlrprmmI7nu4YueGxzeCcpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgbG9hZEluc3QuY2xvc2UoKTsKICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWHuueUn+aIkOWHuumUmTonICsgZXJyKTsKICAgICAgfSk7CiAgICB9LAogICAgZXhwb3J0UXVlcnk6IGZ1bmN0aW9uIGV4cG9ydFF1ZXJ5KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgn5oKo56Gu5a6a6KaB5qC55o2u6L+Z5Lqb5p2h5Lu25a+85Ye65ZCXPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICB2YXIgbG9hZEluc3QgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgICAgZnVsbHNjcmVlbjogdHJ1ZSwKICAgICAgICAgIHRleHQ6ICfmraPlnKjlr7zlh7rmlofku7bvvIzor7fogJDlv4PnrYnlvoUuLi4nCiAgICAgICAgfSk7CiAgICAgICAgX3RoaXM0LiRqYXNwZXIoewogICAgICAgICAgdXJsOiAnL3JwL2V4cG9ydEFsbCcsCiAgICAgICAgICBkYXRhOiBfdGhpczQucWZvcm0sCiAgICAgICAgICByZXNwb25zZVR5cGU6ICdibG9iJwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKGJsb2IpIHsKICAgICAgICAgIGxvYWRJbnN0LmNsb3NlKCk7CiAgICAgICAgICBfdGhpczQuJHNhdmVBcyhibG9iLCAn6LWE5Lqn57uI56uv5py657uR5a6a5piO57uGLnhsc3gnKTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpOwogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLmVycm9yKCflr7zlh7rnlJ/miJDlh7rplJk6JyArIGVycik7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGJhdGNoRGVsOiBmdW5jdGlvbiBiYXRjaERlbCgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIGlmICh0aGlzLml0ZW1zLmxlbmd0aCA9PT0gMCkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Iez5bCR6KaB6YCJ5oup5LiA5p2h6LWE5Lqn6K6w5b2VJyk7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aCqOehruWumuimgeawuOS5heaJuemHj+WIoOmZpOi/meS6m+i1hOS6p+WQlz8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIGlkcyA9IFtdOwogICAgICAgIF90aGlzNS5pdGVtcy5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgICBpZHMucHVzaChyLmlkKTsKICAgICAgICB9KTsKICAgICAgICBfdGhpczUuJGh0dHAoewogICAgICAgICAgdXJsOiAnL3JwL2RlbGV0ZXMnLAogICAgICAgICAgZGF0YTogaWRzCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7CiAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5zdWNjZXNzKCfmibnph4/liKDpmaTmiJDlip8nKTsKICAgICAgICAgICAgX3RoaXM1LnNlYXJjaCgpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgdmlld0l0ZW06IGZ1bmN0aW9uIHZpZXdJdGVtKGl0ZW0pIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIC8vIOajgOafpSBpZCDmmK/lkKbmnInmlYgKICAgICAgaWYgKCFpdGVtIHx8ICFpdGVtLmlkIHx8IGl0ZW0uaWQgPT09ICd1bmRlZmluZWQnKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5peg5pWI55qE6LWE5LqnSUTvvIzml6Dms5Xmn6XnnIvor6bmg4UnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5mdWxsc2NyZWVuTG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuJGh0dHAoJy9hbS9hc3NldC9nZXQvJyArIGl0ZW0uaWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNi5mdWxsc2NyZWVuTG9hZGluZyA9IGZhbHNlOwogICAgICAgIGlmIChyZXMuY29kZSA+IDAgJiYgcmVzLmRhdGEpIHsKICAgICAgICAgIF90aGlzNi4kcmVmcy5kZXRhaWxWaWV3LnNob3cocmVzLmRhdGEpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNi5mdWxsc2NyZWVuTG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcign572R57uc6LaF5pe2Jyk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["PageTable", "Loading", "TreeBox", "DetailView", "status", "components", "data", "form<PERSON>abe<PERSON><PERSON>", "qform", "keyword", "timeBegin", "timeEnd", "dataType", "date<PERSON><PERSON><PERSON>", "items", "deptTree", "regionOptions", "fullscreenLoading", "created", "loadDeptTree", "loadRegionOptions", "mounted", "search", "methods", "_this", "$http", "then", "res", "catch", "$alert", "_this2", "co<PERSON><PERSON><PERSON>", "_", "__", "v", "fnStatus", "handleDateRangeChange", "val", "formatDateTime", "dateTime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "$refs", "grid", "selectionChange", "download", "_this3", "length", "$message", "warning", "ids", "for<PERSON>ach", "r", "push", "id", "loadInst", "service", "fullscreen", "text", "$jasper", "url", "responseType", "blob", "close", "$saveAs", "err", "error", "exportQuery", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "batchDel", "_this5", "code", "success", "viewItem", "item", "_this6", "detailView", "show"], "sources": ["src/views/rp/asset/sn.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-header\">\r\n      <div class=\"page-tollbar\">\r\n        <div class=\"opt\">\r\n          <el-button-group>\r\n            <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"batchDel\">删除</el-button>\r\n            <el-button type=\"success\" size=\"mini\" icon=\"el-icon-download\" @click=\"download\">选中导出</el-button>\r\n            <el-button type=\"success\" size=\"mini\" icon=\"el-icon-download\" @click=\"exportQuery\">根据条件导出</el-button>\r\n          </el-button-group>\r\n        </div>\r\n        <div class=\"search\">\r\n          <el-button-group>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-search\" @click=\"search\">查询</el-button>\r\n          </el-button-group>\r\n        </div>\r\n      </div>\r\n      <div class=\"page-filter\">\r\n        <el-form :model=\"qform\" inline size=\"mini\" label-width=\"110px\" @submit.native.prevent=\"search\">\r\n          <el-form-item label=\"检索：\">\r\n            <el-input v-model=\"qform.keyword\" clearable placeholder=\"输入资产编码、名称、型号、时间、姓名等关键字\" autocomplete=\"off\"\r\n              style=\"width:360px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"数据类型：\">\r\n            <el-select v-model=\"qform.dataType\" clearable placeholder=\"请选择\" style=\"width:180px;\">\r\n              <el-option :value=\"1\" label=\"已领用已绑定数据\"></el-option>\r\n              <el-option :value=\"2\" label=\"已领用未绑定数据\"></el-option>\r\n              <el-option :value=\"3\" label=\"没有绑定设备的网点\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"网点名称：\">\r\n            <el-input v-model=\"qform.locationName\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点地址：\">\r\n            <el-input v-model=\"qform.locationAddress\" clearable autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点区域：\">\r\n            <el-select v-model=\"qform.locationRegion\" clearable placeholder=\"请选择区域\" style=\"width:150px;\">\r\n              <el-option v-for=\"item in regionOptions\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"网点备注：\">\r\n            <el-input v-model=\"qform.locationMemo\" clearable placeholder=\"请输入网点备注\" autocomplete=\"off\" style=\"width:150px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属部门：\">\r\n            <tree-box v-model=\"qform.dept\" :data=\"deptTree\" expand-all clearable style=\"width:180px;\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"网点添加时间：\" style=\"margin-left: 50px;\">\r\n            <el-date-picker v-model=\"dateRange\" type=\"daterange\" range-separator=\"至\" start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" clearable editable\r\n              style=\"width: 360px;\" @change=\"handleDateRangeChange\"></el-date-picker>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <div class=\"page-body\">\r\n      <page-table ref=\"grid\" v-table-height size=\"mini\" path=\"/rp/asset/getAssetSnPage\" stripe border\r\n        highlight-current-row @selection-change=\"selectionChange\">\r\n        <el-table-column type=\"selection\" width=\"45\" fixed=\"left\" align=\"center\" />\r\n        <el-table-column label=\"绑定姓名\" prop=\"name\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.name || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"绑定帐号\" prop=\"account\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.account || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"性别\" prop=\"gender\" width=\"50\" align=\"center\" :formatter=\"colGender\" />\r\n        <el-table-column label=\"状态\" prop=\"status\" width=\"60\" align=\"center\" :formatter=\"fnStatus\" />\r\n        <el-table-column label=\"所属机构\" prop=\"deptName\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.deptName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"销售终端编号\" prop=\"nowSn\" width=\"110\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.nowSn || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.typeName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link v-if=\"scope.row.no\" type=\"danger\" size=\"mini\" @click.stop=\"viewItem(scope.row)\">{{ scope.row.no }}</el-link>\r\n            <span v-else></span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"assetName\" min-width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.assetName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"规格型号\" prop=\"spec\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.spec || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点名称\" prop=\"locationName\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationName || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点地址\" prop=\"locationAddress\" min-width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationAddress || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点区域\" prop=\"locationRegion\" width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"网点备注\" prop=\"locationMemo\" width=\"150\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locationMemo || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"网点添加时间\" prop=\"time\" width=\"140\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"绑定时间\" prop=\"bindTime\" width=\"140\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.bindTime ? formatDateTime(scope.row.bindTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"是否定位\" prop=\"whetherLocation\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.whetherLocation || '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"定位地址\" prop=\"locAddr\" min-width=\"200\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.locAddr || '' }}\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n      <detail-view ref=\"detailView\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport { Loading } from 'element-ui'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport DetailView from './DetailView.vue'\r\n\r\nconst status = { '1': '在职', '5': '离职', '8': '禁用' }\r\n\r\nexport default {\r\n  components: { PageTable, TreeBox, DetailView },\r\n  data() {\r\n    return {\r\n      formLabelWidth: '100px',\r\n      qform: {\r\n        keyword: null,\r\n        timeBegin: null,\r\n        timeEnd: null,\r\n        dataType: null\r\n      },\r\n      dateRange: null,\r\n      items: [],\r\n      deptTree: [],\r\n      regionOptions: [],\r\n      fullscreenLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDeptTree()\r\n    this.loadRegionOptions()\r\n  },\r\n  mounted() {\r\n    this.search()\r\n  },\r\n  methods: {\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegionOptions() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionOptions = res || []\r\n      }).catch(() => { this.$alert('加载区域列表出错') })\r\n    },\r\n    colGender(_, __, v) {\r\n      return v === '1' ? '男' : v === '2' ? '女' : ''\r\n    },\r\n    fnStatus(_, __, v) {\r\n      return status[v] || ''\r\n    },\r\n    handleDateRangeChange(val) {\r\n      if (val) {\r\n        this.qform.timeBegin = val[0]\r\n        this.qform.timeEnd = val[1]\r\n      } else {\r\n        this.qform.timeBegin = null\r\n        this.qform.timeEnd = null\r\n      }\r\n    },\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    search() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    selectionChange(v) {\r\n      this.items = v || []\r\n    },\r\n    download() {\r\n      if (this.items.length === 0) return this.$message.warning('至少要选择一条资产记录')\r\n      const ids = []\r\n      this.items.forEach(r => ids.push(r.id))\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: '/rp/exportBatch', data: ids, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '资产终端机绑定明细.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    exportQuery() {\r\n      this.$confirm('您确定要根据这些条件导出吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n        this.$jasper({ url: '/rp/exportAll', data: this.qform, responseType: 'blob' }).then(blob => {\r\n          loadInst.close()\r\n          this.$saveAs(blob, '资产终端机绑定明细.xlsx')\r\n        }).catch(err => {\r\n          loadInst.close()\r\n          this.$message.error('导出生成出错:' + err)\r\n        })\r\n      })\r\n    },\r\n    batchDel() {\r\n      if (this.items.length === 0) return this.$message.warning('至少要选择一条资产记录')\r\n      this.$confirm('您确定要永久批量删除这些资产吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        const ids = []\r\n        this.items.forEach(r => { ids.push(r.id) })\r\n        this.$http({ url: '/rp/deletes', data: ids }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('批量删除成功')\r\n            this.search()\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    viewItem(item) {\r\n      // 检查 id 是否有效\r\n      if (!item || !item.id || item.id === 'undefined') {\r\n        this.$message.error('无效的资产ID，无法查看详情')\r\n        return\r\n      }\r\n      this.fullscreenLoading = true\r\n      this.$http('/am/asset/get/' + item.id).then(res => {\r\n        this.fullscreenLoading = false\r\n        if (res.code > 0 && res.data) {\r\n          this.$refs.detailView.show(res.data)\r\n        }\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n        this.$message.error('网络超时')\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJA,OAAAA,SAAA;AACA,SAAAC,OAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AAEA,IAAAC,MAAA;EAAA;EAAA;EAAA;AAAA;AAEA;EACAC,UAAA;IAAAL,SAAA,EAAAA,SAAA;IAAAE,OAAA,EAAAA,OAAA;IAAAC,UAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAJ,YAAA,WAAAA,aAAA;MAAA,IAAAK,KAAA;MACA,KAAAC,KAAA,2BAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAT,QAAA,GAAAY,GAAA;MACA,GAAAC,KAAA;QAAAJ,KAAA,CAAAK,MAAA;MAAA;IACA;IACAT,iBAAA,WAAAA,kBAAA;MAAA,IAAAU,MAAA;MACA,KAAAL,KAAA,oBAAAC,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAd,aAAA,GAAAW,GAAA;MACA,GAAAC,KAAA;QAAAE,MAAA,CAAAD,MAAA;MAAA;IACA;IACAE,SAAA,WAAAA,UAAAC,CAAA,EAAAC,EAAA,EAAAC,CAAA;MACA,OAAAA,CAAA,iBAAAA,CAAA;IACA;IACAC,QAAA,WAAAA,SAAAH,CAAA,EAAAC,EAAA,EAAAC,CAAA;MACA,OAAA9B,MAAA,CAAA8B,CAAA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAA7B,KAAA,CAAAE,SAAA,GAAA2B,GAAA;QACA,KAAA7B,KAAA,CAAAG,OAAA,GAAA0B,GAAA;MACA;QACA,KAAA7B,KAAA,CAAAE,SAAA;QACA,KAAAF,KAAA,CAAAG,OAAA;MACA;IACA;IACA2B,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACAhC,MAAA,WAAAA,OAAA;MACA,KAAAmC,KAAA,CAAAC,IAAA,CAAApC,MAAA,MAAAd,KAAA;IACA;IACAmD,eAAA,WAAAA,gBAAAzB,CAAA;MACA,KAAApB,KAAA,GAAAoB,CAAA;IACA;IACA0B,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAA/C,KAAA,CAAAgD,MAAA,oBAAAC,QAAA,CAAAC,OAAA;MACA,IAAAC,GAAA;MACA,KAAAnD,KAAA,CAAAoD,OAAA,WAAAC,CAAA;QAAA,OAAAF,GAAA,CAAAG,IAAA,CAAAD,CAAA,CAAAE,EAAA;MAAA;MACA,IAAAC,QAAA,GAAArE,OAAA,CAAAsE,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,OAAA;QAAAC,GAAA;QAAArE,IAAA,EAAA2D,GAAA;QAAAW,YAAA;MAAA,GAAAlD,IAAA,WAAAmD,IAAA;QACAP,QAAA,CAAAQ,KAAA;QACAjB,MAAA,CAAAkB,OAAA,CAAAF,IAAA;MACA,GAAAjD,KAAA,WAAAoD,GAAA;QACAV,QAAA,CAAAQ,KAAA;QACAjB,MAAA,CAAAE,QAAA,CAAAkB,KAAA,aAAAD,GAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA7D,IAAA;QACA,IAAA4C,QAAA,GAAArE,OAAA,CAAAsE,OAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACAU,MAAA,CAAAT,OAAA;UAAAC,GAAA;UAAArE,IAAA,EAAA6E,MAAA,CAAA3E,KAAA;UAAAoE,YAAA;QAAA,GAAAlD,IAAA,WAAAmD,IAAA;UACAP,QAAA,CAAAQ,KAAA;UACAK,MAAA,CAAAJ,OAAA,CAAAF,IAAA;QACA,GAAAjD,KAAA,WAAAoD,GAAA;UACAV,QAAA,CAAAQ,KAAA;UACAK,MAAA,CAAApB,QAAA,CAAAkB,KAAA,aAAAD,GAAA;QACA;MACA;IACA;IACAQ,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAA3E,KAAA,CAAAgD,MAAA,oBAAAC,QAAA,CAAAC,OAAA;MACA,KAAAoB,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA7D,IAAA;QACA,IAAAuC,GAAA;QACAwB,MAAA,CAAA3E,KAAA,CAAAoD,OAAA,WAAAC,CAAA;UAAAF,GAAA,CAAAG,IAAA,CAAAD,CAAA,CAAAE,EAAA;QAAA;QACAoB,MAAA,CAAAhE,KAAA;UAAAkD,GAAA;UAAArE,IAAA,EAAA2D;QAAA,GAAAvC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA+D,IAAA;YACAD,MAAA,CAAA1B,QAAA,CAAA4B,OAAA;YACAF,MAAA,CAAAnE,MAAA;UACA;QACA;MACA,GAAAM,KAAA,cACA;IACA;IACAgE,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAD,IAAA,KAAAA,IAAA,CAAAxB,EAAA,IAAAwB,IAAA,CAAAxB,EAAA;QACA,KAAAN,QAAA,CAAAkB,KAAA;QACA;MACA;MACA,KAAAhE,iBAAA;MACA,KAAAQ,KAAA,oBAAAoE,IAAA,CAAAxB,EAAA,EAAA3C,IAAA,WAAAC,GAAA;QACAmE,MAAA,CAAA7E,iBAAA;QACA,IAAAU,GAAA,CAAA+D,IAAA,QAAA/D,GAAA,CAAArB,IAAA;UACAwF,MAAA,CAAArC,KAAA,CAAAsC,UAAA,CAAAC,IAAA,CAAArE,GAAA,CAAArB,IAAA;QACA;MACA,GAAAsB,KAAA;QACAkE,MAAA,CAAA7E,iBAAA;QACA6E,MAAA,CAAA/B,QAAA,CAAAkB,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}