<view class="container"><uni-forms class="vue-ref" vue-id="880f66ec-1" modelValue="{{formData}}" rules="{{formRules}}" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item class="form-static" vue-id="{{('880f66ec-2')+','+('880f66ec-1')}}" label="巡检单号" bind:__l="__l" vue-slots="{{['default']}}">{{task.no}}</uni-forms-item><uni-forms-item class="form-static" vue-id="{{('880f66ec-3')+','+('880f66ec-1')}}" label="巡检名称" bind:__l="__l" vue-slots="{{['default']}}">{{task.planName}}</uni-forms-item><uni-forms-item class="form-static" vue-id="{{('880f66ec-4')+','+('880f66ec-1')}}" label="计划时间" bind:__l="__l" vue-slots="{{['default']}}">{{task.timeText}}</uni-forms-item><uni-forms-item vue-id="{{('880f66ec-5')+','+('880f66ec-1')}}" label="巡检结论" required="{{true}}" name="result" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-checkbox bind:input="__e" vue-id="{{('880f66ec-6')+','+('880f66ec-5')}}" localdata="{{resultOption}}" value="{{formData.result}}" data-event-opts="{{[['^input',[['__set_model',['$0','result','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-data-checkbox></uni-forms-item><uni-forms-item vue-id="{{('880f66ec-7')+','+('880f66ec-1')}}" label="当前位置" name="address" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('880f66ec-8')+','+('880f66ec-7')}}" type="text" prefixIcon="location" value="{{formData.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('880f66ec-9')+','+('880f66ec-1')}}" label="结论描述" name="memo" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('880f66ec-10')+','+('880f66ec-9')}}" type="textarea" placeholder="请输入结论描述" value="{{formData.memo}}" data-event-opts="{{[['^input',[['__set_model',['$0','memo','$event',[]],['formData']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms><uni-file-picker vue-id="880f66ec-11" title="附件" limit="{{6}}" fileMediatype="image" mode="grid" value="{{imageValue}}" data-event-opts="{{[['^select',[['uploadSelect']]],['^delete',[['uploadDelete']]],['^input',[['__set_model',['','imageValue','$event',[]]]]]]}}" bind:select="__e" bind:delete="__e" bind:input="__e" bind:__l="__l"></uni-file-picker><view style="margin-top:20rpx;"><button type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交结论</button></view></view>