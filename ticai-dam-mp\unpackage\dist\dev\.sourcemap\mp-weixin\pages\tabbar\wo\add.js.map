{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?4c01", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?d872", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?698b", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?9462", "uni-app:///pages/tabbar/wo/add.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?53dd", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/add.vue?118d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "dataModel", "asset", "locationName", "maintainFlag", "formRules", "contact", "rules", "required", "errorMessage", "phone", "fault", "imageValue", "fileList", "assetNo", "onLoad", "methods", "loadData", "ctx", "that", "searchAssetNo", "uploadSelect", "e", "uploadDelete", "uploadFile", "url", "filePath", "header", "name", "success", "submit"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,8UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAA2xB,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC+D/yB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;QACAC;UACAC;YACAC;YACAC;UACA;QACA;QACAC;UACAH;YACAC;YACAC;UACA;QACA;QACAE;UACAJ;YACAC;YACAC;UACA;QACA;MACA;MACAG;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACA;UACA;UACA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAF;QACA;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAE;MACA;QACA;QACAC;UAAA;QAAA;MACA;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA7B;QACA8B;QACAC;QACAC;UACA;UACA;UACA;QACA;QACAC;QACAC;UACA;UACA;UACA;UACA;UACA7B;UACAmB;QACA;MACA;IACA;IACAW;MACA;MACA;QACA;UAAAjB;QAAA;QACAM;UACAnB;QACA;QACAkB;UACA;UACAA;QACA;UAAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxLA;AAAA;AAAA;AAAA;AAAynC,CAAgB,4mCAAG,EAAC,C;;;;;;;;;;;ACA7oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/wo/add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/wo/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=d9797adc&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/wo/add.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=template&id=d9797adc&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms\" */ \"@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item\" */ \"@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-row/uni-row\" */ \"@dcloudio/uni-ui/lib/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-col/uni-col\" */ \"@dcloudio/uni-ui/lib/uni-col/uni-col.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniFilePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker\" */ \"@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function (evt) {\n      _vm.dataModel.maintainFlag = evt.detail.value\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<uni-forms ref=\"form\" :modelValue=\"dataModel\" :rules=\"formRules\" border label-align=\"right\" label-width=\"80\">\r\n\t\t\t<uni-forms-item label=\"资产编码\" name=\"assetNo\">\r\n\t\t\t\t<uni-row>\r\n\t\t\t\t\t<uni-col :span=\"17\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"text\" size=\"mini\" v-model=\"assetNo\" placeholder=\"请输入资产编码\" />\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t\t<uni-col :span=\"7\">\r\n\t\t\t\t\t\t<button type=\"primary\" class=\"ceshi\" plain=\"true\" @click=\"searchAssetNo\">核查</button>\r\n\t\t\t\t\t</uni-col>\r\n\t\t\t\t</uni-row>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点用户\" name=\"locationName\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.locationName\" placeholder=\"请输入网点用户\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"网点地址\" name=\"locationAddress\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.locationAddress\"  placeholder=\"请输入网点地址\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系人\" required name=\"contact\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.contact\" placeholder=\"请输入联系人\"/>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"联系电话\" required name=\"phone\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.phone\" placeholder=\"请输入联系电话\"/>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"规格型号\" name=\"spec\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.spec\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"序列号\" name=\"sn\">\r\n\t\t\t\t<uni-easyinput type=\"text\" v-model=\"dataModel.sn\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"材料费\" name=\"matCost\">\r\n\t\t\t\t<uni-easyinput type=\"number\" v-model=\"dataModel.matCost\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"维修费\" name=\"maiCost\">\r\n\t\t\t\t<uni-easyinput type=\"number\" v-model=\"dataModel.maiCost\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"故障现象\" required name=\"fault\">\r\n\t\t\t\t<uni-easyinput type=\"textarea\" v-model=\"dataModel.fault\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"是否保修\" name=\"maintainFlag\">\r\n\t\t\t\t<radio-group @change=\"(evt) => { dataModel.maintainFlag = evt.detail.value }\">\r\n\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t<radio value=\"1\" checked=\"true\" />是\r\n\t\t\t\t\t</label>\r\n\t\t\t\t\t<label class=\"radio\">\r\n\t\t\t\t\t\t<radio value=\"2\" />否\r\n\t\t\t\t\t</label>\r\n\t\t\t\t</radio-group>\r\n\t\t\t</uni-forms-item>\r\n\t\t\t<uni-forms-item label=\"维修情况\" name=\"solve\">\r\n\t\t\t\t<uni-easyinput type=\"textarea\" v-model=\"dataModel.solve\" />\r\n\t\t\t</uni-forms-item>\r\n\t\t</uni-forms>\r\n\t\t<uni-file-picker v-model=\"imageValue\" title=\"附件\" :limit=\"6\" fileMediatype=\"image\" mode=\"grid\"\r\n\t\t\t@select=\"uploadSelect\" @delete=\"uploadDelete\" />\r\n\t\t<view style=\"margin-top: 20rpx;\">\r\n\t\t\t<button type=\"primary\" @click=\"submit\">提交</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport settings from '../../../utils/settings.js'\r\nimport * as ctx from '../../../utils/context.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdataModel: { asset: '', locationName: '', maintainFlag: '1' },\r\n\t\t\tformRules: {\r\n\t\t\t\tcontact: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写联系人'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tphone: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写联系人电话'\r\n\t\t\t\t\t}]\r\n\t\t\t\t},\r\n\t\t\t\tfault: {\r\n\t\t\t\t\trules: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\terrorMessage: '请填写故障现象'\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageValue: [],\r\n\t\t\tfileList: [],\r\n\t\t\tassetNo: null,\r\n\t\t}\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.loadData(option.id);\r\n\t},\r\n\tmethods: {\r\n\t\tloadData(id) {\r\n\t\t\tconst that = this;\r\n\t\t\tif (id) {\r\n\t\t\t\tctx.post('/am/asset/getWithLocation/' + id, function (res) {\r\n\t\t\t\t\tif (res.code < 0 || res.data == null) return ctx.error('无法获取资产信息', 'back')\r\n\t\t\t\t\tconst asset = res.data;\r\n\t\t\t\t\t// 允许退库资产进行工单操作\r\n\t\t\t\t\tthat.dataModel.asset = asset.id;\r\n\t\t\t\t\tthat.dataModel.locationId = asset.location || '';\r\n\t\t\t\t\tthat.dataModel.locationName = asset.locationName || '';\r\n\t\t\t\t\tthat.dataModel.locationAddress = asset.locationAddress || '';\r\n\t\t\t\t\tthat.dataModel.contact = asset.locationContact || '';\r\n\t\t\t\t\tthat.dataModel.phone = asset.locationPhone || '';\r\n\t\t\t\t\tthat.dataModel.sn = asset.sn || '';\r\n\t\t\t\t\tthat.dataModel.spec = asset.spec || '';\r\n\t\t\t\t\tthat.assetNo = asset.no || '';\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tsearchAssetNo() {\r\n\t\t\tconst that = this;\r\n\t\t\tif (!this.assetNo || this.assetNo.trim() === '') {\r\n\t\t\t\treturn ctx.error('请输入资产编码');\r\n\t\t\t}\r\n\t\t\tctx.post('/wx/getByNo/' + this.assetNo, function (r) {\r\n\t\t\t\tif (r.code < 0) return ctx.error(r.msg)\r\n\t\t\t\tif (r.data == null) return ctx.error('该资产编码不存在')\r\n\t\t\t\tthat.dataModel.asset = r.data.id;\r\n\t\t\t\tthat.dataModel.locationId = r.data.location;\r\n\t\t\t\tthat.dataModel.locationName = r.data.locationName;\r\n\t\t\t\tthat.dataModel.locationAddress = r.data.locationAddress;\r\n\t\t\t\tthat.dataModel.contact = r.data.locationContact;\r\n\t\t\t\tthat.dataModel.phone = r.data.locationPhone;\r\n\t\t\t\tthat.dataModel.sn = r.data.sn;\r\n\t\t\t\tthat.dataModel.spec = r.data.spec;\r\n\t\t\t})\r\n\t\t},\r\n\t\tuploadSelect(e) {\r\n\t\t\tif (e.tempFiles) {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\te.tempFiles.forEach(file => that.uploadFile(file));\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadDelete(e) {\r\n\t\t\tfor (let i = 0; i < this.fileList.length; i++) {\r\n\t\t\t\tif (this.fileList[i].fileId === e.tempFile.uuid) {\r\n\t\t\t\t\tthis.fileList.splice(i, 1);\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tuploadFile(file) {\r\n\t\t\tconst that = this\r\n\t\t\twx.uploadFile({\r\n\t\t\t\turl: settings.api_host + '/wx/upload/GD',\r\n\t\t\t\tfilePath: file.path,\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'zy_token': wx.getStorageSync('ZY_TOKEN'),\r\n\t\t\t\t\t'appId': settings.api_id,\r\n\t\t\t\t\t'appSecret': settings.api_secret\r\n\t\t\t\t},\r\n\t\t\t\tname: 'file',\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (res.statusCode != 200) return ctx.error('上传失败')\r\n\t\t\t\t\tconst ret = JSON.parse(res.data)\r\n\t\t\t\t\tif (ret.code < 0) return ctx.error(ret.msg)\r\n\t\t\t\t\tlet data = ret.data\r\n\t\t\t\t\tdata.fileId = file.uuid\r\n\t\t\t\t\tthat.fileList.push(data)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsubmit() {\r\n\t\t\tconst that = this\r\n\t\t\tthis.$refs.form.validate().then(_ => {\r\n\t\t\t\tconst data = Object.assign({ fileList: [] }, that.dataModel)\r\n\t\t\t\tthat.fileList.forEach(r => {\r\n\t\t\t\t\tdata.fileList.push(r.id)\r\n\t\t\t\t})\r\n\t\t\t\tctx.post('/wx/wo/create', data, function (res) {\r\n\t\t\t\t\tif (res.code < 0) return ctx.error(res.msg)\r\n\t\t\t\t\tctx.ok('报障成功', 'back')\r\n\t\t\t\t}).catch(() => { ctx.error('网络异常') })\r\n\t\t\t}).catch(() => { })\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tmargin: 12rpx;\r\n}\r\n\r\n.radio {\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.ceshi {\r\n\tfont-size: 24rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\tmargin-left: 10rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752650623965\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}