(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-37bf2a98"],{4825:function(e,t,a){},"4ccd":function(e,t,a){},"5f9e":function(e,t,a){"use strict";a.r(t);var o,l,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("div",{staticClass:"left-board"},[a("div",{staticClass:"logo-wrapper"},[a("div",{staticClass:"logo"},[a("img",{attrs:{src:"logo0.png",alt:"logo"}}),e._v("表单设计器："),a("span",[e._v(" "+e._s(e.formName))])])]),a("el-scrollbar",{staticClass:"left-scrollbar"},[a("div",{staticClass:"components-list"},e._l(e.leftComponents,(function(t,o){return a("div",{key:o},[a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v(e._s(t.title))],1),a("draggable",{staticClass:"components-draggable",attrs:{list:t.list,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(t.list,(function(t,o){return a("div",{key:o,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.__config__.tagIcon}}),e._v(" "+e._s(t.__config__.label)+" ")],1)])})),0)],1)})),0)])],1),a("div",{staticClass:"center-board"},[a("div",{staticClass:"action-bar"},[a("el-button",{attrs:{icon:"el-icon-upload",size:"mini",type:"success"},on:{click:e.saveForm}},[e._v("保 存")]),a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:e.emptyForm}},[e._v("清空")])],1),a("el-scrollbar",{staticClass:"center-scrollbar"},[a("el-row",{staticClass:"center-board-row",attrs:{gutter:e.formConf.gutter}},[a("el-form",{attrs:{size:e.formConf.size,"label-position":e.formConf.labelPosition,disabled:e.formConf.disabled,"label-width":e.formConf.labelWidth+"px"}},[a("draggable",{staticClass:"drawing-board",attrs:{list:e.drawingList,animation:340,group:"componentsGroup"}},e._l(e.drawingList,(function(t,o){return a("draggable-item",{key:t.renderKey,attrs:{"drawing-list":e.drawingList,"current-item":t,index:o,"active-id":e.activeId,"form-conf":e.formConf},on:{activeItem:e.activeFormItem,copyItem:e.drawingItemCopy,deleteItem:e.drawingItemDelete}})})),1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.drawingList.length,expression:"!drawingList.length"}],staticClass:"empty-info"},[e._v("从左侧拖入或点选组件进行表单设计")])],1)],1)],1)],1),a("right-panel",{attrs:{"active-data":e.activeData,"form-conf":e.formConf,"show-field":!!e.drawingList.length},on:{"tag-change":e.tagChange,"fetch-data":e.fetchData}}),a("form-drawer",{attrs:{visible:e.drawerVisible,"form-data":e.formData,size:"100%","generate-conf":e.generateConf},on:{"update:visible":function(t){e.drawerVisible=t}}}),a("code-type-dialog",{attrs:{visible:e.dialogVisible,title:"选择生成类型","show-file-name":e.showFileName},on:{"update:visible":function(t){e.dialogVisible=t},confirm:e.generate}}),a("input",{attrs:{id:"copyNode",type:"hidden"}})],1)},n=[],c=a("53ca"),r=a("5530"),s=(a("99af"),a("7db0"),a("c740"),a("d81d"),a("13d5"),a("a434"),a("b0c0"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("0643"),a("fffc"),a("4e3e"),a("a573"),a("9d4a"),a("159b"),a("b76a")),d=a.n(s),u=a("9d32"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",e._g(e._b({on:{opened:e.onOpen,close:e.onClose}},"el-drawer",e.$attrs,!1),e.$listeners),[a("div",{staticStyle:{height:"100%"}},[a("el-row",{staticStyle:{height:"100%",overflow:"auto"}},[a("el-col",{staticClass:"left-editor",attrs:{md:24,lg:12}},[a("div",{staticClass:"setting",attrs:{title:"资源引用"},on:{click:e.showResource}},[a("el-badge",{staticClass:"item",attrs:{"is-dot":!!e.resources.length}},[a("i",{staticClass:"el-icon-setting"})])],1),a("el-tabs",{staticClass:"editor-tabs",attrs:{type:"card"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{name:"html"}},[a("span",{attrs:{slot:"label"},slot:"label"},["html"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v(" template ")])]),a("el-tab-pane",{attrs:{name:"js"}},[a("span",{attrs:{slot:"label"},slot:"label"},["js"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v(" script ")])]),a("el-tab-pane",{attrs:{name:"css"}},[a("span",{attrs:{slot:"label"},slot:"label"},["css"===e.activeTab?a("i",{staticClass:"el-icon-edit"}):a("i",{staticClass:"el-icon-document"}),e._v(" css ")])])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"html"===e.activeTab,expression:"activeTab==='html'"}],staticClass:"tab-editor",attrs:{id:"editorHtml"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:"js"===e.activeTab,expression:"activeTab==='js'"}],staticClass:"tab-editor",attrs:{id:"editorJs"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:"css"===e.activeTab,expression:"activeTab==='css'"}],staticClass:"tab-editor",attrs:{id:"editorCss"}})],1),a("el-col",{staticClass:"right-preview",attrs:{md:24,lg:12}},[a("div",{staticClass:"action-bar",style:{"text-align":"left"}},[a("span",{staticClass:"bar-btn",on:{click:e.runCode}},[a("i",{staticClass:"el-icon-refresh"}),e._v(" 刷新 ")]),a("span",{staticClass:"bar-btn",on:{click:e.exportFile}},[a("i",{staticClass:"el-icon-download"}),e._v(" 导出vue文件 ")]),a("span",{ref:"copyBtn",staticClass:"bar-btn copy-btn"},[a("i",{staticClass:"el-icon-document-copy"}),e._v(" 复制代码 ")]),a("span",{staticClass:"bar-btn delete-btn",on:{click:function(t){return e.$emit("update:visible",!1)}}},[a("i",{staticClass:"el-icon-circle-close"}),e._v(" 关闭 ")])]),a("iframe",{directives:[{name:"show",rawName:"v-show",value:e.isIframeLoaded,expression:"isIframeLoaded"}],ref:"previewPage",staticClass:"result-wrapper",attrs:{frameborder:"0",src:"preview.html"},on:{load:e.iframeLoad}}),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.isIframeLoaded,expression:"!isIframeLoaded"},{name:"loading",rawName:"v-loading",value:!0,expression:"true"}],staticClass:"result-wrapper"})])],1)],1)]),a("resource-dialog",{attrs:{visible:e.resourceVisible,"origin-resource":e.resources},on:{"update:visible":function(t){e.resourceVisible=t},save:e.setResource}})],1)},_=[],f=(a("8a79"),a("1861")),m=a("b311"),v=a.n(m),h=a("21a6"),b=(a("a15b"),a("e9c4"),a("9a9a"),{"el-input":"blur","el-input-number":"blur","el-select":"change","el-radio-group":"change","el-checkbox-group":"change","el-cascader":"change","el-time-picker":"change","el-date-picker":"change","el-rate":"change",tinymce:"blur"});function g(e){return'<el-dialog v-bind="$attrs" v-on="$listeners" @open="onOpen" @close="onClose" title="Dialog Titile">\n    '.concat(e,'\n    <div slot="footer">\n      <el-button @click="close">取消</el-button>\n      <el-button type="primary" @click="handelConfirm">确定</el-button>\n    </div>\n  </el-dialog>')}function D(e){return"<template>\n    <div>\n      ".concat(e,"\n    </div>\n  </template>")}function y(e){return"<script>\n    ".concat(e,"\n  <\/script>")}function w(e){return"<style>\n    ".concat(e,"\n  </style>")}function x(e,t,a){var o="";"right"!==e.labelPosition&&(o='label-position="'.concat(e.labelPosition,'"'));var i=e.disabled?':disabled="'.concat(e.disabled,'"'):"",n='<el-form ref="'.concat(e.formRef,'" :model="').concat(e.formModel,'" :rules="').concat(e.formRules,'" size="').concat(e.size,'" ').concat(i,' label-width="').concat(e.labelWidth,'px" ').concat(o,">\n      ").concat(t,"\n      ").concat(k(e,a),"\n    </el-form>");return l&&(n='<el-row :gutter="'.concat(e.gutter,'">\n        ').concat(n,"\n      </el-row>")),n}function k(e,t){var a="";return e.formBtns&&"file"===t&&(a='<el-form-item size="large">\n          <el-button type="primary" @click="submitForm">提交</el-button>\n          <el-button @click="resetForm">重置</el-button>\n        </el-form-item>',l&&(a='<el-col :span="24">\n          '.concat(a,"\n        </el-col>"))),a}function C(e,t){return l||24!==e.__config__.span?'<el-col :span="'.concat(e.__config__.span,'">\n      ').concat(t,"\n    </el-col>"):t}var $={colFormItem:function(e){var t=e.__config__,a="",l='label="'.concat(t.label,'"');t.labelWidth&&t.labelWidth!==o.labelWidth&&(a='label-width="'.concat(t.labelWidth,'px"')),!1===t.showLabel&&(a='label-width="0"',l="");var i=!b[t.tag]&&t.required?"required":"",n=I[t.tag]?I[t.tag](e):null,c="<el-form-item ".concat(a," ").concat(l,' prop="').concat(e.__vModel__,'" ').concat(i,">\n        ").concat(n,"\n      </el-form-item>");return c=C(e,c),c},rowFormItem:function(e){var t=e.__config__,a="default"===e.type?"":'type="'.concat(e.type,'"'),o="default"===e.type?"":'justify="'.concat(e.justify,'"'),l="default"===e.type?"":'align="'.concat(e.align,'"'),i=e.gutter?':gutter="'.concat(e.gutter,'"'):"",n=t.children.map((function(e){return $[e.__config__.layout](e)})),c="<el-row ".concat(a," ").concat(o," ").concat(l," ").concat(i,">\n      ").concat(n.join("\n"),"\n    </el-row>");return c=C(e,c),c}},I={"el-button":function(e){var t=T(e),a=t.tag,o=t.disabled,l=e.type?'type="'.concat(e.type,'"'):"",i=e.icon?'icon="'.concat(e.icon,'"'):"",n=e.round?"round":"",c=e.size?'size="'.concat(e.size,'"'):"",r=e.plain?"plain":"",s=e.circle?"circle":"",d=j(e);return d&&(d="\n".concat(d,"\n")),"<".concat(a," ").concat(l," ").concat(i," ").concat(n," ").concat(c," ").concat(r," ").concat(o," ").concat(s,">").concat(d,"</").concat(a,">")},"el-input":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.clearable,n=t.placeholder,c=t.width,r=e.maxlength?':maxlength="'.concat(e.maxlength,'"'):"",s=e["show-word-limit"]?"show-word-limit":"",d=e.readonly?"readonly":"",u=e["prefix-icon"]?"prefix-icon='".concat(e["prefix-icon"],"'"):"",p=e["suffix-icon"]?"suffix-icon='".concat(e["suffix-icon"],"'"):"",_=e["show-password"]?"show-password":"",f=e.type?'type="'.concat(e.type,'"'):"",m=e.autosize&&e.autosize.minRows?':autosize="{minRows: '.concat(e.autosize.minRows,", maxRows: ").concat(e.autosize.maxRows,'}"'):"",v=L(e);return v&&(v="\n".concat(v,"\n")),"<".concat(a," ").concat(l," ").concat(f," ").concat(n," ").concat(r," ").concat(s," ").concat(d," ").concat(o," ").concat(i," ").concat(u," ").concat(p," ").concat(_," ").concat(m," ").concat(c,">").concat(v,"</").concat(a,">")},"el-input-number":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.placeholder,n=e["controls-position"]?"controls-position=".concat(e["controls-position"]):"",c=e.min?":min='".concat(e.min,"'"):"",r=e.max?":max='".concat(e.max,"'"):"",s=e.step?":step='".concat(e.step,"'"):"",d=e["step-strictly"]?"step-strictly":"",u=e.precision?":precision='".concat(e.precision,"'"):"";return"<".concat(a," ").concat(l," ").concat(i," ").concat(s," ").concat(d," ").concat(u," ").concat(n," ").concat(c," ").concat(r," ").concat(o,"></").concat(a,">")},"el-select":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.clearable,n=t.placeholder,c=t.width,r=e.filterable?"filterable":"",s=e.multiple?"multiple":"",d=O(e);return d&&(d="\n".concat(d,"\n")),"<".concat(a," ").concat(l," ").concat(n," ").concat(o," ").concat(s," ").concat(r," ").concat(i," ").concat(c,">").concat(d,"</").concat(a,">")},"el-radio-group":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i='size="'.concat(e.size,'"'),n=z(e);return n&&(n="\n".concat(n,"\n")),"<".concat(a," ").concat(l," ").concat(i," ").concat(o,">").concat(n,"</").concat(a,">")},"el-checkbox-group":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i='size="'.concat(e.size,'"'),n=e.min?':min="'.concat(e.min,'"'):"",c=e.max?':max="'.concat(e.max,'"'):"",r=M(e);return r&&(r="\n".concat(r,"\n")),"<".concat(a," ").concat(l," ").concat(n," ").concat(c," ").concat(i," ").concat(o,">").concat(r,"</").concat(a,">")},"el-switch":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=e["active-text"]?'active-text="'.concat(e["active-text"],'"'):"",n=e["inactive-text"]?'inactive-text="'.concat(e["inactive-text"],'"'):"",c=e["active-color"]?'active-color="'.concat(e["active-color"],'"'):"",r=e["inactive-color"]?'inactive-color="'.concat(e["inactive-color"],'"'):"",s=!0!==e["active-value"]?":active-value='".concat(JSON.stringify(e["active-value"]),"'"):"",d=!1!==e["inactive-value"]?":inactive-value='".concat(JSON.stringify(e["inactive-value"]),"'"):"";return"<".concat(a," ").concat(l," ").concat(i," ").concat(n," ").concat(c," ").concat(r," ").concat(s," ").concat(d," ").concat(o,"></").concat(a,">")},"el-cascader":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.clearable,n=t.placeholder,c=t.width,r=e.options?':options="'.concat(e.__vModel__,'Options"'):"",s=e.props?':props="'.concat(e.__vModel__,'Props"'):"",d=e["show-all-levels"]?"":':show-all-levels="false"',u=e.filterable?"filterable":"",p="/"===e.separator?"":'separator="'.concat(e.separator,'"');return"<".concat(a," ").concat(l," ").concat(r," ").concat(s," ").concat(c," ").concat(d," ").concat(n," ").concat(p," ").concat(u," ").concat(i," ").concat(o,"></").concat(a,">")},"el-slider":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=e.min?":min='".concat(e.min,"'"):"",n=e.max?":max='".concat(e.max,"'"):"",c=e.step?":step='".concat(e.step,"'"):"",r=e.range?"range":"",s=e["show-stops"]?':show-stops="'.concat(e["show-stops"],'"'):"";return"<".concat(a," ").concat(i," ").concat(n," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(o,"></").concat(a,">")},"el-time-picker":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.clearable,n=t.placeholder,c=t.width,r=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",s=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",d=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",u=e["is-range"]?"is-range":"",p=e.format?'format="'.concat(e.format,'"'):"",_=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",f=e["picker-options"]?":picker-options='".concat(JSON.stringify(e["picker-options"]),"'"):"";return"<".concat(a," ").concat(l," ").concat(u," ").concat(p," ").concat(_," ").concat(f," ").concat(c," ").concat(n," ").concat(r," ").concat(s," ").concat(d," ").concat(i," ").concat(o,"></").concat(a,">")},"el-date-picker":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=t.clearable,n=t.placeholder,c=t.width,r=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",s=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",d=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",u=e.format?'format="'.concat(e.format,'"'):"",p=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",_="date"===e.type?"":'type="'.concat(e.type,'"'),f=e.readonly?"readonly":"";return"<".concat(a," ").concat(_," ").concat(l," ").concat(u," ").concat(p," ").concat(c," ").concat(n," ").concat(r," ").concat(s," ").concat(d," ").concat(i," ").concat(f," ").concat(o,"></").concat(a,">")},"el-rate":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i=e.max?":max='".concat(e.max,"'"):"",n=e["allow-half"]?"allow-half":"",c=e["show-text"]?"show-text":"",r=e["show-score"]?"show-score":"";return"<".concat(a," ").concat(l," ").concat(i," ").concat(n," ").concat(c," ").concat(r," ").concat(o,"></").concat(a,">")},"el-color-picker":function(e){var t=T(e),a=t.tag,o=t.disabled,l=t.vModel,i='size="'.concat(e.size,'"'),n=e["show-alpha"]?"show-alpha":"",c=e["color-format"]?'color-format="'.concat(e["color-format"],'"'):"";return"<".concat(a," ").concat(l," ").concat(i," ").concat(n," ").concat(c," ").concat(o,"></").concat(a,">")},"el-upload":function(e){var t=e.__config__.tag,a=e.disabled?":disabled='true'":"",o=e.action?':action="'.concat(e.__vModel__,'Action"'):"",l=e.multiple?"multiple":"",i="text"!==e["list-type"]?'list-type="'.concat(e["list-type"],'"'):"",n=e.accept?'accept="'.concat(e.accept,'"'):"",c="file"!==e.name?'name="'.concat(e.name,'"'):"",r=!1===e["auto-upload"]?':auto-upload="false"':"",s=':before-upload="'.concat(e.__vModel__,'BeforeUpload"'),d=':file-list="'.concat(e.__vModel__,'fileList"'),u='ref="'.concat(e.__vModel__,'"'),p=V(e);return p&&(p="\n".concat(p,"\n")),"<".concat(t," ").concat(u," ").concat(d," ").concat(o," ").concat(r," ").concat(l," ").concat(s," ").concat(i," ").concat(n," ").concat(c," ").concat(a,">").concat(p,"</").concat(t,">")},tinymce:function(e){var t=T(e),a=t.tag,o=t.vModel,l=t.placeholder,i=e.height?':height="'.concat(e.height,'"'):"",n=e.branding?':branding="'.concat(e.branding,'"'):"";return"<".concat(a," ").concat(o," ").concat(l," ").concat(i," ").concat(n,"></").concat(a,">")}};function T(e){return{tag:e.__config__.tag,vModel:'v-model="'.concat(o.formModel,".").concat(e.__vModel__,'"'),clearable:e.clearable?"clearable":"",placeholder:e.placeholder?'placeholder="'.concat(e.placeholder,'"'):"",width:e.style&&e.style.width?":style=\"{width: '100%'}\"":"",disabled:e.disabled?":disabled='true'":""}}function j(e){var t=[],a=e.__slot__||{};return a.default&&t.push(a.default),t.join("\n")}function L(e){var t=[],a=e.__slot__;return a&&a.prepend&&t.push('<template slot="prepend">'.concat(a.prepend,"</template>")),a&&a.append&&t.push('<template slot="append">'.concat(a.append,"</template>")),t.join("\n")}function O(e){var t=[],a=e.__slot__;return a&&a.options&&a.options.length&&t.push('<el-option v-for="(item, index) in '.concat(e.__vModel__,'Options" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>')),t.join("\n")}function z(e){var t=[],a=e.__slot__,o=e.__config__;if(a&&a.options&&a.options.length){var l="button"===o.optionType?"el-radio-button":"el-radio",i=o.border?"border":"";t.push("<".concat(l,' v-for="(item, index) in ').concat(e.__vModel__,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(i,">{{item.label}}</").concat(l,">"))}return t.join("\n")}function M(e){var t=[],a=e.__slot__,o=e.__config__;if(a&&a.options&&a.options.length){var l="button"===o.optionType?"el-checkbox-button":"el-checkbox",i=o.border?"border":"";t.push("<".concat(l,' v-for="(item, index) in ').concat(e.__vModel__,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(i,">{{item.label}}</").concat(l,">"))}return t.join("\n")}function V(e){var t=[],a=e.__config__;return"picture-card"===e["list-type"]?t.push('<i class="el-icon-plus"></i>'):t.push('<el-button size="small" type="primary" icon="el-icon-upload">'.concat(a.buttonText,"</el-button>")),a.showTip&&t.push('<div slot="tip" class="el-upload__tip">只能上传不超过 '.concat(a.fileSize).concat(a.sizeUnit," 的").concat(e.accept,"文件</div>")),t.join("\n")}function F(e,t){var a=[];o=e,l=e.fields.some((function(e){return 24!==e.__config__.span})),e.fields.forEach((function(e){a.push($[e.__config__.layout](e))}));var i=a.join("\n"),n=x(e,i,t);return"dialog"===t&&(n=g(n)),o=null,n}var N,R=a("3022"),S=a("ed08"),W={KB:"1024",MB:"1024 / 1024",GB:"1024 / 1024 / 1024"},q={file:"",dialog:"inheritAttrs: false,"};function A(e,t){N=e=Object(S["c"])(e);var a=[],o=[],l=[],i=[],n=B(t),c=[],r=[];e.fields.forEach((function(e){K(e,a,o,l,n,i,c,r)}));var s=Y(e,t,a.join("\n"),o.join("\n"),l.join("\n"),c.join("\n"),i.join("\n"),n.join("\n"),r.join("\n"));return N=null,s}function K(e,t,a,o,l,i,n,c){var r=e.__config__,s=e.__slot__;if(P(e,t),G(e,a),(e.options||s&&s.options&&s.options.length)&&(H(e,o),"dynamic"===r.dataType)){var d="".concat(e.__vModel__,"Options"),u=Object(S["j"])(d),p="get".concat(u);X(p,d,l,e),E(p,c)}e.props&&e.props.props&&J(e,i),e.action&&"el-upload"===r.tag&&(n.push("".concat(e.__vModel__,"Action: '").concat(e.action,"',\n      ").concat(e.__vModel__,"fileList: [],")),l.push(U(e)),e["auto-upload"]||l.push(Q(e))),r.children&&r.children.forEach((function(e){K(e,t,a,o,l,i,n,c)}))}function E(e,t){t.push("this.".concat(e,"()"))}function B(e){var t=[],a={file:N.formBtns?{submitForm:"submitForm() {\n        this.$refs['".concat(N.formRef,"'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },"),resetForm:"resetForm() {\n        this.$refs['".concat(N.formRef,"'].resetFields()\n      },")}:null,dialog:{onOpen:"onOpen() {},",onClose:"onClose() {\n        this.$refs['".concat(N.formRef,"'].resetFields()\n      },"),close:"close() {\n        this.$emit('update:visible', false)\n      },",handelConfirm:"handelConfirm() {\n        this.$refs['".concat(N.formRef,"'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },")}},o=a[e];return o&&Object.keys(o).forEach((function(e){t.push(o[e])})),t}function P(e,t){var a=e.__config__;if(void 0!==e.__vModel__){var o=JSON.stringify(a.defaultValue);t.push("".concat(e.__vModel__,": ").concat(o,","))}}function G(e,t){var a=e.__config__;if(void 0!==e.__vModel__){var o=[];if(b[a.tag]){if(a.required){var l=Object(R["isArray"])(a.defaultValue)?"type: 'array',":"",i=Object(R["isArray"])(a.defaultValue)?"请至少选择一个".concat(a.label):e.placeholder;void 0===i&&(i="".concat(a.label,"不能为空")),o.push("{ required: true, ".concat(l," message: '").concat(i,"', trigger: '").concat(b[a.tag],"' }"))}a.regList&&Object(R["isArray"])(a.regList)&&a.regList.forEach((function(e){if(e.pattern){var t=Function,l=new t("return "+e.pattern)();o.push("{ pattern: ".concat(l,", message: '").concat(e.message,"', trigger: '").concat(b[a.tag],"' }"))}})),t.push("".concat(e.__vModel__,": [").concat(o.join(","),"],"))}}}function H(e,t){if(void 0!==e.__vModel__){var a=e.options;a||(a=e.__slot__.options),"dynamic"===e.__config__.dataType&&(a=[]);var o="".concat(e.__vModel__,"Options: ").concat(JSON.stringify(a),",");t.push(o)}}function J(e,t){var a="".concat(e.__vModel__,"Props: ").concat(JSON.stringify(e.props.props),",");t.push(a)}function U(e){var t=e.__config__,a=W[t.sizeUnit],o="",l="",i=[];t.fileSize&&(o="let isRightSize = file.size / ".concat(a," < ").concat(t.fileSize,"\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ").concat(t.fileSize).concat(t.sizeUnit,"')\n    }"),i.push("isRightSize")),e.accept&&(l="let isAccept = new RegExp('".concat(e.accept,"').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择").concat(e.accept,"类型的文件')\n    }"),i.push("isAccept"));var n="".concat(e.__vModel__,"BeforeUpload(file) {\n    ").concat(o,"\n    ").concat(l,"\n    return ").concat(i.join("&&"),"\n  },");return i.length?n:""}function Q(e){var t="submitUpload() {\n    this.$refs['".concat(e.__vModel__,"'].submit()\n  },");return t}function X(e,t,a,o){var l=o.__config__,i="".concat(e,"() {\n    // 注意：this.$axios是通过Vue.prototype.$axios = axios挂载产生的\n    this.$axios({\n      method: '").concat(l.method,"',\n      url: '").concat(l.url,"'\n    }).then(resp => {\n      var { data } = resp\n      this.").concat(t," = data.").concat(l.dataPath,"\n    })\n  },");a.push(i)}function Y(e,t,a,o,l,i,n,c,r){var s="".concat(S["d"],"{\n  ").concat(q[t],"\n  components: {},\n  props: [],\n  data () {\n    return {\n      ").concat(e.formModel,": {\n        ").concat(a,"\n      },\n      ").concat(e.formRules,": {\n        ").concat(o,"\n      },\n      ").concat(i,"\n      ").concat(l,"\n      ").concat(n,"\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {\n    ").concat(r,"\n  },\n  mounted () {},\n  methods: {\n    ").concat(c,"\n  }\n}");return s}var Z={"el-rate":".el-rate{display: inline-block; vertical-align: text-top;}","el-upload":".el-upload__tip{line-height: 1.2;}"};function ee(e,t){var a=Z[t.__config__.tag];a&&-1===e.indexOf(a)&&e.push(a),t.__config__.children&&t.__config__.children.forEach((function(t){return ee(e,t)}))}function te(e){var t=[];return e.fields.forEach((function(e){return ee(t,e)})),t.join("\n")}var ae,oe,le=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{title:"外部资源引用",width:"600px","close-on-click-modal":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[e._l(e.resources,(function(t,o){return a("el-input",{key:o,staticClass:"url-item",attrs:{placeholder:"请输入 css 或 js 资源路径","prefix-icon":"el-icon-link",clearable:""},model:{value:e.resources[o],callback:function(t){e.$set(e.resources,o,t)},expression:"resources[index]"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-delete"},on:{click:function(t){return e.deleteOne(o)}},slot:"append"})],1)})),a("el-button-group",{staticClass:"add-item"},[a("el-button",{attrs:{plain:""},on:{click:function(t){return e.addOne("https://lib.baomitu.com/jquery/1.8.3/jquery.min.js")}}},[e._v(" jQuery1.8.3 ")]),a("el-button",{attrs:{plain:""},on:{click:function(t){return e.addOne("https://unpkg.com/http-vue-loader")}}},[e._v(" http-vue-loader ")]),a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",plain:""},on:{click:function(t){return e.addOne("")}}},[e._v(" 添加其他 ")])],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v(" 确定 ")])],1)],2)],1)},ie=[],ne=(a("4de4"),a("2382"),{components:{},inheritAttrs:!1,props:{originResource:{type:Array,default:function(){return[]}}},data:function(){return{resources:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{onOpen:function(){this.resources=this.originResource.length?Object(S["c"])(this.originResource):[""]},onClose:function(){},close:function(){this.$emit("update:visible",!1)},handelConfirm:function(){var e=this.resources.filter((function(e){return!!e}))||[];this.$emit("save",e),this.close(),e.length&&(this.resources=e)},deleteOne:function(e){this.resources.splice(e,1)},addOne:function(e){this.resources.indexOf(e)>-1?this.$message("资源已存在"):this.resources.push(e)}}}),ce=ne,re=(a("a69f"),a("2877")),se=Object(re["a"])(ce,le,ie,!1,null,"26e836f9",null),de=se.exports,ue=a("c88b"),pe=a("5c96"),_e=a.n(pe),fe=a("4771");function me(e){if(ae)e(ae);else{var t=fe["a"].monacoEditorUrl,a=_e.a.Loading.service({fullscreen:!0,lock:!0,text:"编辑器资源初始化中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.5)"});!window.require&&(window.require={}),!window.require.paths&&(window.require.paths={}),window.require.paths.vs=t,Object(ue["a"])("".concat(t,"/loader.js"),(function(){window.require(["vs/editor/editor.main"],(function(){a.close(),ae=window.monaco,e(ae)}))}))}}function ve(e){var t=fe["a"].beautifierUrl;if(oe)e(oe);else{var a=_e.a.Loading.service({fullscreen:!0,lock:!0,text:"格式化资源加载中...",spinner:"el-icon-loading",background:"rgba(255, 255, 255, 0.5)"});Object(ue["a"])(t,(function(){a.close(),oe=beautifier,e(oe)}))}}var he,be,ge,De,ye,we={html:null,js:null,css:null},xe={html:"html",js:"javascript",css:"css"},ke={components:{ResourceDialog:de},props:{formData:{type:Object,default:function(){}},generateConf:{type:Object,default:function(){}}},data:function(){return{activeTab:"html",htmlCode:"",jsCode:"",cssCode:"",codeFrame:"",isIframeLoaded:!1,isInitcode:!1,isRefreshCode:!1,resourceVisible:!1,scripts:[],links:[],monaco:null}},computed:{resources:function(){return this.scripts.concat(this.links)}},watch:{},created:function(){},mounted:function(){var e=this;window.addEventListener("keydown",this.preventDefaultSave);var t=new v.a(".copy-btn",{text:function(t){var a=e.generateCode();return e.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),a}});t.on("error",(function(t){e.$message.error("代码复制失败")}))},beforeDestroy:function(){window.removeEventListener("keydown",this.preventDefaultSave)},methods:{preventDefaultSave:function(e){"s"===e.key&&(e.metaKey||e.ctrlKey)&&e.preventDefault()},onOpen:function(){var e=this,t=this.generateConf.type;this.htmlCode=F(this.formData,t),this.jsCode=A(this.formData,t),this.cssCode=te(this.formData),ve((function(t){he=t,e.htmlCode=he.html(e.htmlCode,S["a"].html),e.jsCode=he.js(e.jsCode,S["a"].js),e.cssCode=he.css(e.cssCode,S["a"].html),me((function(t){be=t,e.setEditorValue("editorHtml","html",e.htmlCode),e.setEditorValue("editorJs","js",e.jsCode),e.setEditorValue("editorCss","css",e.cssCode),e.isInitcode||(e.isRefreshCode=!0,e.isIframeLoaded&&(e.isInitcode=!0)&&e.runCode())}))}))},onClose:function(){this.isInitcode=!1,this.isRefreshCode=!1},iframeLoad:function(){this.isInitcode||(this.isIframeLoaded=!0,this.isRefreshCode&&(this.isInitcode=!0)&&this.runCode())},setEditorValue:function(e,t,a){var o=this;we[t]?we[t].setValue(a):we[t]=be.editor.create(document.getElementById(e),{value:a,theme:"vs-dark",language:xe[t],automaticLayout:!0}),we[t].onKeyDown((function(e){49===e.keyCode&&(e.metaKey||e.ctrlKey)&&o.runCode()}))},runCode:function(){var e=we.js.getValue();try{var t=Object(f["parse"])(e,{sourceType:"module"}),a=t.program.body;if(a.length>1)return void this.$confirm("js格式不能识别，仅支持修改export default的对象内容","提示",{type:"warning"});if("ExportDefaultDeclaration"===a[0].type){var o={type:"refreshFrame",data:{generateConf:this.generateConf,html:we.html.getValue(),js:e.replace(S["d"],""),css:we.css.getValue(),scripts:this.scripts,links:this.links}};this.$refs.previewPage.contentWindow.postMessage(o,location.origin)}else this.$message.error("请使用export default")}catch(l){this.$message.error("js错误：".concat(l)),console.error(l)}},generateCode:function(){var e=D(we.html.getValue()),t=y(we.js.getValue()),a=w(we.css.getValue());return he.html(e+t+a,S["a"].html)},exportFile:function(){var e=this;this.$prompt("文件名:","导出文件",{inputValue:"".concat(+new Date,".vue"),closeOnClickModal:!1,inputPlaceholder:"请输入文件名"}).then((function(t){var a=t.value;a||(a="".concat(+new Date,".vue"));var o=e.generateCode(),l=new Blob([o],{type:"text/plain;charset=utf-8"});Object(h["saveAs"])(l,a)}))},showResource:function(){this.resourceVisible=!0},setResource:function(e){var t=[],a=[];Array.isArray(e)?(e.forEach((function(e){e.endsWith(".css")?a.push(e):t.push(e)})),this.scripts=t,this.links=a):(this.scripts=[],this.links=[])}}},Ce=ke,$e=(a("a4cc"),Object(re["a"])(Ce,p,_,!1,null,"0a0beb17",null)),Ie=$e.exports,Te=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-board"},[a("el-tabs",{staticClass:"center-tabs",model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"组件属性",name:"field"}}),a("el-tab-pane",{attrs:{label:"表单属性",name:"form"}})],1),a("div",{staticClass:"field-box"},[a("a",{staticClass:"document-link",attrs:{target:"_blank",href:e.documentLink,title:"查看组件文档"}},[a("i",{staticClass:"el-icon-link"})]),a("el-scrollbar",{staticClass:"right-scrollbar"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:"field"===e.currentTab&&e.showField,expression:"currentTab==='field' && showField"}],attrs:{size:"small","label-width":"90px"}},[e.activeData.__config__.changeTag?a("el-form-item",{attrs:{label:"组件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择组件类型"},on:{change:e.tagChange},model:{value:e.activeData.__config__.tagIcon,callback:function(t){e.$set(e.activeData.__config__,"tagIcon",t)},expression:"activeData.__config__.tagIcon"}},e._l(e.tagList,(function(t){return a("el-option-group",{key:t.label,attrs:{label:t.label}},e._l(t.options,(function(t){return a("el-option",{key:t.__config__.label,attrs:{label:t.__config__.label,value:t.__config__.tagIcon}},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":t.__config__.tagIcon}}),a("span",[e._v(" "+e._s(t.__config__.label))])],1)})),1)})),1)],1):e._e(),void 0!==e.activeData.__vModel__?a("el-form-item",{attrs:{label:"字段名"}},[a("el-input",{attrs:{placeholder:"请输入字段名（v-model）"},model:{value:e.activeData.__vModel__,callback:function(t){e.$set(e.activeData,"__vModel__",t)},expression:"activeData.__vModel__"}})],1):e._e(),void 0!==e.activeData.__config__.componentName?a("el-form-item",{attrs:{label:"组件名"}},[e._v(" "+e._s(e.activeData.__config__.componentName)+" ")]):e._e(),void 0!==e.activeData.__config__.label?a("el-form-item",{attrs:{label:"标题"}},[a("el-input",{attrs:{placeholder:"请输入标题"},on:{input:e.changeRenderKey},model:{value:e.activeData.__config__.label,callback:function(t){e.$set(e.activeData.__config__,"label",t)},expression:"activeData.__config__.label"}})],1):e._e(),void 0!==e.activeData.placeholder?a("el-form-item",{attrs:{label:"占位提示"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},on:{input:e.changeRenderKey},model:{value:e.activeData.placeholder,callback:function(t){e.$set(e.activeData,"placeholder",t)},expression:"activeData.placeholder"}})],1):e._e(),void 0!==e.activeData["start-placeholder"]?a("el-form-item",{attrs:{label:"开始占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["start-placeholder"],callback:function(t){e.$set(e.activeData,"start-placeholder",t)},expression:"activeData['start-placeholder']"}})],1):e._e(),void 0!==e.activeData["end-placeholder"]?a("el-form-item",{attrs:{label:"结束占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["end-placeholder"],callback:function(t){e.$set(e.activeData,"end-placeholder",t)},expression:"activeData['end-placeholder']"}})],1):e._e(),void 0!==e.activeData.__config__.span?a("el-form-item",{attrs:{label:"表单栅格"}},[a("el-slider",{attrs:{max:24,min:1,marks:{12:""}},on:{change:e.spanChange},model:{value:e.activeData.__config__.span,callback:function(t){e.$set(e.activeData.__config__,"span",t)},expression:"activeData.__config__.span"}})],1):e._e(),"rowFormItem"===e.activeData.__config__.layout&&void 0!==e.activeData.gutter?a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.activeData.gutter,callback:function(t){e.$set(e.activeData,"gutter",t)},expression:"activeData.gutter"}})],1):e._e(),"rowFormItem"===e.activeData.__config__.layout&&void 0!==e.activeData.type?a("el-form-item",{attrs:{label:"布局模式"}},[a("el-radio-group",{model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-radio-button",{attrs:{label:"default"}}),a("el-radio-button",{attrs:{label:"flex"}})],1)],1):e._e(),void 0!==e.activeData.justify&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"水平排列"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择水平排列"},model:{value:e.activeData.justify,callback:function(t){e.$set(e.activeData,"justify",t)},expression:"activeData.justify"}},e._l(e.justifyOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.align&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"垂直排列"}},[a("el-radio-group",{model:{value:e.activeData.align,callback:function(t){e.$set(e.activeData,"align",t)},expression:"activeData.align"}},[a("el-radio-button",{attrs:{label:"top"}}),a("el-radio-button",{attrs:{label:"middle"}}),a("el-radio-button",{attrs:{label:"bottom"}})],1)],1):e._e(),void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.activeData.__config__.labelWidth,callback:function(t){e.$set(e.activeData.__config__,"labelWidth",e._n(t))},expression:"activeData.__config__.labelWidth"}})],1):e._e(),e.activeData.style&&void 0!==e.activeData.style.width?a("el-form-item",{attrs:{label:"组件宽度"}},[a("el-input",{attrs:{placeholder:"请输入组件宽度",clearable:""},model:{value:e.activeData.style.width,callback:function(t){e.$set(e.activeData.style,"width",t)},expression:"activeData.style.width"}})],1):e._e(),void 0!==e.activeData.__vModel__?a("el-form-item",{attrs:{label:"默认值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData.__config__.defaultValue),placeholder:"请输入默认值"},on:{input:e.onDefaultValueInput}})],1):e._e(),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"至少应选"}},[a("el-input-number",{attrs:{value:e.activeData.min,min:0,placeholder:"至少应选"},on:{input:function(t){return e.$set(e.activeData,"min",t||void 0)}}})],1):e._e(),"el-checkbox-group"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"最多可选"}},[a("el-input-number",{attrs:{value:e.activeData.max,min:0,placeholder:"最多可选"},on:{input:function(t){return e.$set(e.activeData,"max",t||void 0)}}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.prepend?a("el-form-item",{attrs:{label:"前缀"}},[a("el-input",{attrs:{placeholder:"请输入前缀"},model:{value:e.activeData.__slot__.prepend,callback:function(t){e.$set(e.activeData.__slot__,"prepend",t)},expression:"activeData.__slot__.prepend"}})],1):e._e(),e.activeData.__slot__&&void 0!==e.activeData.__slot__.append?a("el-form-item",{attrs:{label:"后缀"}},[a("el-input",{attrs:{placeholder:"请输入后缀"},model:{value:e.activeData.__slot__.append,callback:function(t){e.$set(e.activeData.__slot__,"append",t)},expression:"activeData.__slot__.append"}})],1):e._e(),void 0!==e.activeData["prefix-icon"]?a("el-form-item",{attrs:{label:"前图标"}},[a("el-input",{attrs:{placeholder:"请输入前图标名称"},model:{value:e.activeData["prefix-icon"],callback:function(t){e.$set(e.activeData,"prefix-icon",t)},expression:"activeData['prefix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("prefix-icon")}},slot:"append"},[e._v(" 选择 ")])],1)],1):e._e(),void 0!==e.activeData["suffix-icon"]?a("el-form-item",{attrs:{label:"后图标"}},[a("el-input",{attrs:{placeholder:"请输入后图标名称"},model:{value:e.activeData["suffix-icon"],callback:function(t){e.$set(e.activeData,"suffix-icon",t)},expression:"activeData['suffix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("suffix-icon")}},slot:"append"},[e._v(" 选择 ")])],1)],1):e._e(),void 0!==e.activeData["icon"]&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮图标"}},[a("el-input",{attrs:{placeholder:"请输入按钮图标名称"},model:{value:e.activeData["icon"],callback:function(t){e.$set(e.activeData,"icon",t)},expression:"activeData['icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("icon")}},slot:"append"},[e._v(" 选择 ")])],1)],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"选项分隔符"}},[a("el-input",{attrs:{placeholder:"请输入选项分隔符"},model:{value:e.activeData.separator,callback:function(t){e.$set(e.activeData,"separator",t)},expression:"activeData.separator"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最小行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最小行数"},model:{value:e.activeData.autosize.minRows,callback:function(t){e.$set(e.activeData.autosize,"minRows",t)},expression:"activeData.autosize.minRows"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最大行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最大行数"},model:{value:e.activeData.autosize.maxRows,callback:function(t){e.$set(e.activeData.autosize,"maxRows",t)},expression:"activeData.autosize.maxRows"}})],1):e._e(),e.isShowMin?a("el-form-item",{attrs:{label:"最小值"}},[a("el-input-number",{attrs:{placeholder:"最小值"},model:{value:e.activeData.min,callback:function(t){e.$set(e.activeData,"min",t)},expression:"activeData.min"}})],1):e._e(),e.isShowMax?a("el-form-item",{attrs:{label:"最大值"}},[a("el-input-number",{attrs:{placeholder:"最大值"},model:{value:e.activeData.max,callback:function(t){e.$set(e.activeData,"max",t)},expression:"activeData.max"}})],1):e._e(),void 0!==e.activeData.height?a("el-form-item",{attrs:{label:"组件高度"}},[a("el-input-number",{attrs:{placeholder:"高度"},on:{input:e.changeRenderKey},model:{value:e.activeData.height,callback:function(t){e.$set(e.activeData,"height",t)},expression:"activeData.height"}})],1):e._e(),e.isShowStep?a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{attrs:{placeholder:"步数"},model:{value:e.activeData.step,callback:function(t){e.$set(e.activeData,"step",t)},expression:"activeData.step"}})],1):e._e(),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"精度"}},[a("el-input-number",{attrs:{min:0,placeholder:"精度"},model:{value:e.activeData.precision,callback:function(t){e.$set(e.activeData,"precision",t)},expression:"activeData.precision"}})],1):e._e(),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮位置"}},[a("el-radio-group",{model:{value:e.activeData["controls-position"],callback:function(t){e.$set(e.activeData,"controls-position",t)},expression:"activeData['controls-position']"}},[a("el-radio-button",{attrs:{label:""}},[e._v(" 默认 ")]),a("el-radio-button",{attrs:{label:"right"}},[e._v(" 右侧 ")])],1)],1):e._e(),void 0!==e.activeData.maxlength?a("el-form-item",{attrs:{label:"最多输入"}},[a("el-input",{attrs:{placeholder:"请输入字符长度"},model:{value:e.activeData.maxlength,callback:function(t){e.$set(e.activeData,"maxlength",t)},expression:"activeData.maxlength"}},[a("template",{slot:"append"},[e._v(" 个字符 ")])],2)],1):e._e(),void 0!==e.activeData["active-text"]?a("el-form-item",{attrs:{label:"开启提示"}},[a("el-input",{attrs:{placeholder:"请输入开启提示"},model:{value:e.activeData["active-text"],callback:function(t){e.$set(e.activeData,"active-text",t)},expression:"activeData['active-text']"}})],1):e._e(),void 0!==e.activeData["inactive-text"]?a("el-form-item",{attrs:{label:"关闭提示"}},[a("el-input",{attrs:{placeholder:"请输入关闭提示"},model:{value:e.activeData["inactive-text"],callback:function(t){e.$set(e.activeData,"inactive-text",t)},expression:"activeData['inactive-text']"}})],1):e._e(),void 0!==e.activeData["active-value"]?a("el-form-item",{attrs:{label:"开启值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["active-value"]),placeholder:"请输入开启值"},on:{input:function(t){return e.onSwitchValueInput(t,"active-value")}}})],1):e._e(),void 0!==e.activeData["inactive-value"]?a("el-form-item",{attrs:{label:"关闭值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["inactive-value"]),placeholder:"请输入关闭值"},on:{input:function(t){return e.onSwitchValueInput(t,"inactive-value")}}})],1):e._e(),void 0!==e.activeData.type&&"el-date-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"时间类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择时间类型"},on:{change:e.dateTypeChange},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},e._l(e.dateOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.name?a("el-form-item",{attrs:{label:"文件字段名"}},[a("el-input",{attrs:{placeholder:"请输入上传文件字段名"},model:{value:e.activeData.name,callback:function(t){e.$set(e.activeData,"name",t)},expression:"activeData.name"}})],1):e._e(),void 0!==e.activeData.accept?a("el-form-item",{attrs:{label:"文件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择文件类型",clearable:""},model:{value:e.activeData.accept,callback:function(t){e.$set(e.activeData,"accept",t)},expression:"activeData.accept"}},[a("el-option",{attrs:{label:"图片",value:"image/*"}}),a("el-option",{attrs:{label:"视频",value:"video/*"}}),a("el-option",{attrs:{label:"音频",value:"audio/*"}}),a("el-option",{attrs:{label:"excel",value:".xls,.xlsx"}}),a("el-option",{attrs:{label:"word",value:".doc,.docx"}}),a("el-option",{attrs:{label:"pdf",value:".pdf"}}),a("el-option",{attrs:{label:"txt",value:".txt"}})],1)],1):e._e(),void 0!==e.activeData.__config__.fileSize?a("el-form-item",{attrs:{label:"文件大小"}},[a("el-input",{attrs:{placeholder:"请输入文件大小"},model:{value:e.activeData.__config__.fileSize,callback:function(t){e.$set(e.activeData.__config__,"fileSize",e._n(t))},expression:"activeData.__config__.fileSize"}},[a("el-select",{style:{width:"66px"},attrs:{slot:"append"},slot:"append",model:{value:e.activeData.__config__.sizeUnit,callback:function(t){e.$set(e.activeData.__config__,"sizeUnit",t)},expression:"activeData.__config__.sizeUnit"}},[a("el-option",{attrs:{label:"KB",value:"KB"}}),a("el-option",{attrs:{label:"MB",value:"MB"}}),a("el-option",{attrs:{label:"GB",value:"GB"}})],1)],1)],1):e._e(),void 0!==e.activeData.action?a("el-form-item",{attrs:{label:"上传地址"}},[a("el-input",{attrs:{placeholder:"请输入上传地址",clearable:""},model:{value:e.activeData.action,callback:function(t){e.$set(e.activeData,"action",t)},expression:"activeData.action"}})],1):e._e(),void 0!==e.activeData["list-type"]?a("el-form-item",{attrs:{label:"列表类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData["list-type"],callback:function(t){e.$set(e.activeData,"list-type",t)},expression:"activeData['list-type']"}},[a("el-radio-button",{attrs:{label:"text"}},[e._v(" text ")]),a("el-radio-button",{attrs:{label:"picture"}},[e._v(" picture ")]),a("el-radio-button",{attrs:{label:"picture-card"}},[e._v(" picture-card ")])],1)],1):e._e(),void 0!==e.activeData.type&&"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮类型"}},[a("el-select",{style:{width:"100%"},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-option",{attrs:{label:"primary",value:"primary"}}),a("el-option",{attrs:{label:"success",value:"success"}}),a("el-option",{attrs:{label:"warning",value:"warning"}}),a("el-option",{attrs:{label:"danger",value:"danger"}}),a("el-option",{attrs:{label:"info",value:"info"}}),a("el-option",{attrs:{label:"text",value:"text"}})],1)],1):e._e(),void 0!==e.activeData.__config__.buttonText?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"picture-card"!==e.activeData["list-type"],expression:"'picture-card' !== activeData['list-type']"}],attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.__config__.buttonText,callback:function(t){e.$set(e.activeData.__config__,"buttonText",t)},expression:"activeData.__config__.buttonText"}})],1):e._e(),"el-button"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.__slot__.default,callback:function(t){e.$set(e.activeData.__slot__,"default",t)},expression:"activeData.__slot__.default"}})],1):e._e(),void 0!==e.activeData["range-separator"]?a("el-form-item",{attrs:{label:"分隔符"}},[a("el-input",{attrs:{placeholder:"请输入分隔符"},model:{value:e.activeData["range-separator"],callback:function(t){e.$set(e.activeData,"range-separator",t)},expression:"activeData['range-separator']"}})],1):e._e(),void 0!==e.activeData["picker-options"]?a("el-form-item",{attrs:{label:"时间段"}},[a("el-input",{attrs:{placeholder:"请输入时间段"},model:{value:e.activeData["picker-options"].selectableRange,callback:function(t){e.$set(e.activeData["picker-options"],"selectableRange",t)},expression:"activeData['picker-options'].selectableRange"}})],1):e._e(),void 0!==e.activeData.format?a("el-form-item",{attrs:{label:"时间格式"}},[a("el-input",{attrs:{value:e.activeData.format,placeholder:"请输入时间格式"},on:{input:function(t){return e.setTimeValue(t)}}})],1):e._e(),["el-checkbox-group","el-radio-group","el-select"].indexOf(e.activeData.__config__.tag)>-1?[a("el-divider",[e._v("选项")]),a("draggable",{attrs:{list:e.activeData.__slot__.options,animation:340,group:"selectItem",handle:".option-drag"}},e._l(e.activeData.__slot__.options,(function(t,o){return a("div",{key:o,staticClass:"select-item"},[a("div",{staticClass:"select-line-icon option-drag"},[a("i",{staticClass:"el-icon-s-operation"})]),a("el-input",{attrs:{placeholder:"选项名",size:"small"},model:{value:t.label,callback:function(a){e.$set(t,"label",a)},expression:"item.label"}}),a("el-input",{attrs:{placeholder:"选项值",size:"small",value:t.value},on:{input:function(a){return e.setOptionValue(t,a)}}}),a("div",{staticClass:"close-btn select-line-icon",on:{click:function(t){return e.activeData.__slot__.options.splice(o,1)}}},[a("i",{staticClass:"el-icon-remove-outline"})])],1)})),0),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addSelectItem}},[e._v(" 添加选项 ")])],1),a("el-divider")]:e._e(),["el-cascader","el-table"].includes(e.activeData.__config__.tag)?[a("el-divider",[e._v("选项")]),e.activeData.__config__.dataType?a("el-form-item",{attrs:{label:"数据类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData.__config__.dataType,callback:function(t){e.$set(e.activeData.__config__,"dataType",t)},expression:"activeData.__config__.dataType"}},[a("el-radio-button",{attrs:{label:"dynamic"}},[e._v(" 动态数据 ")]),a("el-radio-button",{attrs:{label:"static"}},[e._v(" 静态数据 ")])],1)],1):e._e(),"dynamic"===e.activeData.__config__.dataType?[a("el-form-item",{attrs:{label:"接口地址"}},[a("el-input",{attrs:{title:e.activeData.__config__.url,placeholder:"请输入接口地址",clearable:""},on:{blur:function(t){return e.$emit("fetch-data",e.activeData)}},model:{value:e.activeData.__config__.url,callback:function(t){e.$set(e.activeData.__config__,"url",t)},expression:"activeData.__config__.url"}},[a("el-select",{style:{width:"85px"},attrs:{slot:"prepend"},on:{change:function(t){return e.$emit("fetch-data",e.activeData)}},slot:"prepend",model:{value:e.activeData.__config__.method,callback:function(t){e.$set(e.activeData.__config__,"method",t)},expression:"activeData.__config__.method"}},[a("el-option",{attrs:{label:"get",value:"get"}}),a("el-option",{attrs:{label:"post",value:"post"}}),a("el-option",{attrs:{label:"put",value:"put"}}),a("el-option",{attrs:{label:"delete",value:"delete"}})],1)],1)],1),a("el-form-item",{attrs:{label:"数据位置"}},[a("el-input",{attrs:{placeholder:"请输入数据位置"},on:{blur:function(t){return e.$emit("fetch-data",e.activeData)}},model:{value:e.activeData.__config__.dataPath,callback:function(t){e.$set(e.activeData.__config__,"dataPath",t)},expression:"activeData.__config__.dataPath"}})],1),e.activeData.props&&e.activeData.props.props?[a("el-form-item",{attrs:{label:"标签键名"}},[a("el-input",{attrs:{placeholder:"请输入标签键名"},model:{value:e.activeData.props.props.label,callback:function(t){e.$set(e.activeData.props.props,"label",t)},expression:"activeData.props.props.label"}})],1),a("el-form-item",{attrs:{label:"值键名"}},[a("el-input",{attrs:{placeholder:"请输入值键名"},model:{value:e.activeData.props.props.value,callback:function(t){e.$set(e.activeData.props.props,"value",t)},expression:"activeData.props.props.value"}})],1),a("el-form-item",{attrs:{label:"子级键名"}},[a("el-input",{attrs:{placeholder:"请输入子级键名"},model:{value:e.activeData.props.props.children,callback:function(t){e.$set(e.activeData.props.props,"children",t)},expression:"activeData.props.props.children"}})],1)]:e._e()]:e._e(),"static"===e.activeData.__config__.dataType?a("el-tree",{attrs:{draggable:"",data:e.activeData.options,"node-key":"id","expand-on-click-node":!1,"render-content":e.renderContent}}):e._e(),"static"===e.activeData.__config__.dataType?a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addTreeItem}},[e._v(" 添加父级 ")])],1):e._e(),a("el-divider")]:e._e(),void 0!==e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"选项样式"}},[a("el-radio-group",{model:{value:e.activeData.__config__.optionType,callback:function(t){e.$set(e.activeData.__config__,"optionType",t)},expression:"activeData.__config__.optionType"}},[a("el-radio-button",{attrs:{label:"default"}},[e._v(" 默认 ")]),a("el-radio-button",{attrs:{label:"button"}},[e._v(" 按钮 ")])],1)],1):e._e(),void 0!==e.activeData["active-color"]?a("el-form-item",{attrs:{label:"开启颜色"}},[a("el-color-picker",{model:{value:e.activeData["active-color"],callback:function(t){e.$set(e.activeData,"active-color",t)},expression:"activeData['active-color']"}})],1):e._e(),void 0!==e.activeData["inactive-color"]?a("el-form-item",{attrs:{label:"关闭颜色"}},[a("el-color-picker",{model:{value:e.activeData["inactive-color"],callback:function(t){e.$set(e.activeData,"inactive-color",t)},expression:"activeData['inactive-color']"}})],1):e._e(),void 0!==e.activeData.__config__.showLabel&&void 0!==e.activeData.__config__.labelWidth?a("el-form-item",{attrs:{label:"显示标签"}},[a("el-switch",{model:{value:e.activeData.__config__.showLabel,callback:function(t){e.$set(e.activeData.__config__,"showLabel",t)},expression:"activeData.__config__.showLabel"}})],1):e._e(),void 0!==e.activeData.branding?a("el-form-item",{attrs:{label:"品牌烙印"}},[a("el-switch",{on:{input:e.changeRenderKey},model:{value:e.activeData.branding,callback:function(t){e.$set(e.activeData,"branding",t)},expression:"activeData.branding"}})],1):e._e(),void 0!==e.activeData["allow-half"]?a("el-form-item",{attrs:{label:"允许半选"}},[a("el-switch",{model:{value:e.activeData["allow-half"],callback:function(t){e.$set(e.activeData,"allow-half",t)},expression:"activeData['allow-half']"}})],1):e._e(),void 0!==e.activeData["show-text"]?a("el-form-item",{attrs:{label:"辅助文字"}},[a("el-switch",{on:{change:e.rateTextChange},model:{value:e.activeData["show-text"],callback:function(t){e.$set(e.activeData,"show-text",t)},expression:"activeData['show-text']"}})],1):e._e(),void 0!==e.activeData["show-score"]?a("el-form-item",{attrs:{label:"显示分数"}},[a("el-switch",{on:{change:e.rateScoreChange},model:{value:e.activeData["show-score"],callback:function(t){e.$set(e.activeData,"show-score",t)},expression:"activeData['show-score']"}})],1):e._e(),void 0!==e.activeData["show-stops"]?a("el-form-item",{attrs:{label:"显示间断点"}},[a("el-switch",{model:{value:e.activeData["show-stops"],callback:function(t){e.$set(e.activeData,"show-stops",t)},expression:"activeData['show-stops']"}})],1):e._e(),void 0!==e.activeData.range?a("el-form-item",{attrs:{label:"范围选择"}},[a("el-switch",{on:{change:e.rangeChange},model:{value:e.activeData.range,callback:function(t){e.$set(e.activeData,"range",t)},expression:"activeData.range"}})],1):e._e(),void 0!==e.activeData.__config__.border&&"default"===e.activeData.__config__.optionType?a("el-form-item",{attrs:{label:"是否带边框"}},[a("el-switch",{model:{value:e.activeData.__config__.border,callback:function(t){e.$set(e.activeData.__config__,"border",t)},expression:"activeData.__config__.border"}})],1):e._e(),"el-color-picker"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"颜色格式"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择颜色格式",clearable:""},on:{change:e.colorFormatChange},model:{value:e.activeData["color-format"],callback:function(t){e.$set(e.activeData,"color-format",t)},expression:"activeData['color-format']"}},e._l(e.colorFormatOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0===e.activeData.size||"button"!==e.activeData.__config__.optionType&&!e.activeData.__config__.border&&"el-color-picker"!==e.activeData.__config__.tag&&"el-button"!==e.activeData.__config__.tag?e._e():a("el-form-item",{attrs:{label:"组件尺寸"}},[a("el-radio-group",{model:{value:e.activeData.size,callback:function(t){e.$set(e.activeData,"size",t)},expression:"activeData.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v(" 中等 ")]),a("el-radio-button",{attrs:{label:"small"}},[e._v(" 较小 ")]),a("el-radio-button",{attrs:{label:"mini"}},[e._v(" 迷你 ")])],1)],1),void 0!==e.activeData["show-word-limit"]?a("el-form-item",{attrs:{label:"输入统计"}},[a("el-switch",{model:{value:e.activeData["show-word-limit"],callback:function(t){e.$set(e.activeData,"show-word-limit",t)},expression:"activeData['show-word-limit']"}})],1):e._e(),"el-input-number"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"严格步数"}},[a("el-switch",{model:{value:e.activeData["step-strictly"],callback:function(t){e.$set(e.activeData,"step-strictly",t)},expression:"activeData['step-strictly']"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"任选层级"}},[a("el-switch",{model:{value:e.activeData.props.props.checkStrictly,callback:function(t){e.$set(e.activeData.props.props,"checkStrictly",t)},expression:"activeData.props.props.checkStrictly"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{model:{value:e.activeData.props.props.multiple,callback:function(t){e.$set(e.activeData.props.props,"multiple",t)},expression:"activeData.props.props.multiple"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"展示全路径"}},[a("el-switch",{model:{value:e.activeData["show-all-levels"],callback:function(t){e.$set(e.activeData,"show-all-levels",t)},expression:"activeData['show-all-levels']"}})],1):e._e(),"el-cascader"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"可否筛选"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),void 0!==e.activeData.clearable?a("el-form-item",{attrs:{label:"能否清空"}},[a("el-switch",{model:{value:e.activeData.clearable,callback:function(t){e.$set(e.activeData,"clearable",t)},expression:"activeData.clearable"}})],1):e._e(),void 0!==e.activeData.__config__.showTip?a("el-form-item",{attrs:{label:"显示提示"}},[a("el-switch",{model:{value:e.activeData.__config__.showTip,callback:function(t){e.$set(e.activeData.__config__,"showTip",t)},expression:"activeData.__config__.showTip"}})],1):e._e(),"el-upload"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"多选文件"}},[a("el-switch",{model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData["auto-upload"]?a("el-form-item",{attrs:{label:"自动上传"}},[a("el-switch",{model:{value:e.activeData["auto-upload"],callback:function(t){e.$set(e.activeData,"auto-upload",t)},expression:"activeData['auto-upload']"}})],1):e._e(),void 0!==e.activeData.readonly?a("el-form-item",{attrs:{label:"是否只读"}},[a("el-switch",{model:{value:e.activeData.readonly,callback:function(t){e.$set(e.activeData,"readonly",t)},expression:"activeData.readonly"}})],1):e._e(),void 0!==e.activeData.disabled?a("el-form-item",{attrs:{label:"是否禁用"}},[a("el-switch",{model:{value:e.activeData.disabled,callback:function(t){e.$set(e.activeData,"disabled",t)},expression:"activeData.disabled"}})],1):e._e(),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"能否搜索"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),"el-select"===e.activeData.__config__.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{on:{change:e.multipleChange},model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData.__config__.required?a("el-form-item",{attrs:{label:"是否必填"}},[a("el-switch",{model:{value:e.activeData.__config__.required,callback:function(t){e.$set(e.activeData.__config__,"required",t)},expression:"activeData.__config__.required"}})],1):e._e(),e.activeData.__config__.layoutTree?[a("el-divider",[e._v("布局结构树")]),a("el-tree",{attrs:{data:[e.activeData.__config__],props:e.layoutTreeProps,"node-key":"renderKey","default-expand-all":"",draggable:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.node,l=t.data;return a("span",{},[a("span",{staticClass:"node-label"},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":l.__config__?l.__config__.tagIcon:l.tagIcon}}),e._v(" "+e._s(o.label)+" ")],1)])}}],null,!1,3924665115)})]:e._e(),Array.isArray(e.activeData.__config__.regList)?[a("el-divider",[e._v("正则校验")]),e._l(e.activeData.__config__.regList,(function(t,o){return a("div",{key:o,staticClass:"reg-item"},[a("span",{staticClass:"close-btn",on:{click:function(t){return e.activeData.__config__.regList.splice(o,1)}}},[a("i",{staticClass:"el-icon-close"})]),a("el-form-item",{attrs:{label:"表达式"}},[a("el-input",{attrs:{placeholder:"请输入正则"},model:{value:t.pattern,callback:function(a){e.$set(t,"pattern",a)},expression:"item.pattern"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"错误提示"}},[a("el-input",{attrs:{placeholder:"请输入错误提示"},model:{value:t.message,callback:function(a){e.$set(t,"message",a)},expression:"item.message"}})],1)],1)})),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addReg}},[e._v(" 添加规则 ")])],1)]:e._e()],2),a("el-form",{directives:[{name:"show",rawName:"v-show",value:"form"===e.currentTab,expression:"currentTab === 'form'"}],attrs:{size:"small","label-width":"90px"}},[a("el-form-item",{attrs:{label:"表单名"}},[a("el-input",{attrs:{placeholder:"请输入表单名（ref）"},model:{value:e.formConf.formRef,callback:function(t){e.$set(e.formConf,"formRef",t)},expression:"formConf.formRef"}})],1),a("el-form-item",{attrs:{label:"表单模型"}},[a("el-input",{attrs:{placeholder:"请输入数据模型"},model:{value:e.formConf.formModel,callback:function(t){e.$set(e.formConf,"formModel",t)},expression:"formConf.formModel"}})],1),a("el-form-item",{attrs:{label:"校验模型"}},[a("el-input",{attrs:{placeholder:"请输入校验模型"},model:{value:e.formConf.formRules,callback:function(t){e.$set(e.formConf,"formRules",t)},expression:"formConf.formRules"}})],1),a("el-form-item",{attrs:{label:"表单尺寸"}},[a("el-radio-group",{model:{value:e.formConf.size,callback:function(t){e.$set(e.formConf,"size",t)},expression:"formConf.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v(" 中等 ")]),a("el-radio-button",{attrs:{label:"small"}},[e._v(" 较小 ")]),a("el-radio-button",{attrs:{label:"mini"}},[e._v(" 迷你 ")])],1)],1),a("el-form-item",{attrs:{label:"标签对齐"}},[a("el-radio-group",{model:{value:e.formConf.labelPosition,callback:function(t){e.$set(e.formConf,"labelPosition",t)},expression:"formConf.labelPosition"}},[a("el-radio-button",{attrs:{label:"left"}},[e._v(" 左对齐 ")]),a("el-radio-button",{attrs:{label:"right"}},[e._v(" 右对齐 ")]),a("el-radio-button",{attrs:{label:"top"}},[e._v(" 顶部对齐 ")])],1)],1),a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.formConf.labelWidth,callback:function(t){e.$set(e.formConf,"labelWidth",e._n(t))},expression:"formConf.labelWidth"}})],1),a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.formConf.gutter,callback:function(t){e.$set(e.formConf,"gutter",t)},expression:"formConf.gutter"}})],1),a("el-form-item",{attrs:{label:"禁用表单"}},[a("el-switch",{model:{value:e.formConf.disabled,callback:function(t){e.$set(e.formConf,"disabled",t)},expression:"formConf.disabled"}})],1),a("el-form-item",{attrs:{label:"表单按钮"}},[a("el-switch",{model:{value:e.formConf.formBtns,callback:function(t){e.$set(e.formConf,"formBtns",t)},expression:"formConf.formBtns"}})],1),a("el-form-item",{attrs:{label:"显示未选中组件边框"}},[a("el-switch",{model:{value:e.formConf.unFocusedComponentBorder,callback:function(t){e.$set(e.formConf,"unFocusedComponentBorder",t)},expression:"formConf.unFocusedComponentBorder"}})],1)],1)],1)],1),a("treeNode-dialog",{attrs:{visible:e.dialogVisible,title:"添加选项"},on:{"update:visible":function(t){e.dialogVisible=t},commit:e.addNode}}),a("icons-dialog",{attrs:{visible:e.iconsVisible,current:e.activeData[e.currentIconModel]},on:{"update:visible":function(t){e.iconsVisible=t},select:e.setIcon}})],1)},je=[],Le=(a("caad"),a("b648")),Oe=a("4df4e"),ze={formRef:"elForm",formModel:"formData",size:"medium",labelPosition:"right",labelWidth:100,formRules:"rules",gutter:15,disabled:!1,span:24,formBtns:!0},Me=[{__config__:{label:"单行文本",labelWidth:null,showLabel:!0,changeTag:!0,tag:"el-input",tagIcon:"input",defaultValue:void 0,required:!0,layout:"colFormItem",span:24,document:"https://element.eleme.cn/#/zh-CN/component/input",regList:[]},__slot__:{prepend:"",append:""},placeholder:"请输入",style:{width:"100%"},clearable:!0,"prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"多行文本",labelWidth:null,showLabel:!0,tag:"el-input",tagIcon:"textarea",defaultValue:void 0,required:!0,layout:"colFormItem",span:24,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input"},type:"textarea",placeholder:"请输入",autosize:{minRows:4,maxRows:4},style:{width:"100%"},maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"密码",showLabel:!0,labelWidth:null,changeTag:!0,tag:"el-input",tagIcon:"password",defaultValue:void 0,layout:"colFormItem",span:24,required:!0,regList:[],document:"https://element.eleme.cn/#/zh-CN/component/input"},__slot__:{prepend:"",append:""},placeholder:"请输入","show-password":!0,style:{width:"100%"},clearable:!0,"prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1},{__config__:{label:"计数器",showLabel:!0,changeTag:!0,labelWidth:null,tag:"el-input-number",tagIcon:"number",defaultValue:void 0,span:24,layout:"colFormItem",required:!0,regList:[],document:"https://element.eleme.cn/#/zh-CN/component/input-number"},placeholder:"",min:void 0,max:void 0,step:1,"step-strictly":!1,precision:void 0,"controls-position":"",disabled:!1},{__config__:{label:"编辑器",showLabel:!0,changeTag:!0,labelWidth:null,tag:"tinymce",tagIcon:"rich-text",defaultValue:null,span:24,layout:"colFormItem",required:!0,regList:[],document:"http://tinymce.ax-z.cn"},placeholder:"请输入",height:300,branding:!1}],Ve=[{__config__:{label:"下拉选择",showLabel:!0,labelWidth:null,tag:"el-select",tagIcon:"select",layout:"colFormItem",span:24,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/select"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},placeholder:"请选择",style:{width:"100%"},clearable:!0,disabled:!1,filterable:!1,multiple:!1},{__config__:{label:"级联选择",url:"https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/cascaderList",method:"get",dataPath:"list",dataConsumer:"options",showLabel:!0,labelWidth:null,tag:"el-cascader",tagIcon:"cascader",layout:"colFormItem",defaultValue:[],dataType:"dynamic",span:24,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/cascader"},options:[{id:1,value:1,label:"选项1",children:[{id:2,value:2,label:"选项1-1"}]}],placeholder:"请选择",style:{width:"100%"},props:{props:{multiple:!1,label:"label",value:"value",children:"children"}},"show-all-levels":!0,disabled:!1,clearable:!0,filterable:!1,separator:"/"},{__config__:{label:"单选框组",labelWidth:null,showLabel:!0,tag:"el-radio-group",tagIcon:"radio",changeTag:!0,defaultValue:void 0,layout:"colFormItem",span:24,optionType:"default",regList:[],required:!0,border:!1,document:"https://element.eleme.cn/#/zh-CN/component/radio"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",disabled:!1},{__config__:{label:"多选框组",tag:"el-checkbox-group",tagIcon:"checkbox",defaultValue:[],span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",optionType:"default",required:!0,regList:[],changeTag:!0,border:!1,document:"https://element.eleme.cn/#/zh-CN/component/checkbox"},__slot__:{options:[{label:"选项一",value:1},{label:"选项二",value:2}]},style:{},size:"medium",min:null,max:null,disabled:!1},{__config__:{label:"开关",tag:"el-switch",tagIcon:"switch",defaultValue:!1,span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/switch"},style:{},disabled:!1,"active-text":"","inactive-text":"","active-color":null,"inactive-color":null,"active-value":!0,"inactive-value":!1},{__config__:{label:"滑块",tag:"el-slider",tagIcon:"slider",defaultValue:null,span:24,showLabel:!0,layout:"colFormItem",labelWidth:null,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/slider"},disabled:!1,min:0,max:100,step:1,"show-stops":!1,range:!1},{__config__:{label:"时间选择",tag:"el-time-picker",tagIcon:"time",defaultValue:null,span:24,showLabel:!0,layout:"colFormItem",labelWidth:null,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},placeholder:"请选择",style:{width:"100%"},disabled:!1,clearable:!0,"picker-options":{selectableRange:"00:00:00-23:59:59"},format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"时间范围",tag:"el-time-picker",tagIcon:"time-range",span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",defaultValue:null,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},style:{width:"100%"},disabled:!1,clearable:!0,"is-range":!0,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},{__config__:{label:"日期选择",tag:"el-date-picker",tagIcon:"date",defaultValue:null,showLabel:!0,labelWidth:null,span:24,layout:"colFormItem",required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},placeholder:"请选择",type:"date",style:{width:"100%"},disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1},{__config__:{label:"日期范围",tag:"el-date-picker",tagIcon:"date-range",defaultValue:null,span:24,showLabel:!0,labelWidth:null,required:!0,layout:"colFormItem",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},style:{width:"100%"},type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:!1,clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1},{__config__:{label:"评分",tag:"el-rate",tagIcon:"rate",defaultValue:0,span:24,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/rate"},style:{},max:5,"allow-half":!1,"show-text":!1,"show-score":!1,disabled:!1},{__config__:{label:"颜色选择",tag:"el-color-picker",tagIcon:"color",span:24,defaultValue:null,showLabel:!0,labelWidth:null,layout:"colFormItem",required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/color-picker"},"show-alpha":!1,"color-format":"",disabled:!1,size:"medium"},{__config__:{label:"上传",tag:"el-upload",tagIcon:"upload",layout:"colFormItem",defaultValue:null,showLabel:!0,labelWidth:null,required:!0,span:24,showTip:!1,buttonText:"点击上传",regList:[],changeTag:!0,fileSize:2,sizeUnit:"MB",document:"https://element.eleme.cn/#/zh-CN/component/upload"},__slot__:{"list-type":!0},action:"https://jsonplaceholder.typicode.com/posts/",disabled:!1,accept:"",name:"file","auto-upload":!0,"list-type":"text",multiple:!1}],Fe=[{__config__:{layout:"rowFormItem",tagIcon:"row",label:"行容器",layoutTree:!0,document:"https://element.eleme.cn/#/zh-CN/component/layout#row-attributes"},type:"default",justify:"start",align:"top"},{__config__:{label:"按钮",showLabel:!0,changeTag:!0,labelWidth:null,tag:"el-button",tagIcon:"button",span:24,layout:"colFormItem",document:"https://element.eleme.cn/#/zh-CN/component/button"},__slot__:{default:"主要按钮"},type:"primary",icon:"el-icon-search",round:!1,size:"medium",plain:!1,circle:!1,disabled:!1},{__config__:{layout:"colFormItem",tagIcon:"table",tag:"el-table",document:"https://element.eleme.cn/#/zh-CN/component/table",span:24,formId:101,renderKey:1595761764203,componentName:"row101",showLabel:!0,changeTag:!0,labelWidth:null,label:"表格[开发中]",dataType:"dynamic",method:"get",dataPath:"list",dataConsumer:"data",url:"https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/tableData",children:[{__config__:{layout:"raw",tag:"el-table-column",renderKey:0xe836c21d0f9},prop:"date",label:"日期"},{__config__:{layout:"raw",tag:"el-table-column",renderKey:0xe836c21d0f8},prop:"address",label:"地址"},{__config__:{layout:"raw",tag:"el-table-column",renderKey:0xe836c21d0f7},prop:"name",label:"名称"},{__config__:{layout:"raw",tag:"el-table-column",renderKey:1595774496335,children:[{__config__:{label:"按钮",tag:"el-button",tagIcon:"button",layout:"raw",renderKey:1595779809901},__slot__:{default:"主要按钮"},type:"primary",icon:"el-icon-search",round:!1,size:"medium"}]},label:"操作"}]},data:[],directives:[{name:"loading",value:!0}],border:!0,type:"default",justify:"start",align:"top"}],Ne=a("e31c"),Re={date:"yyyy-MM-dd",week:"yyyy 第 WW 周",month:"yyyy-MM",year:"yyyy",datetime:"yyyy-MM-dd HH:mm:ss",daterange:"yyyy-MM-dd",monthrange:"yyyy-MM",datetimerange:"yyyy-MM-dd HH:mm:ss"},Se=["tinymce"],We={components:{draggable:d.a,TreeNodeDialog:Le["a"],IconsDialog:Oe["a"]},props:{showField:{type:Boolean,default:!1},activeData:{type:Object,default:function(){}},formConf:{type:Object,default:function(){}}},data:function(){return{currentTab:"field",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:"日(date)",value:"date"},{label:"周(week)",value:"week"},{label:"月(month)",value:"month"},{label:"年(year)",value:"year"},{label:"日期时间(datetime)",value:"datetime"}],dateRangeTypeOptions:[{label:"日期范围(daterange)",value:"daterange"},{label:"月范围(monthrange)",value:"monthrange"},{label:"日期时间范围(datetimerange)",value:"datetimerange"}],colorFormatOptions:[{label:"hex",value:"hex"},{label:"rgb",value:"rgb"},{label:"rgba",value:"rgba"},{label:"hsv",value:"hsv"},{label:"hsl",value:"hsl"}],justifyOptions:[{label:"start",value:"start"},{label:"end",value:"end"},{label:"center",value:"center"},{label:"space-around",value:"space-around"},{label:"space-between",value:"space-between"}],layoutTreeProps:{label:function(e,t){var a=e.__config__;return e.componentName||"".concat(a.label,": ").concat(e.__vModel__)}}}},computed:{documentLink:function(){return this.activeData.__config__.document||"https://element.eleme.cn/#/zh-CN/component/installation"},dateOptions:function(){return void 0!==this.activeData.type&&"el-date-picker"===this.activeData.__config__.tag?void 0===this.activeData["start-placeholder"]?this.dateTypeOptions:this.dateRangeTypeOptions:[]},tagList:function(){return[{label:"输入型组件",options:Me},{label:"选择型组件",options:Ve}]},activeTag:function(){return this.activeData.__config__.tag},isShowMin:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1},isShowMax:function(){return["el-input-number","el-slider","el-rate"].indexOf(this.activeTag)>-1},isShowStep:function(){return["el-input-number","el-slider"].indexOf(this.activeTag)>-1}},watch:{formConf:{handler:function(e){Object(Ne["f"])(e)},deep:!0}},methods:{addReg:function(){this.activeData.__config__.regList.push({pattern:"",message:""})},addSelectItem:function(){this.activeData.__slot__.options.push({label:"",value:""})},addTreeItem:function(){++this.idGlobal,this.dialogVisible=!0,this.currentNode=this.activeData.options},renderContent:function(e,t){var a=this,o=t.node,l=t.data;t.store;return e("div",{class:"custom-tree-node"},[e("span",[o.label]),e("span",{class:"node-operation"},[e("i",{on:{click:function(){return a.append(l)}},class:"el-icon-plus",attrs:{title:"添加"}}),e("i",{on:{click:function(){return a.remove(o,l)}},class:"el-icon-delete",attrs:{title:"删除"}})])])},append:function(e){e.children||this.$set(e,"children",[]),this.dialogVisible=!0,this.currentNode=e.children},remove:function(e,t){this.activeData.__config__.defaultValue=[];var a=e.parent,o=a.data.children||a.data,l=o.findIndex((function(e){return e.id===t.id}));o.splice(l,1)},addNode:function(e){this.currentNode.push(e)},setOptionValue:function(e,t){e.value=Object(S["f"])(t)?+t:t},setDefaultValue:function(e){return Array.isArray(e)?e.join(","):"boolean"===typeof e?"".concat(e):e},onDefaultValueInput:function(e){Array.isArray(this.activeData.__config__.defaultValue)?this.$set(this.activeData.__config__,"defaultValue",e.split(",").map((function(e){return Object(S["f"])(e)?+e:e}))):["true","false"].indexOf(e)>-1?this.$set(this.activeData.__config__,"defaultValue",JSON.parse(e)):this.$set(this.activeData.__config__,"defaultValue",Object(S["f"])(e)?+e:e)},onSwitchValueInput:function(e,t){["true","false"].indexOf(e)>-1?this.$set(this.activeData,t,JSON.parse(e)):this.$set(this.activeData,t,Object(S["f"])(e)?+e:e)},setTimeValue:function(e,t){var a="week"===t?Re.date:e;this.$set(this.activeData.__config__,"defaultValue",null),this.$set(this.activeData,"value-format",a),this.$set(this.activeData,"format",e)},spanChange:function(e){this.formConf.span=e},multipleChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[]:"")},dateTypeChange:function(e){this.setTimeValue(Re[e],e)},rangeChange:function(e){this.$set(this.activeData.__config__,"defaultValue",e?[this.activeData.min,this.activeData.max]:this.activeData.min)},rateTextChange:function(e){e&&(this.activeData["show-score"]=!1)},rateScoreChange:function(e){e&&(this.activeData["show-text"]=!1)},colorFormatChange:function(e){this.activeData.__config__.defaultValue=null,this.activeData["show-alpha"]=e.indexOf("a")>-1,this.activeData.__config__.renderKey=+new Date},openIconsDialog:function(e){this.iconsVisible=!0,this.currentIconModel=e},setIcon:function(e){this.activeData[this.currentIconModel]=e},tagChange:function(e){var t=Me.find((function(t){return t.__config__.tagIcon===e}));t||(t=Ve.find((function(t){return t.__config__.tagIcon===e}))),this.$emit("tag-change",t)},changeRenderKey:function(){Se.includes(this.activeData.__config__.tag)&&(this.activeData.__config__.renderKey=+new Date)}}},qe=We,Ae=(a("f442"),Object(re["a"])(qe,Te,je,!1,null,"2926a756",null)),Ke=Ae.exports,Ee=[{__config__:{label:"单行文本",labelWidth:null,showLabel:!0,tag:"el-input",tagIcon:"input",defaultValue:void 0,required:!0,layout:"colFormItem",span:24},__slot__:{prepend:"",append:""},__vModel__:"mobile",placeholder:"请输入",style:{width:"100%"},clearable:!0,"prefix-icon":"el-icon-mobile","suffix-icon":"",maxlength:11,"show-word-limit":!0,readonly:!1,disabled:!1}],Be=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{width:"500px","close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("el-row",{attrs:{gutter:15}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生成类型",prop:"type"}},[a("el-radio-group",{model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.typeOptions,(function(t,o){return a("el-radio-button",{key:o,attrs:{label:t.value,disabled:t.disabled}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),e.showFileName?a("el-form-item",{attrs:{label:"文件名",prop:"fileName"}},[a("el-input",{attrs:{placeholder:"请输入文件名",clearable:""},model:{value:e.formData.fileName,callback:function(t){e.$set(e.formData,"fileName",t)},expression:"formData.fileName"}})],1):e._e()],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v(" 确定 ")])],1)],1)],1)},Pe=[],Ge={inheritAttrs:!1,props:{showFileName:{type:String,default:""}},data:function(){return{formData:{fileName:void 0,type:"file"},rules:{fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},typeOptions:[{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName="".concat(+new Date,".vue"))},onClose:function(){},close:function(e){this.$emit("update:visible",!1)},handelConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(e.$emit("confirm",Object(r["a"])({},e.formData)),e.close())}))}}},He=Ge,Je=Object(re["a"])(He,Be,Pe,!1,null,"62c3c12f",null),Ue=Je.exports,Qe=a("f1e9"),Xe=Object(Ne["a"])(),Ye=Object(Ne["b"])(),Ze=Object(Ne["c"])(),et={components:{draggable:d.a,FormDrawer:Ie,RightPanel:Ke,CodeTypeDialog:Ue,DraggableItem:Qe["a"]},data:function(){return{formName:"",idGlobal:Ze,formConf:ze,inputComponents:Me,selectComponents:Ve,layoutComponents:Fe,labelWidth:100,drawingList:Ee,drawingData:{},activeId:Ee[0].formId,drawerVisible:!1,formData:{},dialogVisible:!1,generateConf:null,showFileName:!1,activeData:Ee[0],saveDrawingListDebounce:Object(u["a"])(340,Ne["e"]),saveIdGlobalDebounce:Object(u["a"])(340,Ne["g"]),leftComponents:[{title:"输入型组件",list:Me},{title:"选择型组件",list:Ve},{title:"布局型组件",list:Fe}]}},watch:{"activeData.__config__.label":function(e,t){void 0!==this.activeData.placeholder&&this.activeData.__config__.tag&&De===this.activeId&&(this.activeData.placeholder=this.activeData.placeholder.replace(t,"")+e)},activeId:{handler:function(e){De=e},immediate:!0},drawingList:{handler:function(e){this.saveDrawingListDebounce(e),0===e.length&&(this.idGlobal=100)},deep:!0},idGlobal:{handler:function(e){this.saveIdGlobalDebounce(e)},immediate:!0}},mounted:function(){var e=this.$route.query;this.formName=e&&e.name?e.name:"",Array.isArray(Xe)&&Xe.length>0?this.drawingList=Xe:this.drawingList=Ee,this.activeFormItem(this.drawingList[0]),Ye&&(this.formConf=Ye),ve((function(e){ge=e}))},methods:{setObjectValueReduce:function(e,t,a){var o=t.split(".");o.reduce((function(e,t,l){return o.length===l+1?e[t]=a:Object(S["g"])(e[t])||(e[t]={}),e[t]}),e)},setRespData:function(e,t){var a=e.__config__,o=a.dataPath,l=a.renderKey,i=a.dataConsumer;if(o&&i){var n=o.split(".").reduce((function(e,t){return e[t]}),t);this.setObjectValueReduce(e,i,n);var c=this.drawingList.findIndex((function(e){return e.__config__.renderKey===l}));c>-1&&this.$set(this.drawingList,c,e)}},fetchData:function(e){var t=this,a=e.__config__,o=a.dataType,l=a.method,i=a.url;"dynamic"===o&&l&&i&&(this.setLoading(e,!0),this.$axios({method:l,url:i}).then((function(a){t.setLoading(e,!1),t.setRespData(e,a.data)})))},setLoading:function(e,t){var a=e.directives;if(Array.isArray(a)){var o=a.find((function(e){return"loading"===e.name}));o&&(o.value=t)}},activeFormItem:function(e){this.activeData=e,this.activeId=e.__config__.formId},onEnd:function(e){e.from!==e.to&&(this.fetchData(ye),this.activeData=ye,this.activeId=this.idGlobal)},addComponent:function(e){var t=this.cloneComponent(e);this.fetchData(t),this.drawingList.push(t),this.activeFormItem(t)},cloneComponent:function(e){var t=Object(S["c"])(e),a=t.__config__;return a.span=this.formConf.span,this.createIdAndKey(t),void 0!==t.placeholder&&(t.placeholder+=a.label),ye=t,ye},createIdAndKey:function(e){var t=this,a=e.__config__;return a.formId=++this.idGlobal,a.renderKey="".concat(a.formId).concat(+new Date),"colFormItem"===a.layout?e.__vModel__="field".concat(this.idGlobal):"rowFormItem"===a.layout&&(a.componentName="row".concat(this.idGlobal),!Array.isArray(a.children)&&(a.children=[]),delete a.label),Array.isArray(a.children)&&(a.children=a.children.map((function(e){return t.createIdAndKey(e)}))),e},AssembleFormData:function(){this.formData=Object(r["a"])({fields:Object(S["c"])(this.drawingList)},this.formConf)},generate:function(e){var t=this["exec".concat(Object(S["j"])(this.operationType))];this.generateConf=e,t&&t(e)},execRun:function(e){this.AssembleFormData(),this.drawerVisible=!0},execCopy:function(e){document.getElementById("copyNode").click()},emptyForm:function(){var e=this;this.$confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then((function(){e.drawingList=[],e.idGlobal=100}))},drawingItemCopy:function(e,t){var a=Object(S["c"])(e);a=this.createIdAndKey(a),t.push(a),this.activeFormItem(a)},drawingItemDelete:function(e,t){var a=this;t.splice(e,1),this.$nextTick((function(){var e=a.drawingList.length;e&&a.activeFormItem(a.drawingList[e-1])}))},generateCode:function(){var e=this.generateConf.type;this.AssembleFormData();var t=y(A(this.formData,e)),a=D(F(this.formData,e)),o=w(te(this.formData));return ge.html(a+t+o,S["a"].html)},saveForm:function(){},copy:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="copy"},tagChange:function(e){var t=this;e=this.cloneComponent(e);var a=e.__config__;e.__vModel__=this.activeData.__vModel__,a.formId=this.activeId,a.span=this.activeData.__config__.span,this.activeData.__config__.tag=a.tag,this.activeData.__config__.tagIcon=a.tagIcon,this.activeData.__config__.document=a.document,Object(c["a"])(this.activeData.__config__.defaultValue)===Object(c["a"])(a.defaultValue)&&(a.defaultValue=this.activeData.__config__.defaultValue),Object.keys(e).forEach((function(a){void 0!==t.activeData[a]&&(e[a]=t.activeData[a])})),this.activeData=e,this.updateDrawingList(e,this.drawingList)},updateDrawingList:function(e,t){var a=this,o=t.findIndex((function(e){return e.__config__.formId===a.activeId}));o>-1?t.splice(o,1,e):t.forEach((function(t){Array.isArray(t.__config__.children)&&a.updateDrawingList(e,t.__config__.children)}))},refreshJson:function(e){this.drawingList=Object(S["c"])(e.fields),delete e.fields,this.formConf=e}}},tt=et,at=(a("ada0"),Object(re["a"])(tt,i,n,!1,null,null,null));t["default"]=at.exports},"6a46":function(e,t,a){},a4cc:function(e,t,a){"use strict";a("4825")},a69f:function(e,t,a){"use strict";a("6a46")},ada0:function(e,t,a){"use strict";a("cdc8")},cdc8:function(e,t,a){},f442:function(e,t,a){"use strict";a("4ccd")}}]);