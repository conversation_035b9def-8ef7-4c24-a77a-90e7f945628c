(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0bf04cb8"],{c6ae:function(e,n,t){},f5c1:function(e,n,t){"use strict";t("c6ae")},fb47:function(e,n,t){"use strict";t.r(n);var i=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("div")},c=[],o={name:"DpPatrol",data:function(){return{}},beforeMount:function(){window.addEventListener("resize",this.resizeHandler)},beforeDestroy:function(){window.removeEventListener("resize",this.resizeHandler)},created:function(){this.resizeHandler()},mounted:function(){},methods:{resizeHandler:function(){this.containerHeight=document.documentElement.clientHeight-67,this.infoHeight=document.documentElement.clientHeight-280}}},r=o,s=(t("f5c1"),t("2877")),u=Object(s["a"])(r,i,c,!1,null,"732399fb",null);n["default"]=u.exports}}]);