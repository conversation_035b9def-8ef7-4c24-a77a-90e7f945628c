(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d237cb1"],{fd41:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("el-row",{attrs:{gutter:15}},[i("el-col",{attrs:{lg:12,md:24}},[i("v-chart",{staticStyle:{width:"100%",height:"550px"},attrs:{autoresize:"",option:t.deptOptions}})],1),i("el-col",{attrs:{lg:12,md:24}},[i("v-chart",{staticStyle:{width:"100%",height:"550px"},attrs:{autoresize:"",option:t.typeOptions}})],1)],1),i("el-divider"),i("v-chart",{staticStyle:{width:"100%",height:"500px"},attrs:{autoresize:"",option:t.regionOptions}})],1)},n=[],o=(i("b0c0"),i("d3b7"),i("0643"),i("4e3e"),i("159b"),{data:function(){return{deptOptions:{title:{text:"各分中心销售终端情况",subtext:"终端机数量",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}台 ({d}%)"},legend:{bottom:0,left:"center"},series:[{type:"pie",name:"终端数量",radius:"70%",center:["50%","50%"],selectedMode:"single",emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:[],label:{formatter:"{name|{b}}\n{time|{c} 台}",minMargin:3,edgeDistance:15,lineHeight:15,rich:{time:{fontSize:10,color:"#36F"}}}}]},typeOptions:{title:{text:"各类型资产分布",subtext:"资产数量",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{bottom:0,left:"center"},series:[{type:"pie",name:"资产数量",radius:"70%",center:["50%","50%"],selectedMode:"single",emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:[],label:{formatter:"{name|{b}}\n{time|{c} 台}",minMargin:3,edgeDistance:15,lineHeight:15,rich:{time:{fontSize:10,color:"#36F"}}}}]},regionOptions:{title:{text:"各市县在用销售终端数",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}台 ({d}%)"},xAxis:{type:"category",data:[]},yAxis:{type:"value",animationDurationUpdate:500},series:[{type:"bar",name:"终端数量",data:[],label:{show:!0,position:"top",valueAnimation:!0,formatter:"{c} 台"}}]}}},mounted:function(){this.init()},methods:{init:function(){var t=this;this.$http("/rp/asset/dept/0101").then((function(e){e.code>0&&(t.deptOptions.series[0].data=e.data)})),this.$http("/rp/asset/type/0").then((function(e){e.code>0&&(t.typeOptions.series[0].data=e.data)})),this.$http("/rp/asset/region/0101").then((function(e){if(e.code>0){var i=[],a=[];e.data.forEach((function(t){i.push(t.name),a.push(t.value)})),t.regionOptions.xAxis.data=i,t.regionOptions.series[0].data=a}}))}}}),r=o,s=i("2877"),l=Object(s["a"])(r,a,n,!1,null,null,null);e["default"]=l.exports}}]);