(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c4c1f66"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"1e66":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[i("div",{staticClass:"filter"},[i("div",{staticClass:"filter-item"},[i("label",[t._v("检索：")]),i("div",[i("el-input",{attrs:{placeholder:"输入关键字",autocomplete:"off"},model:{value:t.qform.keyword,callback:function(e){t.$set(t.qform,"keyword",e)},expression:"qform.keyword"}})],1)]),i("div",{staticClass:"filter-item"},[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)]),i("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{path:"/sys/dict/page",query:t.qform,stripe:!0,border:!0}},[i("el-table-column",{attrs:{type:"index",width:"50"}}),i("el-table-column",{attrs:{label:"代码",prop:"code",width:"250"}}),i("el-table-column",{attrs:{label:"名称",prop:"name"}}),i("el-table-column",{attrs:{label:"代码长度",prop:"length",width:"80"}}),i("el-table-column",{attrs:{label:"代码类型",prop:"type",width:"80",formatter:t.fnType}}),i("el-table-column",{attrs:{label:"操作",width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.editable?i("el-button",{attrs:{type:"primary",icon:"el-icon-settings"},on:{click:function(i){return i.stopPropagation(),t.showCode(e.row)}}},[t._v("设置字典")]):i("el-button",{attrs:{type:"success",icon:"el-icon-settings"},on:{click:function(i){return i.stopPropagation(),t.showCode(e.row)}}},[t._v("字典清单")])]}}])})],1),i("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:t.codeTitle,width:"800px",top:"50px",visible:t.dialogVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("el-table",{attrs:{data:t.codeList,fit:"",border:"",size:"small"}},[i("el-table-column",{attrs:{type:"index",width:"50"}}),i("el-table-column",{attrs:{label:"编码",prop:"code",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t.editable&&a.edit?i("el-input",{attrs:{maxlength:a.length,"auto-complete":"off"},model:{value:a.code,callback:function(e){t.$set(a,"code",e)},expression:"row.code"}}):i("span",[t._v(t._s(a.code))])]}}])}),i("el-table-column",{attrs:{label:"名称",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t.editable&&a.edit?i("el-input",{attrs:{"auto-complete":"off"},model:{value:a.name,callback:function(e){t.$set(a,"name",e)},expression:"row.name"}}):i("span",[t._v(t._s(a.name))])]}}])}),i("el-table-column",{attrs:{label:"排序",prop:"ord",width:"60",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t.editable&&a.edit?i("el-input-number",{staticStyle:{width:"70px"},attrs:{controls:!1,min:0,max:9999},model:{value:a.ord,callback:function(e){t.$set(a,"ord",e)},expression:"row.ord"}}):i("span",[t._v(t._s(a.ord))])]}}])}),t.editable?i("el-table-column",{attrs:{width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row,n=e.$index;return[a.edit?i("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-circle-check"},on:{click:function(e){return t.confirmRowEdit(a)}}}):i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){a.edit=!0}}}),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(e){return t.handleDelete(a,n)}}})]}}],null,!1,1555431614)},[i("template",{slot:"header"},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:t.addRow}},[t._v("新增编码")])],1)],2):t._e()],1),t.editable?i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-row",[i("el-col",{staticStyle:{"text-align":"left"},attrs:{span:12}},[i("el-button",{attrs:{type:"success"},on:{click:t.loadCode}},[t._v("重新加载字典")])],1),i("el-col",{attrs:{span:12}},[i("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保 存")])],1)],1)],1):t._e()],1)],1)},n=[],o=(i("a15b"),i("a434"),i("b0c0"),i("d3b7"),i("ac1f"),i("841c"),i("0643"),i("4e3e"),i("159b"),i("6ecd")),s={i:"整数",f:"小数",d:"日期",s:"字符串"},r={components:{PageTable:o["a"]},data:function(){return{loading:!1,dialogVisible:!1,qform:{keyword:null},codeTitle:"",dict:{},editable:!1,codeList:[]}},created:function(){},mounted:function(){},methods:{fnType:function(t,e,i,a){return s[i]},search:function(){this.$refs.grid.search(this.qform)},showCode:function(t){this.dialogVisible=!0,this.dict=t,this.editable="1"===t.flagEdit,this.codeTitle=t.name+" - 字典清单",this.loadCode()},loadCode:function(){var t=this;this.loading=!0,this.$http("/sys/dict/code/"+this.dict.code).then((function(e){t.codeList=[],e&&e.length&&e.forEach((function(e){e.edit=!1,e.updating=!1,t.codeList.push(e)})),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error("加载字典清单出错: "+e)}))},addRow:function(){this.codeList.push({edit:!0,updating:!0,ord:1})},confirmRowEdit:function(t){t.updating=!0,t.edit=!1},handleDelete:function(t,e){this.codeList.splice(e,1)},save:function(){var t=this,e={code:this.dict.code,list:[]},i=[],a=[];this.codeList.forEach((function(t){if(t.code&&t.name){for(var n=!1,o=0;o<i.length;o++)if(i[o]===t.code){n=!0;break}n?a.push(t.code):i.push(t.code),e.list.push({code:t.code,name:t.name,ord:t.ord||1})}})),a.length?this.$message.warning("以下编码重复："+a.join(", ")):e.list.length?this.submit(e):this.$confirm("确定要清空所有字典编码吗？").then((function(){t.submit(e)})).catch((function(){}))},submit:function(t){var e=this;this.$http({url:"/sys/dict/save",data:t}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.search()),e.dialogVisible=!1})).catch((function(){}))}}},l=r,c=i("2877"),d=Object(c["a"])(l,a,n,!1,null,null,null);e["default"]=d.exports},"6ecd":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-container",{staticClass:"page-table-ctn"},[i("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":t.maxHeight,data:t.rows}},"el-table",t.$attrs,!1),t.$listeners),[t._t("default")],2),t.paging?i("el-footer",{staticClass:"footer"},[i("div",{staticClass:"size-info"},[t.total>1?i("span",[t._v("显示第 "+t._s(t.from)+" 条到第 "+t._s(t.to)+" 条的数据，")]):t._e(),t._v(" 共"+t._s(t.total)+" 条数据 ")]),i("el-pagination",t._b({staticStyle:{float:"right"},attrs:{layout:t.layout,"page-sizes":t.pageSizes,"current-page":t.pi,"page-size":t.pz,total:t.total},on:{"current-change":t.handleNumberChange,"size-change":t.handleSizeChange}},"el-pagination",t.$attrs,!1))],1):t._e()],1)},n=[],o=i("53ca"),s=(i("a9e3"),i("d3b7"),i("ac1f"),i("841c"),i("0643"),i("4e3e"),i("159b"),i("b775")),r={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(t){this.params=t||{}},setMaxHeight:function(t){this.maxHeight=t,this.$refs.grid.doLayout()},handleSizeChange:function(t){this.pz=t,this.search(1)},handleNumberChange:function(t){this.search(t)},search:function(t,e){var i=this;if(this.path){var a={pageNumber:1},n=Object(o["a"])(t);"undefined"===n?a.pageNumber=1:"number"===n?a.pageNumber=t:"object"===n?(this.params=t,"number"===typeof e&&(a.pageNumber=e),"boolean"===typeof e&&this.empty()):a.pageNumber=t.pageNumber,this.pi=a.pageNumber,this.paging&&(this.params.pageNumber=a.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(s["a"])({url:this.path,data:this.params}).then((function(t){i.loading=!1,i.paging?i.renderPage(t):i.renderList(t.rows?t.rows:t),i.$emit("loaded",t)})).catch((function(t){i.loading=!1,console.log(t)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(t){this.rows=t},renderPage:function(t){var e=this;this.checkField&&t.rows.forEach((function(t){t[e.checkField]=!1})),this.rows=t.rows,this.total=t.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(t){var e=this.$refs.grid.selection;t||(t="id");for(var i=[],a=0;a<e.length;a++)e[a][t]&&i.push(e[a][t]);return i}}},l=r,c=(i("b2d4"),i("2877")),d=Object(c["a"])(l,a,n,!1,null,"bdcc19d8",null);e["a"]=d.exports},"841c":function(t,e,i){"use strict";var a=i("d784"),n=i("825a"),o=i("1d80"),s=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=o(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var o=n(t),l=String(this),c=o.lastIndex;s(c,0)||(o.lastIndex=0);var d=r(o,l);return s(o.lastIndex,c)||(o.lastIndex=c),null===d?-1:d.index}]}))},a434:function(t,e,i){"use strict";var a=i("23e7"),n=i("23cb"),o=i("a691"),s=i("50c4"),r=i("7b0b"),l=i("65f0"),c=i("8418"),d=i("1dde"),u=i("ae40"),h=d("splice"),p=u("splice",{ACCESSORS:!0,0:0,1:2}),f=Math.max,g=Math.min,m=9007199254740991,b="Maximum allowed length exceeded";a({target:"Array",proto:!0,forced:!h||!p},{splice:function(t,e){var i,a,d,u,h,p,v=r(this),w=s(v.length),y=n(t,w),_=arguments.length;if(0===_?i=a=0:1===_?(i=0,a=w-y):(i=_-2,a=g(f(o(e),0),w-y)),w+i-a>m)throw TypeError(b);for(d=l(v,a),u=0;u<a;u++)h=y+u,h in v&&c(d,u,v[h]);if(d.length=a,i<a){for(u=y;u<w-a;u++)h=u+a,p=u+i,h in v?v[p]=v[h]:delete v[p];for(u=w;u>w-a+i;u--)delete v[u-1]}else if(i>a)for(u=w-a;u>y;u--)h=u+a-1,p=u+i-1,h in v?v[p]=v[h]:delete v[p];for(u=0;u<i;u++)v[u+y]=arguments[u+2];return v.length=w-a+i,d}})},ac65:function(t,e,i){},b2d4:function(t,e,i){"use strict";i("ac65")}}]);