
.container {
	padding: 0 60rpx;
	background: #fff;
}
.wechatapp {
	padding: 80rpx 0 48rpx;
	border-bottom: 1rpx solid #e3e3e3;
	margin-bottom: 72rpx;
	text-align: center;
}
.wechatapp .header-avatar {
	width: 250rpx;
	height: 250rpx;
	border: 2px solid #fff;
	margin: 0 auto;
	padding: 0;
	border-radius: 50%;
	overflow: hidden;
	box-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);
}
.auth-title {
	color: #585858;
	font-size: 40rpx;
	margin-bottom: 40rpx;
}
.auth-subtitle {
	color: #888;
	margin-bottom: 88rpx;
}
.login-btn {
	border: none;
	height: 88rpx;
	line-height: 88rpx;
	padding: 0;
	background: #04be01;
	color: #fff;
	border-radius: 999rpx;
}
.login-btn::after {
	display: none;
}
.login-btn.button-hover {
	box-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);
}
.login-close {
	border: none;
	height: 88rpx;
	line-height: 88rpx;
	padding: 0;
	background: #989898;
	color: #fff;
	border-radius: 999rpx;
	margin-top: 10px;
}

