(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-27992f13"],{"69db":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-select",t._g(t._b({attrs:{loading:t.loading},on:{change:t.changeMe}},"el-select",t.$attrs,!1),t.$listeners),[t.all?a("el-option",{attrs:{label:t.all,value:""}}):t._e(),t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})}))],2)},s=[],l=(a("d3b7"),a("ac1f"),a("00b4"),a("0643"),a("4e3e"),a("159b"),a("b775")),n={name:"Chosen",props:{path:{type:String,default:null},option:{type:Array,default:function(){return[]}},valueField:{type:String,default:"value"},labelField:{type:String,default:"text"},all:{type:String,default:null},isNode:{type:Boolean,default:!1}},data:function(){return{loading:!1,options:[]}},watch:{path:function(){this.load()}},mounted:function(){this.load()},methods:{load:function(){var t=this;this.path?/^\//.test(this.path)?(this.loading=!0,Object(l["a"])({url:this.path}).then((function(e){if(t.loading=!1,t.isNode){var a=[];e.forEach((function(t){return a.push({value:t.id,text:t.label,tag:t})})),t.options=a}else if(t.valueField||t.labelField){var i=[];e.forEach((function(e){i.push({value:e[t.valueField||"value"],text:e[t.labelField||"text"],tag:e})})),t.options=i}else t.options=e})).catch((function(e){t.loading=!1,console.log(e)}))):this.options=this.$store.getters.dict[this.path]:this.options=this.option},changeMe:function(t){if(null==t)this.$emit("changeItem",null);else for(var e=0;e<this.options.length;e++)if(this.options[e].value===t){this.$emit("changeItem",t,this.options[e].tag);break}}}},o=n,r=a("2877"),c=Object(r["a"])(o,i,s,!1,null,null,null);e["a"]=c.exports},7027:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"0 10px"}},[a("el-tabs",{attrs:{type:"border-card"},model:{value:t.tabIndex,callback:function(e){t.tabIndex=e},expression:"tabIndex"}},[a("el-tab-pane",{attrs:{label:"资产指标",name:"1"}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{disabled:0==t.items.length,type:"success",size:"mini",icon:"el-icon-share"},on:{click:t.append1}},[t._v("批量关联指标")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search1}},[t._v("查询")])],1)],1)]),a("div",{staticClass:"page-filter",staticStyle:{"padding-bottom":"0"}},[a("el-form",{attrs:{model:t.qform1,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search(e)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入资产编码、名称",autocomplete:"off"},model:{value:t.qform1.keyword,callback:function(e){t.$set(t.qform1,"keyword",e)},expression:"qform1.keyword"}})],1),a("el-form-item",{attrs:{label:"资产类型："}},[a("chosen",{staticStyle:{width:"180px"},attrs:{clearable:"",path:"/am/asset/type/list","value-field":"code","label-field":"name",placeholder:"请选择资产类型"},model:{value:t.qform1.type,callback:function(e){t.$set(t.qform1,"type",e)},expression:"qform1.type"}})],1),a("el-form-item",{attrs:{label:"区域/地点："}})],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid1",attrs:{size:"mini",path:"/am/patrol/kpi/pageAsset",query:t.qform1,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange1}},[a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"140","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"140","header-align":"center",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewItem(e.row)}}},[t._v(t._s(e.row.no))])]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"180",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.viewItem(e.row)}}},[t._v(t._s(e.row.name))])]}}])}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName","min-width":"100","header-align":"center"}}),a("el-table-column",{attrs:{label:"指标数量",prop:"kpiCount",width:"90",align:"center"}}),a("el-table-column",{attrs:{width:"70",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return a.stopPropagation(),t.appendItem1(e.row)}}},[t._v("指标")])]}}])})],1)],1)]),a("el-tab-pane",{attrs:{label:"类型指标",name:"2"}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{attrs:{disabled:0==t.types.length,type:"success",size:"mini",icon:"el-icon-share"},on:{click:t.append2}},[t._v("批量关联指标")])],1)],1),a("div",{staticClass:"search"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:t.search2}},[t._v("查 询")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:t.qform2,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(e){return e.preventDefault(),t.search2(e)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入名称关键字",autocomplete:"off"},model:{value:t.qform2.name,callback:function(e){t.$set(t.qform2,"name",e)},expression:"qform2.name"}})],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid2",attrs:{size:"mini",path:"/am/patrol/kpi/pageAssetType",query:t.qform2,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":t.selectionChange2}},[a("el-table-column",{attrs:{type:"selection",width:"45",align:"center"}}),a("el-table-column",{attrs:{label:"类型编码",prop:"code",width:"110","header-align":"center"}}),a("el-table-column",{attrs:{label:"类型名称",prop:"name","min-width":"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"上级类型",prop:"pname","min-width":"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"指标数量",prop:"kpiCount",width:"90",align:"center"}}),a("el-table-column",{attrs:{width:"70",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return a.stopPropagation(),t.appendItem2(e.row)}}},[t._v("指标")])]}}])})],1)],1)])],1),a("detail-view",{ref:"detailView"}),a("kpi-chosen",{ref:"kpiChosen",on:{confirm:t.confirmKpi}})],1)},s=[],l=(a("b0c0"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("6ecd")),n=a("69db"),o=a("c21a"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{visible:t.visible,title:"指标配置",width:"1100px",top:"10px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[t.names&&t.names.length>0?a("div",[t._l(t.names,(function(e,i){return a("el-tag",{key:i,staticClass:"name-item",attrs:{size:"small"}},[t._v(t._s(e))])})),a("el-divider")],2):t._e(),a("div",{staticClass:"tree-block"},[a("div",{staticClass:"tree-left"},[a("el-tree",{ref:"tree",attrs:{"default-expand-all":"","show-checkbox":"","node-key":"id",data:t.treeData},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.data;return a("span",{staticClass:"tree-node-line"},[a("span",[t._v(t._s(i.label))]),i.leaf?a("span",[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return e.stopPropagation(),function(){return t.showKpi(i)}()}}},[t._v("详情")])],1):t._e()])}}])})],1),a("div",{staticClass:"tree-right"},[a("div",{directives:[{name:"show",rawName:"v-show",value:null!=t.kpi.id,expression:"kpi.id != null"}]},[a("el-form",{attrs:{"label-width":"100px",size:"small"}},[a("el-row",[a("el-col",{attrs:{span:14}},[a("el-form-item",{attrs:{label:"指标编码："}},[a("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.kpi.no,callback:function(e){t.$set(t.kpi,"no",e)},expression:"kpi.no"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"指标类型："}},[a("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.modeText,callback:function(e){t.modeText=e},expression:"modeText"}})],1)],1)],1),a("el-form-item",{attrs:{label:"指标名称："}},[a("el-input",{staticClass:"form-static",staticStyle:{},attrs:{readonly:""},model:{value:t.kpi.name,callback:function(e){t.$set(t.kpi,"name",e)},expression:"kpi.name"}})],1),a("el-form-item",{attrs:{label:"指导意见："}},[a("el-input",{staticClass:"form-static",staticStyle:{},attrs:{type:"textarea",rows:5,readonly:""},model:{value:t.kpi.content,callback:function(e){t.$set(t.kpi,"content",e)},expression:"kpi.content"}})],1)],1),t.kpi.itemList&&t.kpi.itemList.length>0?a("div",[a("el-divider"),a("div",{staticStyle:{padding:"0 20px"}},t._l(t.kpi.itemList,(function(e){return a("div",{key:e.id,staticClass:"kpi-item"},[a("div",{class:t.itemCls}),a("div",{staticClass:"kpi-content"},[t._v(t._s(e.no)+"、 "+t._s(e.content))])])})),0)],1):t._e()],1)])]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确 定")])],1)])],1)},c=[],d={data:function(){return{visible:!1,treeData:[],names:[],kpis:[],extra:null,nodes:{},kpi:{},modeText:"",itemCls:""}},methods:{show:function(t){t||(t={}),this.names=t.names||[],this.kpis=t.kpis||[],this.extra=t,this.visible=!0,0===this.treeData.length?this.loadTree():this.loadCheck()},loadTree:function(){var t=this;this.$http("/am/patrol/kpi/tree").then((function(e){t.treeData=e,t.loadCheck()}))},confirm:function(){var t=[];this.$refs.tree.getCheckedNodes().forEach((function(e){e.leaf&&t.push(e.id)})),this.$emit("confirm",t,this.extra),this.visible=!1},loadCheck:function(){var t=this;this.$nextTick((function(){t.$refs.tree.setCheckedKeys(t.kpis)}))},showKpi:function(t){var e=this;this.kpi={},this.modeText="",this.$http("/am/patrol/kpi/get/"+t.id).then((function(t){t.code&&t.data&&(e.modeText={1:"单选",2:"多选",3:"判断",4:"填空"}[t.data.mode],e.itemCls="1"===t.data.mode?"kpi-item-radiobox":"2"===t.data.mode?"kpi-item-checkbox":"",e.kpi=t.data)}))}}},p=d,m=(a("9215"),a("2877")),u=Object(m["a"])(p,r,c,!1,null,"5c796ecc",null),h=u.exports,f={components:{PageTable:l["a"],Chosen:n["a"],DetailView:o["a"],KpiChosen:h},data:function(){return{fullscreenLoading:!1,tabIndex:"1",qform1:{keyword:null},more:!1,regions:[],items:[],qform2:{name:null},types:[]}},watch:{regions:function(t){this.qform1.location=t.length>1?t[1]:null,this.qform1.region=t.length>0?t[0]:null},tabIndex:function(t){"1"===t?this.search1():this.search2()}},mounted:function(){this.search1()},methods:{search1:function(){this.$refs.grid1.search(this.qform1)},selectionChange1:function(t){this.items=t||[]},preview:function(){1===this.items.length&&this.viewItem(this.items[0])},viewItem:function(t){var e=this;this.fullscreenLoading=!0,this.$http("/am/asset/get/"+t.id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&e.$refs.detailView.show(t.data)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")}))},append1:function(){if(1===this.items.length)this.appendItem1(this.items[0]);else{var t=[];this.items.forEach((function(e){return t.push(e.name)})),this.$refs.kpiChosen.show({names:t,kpis:[],items:this.items,index:1})}},appendItem1:function(t){var e=this;this.kpiIndex=1,this.$http("/am/patrol/kpi/asset/kpi/"+t.id).then((function(a){e.$refs.kpiChosen.show({names:[t.name],kpis:a||[],items:[t],index:1})}))},append2:function(){if(this.kpiIndex=2,1===this.types.length)this.appendItem2(this.types[0]);else{var t=[];this.types.forEach((function(e){return t.push(e.name)})),this.$refs.kpiChosen.show({names:t,kpis:[],items:this.types,index:2})}},appendItem2:function(t){var e=this;this.kpiIndex=2,this.$http("/am/patrol/kpi/assetType/kpi/"+t.code).then((function(a){e.$refs.kpiChosen.show({names:[t.name],kpis:a||[],items:[t],index:2})}))},search2:function(){this.$refs.grid2.search(this.qform2)},selectionChange2:function(t){this.types=t||[]},confirmKpi:function(t,e){var a=this;if(1===e.index){var i={assetList:[],kpiList:[]};e.items.forEach((function(t){return i.assetList.push(t.id)})),t&&t.forEach((function(t){return i.kpiList.push(t)})),this.$http({url:"/am/patrol/kpi/asset/append",data:i}).then((function(t){t.code>0&&(a.$message.success("保存资产指标成功"),a.search1())}))}else{var s={assetList:[],kpiList:[]};e.items.forEach((function(t){return s.assetList.push(t.code)})),t&&t.forEach((function(t){return s.kpiList.push(t)})),this.$http({url:"/am/patrol/kpi/assetType/append",data:s}).then((function(t){t.code>0&&(a.$message.success("保存资产类型指标成功"),a.search2())}))}}}},v=f,b=Object(m["a"])(v,i,s,!1,null,null,null);e["default"]=b.exports},9215:function(t,e,a){"use strict";a("cd94")},c21a:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"},{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"custom-class":"dialog-tab dialog-full",width:"1180px",visible:t.visible,"append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[t._v("资产详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":t.activeIndex,mode:"horizontal"},on:{select:t.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[t._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[t._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[t._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[t._v("附件信息")]),a("el-menu-item",{attrs:{index:"5"}},[t._v("地理位置")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===t.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-form",{ref:"baseform",attrs:{model:t.data,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.typeName,callback:function(e){t.$set(t.data,"typeName",e)},expression:"data.typeName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.no,callback:function(e){t.$set(t.data,"no",e)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.name,callback:function(e){t.$set(t.data,"name",e)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.deptName,callback:function(e){t.$set(t.data,"deptName",e)},expression:"data.deptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.regionName,callback:function(e){t.$set(t.data,"regionName",e)},expression:"data.regionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.sn,callback:function(e){t.$set(t.data,"sn",e)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.maDate,callback:function(e){t.$set(t.data,"maDate",e)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.guDate,callback:function(e){t.$set(t.data,"guDate",e)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.productDate,callback:function(e){t.$set(t.data,"productDate",e)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.takeDate,callback:function(e){t.$set(t.data,"takeDate",e)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boDate,callback:function(e){t.$set(t.data,"boDate",e)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.boAmount,callback:function(e){t.$set(t.data,"boAmount",e)},expression:"data.boAmount"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.expiryMonth,callback:function(e){t.$set(t.data,"expiryMonth",e)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[t._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.financeDate,callback:function(e){t.$set(t.data,"financeDate",e)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.value,callback:function(e){t.$set(t.data,"value",e)},expression:"data.value"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.spec,callback:function(e){t.$set(t.data,"spec",e)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.brand,callback:function(e){t.$set(t.data,"brand",e)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.manu,callback:function(e){t.$set(t.data,"manu",e)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.invoice,callback:function(e){t.$set(t.data,"invoice",e)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.seller,callback:function(e){t.$set(t.data,"seller",e)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.voucher,callback:function(e){t.$set(t.data,"voucher",e)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.price,callback:function(e){t.$set(t.data,"price",e)},expression:"data.price"}},[a("template",{slot:"append"},[t._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.contract,callback:function(e){t.$set(t.data,"contract",e)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.memo,callback:function(e){t.$set(t.data,"memo",e)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDeptName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useDeptName,callback:function(e){t.$set(t.data,"useDeptName",e)},expression:"data.useDeptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUserName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.useUserName,callback:function(e){t.$set(t.data,"useUserName",e)},expression:"data.useUserName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最后打印：",prop:"printTime"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.printTime,callback:function(e){t.$set(t.data,"printTime",e)},expression:"data.printTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原存放地址：",prop:"fromAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromAddr,callback:function(e){t.$set(t.data,"fromAddr",e)},expression:"data.fromAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"定位地址：",prop:"locAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locAddr,callback:function(e){t.$set(t.data,"locAddr",e)},expression:"data.locAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原备注：",prop:"fromMemo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.fromMemo,callback:function(e){t.$set(t.data,"fromMemo",e)},expression:"data.fromMemo"}})],1)],1)],1),t.data.location?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用网点：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationName,callback:function(e){t.$set(t.data,"locationName",e)},expression:"data.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点联系人：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationContact,callback:function(e){t.$set(t.data,"locationContact",e)},expression:"data.locationContact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点电话：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:t.data.locationPhone,callback:function(e){t.$set(t.data,"locationPhone",e)},expression:"data.locationPhone"}})],1)],1)],1):t._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===t.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},t._l(t.fields,(function(t,e){return a("element-view-item",{key:t.renderKey,attrs:{"current-item":t,index:e}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===t.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-table",{attrs:{data:t.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:t.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:t.colRelDescr}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===t.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("upload-file",{attrs:{multiple:"",disabled:"",type:"ASSET"},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===t.activeIndex,expression:"activeIndex === '5'"}],staticStyle:{"min-height":"500px"}},[a("div",{ref:"map",style:{"min-height":"500px",height:"100%"}},[null==t.lnglat?a("div",{staticStyle:{"text-align":"center","line-height":"50px","font-size":"24px"}},[t._v("该资产还未定位")]):t._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===t.activeIndex,expression:"activeIndex === '6'"}],staticStyle:{"min-height":"500px"}},[a("div",{staticStyle:{width:"400px",height:"400px",margin:"45px auto"}},[a("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.imgQr||t.defaultQr}})])])],2):t._e()],1)},s=[],l=(a("d81d"),a("b0c0"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("1cfe")),n=a("660a"),o={components:{ElementViewItem:l["a"],UploadFile:n["a"]},data:function(){return{visible:!1,loading:!1,activeIndex:"1",data:{},regionText:"",latlng:"",fields:[],relList:[],fileList:[],attachContext:this.$store.getters.attachContext,omap:null,map:{center:null,zoom:15,satellite:!1,markers:[]},currPoint:null,lnglat:null,infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)}),defaultQr:"/images/default_qr.png",imgQr:null,address:null}},watch:{visible:function(t){t||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{show:function(t){var e=this;this.visible=!0,"string"===typeof t?(this.loading=!0,this.$http("/am/asset/get/"+t).then((function(t){e.loading=!1,t.code>0&&t.data?e.showItem(t.data):e.visible=!1})).catch((function(){e.loading=!1,e.$message.error("网络超时"),e.visible=!1}))):this.showItem(t)},showItem:function(t){this.regionText=(t.regionName||"")+"/"+(t.locationName||""),this.latlng=t.lat&&t.lng?t.lat+", "+t.lng:"",this.activeIndex="1",this.data=t,this.fields=t.attr?JSON.parse(t.attr):[];var e=[];t.relList&&t.relList.forEach((function(t){switch(t.type){case"1":t.the?e.push({id:t.id,type:"2",rel:t.the,name:t.name}):e.push({id:t.id,type:"1",rel:t.rel,name:t.name});break;case"2":t.the?e.push({id:t.id,type:"4",rel:t.the,name:t.name}):e.push({id:t.id,type:"3",rel:t.rel,name:t.name})}})),this.relList=e,this.fileList=t.fileList||[],this.lnglat=t.lng&&t.lng?new window.AMap.LngLat(t.lng,t.lat):null,this.lnglat&&null==this.omap&&this.initMap()},handleSelectMenu:function(t){this.activeIndex=t,"6"===t&&this.data&&this.data.id&&(this.imgQr=this.defaultQr,this.imgQr="/api/am/asset/qrcode/"+this.data.id)},colRelType:function(t){switch(t.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(t){switch(t.type){case"1":return"当前资产包含【"+t.name+"】";case"2":return"当前资产属于【"+t.name+"】";case"3":return"当前资产安装了【"+t.name+"】";case"4":return"当前资产运行与【"+t.name+"】"}return""},initMap:function(){var t=this;this.$nextTick((function(){t.omap=new window.AMap.Map(t.$refs.map,t.map),t.omap.on("complete",t.mapComplete)}))},mapComplete:function(t){var e=this;this.currPoint=new window.AMap.Marker({position:this.lnglat,label:{direction:"top",content:this.data.name},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.omap.setCenter(this.lnglat),window.AMap.plugin("AMap.Geocoder",(function(){var t=new window.AMap.Geocoder({city:"全国"});t.getAddress([e.data.lng,e.data.lat],(function(t,a){"complete"===t&&"OK"===a.info&&a.regeocode&&(e.address=a.regeocode.formattedAddress)}))})),this.currPoint.on("click",(function(t){var a='<div style="padding:20px 15px;">';a+="<div>经度："+e.data.lng+"</div>",a+="<div>纬度："+e.data.lat+"</div>",a+="<div>定位地址："+e.address+"</div>",a+="</div>",e.infoWindow.setContent(a),e.infoWindow.open(e.omap,e.lnglat)}))}}},r=o,c=a("2877"),d=Object(c["a"])(r,i,s,!1,null,"4dd0b724",null);e["a"]=d.exports},cd94:function(t,e,a){}}]);