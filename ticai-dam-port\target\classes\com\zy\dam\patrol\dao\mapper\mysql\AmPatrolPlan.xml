<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.patrol.dao.AmPatrolPlanDAO">

    <sql id="meta">
			a.ID_
			,a.NO_
			,a.NAME_
			,a.TYPE_
			,a.GPS_
			,a.SCOPE_
			,a.ORD_FLAG_
			,a.USER_
			,a.TIME_MODE_
			,a.START_DATE_
			,a.END_DATE_
			,a.START_TIME_
			,a.END_TIME_
			,a.MEMO_
			,a.STATUS_
			,a.PERIOD_STATUS_
			,a.CTIME_
			,a.CUSER_
			,a.FLAG_
	</sql>
    <resultMap id="VO" type="com.zy.dam.patrol.vo.PatrolPlanVo">
        <result column="a.PERIOD_DATA_" jdbcType="BLOB" javaType="String"/>
    </resultMap>
    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.patrol.vo.PatrolPlanVo">
        select
        <include refid="meta"/>
        ,b.NAME_ user_name
        from AM_PATROL_PLAN a left join SYS_USER b on a.USER_=b.ID_
        where a.FLAG_!='9'
        <if test="keyword != null">and (a.NAME_ like concat('%',#{keyword},'%') or a.NO_ like concat('%',#{keyword},'%') )</if>
        <if test="periodStatus != null">and a.PERIOD_STATUS_=#{periodStatus}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="userName != null">and (b.NAME_ like concat('%',#{userName},'%') or b.NO_ like concat('%',#{userName},'%') )</if>
        <if test="begin != null">and a.START_DATE_ &gt;=#{begin}</if>
        <if test="end != null">and a.END_DATE_ &lt; date_add(#{end}, interval 1 day)</if>
        order by a.NO_ desc
    </select>

    <!-- 获取唯一的资管-巡检计划数据 -->
    <select id="findOne" resultType="com.zy.dam.patrol.orm.AmPatrolPlan">
        select
        <include refid="meta"/>
        from AM_PATROL_PLAN a where a.ID_=#{0}
    </select>

    <select id="findVo" resultType="com.zy.dam.patrol.vo.PatrolPlanVo">
        select
        <include refid="meta"/>
        ,a.PERIOD_DATA_
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USER_) user_name
        from AM_PATROL_PLAN a where a.ID_=#{0}
    </select>

    <select id="findPath" resultType="String">
        select REF_ from AM_PATROL_PLAN_ITEM where FLAG_='1' and TYPE_='1' and PLAN_=#{0} order by ORD_ limit 0, 1
    </select>

    <select id="findPoint" resultType="String">
        select REF_ from AM_PATROL_PLAN_ITEM where FLAG_='1' and TYPE_='2' and PLAN_=#{0} order by ORD_
    </select>

    <select id="findAssetByPlan" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select a.ID_,a.NO_,a.NAME_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        from AM_ASSET a,AM_PATROL_PLAN_ITEM b
        where b.FLAG_='1' and b.TYPE_='3' and a.ID_=b.REF_ and b.PLAN_=#{0}
        order by b.ORD_
    </select>

</mapper>
