(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b1ab4168"],{"04bd":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"custom-class":"dialog-tab",width:"1180px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产入库")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[e._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[e._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[e._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[e._v("附件信息")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}]},[a("el-form",{ref:"baseform",attrs:{model:e.data,"status-icon":"",rules:e.baseRules,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("asset-type-chosen",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择资产类型"},on:{selected:e.changeType},model:{value:e.data.type,callback:function(t){e.$set(e.data,"type",t)},expression:"data.type"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产编码","auto-complete":"off"},model:{value:e.data.no,callback:function(t){e.$set(e.data,"no",t)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产名称","auto-complete":"off"},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.data.dept,callback:function(t){e.$set(e.data,"dept",t)},expression:"data.dept"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("chosen",{staticStyle:{width:"100%"},attrs:{path:"/am/region/list","value-field":"id","label-field":"name",placeholder:"请选择区域"},model:{value:e.data.region,callback:function(t){e.$set(e.data,"region",t)},expression:"data.region"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售终端编号","auto-complete":"off"},model:{value:e.data.sn,callback:function(t){e.$set(e.data,"sn",t)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择出厂日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.maDate,callback:function(t){e.$set(e.data,"maDate",t)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择有效期限","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.guDate,callback:function(t){e.$set(e.data,"guDate",t)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择投产日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.productDate,callback:function(t){e.$set(e.data,"productDate",t)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择取得日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.takeDate,callback:function(t){e.$set(e.data,"takeDate",t)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择购置日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.boDate,callback:function(t){e.$set(e.data,"boDate",t)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入购置金额"},model:{value:e.data.boAmount,callback:function(t){e.$set(e.data,"boAmount",t)},expression:"data.boAmount"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"600",placeholder:"请输入使用时限"},model:{value:e.data.expiryMonth,callback:function(t){e.$set(e.data,"expiryMonth",t)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[e._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选入账日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.financeDate,callback:function(t){e.$set(e.data,"financeDate",t)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入价值"},model:{value:e.data.value,callback:function(t){e.$set(e.data,"value",t)},expression:"data.value"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入规则型号","auto-complete":"off"},model:{value:e.data.spec,callback:function(t){e.$set(e.data,"spec",t)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入品牌","auto-complete":"off"},model:{value:e.data.brand,callback:function(t){e.$set(e.data,"brand",t)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入生产厂商","auto-complete":"off"},model:{value:e.data.manu,callback:function(t){e.$set(e.data,"manu",t)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入发票号码","auto-complete":"off"},model:{value:e.data.invoice,callback:function(t){e.$set(e.data,"invoice",t)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售商","auto-complete":"off"},model:{value:e.data.seller,callback:function(t){e.$set(e.data,"seller",t)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入会计凭证号","auto-complete":"off"},model:{value:e.data.voucher,callback:function(t){e.$set(e.data,"voucher",t)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入单价"},model:{value:e.data.price,callback:function(t){e.$set(e.data,"price",t)},expression:"data.price"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入合同编号","auto-complete":"off"},model:{value:e.data.contract,callback:function(t){e.$set(e.data,"contract",t)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入备注","auto-complete":"off"},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"250px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},e._l(e.fields,(function(e,t){return a("element-item",{key:e.renderKey,attrs:{"current-item":e,index:t}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"250px"}},[a("div",{staticStyle:{"margin-bottom":"20px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.addRel("1")}}},[e._v("包含资产")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.addRel("2")}}},[e._v("属于资产")]),a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(t){return e.addRel("3")}}},[e._v("安装资产")]),a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(t){return e.addRel("4")}}},[e._v("被安装资产")])],1),a("el-table",{attrs:{data:e.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:e.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:e.colRelDescr}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),e.removeRel(t.index)}}},[e._v("移除")])]}}],null,!1,2372612604)})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===e.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"300px"}},[a("upload-file",{attrs:{drag:"",multiple:"",type:"ASSET"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.trySave}},[e._v("确 定")])],1)],2):e._e(),a("asset-chosen",{ref:"assetChosen",on:{selected:e.selectedAsset}})],1)},s=[],i=(a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("d2ed")),r=a("5edd"),o=a("69db"),n=a("b32f"),c=a("660a"),d=a("5281"),p={components:{AssetTypeChosen:i["a"],DeptTreeBox:r["a"],Chosen:o["a"],ElementItem:n["a"],UploadFile:c["a"],AssetChosen:d["a"]},data:function(){return{visible:!1,activeIndex:"1",data:{region:null},baseRules:{type:[{required:!0,message:"请选择资产类型",trigger:"blur"}],no:[{required:!0,message:"请选择资产编码",trigger:"blur"}],name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],dept:[{required:!0,message:"请选择所属部门",trigger:"blur"}]},fields:[],relList:[],fileList:[],relType:null}},methods:{show:function(){this.clear(),this.visible=!0},clear:function(){this.activeIndex="1",this.data={imp:"1"},this.fields=[],this.relList=[],this.fileList=[]},handleSelectMenu:function(e){this.activeIndex=e},changeType:function(e){var t=this;this.fields=[],e&&e.length&&this.$http("/bd/form/getByCode/ASSET_"+e[e.length-1]).then((function(e){e.code>0&&e.data&&e.data.data&&(t.fields=JSON.parse(e.data.data))}))},colRelType:function(e){switch(e.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(e){switch(e.type){case"1":return"当前资产包含【"+e.name+"】";case"2":return"当前资产属于【"+e.name+"】";case"3":return"当前资产安装了【"+e.name+"】";case"4":return"当前资产运行与【"+e.name+"】"}return""},addRel:function(e){this.relType=e,this.$refs.assetChosen.show()},removeRel:function(e){var t=this;this.$confirm("确定要移除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.relList.splice(e,1)})).catch((function(){}))},selectedAsset:function(e){if(e){for(var t=0;t<this.relList.length;t++)if(this.relList[t].rel===e.id)return this.$message.warning("资产“"+e.name+"”已经存在关联");this.relList.push({type:this.relType,rel:e.id,name:e.name})}},trySave:function(){var e=this;this.$refs.baseform.validate((function(t){if(t){if(e.fields.length){var a="";if(e.fields.forEach((function(e){!e.__config__.required||e.__config__.defaultValue&&0!==e.__config__.defaultValue.length||(a+="<li><label>"+e.__config__.label+"：</label><span>不能为空</span></li>")})),a.length)return e.activeIndex="2",a='<ul class="form-err-ext">'+a+"</ul>",void e.$notify({type:"warning",title:"扩展信息填写提示",dangerouslyUseHTMLString:!0,message:a,duration:2e4,showClose:!0});e.data.attr=JSON.stringify(e.fields)}e.data.relList=e.relList,e.data.fileList=e.fileList,e.save()}else e.activeIndex="1"}))},save:function(){var e=this;this.data.prop="1",this.data.imp="1",this.$http({url:"/am/asset/add",data:this.data}).then((function(t){t.code>0&&(e.$confirm("资产入库成功！","提示",{confirmButtonText:"继续入库",cancelButtonText:"不需要了",type:"success"}).then((function(){e.clear()})).catch((function(){e.visible=!1})),e.$emit("success"))}))}}},u=p,m=a("2877"),f=Object(m["a"])(u,l,s,!1,null,"383b3bc2",null);t["a"]=f.exports},"31c2":function(e,t,a){"use strict";a("9256")},"39d0":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"custom-class":"dialog-tab",width:"1180px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产信息编辑")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[e._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[e._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[e._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[e._v("附件信息")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}]},[a("el-form",{ref:"baseform",attrs:{model:e.data,"status-icon":"",rules:e.baseRules,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("asset-type-chosen",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择资产类型"},on:{selected:e.changeType},model:{value:e.data.type,callback:function(t){e.$set(e.data,"type",t)},expression:"data.type"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产编码","auto-complete":"off"},model:{value:e.data.no,callback:function(t){e.$set(e.data,"no",t)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产名称","auto-complete":"off"},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.data.dept,callback:function(t){e.$set(e.data,"dept",t)},expression:"data.dept"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("chosen",{staticStyle:{width:"100%"},attrs:{path:"/am/region/list","value-field":"id","label-field":"name",placeholder:"请选择区域"},model:{value:e.data.region,callback:function(t){e.$set(e.data,"region",t)},expression:"data.region"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售终端编号","auto-complete":"off"},model:{value:e.data.sn,callback:function(t){e.$set(e.data,"sn",t)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择出厂日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.maDate,callback:function(t){e.$set(e.data,"maDate",t)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择有效期限","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.guDate,callback:function(t){e.$set(e.data,"guDate",t)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择投产日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.productDate,callback:function(t){e.$set(e.data,"productDate",t)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择取得日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.takeDate,callback:function(t){e.$set(e.data,"takeDate",t)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择购置日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.boDate,callback:function(t){e.$set(e.data,"boDate",t)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入购置金额"},model:{value:e.data.boAmount,callback:function(t){e.$set(e.data,"boAmount",t)},expression:"data.boAmount"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"600",placeholder:"请输入使用时限"},model:{value:e.data.expiryMonth,callback:function(t){e.$set(e.data,"expiryMonth",t)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[e._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选入账日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.financeDate,callback:function(t){e.$set(e.data,"financeDate",t)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入价值"},model:{value:e.data.value,callback:function(t){e.$set(e.data,"value",t)},expression:"data.value"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入规则型号","auto-complete":"off"},model:{value:e.data.spec,callback:function(t){e.$set(e.data,"spec",t)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入品牌","auto-complete":"off"},model:{value:e.data.brand,callback:function(t){e.$set(e.data,"brand",t)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入生产厂商","auto-complete":"off"},model:{value:e.data.manu,callback:function(t){e.$set(e.data,"manu",t)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入发票号码","auto-complete":"off"},model:{value:e.data.invoice,callback:function(t){e.$set(e.data,"invoice",t)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售商","auto-complete":"off"},model:{value:e.data.seller,callback:function(t){e.$set(e.data,"seller",t)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入会计凭证号","auto-complete":"off"},model:{value:e.data.voucher,callback:function(t){e.$set(e.data,"voucher",t)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入单价"},model:{value:e.data.price,callback:function(t){e.$set(e.data,"price",t)},expression:"data.price"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入合同编号","auto-complete":"off"},model:{value:e.data.contract,callback:function(t){e.$set(e.data,"contract",t)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入备注","auto-complete":"off"},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"250px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},e._l(e.fields,(function(e,t){return a("element-item",{key:e.renderKey,attrs:{"current-item":e,index:t}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"250px"}},[a("div",{staticStyle:{"margin-bottom":"20px"}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.addRel("1")}}},[e._v("包含资产")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.addRel("2")}}},[e._v("属于资产")]),a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(t){return e.addRel("3")}}},[e._v("安装资产")]),a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(t){return e.addRel("4")}}},[e._v("被安装资产")])],1),a("el-table",{attrs:{data:e.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:e.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:e.colRelDescr}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),e.removeRel(t.index)}}},[e._v("移除")])]}}],null,!1,2372612604)})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===e.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"300px"}},[a("upload-file",{attrs:{drag:"",multiple:"",type:"ASSET"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.trySave}},[e._v("确 定")])],1)],2):e._e(),a("asset-chosen",{ref:"assetChosen",on:{selected:e.selectedAsset}})],1)},s=[],i=(a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("d2ed")),r=a("69db"),o=a("5edd"),n=a("b32f"),c=a("660a"),d=a("5281"),p={components:{AssetTypeChosen:i["a"],DeptTreeBox:o["a"],Chosen:r["a"],ElementItem:n["a"],UploadFile:c["a"],AssetChosen:d["a"]},data:function(){return{visible:!1,activeIndex:"1",data:{region:null},regions:[],baseRules:{type:[{required:!0,message:"请选择资产类型",trigger:"blur"}],no:[{required:!0,message:"请选择资产编码",trigger:"blur"}],name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],dept:[{required:!0,message:"请选择所属部门",trigger:"blur"}]},fields:[],relList:[],fileList:[],relType:null,itemType:null,itemFields:[]}},methods:{show:function(e){this.visible=!0;var t=[];e.region&&t.push(e.region),e.location&&t.push(e.location),this.regions=t,this.activeIndex="1",this.data=e,this.fields=e.attr?JSON.parse(e.attr):[];var a=[];e.relList&&e.relList.forEach((function(e){switch(e.type){case"1":e.the?a.push({id:e.id,type:"2",rel:e.the,name:e.name}):a.push({id:e.id,type:"1",rel:e.rel,name:e.name});break;case"2":e.the?a.push({id:e.id,type:"4",rel:e.the,name:e.name}):a.push({id:e.id,type:"3",rel:e.rel,name:e.name})}})),this.relList=a,this.fileList=e.fileList||[],this.itemType=e.type,e.attr&&(this.itemFields=JSON.parse(e.attr)),this.changeType(this.itemType)},handleSelectMenu:function(e){this.activeIndex=e},changeType:function(e){var t=this;this.fields=[],e&&e.length&&this.$http("/bd/form/getByCode/ASSET_"+e[e.length-1]).then((function(e){if(e.code>0&&e.data&&e.data.data){var a=JSON.parse(e.data.data)||[];t.itemFields&&t.itemFields.length&&a.forEach((function(e){for(var a=0;a<t.itemFields.length;a++){var l=t.itemFields[a];if(e.__config__&&l.__config__&&e.__config__.renderKey===l.__config__.renderKey){e.__config__.defaultValue=l.__config__.defaultValue;break}}})),t.fields=a}}))},colRelType:function(e){switch(e.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(e){switch(e.type){case"1":return"当前资产包含【"+e.name+"】";case"2":return"当前资产属于【"+e.name+"】";case"3":return"当前资产安装了【"+e.name+"】";case"4":return"当前资产运行与【"+e.name+"】"}return""},addRel:function(e){this.relType=e,this.$refs.assetChosen.show({ignoreId:this.data.id})},removeRel:function(e){var t=this;this.$confirm("确定要移除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.relList.splice(e,1)})).catch((function(){}))},selectedAsset:function(e){if(e){for(var t=0;t<this.relList.length;t++)if(this.relList[t].rel===e.id)return this.$message.warning("资产“"+e.name+"”已经存在关联");this.relList.push({type:this.relType,rel:e.id,name:e.name})}},trySave:function(){var e=this;this.$refs.baseform.validate((function(t){if(t){if(e.fields.length){var a="";if(e.fields.forEach((function(e){!e.__config__.required||e.__config__.defaultValue&&0!==e.__config__.defaultValue.length||(a+="<li><label>"+e.__config__.label+"：</label><span>不能为空</span></li>")})),a.length)return e.activeIndex="2",a='<ul class="form-err-ext">'+a+"</ul>",void e.$notify({type:"warning",title:"扩展信息填写提示",dangerouslyUseHTMLString:!0,message:a,duration:2e4,showClose:!0});e.data.attr=JSON.stringify(e.fields)}e.data.relList=e.relList,e.data.fileList=e.fileList,e.save()}else e.activeIndex="1"}))},save:function(){var e=this;this.$http({url:"/am/asset/edit",data:this.data}).then((function(t){t.code>0&&(e.$message.success("资产信息修改成功！"),e.$emit("success"),e.visible=!1)}))}}},u=p,m=a("2877"),f=Object(m["a"])(u,l,s,!1,null,"20dd8451",null);t["a"]=f.exports},4736:function(e,t,a){},"4f8b":function(e,t,a){"use strict";a("4736")},5281:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.visible?a("div",[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{visible:e.visible,title:"选择资产",width:"1000px","append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:e.qform,inline:"",size:"mini","label-width":"60px"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-form-item",{attrs:{label:"检索："}},[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入编码、名称",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"类型："}},[a("chosen",{staticStyle:{width:"120px"},attrs:{clearable:"",path:"/am/asset/type/list","value-field":"code","label-field":"name",placeholder:"请选择类型"},model:{value:e.qform.mold,callback:function(t){e.$set(e.qform,"mold",t)},expression:"qform.mold"}})],1),a("el-form-item",{attrs:{label:"部门/区域：","label-width":"100px"}},[a("dept-region-chosen",{staticStyle:{width:"130px"},attrs:{simple:!1,clearable:"",placeholder:"请选择部门/区域"},model:{value:e.regions,callback:function(t){e.regions=t},expression:"regions"}})],1),a("el-form-item",{attrs:{label:"终端号:"}},[a("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请输入终端号",autocomplete:"off"},model:{value:e.qform.sn,callback:function(t){e.$set(e.qform,"sn",t)},expression:"qform.sn"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/page",stripe:"",border:"","highlight-current-row":"","row-class-name":e.getRowClassName},on:{"selection-change":e.selectionChange,"row-click":e.clickRow}},[e.multiple?a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center",selectable:e.isSelectable}}):a("el-table-column",{attrs:{label:"",width:"45",fixed:"left",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.selectedItem&&e.selectedItem.id===t.row.id?a("el-link",{attrs:{underline:!1,type:"primary"}},[a("svg-icon",{attrs:{"icon-class":"ic_radio"}})],1):e.isSelectable(t.row)?a("el-link",{attrs:{underline:!1}},[a("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1):a("el-link",{staticStyle:{color:"#ccc"},attrs:{underline:!1}},[a("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1)]}}],null,!1,2496327718)}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"150",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"180","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:e.getAssetStatusType(t.row),size:"mini","disable-transitions":""}},[e._v(e._s(e.getAssetStatusText(t.row)))])]}}],null,!1,2633502830)})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)])],1):e._e()},s=[],i=(a("ac1f"),a("841c"),a("6ecd")),r=a("92cc"),o=a("69db"),n=a("576f"),c={components:{PageTable:i["a"],DeptRegionChosen:r["a"],Chosen:o["a"]},props:{multiple:{type:Boolean,default:!1}},data:function(){return{visible:!1,qform:{keyword:null,ignoreId:null},tag:null,regions:[],statusOptions:[],selectedItem:null,items:[],allowDamaged:!1}},watch:{regions:function(e){this.qform.region=e.length>1?e[1]:null,this.qform.dept=e.length>0?e[0]:null}},methods:{getAssetStatusType:function(e){return Object(n["c"])(e.status)},getAssetStatusText:function(e){return Object(n["b"])(e.status)},isSelectable:function(e){return!!this.allowDamaged||"7"!==e.status},getRowClassName:function(e){var t=e.row;return this.allowDamaged?"":"7"===t.status?"disabled-row":""},search:function(){this.$refs.grid.search(this.qform)},show:function(e,t){var a=this;e||(e={}),this.tag=t,this.qform.ignoreId=e.ignoreId,this.qform.status=e.status,this.qform.statusList=e.statusList,this.qform.location=e.location,this.qform.type=e.type,this.qform.ignoreDept=e.ignoreDept,this.allowDamaged=e.allowDamaged||!1,this.selectedItem=null,this.items=[],this.visible=!0,this.$refs.grid?this.$refs.grid.search(this.qform,!0):this.$nextTick((function(){return a.$refs.grid.search(a.qform,!0)}))},clickRow:function(e){this.multiple||(this.isSelectable(e)?this.selectedItem&&this.selectedItem.id===e.id?this.selectedItem=null:this.selectedItem=e:this.$message.warning("已损坏状态的资产不可选择"))},selectionChange:function(e){this.multiple&&(this.items=e||[])},confirm:function(){this.$emit("selected",this.multiple?this.items:this.selectedItem,this.tag),this.visible=!1}}},d=c,p=(a("31c2"),a("2877")),u=Object(p["a"])(d,l,s,!1,null,"3d300f83",null);t["a"]=u.exports},"576f":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return r}));var l={1:"闲置中",2:"使用中",3:"已借用",5:"维修中",6:"已锁定",7:"已损坏",8:"已报废"};function s(e){var t=[];for(var a in l)t.push({value:a,text:l[a]});return t}function i(e){switch(e){case"1":return"success";case"2":return"primary";case"3":return"warning";case"5":return"danger";case"6":return"secondary";case"7":return"warning";case"8":return"info"}return""}function r(e){return l[e]||""}},"5edd":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tree-box",e._g(e._b({ref:"treeBox",attrs:{data:e.deptTree,loading:e.loading},on:{selected:e.selected}},"tree-box",e.$attrs,!1),e.$listeners))},s=[],i=a("b775"),r=a("ee5a"),o={name:"DeptTreeBox",components:{TreeBox:r["a"]},data:function(){return{loading:!1,deptTree:[]}},mounted:function(){this.load()},methods:{load:function(){var e=this;this.loading=!0,Object(i["a"])("/sys/dept/tree").then((function(t){e.loading=!1,e.deptTree=t,e.$nextTick((function(){e.$refs.treeBox.init(t)}))})).catch((function(){e.loading=!1,e.$alert("加载机构树出错")}))},selected:function(e){this.$emit("input",e)},getData:function(){return this.deptTree}}},n=o,c=a("2877"),d=Object(c["a"])(n,l,s,!1,null,null,null);t["a"]=d.exports},"69db":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",e._g(e._b({attrs:{loading:e.loading},on:{change:e.changeMe}},"el-select",e.$attrs,!1),e.$listeners),[e.all?a("el-option",{attrs:{label:e.all,value:""}}):e._e(),e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})}))],2)},s=[],i=(a("d3b7"),a("ac1f"),a("00b4"),a("0643"),a("4e3e"),a("159b"),a("b775")),r={name:"Chosen",props:{path:{type:String,default:null},option:{type:Array,default:function(){return[]}},valueField:{type:String,default:"value"},labelField:{type:String,default:"text"},all:{type:String,default:null},isNode:{type:Boolean,default:!1}},data:function(){return{loading:!1,options:[]}},watch:{path:function(){this.load()}},mounted:function(){this.load()},methods:{load:function(){var e=this;this.path?/^\//.test(this.path)?(this.loading=!0,Object(i["a"])({url:this.path}).then((function(t){if(e.loading=!1,e.isNode){var a=[];t.forEach((function(e){return a.push({value:e.id,text:e.label,tag:e})})),e.options=a}else if(e.valueField||e.labelField){var l=[];t.forEach((function(t){l.push({value:t[e.valueField||"value"],text:t[e.labelField||"text"],tag:t})})),e.options=l}else e.options=t})).catch((function(t){e.loading=!1,console.log(t)}))):this.options=this.$store.getters.dict[this.path]:this.options=this.option},changeMe:function(e){if(null==e)this.$emit("changeItem",null);else for(var t=0;t<this.options.length;t++)if(this.options[t].value===e){this.$emit("changeItem",e,this.options[t].tag);break}}}},o=r,n=a("2877"),c=Object(n["a"])(o,l,s,!1,null,null,null);t["a"]=c.exports},"73a0":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-radio-group",e._g(e._b({on:{change:e.changeMe}},"el-radio-group",e.$attrs,!1),e.$listeners),[e.button?[e.all?a("el-radio-button",{attrs:{label:""}},[e._v(e._s(e.all))]):e._e(),e._l(e.options,(function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.text))])}))]:[e.all?a("el-radio",{attrs:{label:""}},[e._v(e._s(e.all))]):e._e(),e._l(e.options,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.text))])}))]],2)},s=[],i={name:"RadioBox",props:{button:{type:Boolean,default:!1},all:{type:String,default:null},options:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{changeMe:function(e){this.$emit("selected",e)}}},r=i,o=a("2877"),n=Object(o["a"])(r,l,s,!1,null,null,null);t["a"]=n.exports},9256:function(e,t,a){},"92cc":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-cascader",e._g(e._b({attrs:{options:e.options,props:e.props},on:{change:e.changeMe}},"el-cascader",e.$attrs,!1),e.$listeners))},s=[],i=a("b775"),r={name:"DeptRegionChosen",props:{anyNode:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},simple:{type:Boolean,default:!0}},data:function(){return{options:[],props:{checkStrictly:this.anyNode,multiple:this.multiple,value:"id"}}},mounted:function(){this.load()},methods:{load:function(){var e=this;Object(i["a"])("/sys/dept/tree").then((function(t){t.length&&(e.options=t)}))},changeMe:function(e){this.simple?this.$emit("input",e&&e.length?e[e.length-1]:null):this.$emit("input",e),this.$emit("selected",e)}}},o=r,n=a("2877"),c=Object(n["a"])(o,l,s,!1,null,null,null);t["a"]=c.exports},ad10:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",e._g(e._b({attrs:{loading:e.loading,filterable:"","filter-method":e.filter}},"el-select",e.$attrs,!1),e.$listeners),e._l(e.options,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.no||t.account))])])})),1)},s=[],i=(a("4de4"),a("b0c0"),a("d3b7"),a("0643"),a("2382"),a("4e3e"),a("159b"),a("b775")),r={name:"UserChosen",props:{type:{type:String,default:null}},data:function(){return{loading:!1,options:[],list:[]}},mounted:function(){this.load()},methods:{load:function(){var e=this;this.loading=!0,Object(i["a"])("/sys/user/list/"+(this.type||"all")).then((function(t){e.loading=!1,t&&t.length&&(e.list=t,e.filter())})).catch((function(t){e.loading=!1,console.log(t)}))},getData:function(){return this.options},filter:function(e){if(e){var t=[];this.options.forEach((function(a){if(a.name&&-1!==a.name.indexOf(e))t.push(a);else if(a.no){if(-1!==a.no.indexOf(e))return void t.push(a)}else if(a.account&&-1!==a.account.indexOf(e))return void t.push(a)})),this.options=t}else this.options=this.list}}},o=r,n=a("2877"),c=Object(n["a"])(o,l,s,!1,null,null,null);t["a"]=c.exports},b32f:function(e,t,a){"use strict";a("d81d"),a("a9e3"),a("d3b7"),a("0643"),a("a573");var l=a("4758");function s(e,t){var a=this,s=t.__config__,r=!1===s.showLabel?"0":s.labelWidth?"".concat(s.labelWidth,"px"):null,o=s.showLabel?s.label+"：":"",n=i.apply(this,arguments);return e("el-col",{attrs:{span:s.span}},[e("el-form-item",{attrs:{"label-width":r,label:o,required:s.required}},[e(l["a"],{key:s.renderKey,attrs:{conf:t},on:{input:function(e){a.$set(s,"defaultValue",e)}}},[n])])])}function i(e,t){var a=this,l=t.__config__;return Array.isArray(l.children)?l.children.map((function(t,i){return s.call(a,e,t,i,l.children)})):null}var r,o,n={components:{render:l["a"]},props:{currentItem:{type:Object,default:function(){}},index:{type:Number,default:0}},render:function(e){return"colFormItem"===this.currentItem.__config__.layout?s.call(this,e,this.currentItem):""}},c=n,d=a("2877"),p=Object(d["a"])(c,r,o,!1,null,null,null);t["a"]=p.exports},c21a:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"},{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{"custom-class":"dialog-tab dialog-full",width:"1180px",visible:e.visible,"append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产详情")]),a("div",{staticClass:"tabbar"},[a("el-menu",{attrs:{"default-active":e.activeIndex,mode:"horizontal"},on:{select:e.handleSelectMenu}},[a("el-menu-item",{attrs:{index:"1"}},[e._v("基本信息")]),a("el-menu-item",{attrs:{index:"2"}},[e._v("扩展信息")]),a("el-menu-item",{attrs:{index:"3"}},[e._v("资产关联")]),a("el-menu-item",{attrs:{index:"4"}},[e._v("附件信息")]),a("el-menu-item",{attrs:{index:"5"}},[e._v("地理位置")])],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-form",{ref:"baseform",attrs:{model:e.data,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.typeName,callback:function(t){e.$set(e.data,"typeName",t)},expression:"data.typeName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.no,callback:function(t){e.$set(e.data,"no",t)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.deptName,callback:function(t){e.$set(e.data,"deptName",t)},expression:"data.deptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.regionName,callback:function(t){e.$set(e.data,"regionName",t)},expression:"data.regionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.sn,callback:function(t){e.$set(e.data,"sn",t)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.maDate,callback:function(t){e.$set(e.data,"maDate",t)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.guDate,callback:function(t){e.$set(e.data,"guDate",t)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.productDate,callback:function(t){e.$set(e.data,"productDate",t)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.takeDate,callback:function(t){e.$set(e.data,"takeDate",t)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.boDate,callback:function(t){e.$set(e.data,"boDate",t)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.boAmount,callback:function(t){e.$set(e.data,"boAmount",t)},expression:"data.boAmount"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.expiryMonth,callback:function(t){e.$set(e.data,"expiryMonth",t)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[e._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.financeDate,callback:function(t){e.$set(e.data,"financeDate",t)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.value,callback:function(t){e.$set(e.data,"value",t)},expression:"data.value"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.spec,callback:function(t){e.$set(e.data,"spec",t)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.brand,callback:function(t){e.$set(e.data,"brand",t)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.manu,callback:function(t){e.$set(e.data,"manu",t)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.invoice,callback:function(t){e.$set(e.data,"invoice",t)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.seller,callback:function(t){e.$set(e.data,"seller",t)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.voucher,callback:function(t){e.$set(e.data,"voucher",t)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.price,callback:function(t){e.$set(e.data,"price",t)},expression:"data.price"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.contract,callback:function(t){e.$set(e.data,"contract",t)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"useDeptName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.useDeptName,callback:function(t){e.$set(e.data,"useDeptName",t)},expression:"data.useDeptName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUserName"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.useUserName,callback:function(t){e.$set(e.data,"useUserName",t)},expression:"data.useUserName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最后打印：",prop:"printTime"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.printTime,callback:function(t){e.$set(e.data,"printTime",t)},expression:"data.printTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原存放地址：",prop:"fromAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.fromAddr,callback:function(t){e.$set(e.data,"fromAddr",t)},expression:"data.fromAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"定位地址：",prop:"locAddr"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.locAddr,callback:function(t){e.$set(e.data,"locAddr",t)},expression:"data.locAddr"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"原备注：",prop:"fromMemo"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.fromMemo,callback:function(t){e.$set(e.data,"fromMemo",t)},expression:"data.fromMemo"}})],1)],1)],1),e.data.location?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用网点：",prop:"voucher"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.locationName,callback:function(t){e.$set(e.data,"locationName",t)},expression:"data.locationName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点联系人：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.locationContact,callback:function(t){e.$set(e.data,"locationContact",t)},expression:"data.locationContact"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"网点电话：",prop:"locationPhone"}},[a("el-input",{staticClass:"form-static",staticStyle:{width:"100%"},model:{value:e.data.locationPhone,callback:function(t){e.$set(e.data,"locationPhone",t)},expression:"data.locationPhone"}})],1)],1)],1):e._e()],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"2"===e.activeIndex,expression:"activeIndex === '2'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-row",{attrs:{gutter:20}},[a("el-form",{ref:"extform",attrs:{size:"small","label-width":"120px"}},e._l(e.fields,(function(e,t){return a("element-view-item",{key:e.renderKey,attrs:{"current-item":e,index:t}})})),1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"3"===e.activeIndex,expression:"activeIndex === '3'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("el-table",{attrs:{data:e.relList,size:"small",border:""}},[a("el-table-column",{attrs:{prop:"type",label:"关联类型",align:"center",width:"70",formatter:e.colRelType}}),a("el-table-column",{attrs:{prop:"name",label:"关联资产","header-align":"center",width:"250"}}),a("el-table-column",{attrs:{label:"关联描述","header-align":"center",formatter:e.colRelDescr}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"4"===e.activeIndex,expression:"activeIndex === '4'"}],staticStyle:{"min-height":"500px",padding:"10px 15px"}},[a("upload-file",{attrs:{multiple:"",disabled:"",type:"ASSET"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"5"===e.activeIndex,expression:"activeIndex === '5'"}],staticStyle:{"min-height":"500px"}},[a("div",{ref:"map",style:{"min-height":"500px",height:"100%"}},[null==e.lnglat?a("div",{staticStyle:{"text-align":"center","line-height":"50px","font-size":"24px"}},[e._v("该资产还未定位")]):e._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"6"===e.activeIndex,expression:"activeIndex === '6'"}],staticStyle:{"min-height":"500px"}},[a("div",{staticStyle:{width:"400px",height:"400px",margin:"45px auto"}},[a("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.imgQr||e.defaultQr}})])])],2):e._e()],1)},s=[],i=(a("d81d"),a("b0c0"),a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("1cfe")),r=a("660a"),o={components:{ElementViewItem:i["a"],UploadFile:r["a"]},data:function(){return{visible:!1,loading:!1,activeIndex:"1",data:{},regionText:"",latlng:"",fields:[],relList:[],fileList:[],attachContext:this.$store.getters.attachContext,omap:null,map:{center:null,zoom:15,satellite:!1,markers:[]},currPoint:null,lnglat:null,infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)}),defaultQr:"/images/default_qr.png",imgQr:null,address:null}},watch:{visible:function(e){e||(this.omap&&this.omap.destroy(),this.omap=null)}},methods:{show:function(e){var t=this;this.visible=!0,"string"===typeof e?(this.loading=!0,this.$http("/am/asset/get/"+e).then((function(e){t.loading=!1,e.code>0&&e.data?t.showItem(e.data):t.visible=!1})).catch((function(){t.loading=!1,t.$message.error("网络超时"),t.visible=!1}))):this.showItem(e)},showItem:function(e){this.regionText=(e.regionName||"")+"/"+(e.locationName||""),this.latlng=e.lat&&e.lng?e.lat+", "+e.lng:"",this.activeIndex="1",this.data=e,this.fields=e.attr?JSON.parse(e.attr):[];var t=[];e.relList&&e.relList.forEach((function(e){switch(e.type){case"1":e.the?t.push({id:e.id,type:"2",rel:e.the,name:e.name}):t.push({id:e.id,type:"1",rel:e.rel,name:e.name});break;case"2":e.the?t.push({id:e.id,type:"4",rel:e.the,name:e.name}):t.push({id:e.id,type:"3",rel:e.rel,name:e.name})}})),this.relList=t,this.fileList=e.fileList||[],this.lnglat=e.lng&&e.lng?new window.AMap.LngLat(e.lng,e.lat):null,this.lnglat&&null==this.omap&&this.initMap()},handleSelectMenu:function(e){this.activeIndex=e,"6"===e&&this.data&&this.data.id&&(this.imgQr=this.defaultQr,this.imgQr="/api/am/asset/qrcode/"+this.data.id)},colRelType:function(e){switch(e.type){case"1":return"包含";case"2":return"属于";case"3":return"安装";case"4":return"被安装"}return""},colRelDescr:function(e){switch(e.type){case"1":return"当前资产包含【"+e.name+"】";case"2":return"当前资产属于【"+e.name+"】";case"3":return"当前资产安装了【"+e.name+"】";case"4":return"当前资产运行与【"+e.name+"】"}return""},initMap:function(){var e=this;this.$nextTick((function(){e.omap=new window.AMap.Map(e.$refs.map,e.map),e.omap.on("complete",e.mapComplete)}))},mapComplete:function(e){var t=this;this.currPoint=new window.AMap.Marker({position:this.lnglat,label:{direction:"top",content:this.data.name},icon:"/icons/pin-red.png"}),this.omap.add(this.currPoint),this.omap.setCenter(this.lnglat),window.AMap.plugin("AMap.Geocoder",(function(){var e=new window.AMap.Geocoder({city:"全国"});e.getAddress([t.data.lng,t.data.lat],(function(e,a){"complete"===e&&"OK"===a.info&&a.regeocode&&(t.address=a.regeocode.formattedAddress)}))})),this.currPoint.on("click",(function(e){var a='<div style="padding:20px 15px;">';a+="<div>经度："+t.data.lng+"</div>",a+="<div>纬度："+t.data.lat+"</div>",a+="<div>定位地址："+t.address+"</div>",a+="</div>",t.infoWindow.setContent(a),t.infoWindow.open(t.omap,t.lnglat)}))}}},n=o,c=a("2877"),d=Object(c["a"])(n,l,s,!1,null,"4dd0b724",null);t["a"]=d.exports},d2ed:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-cascader",e._g(e._b({attrs:{options:e.options,props:e.props},on:{change:e.changeMe}},"el-cascader",e.$attrs,!1),e.$listeners))},s=[],i=a("b775"),r={name:"AssetTypeChosen",props:{anyNode:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},simple:{type:Boolean,default:!0}},data:function(){return{options:[],props:{checkStrictly:this.anyNode,multiple:this.multiple,value:"id"}}},mounted:function(){this.load()},methods:{load:function(){var e=this;Object(i["a"])("/am/asset/type/cascade").then((function(t){t.length&&(e.options=t)}))},changeMe:function(e){this.simple?this.$emit("input",e&&e.length?e[e.length-1]:null):this.$emit("input",e),this.$emit("selected",e)}}},o=r,n=a("2877"),c=Object(n["a"])(o,l,s,!1,null,null,null);t["a"]=c.exports},d8a3:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.visible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{"custom-class":"dialog-tab",width:"1180px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("template",{slot:"title"},[a("div",{staticClass:"header-bar"},[a("div",{staticClass:"caption"},[e._v("资产信息修改")])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"1"===e.activeIndex,expression:"activeIndex === '1'"}]},[a("el-form",{ref:"baseform",attrs:{model:e.data,"status-icon":"",rules:e.baseRules,size:"small","label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产类型：",prop:"type"}},[a("asset-type-chosen",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择资产类型"},on:{selected:e.changeType},model:{value:e.data.type,callback:function(t){e.$set(e.data,"type",t)},expression:"data.type"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产编码：",prop:"no"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产编码","auto-complete":"off"},model:{value:e.data.no,callback:function(t){e.$set(e.data,"no",t)},expression:"data.no"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产名称：",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入资产名称","auto-complete":"off"},model:{value:e.data.name,callback:function(t){e.$set(e.data,"name",t)},expression:"data.name"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属部门：",prop:"dept"}},[a("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.data.dept,callback:function(t){e.$set(e.data,"dept",t)},expression:"data.dept"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所在区域：",prop:"region"}},[a("chosen",{staticStyle:{width:"100%"},attrs:{path:"/am/region/list","value-field":"id","label-field":"name",placeholder:"请选择区域"},model:{value:e.data.region,callback:function(t){e.$set(e.data,"region",t)},expression:"data.region"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售终端编号：",prop:"sn"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售终端编号","auto-complete":"off"},model:{value:e.data.sn,callback:function(t){e.$set(e.data,"sn",t)},expression:"data.sn"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出厂日期：",prop:"maDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择出厂日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.maDate,callback:function(t){e.$set(e.data,"maDate",t)},expression:"data.maDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期限：",prop:"guDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择有效期限","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.guDate,callback:function(t){e.$set(e.data,"guDate",t)},expression:"data.guDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"投产日期：",prop:"productDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择投产日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.productDate,callback:function(t){e.$set(e.data,"productDate",t)},expression:"data.productDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"取得日期：",prop:"takeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择取得日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.takeDate,callback:function(t){e.$set(e.data,"takeDate",t)},expression:"data.takeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置日期：",prop:"boDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择购置日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.boDate,callback:function(t){e.$set(e.data,"boDate",t)},expression:"data.boDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"购置金额：",prop:"boAmount"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入购置金额"},model:{value:e.data.boAmount,callback:function(t){e.$set(e.data,"boAmount",t)},expression:"data.boAmount"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用时限：",prop:"expiryMonth"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"600",placeholder:"请输入使用时限"},model:{value:e.data.expiryMonth,callback:function(t){e.$set(e.data,"expiryMonth",t)},expression:"data.expiryMonth"}},[a("template",{slot:"append"},[e._v("月")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入账日期：",prop:"financeDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选入账日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.data.financeDate,callback:function(t){e.$set(e.data,"financeDate",t)},expression:"data.financeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"价值：",prop:"value"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入价值"},model:{value:e.data.value,callback:function(t){e.$set(e.data,"value",t)},expression:"data.value"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"规则型号：",prop:"spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入规则型号","auto-complete":"off"},model:{value:e.data.spec,callback:function(t){e.$set(e.data,"spec",t)},expression:"data.spec"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"品牌：",prop:"brand"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入品牌","auto-complete":"off"},model:{value:e.data.brand,callback:function(t){e.$set(e.data,"brand",t)},expression:"data.brand"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"生产厂商：",prop:"manu"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入生产厂商","auto-complete":"off"},model:{value:e.data.manu,callback:function(t){e.$set(e.data,"manu",t)},expression:"data.manu"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发票号码：",prop:"invoice"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入发票号码","auto-complete":"off"},model:{value:e.data.invoice,callback:function(t){e.$set(e.data,"invoice",t)},expression:"data.invoice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"销售商：",prop:"seller"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入销售商","auto-complete":"off"},model:{value:e.data.seller,callback:function(t){e.$set(e.data,"seller",t)},expression:"data.seller"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会计凭证号：",prop:"voucher"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入会计凭证号","auto-complete":"off"},model:{value:e.data.voucher,callback:function(t){e.$set(e.data,"voucher",t)},expression:"data.voucher"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单价：",prop:"price"}},[a("el-input",{attrs:{clearable:"",type:"number",min:"0",max:"99999999",placeholder:"请输入单价"},model:{value:e.data.price,callback:function(t){e.$set(e.data,"price",t)},expression:"data.price"}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"合同编号：",prop:"contract"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入合同编号","auto-complete":"off"},model:{value:e.data.contract,callback:function(t){e.$set(e.data,"contract",t)},expression:"data.contract"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注：",prop:"memo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入备注","auto-complete":"off"},model:{value:e.data.memo,callback:function(t){e.$set(e.data,"memo",t)},expression:"data.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用部门：",prop:"region"}},[a("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择使用部门"},model:{value:e.data.useDept,callback:function(t){e.$set(e.data,"useDept",t)},expression:"data.useDept"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"useUser"}},[a("user-chosen",{staticStyle:{width:"100%"},attrs:{type:"1",clearable:"",placeholder:"请选择使用人"},model:{value:e.data.useUser,callback:function(t){e.$set(e.data,"useUser",t)},expression:"data.useUser"}})],1)],1),"1"===e.originalStatus||"7"===e.originalStatus?a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"资产状态：",prop:"status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择资产状态"},on:{change:e.statusChange},model:{value:e.data.status,callback:function(t){e.$set(e.data,"status",t)},expression:"data.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1)],1):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.trySave}},[e._v("确 定")])],1)],2):e._e()],1)},s=[],i=(a("b64b"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("d2ed")),r=a("69db"),o=a("5edd"),n=a("ad10"),c={components:{AssetTypeChosen:i["a"],DeptTreeBox:o["a"],Chosen:r["a"],UserChosen:n["a"]},data:function(){return{visible:!1,activeIndex:"1",data:{region:null},regions:[],baseRules:{type:[{required:!0,message:"请选择资产类型",trigger:"blur"}],no:[{required:!0,message:"请选择资产编码",trigger:"blur"}],name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],dept:[{required:!0,message:"请选择所属部门",trigger:"blur"}]},fields:[],itemFields:[],statusOptions:[{value:"1",text:"闲置中"},{value:"7",text:"已损坏"}],originalStatus:""}},methods:{show:function(e){this.visible=!0;var t=[];e.region&&t.push(e.region),e.location&&t.push(e.location),this.regions=t,this.activeIndex="1",this.data=e,this.originalStatus=e.status,this.fields=e.attr?JSON.parse(e.attr):[]},statusChange:function(e){var t="7"===e&&"1"===this.originalStatus||"1"===e&&"7"===this.originalStatus;t||e===this.originalStatus||("7"===e&&"1"!==this.originalStatus?this.$message.warning("只有闲置状态的资产才能修改为已损坏状态"):"1"===e&&"7"!==this.originalStatus?this.$message.warning("只有已损坏状态的资产才能修改为闲置状态"):this.$message.warning("只允许在闲置和已损坏状态之间切换"),this.data.status=this.originalStatus)},changeType:function(e){var t=this;this.fields=[],e&&e.length&&this.$http("/bd/form/getByCode/ASSET_"+e[e.length-1]).then((function(e){if(e.code>0&&e.data&&e.data.data){var a=JSON.parse(e.data.data)||[];t.itemFields&&t.itemFields.length&&a.forEach((function(e){for(var a=0;a<t.itemFields.length;a++){var l=t.itemFields[a];if(e.__config__&&l.__config__&&e.__config__.renderKey===l.__config__.renderKey){e.__config__.defaultValue=l.__config__.defaultValue;break}}})),t.fields=a}}))},trySave:function(){var e=this,t="1"===this.originalStatus||"7"===this.originalStatus;if(t&&this.data.status!==this.originalStatus){var a="7"===this.data.status&&"1"===this.originalStatus||"1"===this.data.status&&"7"===this.originalStatus;if(!a)return"7"===this.data.status&&"1"!==this.originalStatus?this.$message.warning("只有闲置状态的资产才能修改为已损坏状态"):"1"===this.data.status&&"7"!==this.originalStatus?this.$message.warning("只有已损坏状态的资产才能修改为闲置状态"):this.$message.warning("只允许在闲置和已损坏状态之间切换"),void(this.data.status=this.originalStatus)}this.$http({url:"/am/asset/revise",data:this.data}).then((function(t){t.code>0?(e.$message.success("资产信息修改成功！"),e.$emit("success"),e.visible=!1):e.$message.error(t.msg||"资产信息修改失败！")}))}}},d=c,p=a("2877"),u=Object(p["a"])(d,l,s,!1,null,"9937fd36",null);t["a"]=u.exports},ee5a:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-popover",{ref:"popover",attrs:{placement:"bottom-start",trigger:"click"},on:{show:e.onShowPopover,hide:e.onHidePopover}},[a("el-tree",{ref:"tree",staticClass:"select-tree",style:"min-width: "+e.treeWidth,attrs:{"highlight-current":"",data:e.data,props:e.props,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"default-expand-all":e.expandAll},on:{"node-click":e.onClickNode}}),a("el-input",{ref:"input",class:{rotate:e.showStatus},style:"width: "+e.width+"px",attrs:{slot:"reference",clearable:e.clearable,"suffix-icon":"el-icon-arrow-down",placeholder:e.placeholder},slot:"reference",model:{value:e.labelModel,callback:function(t){e.labelModel=t},expression:"labelModel"}})],1)},s=[],i=(a("99af"),a("4de4"),a("d3b7"),a("0643"),a("2382"),a("b775")),r={name:"TreeBox",model:{prop:"value",event:"selected"},props:{value:{type:String,default:null},width:{type:String,default:null},expandAll:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},data:{type:Array,default:null},path:{type:String,default:null},params:{type:Object,default:null},placeholder:{type:String,required:!1,default:"请选择"},labelField:{type:String,default:null},props:{type:Object,required:!1,default:function(){return{parent:"pid",value:"id",label:"label",children:"children"}}}},data:function(){return{showStatus:!1,treeWidth:"auto",labelModel:"",valueModel:"0"}},watch:{labelModel:function(e){e||(this.valueModel=""),this.$refs.tree.filter(e)},value:function(e){this.labelModel=this.queryTree(this.data,e)}},created:function(){var e=this;null!=this.path?Object(i["a"])({url:this.path,data:this.params}).then((function(t){e.init(t),e.$emit("loaded",t)})).catch((function(e){console.log(e)})):this.init(this.data)},methods:{init:function(e){var t=this;this.data=e||[],this.labelField&&(this.props.label=this.labelField),this.value&&(this.labelModel=this.queryTree(this.data,this.value)),this.$nextTick((function(){t.treeWidth="".concat((t.width||t.$refs.input.$refs.input.clientWidth)-24,"px")}))},onClickNode:function(e){this.labelModel=e[this.props.label],this.valueModel=e[this.props.value],this.onCloseTree()},onCloseTree:function(){this.$refs.popover.showPopper=!1},onShowPopover:function(){this.showStatus=!0,this.$refs.tree.filter(!1)},onHidePopover:function(){this.showStatus=!1,this.$emit("selected",this.valueModel)},filterNode:function(e,t){return!e||-1!==t[this.props.label].indexOf(e)},queryTree:function(e,t){var a=[];a=a.concat(e);while(a.length){var l=a.shift();if(l[this.props.children]&&(a=a.concat(l[this.props.children])),l[this.props.value]===t)return l[this.props.label]}return""}}},o=r,n=(a("4f8b"),a("2877")),c=Object(n["a"])(o,l,s,!1,null,null,null);t["a"]=c.exports}}]);