{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1752649876948}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aScNCmltcG9ydCBQYWdlVGFibGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1BhZ2VUYWJsZS52dWUnDQppbXBvcnQgUmVnaW9uIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9SZWdpb24udnVlJw0KaW1wb3J0IFRyZWVCb3ggZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1RyZWVCb3gudnVlJw0KaW1wb3J0IFVzZXJDaG9zZW4gZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1VzZXJDaG9zZW4udnVlJw0KaW1wb3J0IE1hcExvY2F0aW9uIGZyb20gJ0Avdmlld3MvbWFwL3V0aWwvbG9jYXRpb24udnVlJw0KaW1wb3J0IFVwbG9hZEZpbGUgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL1VwbG9hZEZpbGUudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgUGFnZVRhYmxlLCBSZWdpb24sIFRyZWVCb3gsIFVzZXJDaG9zZW4sIE1hcExvY2F0aW9uLCBVcGxvYWRGaWxlIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHJlZ2lvbkxpc3Q6IFtdLA0KICAgICAgZGVwdFRyZWU6IFtdLCAvLyDmnLrmnoTmlbDmja7liJfooagNCiAgICAgIGFjdGl2ZUl0ZW06IHt9LA0KICAgICAgdGFibGVIZWlnaHQ6IDMwMCwNCiAgICAgIHJlZ2lvblZpc2libGU6IGZhbHNlLA0KICAgICAgcmVnaW9uRGF0YTogeyByZWdpb246ICcnIH0sDQogICAgICByZWdpb25SdWxlczogew0KICAgICAgICBjb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWMuuWfn+e8lueggScsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXljLrln5/lkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIGRlcHQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5omA5bGe5py65p6EJywgdHJpZ2dlcjogJ2JsdXInIH1dLA0KICAgICAgICByZWdpb246IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5omA5Zyo5Yy65YiSJywgdHJpZ2dlcjogJ2JsdXInIH1dDQogICAgICB9LA0KICAgICAgcWZvcm06IHsga2V5d29yZDogJycgfSwNCiAgICAgIGxvY2F0aW9uVmlzaWJsZTogZmFsc2UsDQogICAgICBsb2NhdGlvbkRhdGE6IHsgcmVnaW9uOiAnJyB9LA0KICAgICAgbG9jYXRpb25SdWxlczogew0KICAgICAgICBjb2RlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWcsOeCuee8lueggScsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlnLDngrnlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0NCiAgICAgIH0sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRMaXN0OiBbXSwNCiAgICAgIHVwbG9hZFZpc2libGU6IGZhbHNlLA0KICAgICAgc2hvd1VwbG9hZEFsbDogdHJ1ZSwNCiAgICAgIGFtTG9jYXRpb25Bc3NldDogW3t9XSwNCiAgICAgIC8vIOaJuemHj+S/ruaUueebuOWFsw0KICAgICAgYmF0Y2hGaWxlTGlzdDogW10sDQogICAgICBiYXRjaFVwbG9hZExpc3Q6IFtdLA0KICAgICAgYmF0Y2hVcGxvYWRWaXNpYmxlOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmxvYWREZXB0VHJlZSgpDQogICAgdGhpcy5sb2FkUmVnaW9uKCkNCiAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICB0aGlzLiRyZWZzLmdyaWQuc2V0TWF4SGVpZ2h0KE1hdGgubWF4KGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSAzODAsIDIwMCkpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmb3JtYXREYXRlVGltZShkYXRlVGltZSkgew0KICAgICAgaWYgKCFkYXRlVGltZSkgcmV0dXJuICcnDQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVRpbWUpDQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpDQogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhkYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpDQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhkYXRlLmdldE1pbnV0ZXMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgY29uc3Qgc2Vjb25kcyA9IFN0cmluZyhkYXRlLmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKQ0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gDQogICAgfSwNCiAgICBsb2FkRGVwdFRyZWUoKSB7DQogICAgICB0aGlzLiRodHRwKCcvc3lzL2RlcHQvdHJlZUJ5VHlwZS8xJykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmRlcHRUcmVlID0gcmVzDQogICAgICB9KS5jYXRjaCgoKSA9PiB7IHRoaXMuJGFsZXJ0KCfliqDovb3mnLrmnoTmoJHlh7rplJknKSB9KQ0KICAgIH0sDQogICAgbG9hZFJlZ2lvbigpIHsNCiAgICAgIHRoaXMuJGh0dHAoJy9hbS9yZWdpb24vbGlzdCcpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5yZWdpb25MaXN0ID0gcmVzIHx8IFtdDQogICAgICAgIGlmICh0aGlzLmFjdGl2ZUl0ZW0gJiYgdGhpcy5hY3RpdmVJdGVtLmlkKSB7DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByZXMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgIGlmIChyZXNbaV0uaWQgPT09IHRoaXMuYWN0aXZlSXRlbS5pZCkgew0KICAgICAgICAgICAgICB0aGlzLmFjdGl2ZUl0ZW0gPSByZXNbaV0NCiAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNob3dSZWdpb24oaXRlbSkgew0KICAgICAgdGhpcy5hY3RpdmVJdGVtID0gaXRlbQ0KICAgICAgdGhpcy5xZm9ybS5yZWdpb24gPSBpdGVtLmlkDQogICAgICB0aGlzLnFmb3JtLmtleXdvcmQgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hMb2NhdGlvbigpDQogICAgfSwNCiAgICBhZGRSZWdpb24oKSB7DQogICAgICB0aGlzLnJlZ2lvblZpc2libGUgPSB0cnVlDQogICAgICB0aGlzLnJlZ2lvbkRhdGEgPSB7IG9yZDogMSB9DQogICAgfSwNCiAgICBlZGl0UmVnaW9uKCkgew0KICAgICAgaWYgKCF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHJldHVybg0KICAgICAgdGhpcy5yZWdpb25WaXNpYmxlID0gdHJ1ZQ0KICAgICAgY29uc3QganNvbiA9IEpTT04uc3RyaW5naWZ5KHRoaXMuYWN0aXZlSXRlbSkNCiAgICAgIHRoaXMucmVnaW9uRGF0YSA9IEpTT04ucGFyc2UoanNvbikNCiAgICB9LA0KICAgIHNhdmVSZWdpb24oKSB7DQogICAgICB0aGlzLiRyZWZzLnJlZ2lvbmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL3JlZ2lvbi9zYXZlJywgZGF0YTogdGhpcy5yZWdpb25EYXRhIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjljLrln5/kv6Hmga/miJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLnJlZ2lvblZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmxvYWRSZWdpb24oKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICByZW1vdmVSZWdpb24oKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XljLrln58sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgeyBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLCB0eXBlOiAnd2FybmluZycgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vcmVnaW9uL2RlbGV0ZS8nICsgdGhpcy5hY3RpdmVJdGVtLmlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgICB0aGlzLmFjdGl2ZUl0ZW0gPSB7fQ0KICAgICAgICAgICAgdGhpcy5sb2FkUmVnaW9uKCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7IH0pDQogICAgfSwNCiAgICBzZWFyY2hMb2NhdGlvbigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLlj4LmlbA6JywgSlNPTi5zdHJpbmdpZnkodGhpcy5xZm9ybSkpDQogICAgICB0aGlzLiRyZWZzLmdyaWQuc2VhcmNoKHRoaXMucWZvcm0pDQogICAgfSwNCiAgICBhZGRMb2NhdGlvbigpIHsNCiAgICAgIHRoaXMubG9jYXRpb25WaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy5sb2NhdGlvbkRhdGEgPSB7IHJlZ2lvbjogdGhpcy5hY3RpdmVJdGVtLmlkIH0NCiAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0ID0gW10NCiAgICB9LA0KICAgIC8vIGVkaXRMb2NhdGlvbihpdGVtKSB7DQogICAgLy8gICB0aGlzLmxvY2F0aW9uVmlzaWJsZSA9IHRydWUNCiAgICAvLyAgIGNvbnN0IGpzb24gPSBKU09OLnN0cmluZ2lmeShpdGVtKQ0KICAgIC8vICAgdGhpcy5sb2NhdGlvbkRhdGEgPSBKU09OLnBhcnNlKGpzb24pDQogICAgLy8gfSwNCiAgICBlZGl0TG9jYXRpb24oaXRlbSkgew0KICAgICAgdGhpcy4kaHR0cCgnL2FtL2xvY2F0aW9uL2dldC8nICsgaXRlbS5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPiAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgdGhpcy5sb2NhdGlvblZpc2libGUgPSB0cnVlDQogICAgICAgICAgdGhpcy5sb2NhdGlvbkRhdGEgPSByZXMuZGF0YQ0KICAgICAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0ID0gcmVzLmRhdGEuYW1Mb2NhdGlvbkFzc2V0IHx8IFtdDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlTG9jYXRpb24oKSB7DQogICAgICB0aGlzLiRyZWZzLmxvY2F0aW9uZm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGRldGFpbHMgPSBbXQ0KICAgICAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0LmZvckVhY2gociA9PiBkZXRhaWxzLnB1c2goeyBzbjogci5zbiB9KSkNCiAgICAgICAgICBpZiAoIWRldGFpbHMubGVuZ3RoKSByZXR1cm4gdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flvZXlhaXnu4jnq6/kv6Hmga8nKQ0KICAgICAgICAgIHRoaXMubG9jYXRpb25EYXRhLmFtTG9jYXRpb25Bc3NldCA9IGRldGFpbHMNCiAgICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL2xvY2F0aW9uL3NhdmVEZXZpY2UnLCBkYXRhOiB0aGlzLmxvY2F0aW9uRGF0YSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5Yy65Z+f5L+h5oGv5oiQ5YqfJykNCiAgICAgICAgICAgICAgdGhpcy5sb2NhdGlvblZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgcmVtb3ZlTG9jYXRpb24oaXRlbSkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5Zyw54K5LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLCBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRodHRwKHsgdXJsOiAnL2FtL2xvY2F0aW9uL2RlbGV0ZS8nICsgaXRlbS5pZCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hMb2NhdGlvbigpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4geyB9KQ0KICAgIH0sDQogICAgbWFwUGluKCkgew0KICAgICAgY29uc3QgbGwgPSB0aGlzLmxvY2F0aW9uRGF0YS5sYXQgPyB7IGxuZzogdGhpcy5sb2NhdGlvbkRhdGEubG5nLCBsYXQ6IHRoaXMubG9jYXRpb25EYXRhLmxhdCB9IDogbnVsbA0KICAgICAgdGhpcy4kcmVmcy5tYXBMb2NhdGlvbi5zaG93KGxsKQ0KICAgIH0sDQogICAgcGluZWQocikgew0KICAgICAgdGhpcy4kc2V0KHRoaXMubG9jYXRpb25EYXRhLCAnYWRkcmVzcycsIHIuYWRkcmVzcykNCiAgICAgIHRoaXMuJHNldCh0aGlzLmxvY2F0aW9uRGF0YSwgJ2xuZycsIHIubG5nbGF0ID8gci5sbmdsYXQubG5nIDogbnVsbCkNCiAgICAgIHRoaXMuJHNldCh0aGlzLmxvY2F0aW9uRGF0YSwgJ2xhdCcsIHIubG5nbGF0ID8gci5sbmdsYXQubGF0IDogbnVsbCkNCiAgICB9LA0KICAgIHVwbG9hZCgpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXQ0KICAgICAgdGhpcy51cGxvYWRMaXN0ID0gW10NCiAgICAgIHRoaXMudXBsb2FkVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIHVwbG9hZFJlbW92ZSgpIHsNCiAgICAgIHRoaXMudXBsb2FkTGlzdCA9IFtdDQogICAgfSwNCiAgICB0b2dnbGVFcnIoKSB7DQogICAgICB0aGlzLnNob3dVcGxvYWRBbGwgPSAhdGhpcy5zaG93VXBsb2FkQWxsDQogICAgfSwNCiAgICB1cGxvYWRlZChmaWxlTGlzdCkgew0KICAgICAgaWYgKGZpbGVMaXN0ICYmIGZpbGVMaXN0Lmxlbmd0aCkgew0KICAgICAgICBjb25zdCBsb2FkSW5zdCA9IExvYWRpbmcuc2VydmljZSh7IGZ1bGxzY3JlZW46IHRydWUsIHRleHQ6ICfop6PmnpDmlofku7bkuK0uLi4nIH0pDQogICAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vdXBsb2FkRmlsZScsIGRhdGE6IGZpbGVMaXN0WzBdIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPiAwKSB7DQogICAgICAgICAgICB0aGlzLnNob3dVcGxvYWRBbGwgPSB0cnVlDQogICAgICAgICAgICB0aGlzLnVwbG9hZExpc3QgPSByZXMuZGF0YQ0KICAgICAgICAgIH0NCiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRVcGxvYWQoKSB7DQogICAgICBpZiAodGhpcy51cGxvYWRMaXN0Lmxlbmd0aCA9PT0gMCkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5Y+v5o+Q5Lqk55qE5pWw5o2uJykNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+aVsOaNruS4iuS8oOS4rS4uLicgfSkNCiAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vdXBsb2FkRGF0YScsIGRhdGE6IHRoaXMudXBsb2FkTGlzdCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykNCiAgICAgICAgICB0aGlzLnVwbG9hZFZpc2libGUgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICAgICAgfSBlbHNlIGlmIChyZXMuY29kZSA9PT0gMikgew0KICAgICAgICAgIHRoaXMudXBsb2FkTGlzdCA9IHJlcy5kYXRhDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a2Y5Zyo6ZSZ6K+v55qE5pWw5o2u6KGMJykNCiAgICAgICAgfQ0KICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc6LaF5pe2JykNCiAgICAgIH0pDQogICAgfSwNCiAgICBhZGRBc3NldCgpIHsNCiAgICAgIHRoaXMuYW1Mb2NhdGlvbkFzc2V0LnB1c2goe30pDQogICAgfSwNCiAgICByZW1vdmVBc3NldChyb3dJbmRleCkgew0KICAgICAgdGhpcy5hbUxvY2F0aW9uQXNzZXQuc3BsaWNlKHJvd0luZGV4LCAxKQ0KICAgIH0sDQogICAgLy8g5om56YeP5L+u5pS555u45YWz5pa55rOVDQogICAgaGFuZGxlQmF0Y2hDb21tYW5kKGNvbW1hbmQpIHsNCiAgICAgIGlmIChjb21tYW5kID09PSAnZXhwb3J0Jykgew0KICAgICAgICB0aGlzLmV4cG9ydEJhdGNoKCkNCiAgICAgIH0gZWxzZSBpZiAoY29tbWFuZCA9PT0gJ2ltcG9ydCcpIHsNCiAgICAgICAgdGhpcy5pbXBvcnRCYXRjaCgpDQogICAgICB9DQogICAgfSwNCiAgICBleHBvcnRCYXRjaCgpIHsNCiAgICAgIGlmICghdGhpcy5hY3RpdmVJdGVtIHx8ICF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5LiA5Liq5Yy65Z+fJykNCiAgICAgIH0NCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ato+WcqOWvvOWHuuaWh+S7tu+8jOivt+iAkOW/g+etieW+hS4uLicgfSkNCiAgICAgIHRoaXMuJGphc3Blcih7IHVybDogYC9hbS9sb2NhdGlvbi9leHBvcnRCYXRjaC8ke3RoaXMuYWN0aXZlSXRlbS5pZH1gLCByZXNwb25zZVR5cGU6ICdibG9iJyB9KS50aGVuKGJsb2IgPT4gew0KICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIHRoaXMuJHNhdmVBcyhibG9iLCAn572R54K55om56YeP5L+u5pS55qih5p2/Lnhsc3gnKQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflr7zlh7rnlJ/miJDlh7rplJk6JyArIGVycikNCiAgICAgIH0pDQogICAgfSwNCiAgICBpbXBvcnRCYXRjaCgpIHsNCiAgICAgIGlmICghdGhpcy5hY3RpdmVJdGVtIHx8ICF0aGlzLmFjdGl2ZUl0ZW0uaWQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5LiA5Liq5Yy65Z+fJykNCiAgICAgIH0NCiAgICAgIHRoaXMuYmF0Y2hGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLmJhdGNoVXBsb2FkTGlzdCA9IFtdDQogICAgICB0aGlzLmJhdGNoVXBsb2FkVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGJhdGNoVXBsb2FkUmVtb3ZlKCkgew0KICAgICAgdGhpcy5iYXRjaFVwbG9hZExpc3QgPSBbXQ0KICAgIH0sDQogICAgcmVtb3ZlQmF0Y2hSb3coaW5kZXgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOi/meadoeaVsOaNruWQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRMaXN0LnNwbGljZShpbmRleCwgMSkNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBnZXRCYXRjaEVycm9yQ291bnQoKSB7DQogICAgICByZXR1cm4gdGhpcy5iYXRjaFVwbG9hZExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yb3dNc2cgIT0gbnVsbCkubGVuZ3RoDQogICAgfSwNCiAgICBiYXRjaFVwbG9hZGVkKGZpbGVMaXN0KSB7DQogICAgICBpZiAoZmlsZUxpc3QgJiYgZmlsZUxpc3QubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+ino+aekOaWh+S7tuS4rS4uLicgfSkNCiAgICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9hbS9sb2NhdGlvbi91cGxvYWRCYXRjaEZpbGUnLCBkYXRhOiBmaWxlTGlzdFswXSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID4gMCkgew0KICAgICAgICAgICAgdGhpcy5iYXRjaFVwbG9hZExpc3QgPSByZXMuZGF0YQ0KICAgICAgICAgIH0NCiAgICAgICAgICBsb2FkSW5zdC5jbG9zZSgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRCYXRjaFVwZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmJhdGNoVXBsb2FkTGlzdC5sZW5ndGggPT09IDApIHJldHVybiB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+aPkOS6pOeahOaVsOaNricpDQoNCiAgICAgIC8vIOi/h+a7pOWHuuayoeaciemUmeivr+eahOaVsOaNrg0KICAgICAgY29uc3QgdmFsaWREYXRhID0gdGhpcy5iYXRjaFVwbG9hZExpc3QuZmlsdGVyKGl0ZW0gPT4gIWl0ZW0ucm93TXNnKQ0KICAgICAgaWYgKHZhbGlkRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5pyJ5pWI55qE5pWw5o2u5Y+v5Lul5o+Q5Lqk77yM6K+35YWI5Yig6Zmk6ZSZ6K+v6KGM5oiW5L+u5q2j5pWw5o2uJykNCiAgICAgIH0NCg0KICAgICAgY29uc3QgZXJyb3JDb3VudCA9IHRoaXMuZ2V0QmF0Y2hFcnJvckNvdW50KCkNCiAgICAgIGlmIChlcnJvckNvdW50ID4gMCkgew0KICAgICAgICB0aGlzLiRjb25maXJtKGDlvZPliY3mnIkgJHtlcnJvckNvdW50fSDmnaHplJnor6/mlbDmja7lsIbooqvlv73nlaXvvIzlj6rmj5DkuqQgJHt2YWxpZERhdGEubGVuZ3RofSDmnaHmnInmlYjmlbDmja7vvIzmmK/lkKbnu6fnu63vvJ9gLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jaGVja0R1cGxpY2F0ZXNBbmRVcGRhdGUodmFsaWREYXRhKQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2hlY2tBbmRDb25maXJtRHVwbGljYXRlcyh2YWxpZERhdGEpDQogICAgICB9DQogICAgfSwNCiAgICBjaGVja0FuZENvbmZpcm1EdXBsaWNhdGVzKGRhdGEpIHsNCiAgICAgIC8vIOajgOafpemHjeWkjee8lueggQ0KICAgICAgdGhpcy4kaHR0cCh7IHVybDogJy9hbS9sb2NhdGlvbi9jaGVja0JhdGNoRHVwbGljYXRlcycsIGRhdGE6IGRhdGEgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDEgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBkdXBsaWNhdGVJbmZvID0gcmVzLmRhdGENCiAgICAgICAgICBpZiAoZHVwbGljYXRlSW5mby5oYXNEdXBsaWNhdGVzKSB7DQogICAgICAgICAgICBsZXQgbWVzc2FnZSA9ICcnDQogICAgICAgICAgICBjb25zdCBpbXBvcnREdXBsaWNhdGVzID0gZHVwbGljYXRlSW5mby5pbXBvcnREdXBsaWNhdGVzIHx8IFtdDQogICAgICAgICAgICBjb25zdCBkYkR1cGxpY2F0ZXMgPSBkdXBsaWNhdGVJbmZvLmRiRHVwbGljYXRlcyB8fCBbXQ0KDQogICAgICAgICAgICBpZiAoaW1wb3J0RHVwbGljYXRlcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOajgOa1i+WIsOS7peS4i+e9keeCuee8lueggeWcqOWvvOWFpeaVsOaNruS4remHjeWkjeWHuueOsO+8miR7aW1wb3J0RHVwbGljYXRlcy5qb2luKCcsICcpfeOAglxuYA0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBpZiAoZGJEdXBsaWNhdGVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgbWVzc2FnZSArPSBg5qOA5rWL5Yiw5Lul5LiL572R54K557yW56CB5Zyo5pWw5o2u5bqT5Lit5a2Y5Zyo5aSa5p2h6K6w5b2V77yaJHtkYkR1cGxpY2F0ZXMuam9pbignLCAnKX3jgIJcbmANCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIG1lc3NhZ2UgKz0gYOmHjeWkjee8lueggeWwhuS7peacgOWQjuS4gOadoeaVsOaNruS4uuWHhlxuYA0KICAgICAgICAgICAgbWVzc2FnZSArPSBg5piv5ZCm57un57ut5omn6KGM5om56YeP5pu05paw77yfYA0KDQogICAgICAgICAgICB0aGlzLiRjb25maXJtKG1lc3NhZ2UsICflj5HnjrDph43lpI3nvJbnoIEnLCB7DQogICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5pu05pawJywNCiAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiBmYWxzZQ0KICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZG9CYXRjaFVwZGF0ZShkYXRhKQ0KICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgICAvLyDnlKjmiLflj5bmtojvvIzkuI3miafooYzmm7TmlrANCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOayoeaciemHjeWkjee8luegge+8jOebtOaOpeabtOaWsA0KICAgICAgICAgICAgdGhpcy5kb0JhdGNoVXBkYXRlKGRhdGEpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOajgOafpeWksei0pe+8jOebtOaOpeabtOaWsA0KICAgICAgICAgIHRoaXMuZG9CYXRjaFVwZGF0ZShkYXRhKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIC8vIOajgOafpeWksei0pe+8jOebtOaOpeabtOaWsA0KICAgICAgICB0aGlzLmRvQmF0Y2hVcGRhdGUoZGF0YSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBkb0JhdGNoVXBkYXRlKGRhdGEpIHsNCiAgICAgIGNvbnN0IGxvYWRJbnN0ID0gTG9hZGluZy5zZXJ2aWNlKHsgZnVsbHNjcmVlbjogdHJ1ZSwgdGV4dDogJ+aVsOaNruS4iuS8oOS4rS4uLicgfSkNCiAgICAgIHRoaXMuJGh0dHAoeyB1cmw6ICcvYW0vbG9jYXRpb24vYmF0Y2hVcGRhdGUnLCBkYXRhOiBkYXRhIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAxKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmibnph4/mm7TmlrDmiJDlip9gKQ0KICAgICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnNlYXJjaExvY2F0aW9uKCkNCiAgICAgICAgfSBlbHNlIGlmIChyZXMuY29kZSA9PT0gMikgew0KICAgICAgICAgIHRoaXMuYmF0Y2hVcGxvYWRMaXN0ID0gcmVzLmRhdGENCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfpg6jliIbmlbDmja7mm7TmlrDlpLHotKXvvIzor7fmn6XnnIvplJnor6/kv6Hmga8nKQ0KICAgICAgICB9DQogICAgICAgIGxvYWRJbnN0LmNsb3NlKCkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgbG9hZEluc3QuY2xvc2UoKQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/bd/region", "sourcesContent": ["<template>\r\n  <div class=\"layout-lr\">\r\n    <div class=\"left\">\r\n      <div class=\"head\">区域列表</div>\r\n      <div class=\"body\">\r\n        <ul>\r\n          <li :class=\"{ act: activeItem == null || activeItem.id == null }\" @click=\"showRegion({})\">所有区域</li>\r\n          <li v-for=\"item in regionList\" :key=\"item.id\" :class=\"{ act: activeItem && activeItem.id == item.id }\"\r\n            @click=\"showRegion(item)\">{{ item.name }}</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <el-descriptions title=\"区域信息\" :column=\"3\" border class=\"descr-3\">\r\n        <template slot=\"extra\">\r\n          <div class=\"button-bar\">\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addRegion\">新增区域</el-button>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-upload\" @click=\"upload\">网点导入</el-button>\r\n            <template v-if=\"activeItem && activeItem.id\">\r\n              <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-edit\" @click=\"editRegion\">编辑区域</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"removeRegion\">删除区域</el-button>\r\n              <el-button type=\"success\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addLocation\">新增网点</el-button>\r\n              <el-dropdown @command=\"handleBatchCommand\" style=\"margin-left:10px\">\r\n                <el-button type=\"warning\" size=\"mini\">\r\n                  批量修改<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"export\">导出</el-dropdown-item>\r\n                  <el-dropdown-item command=\"import\">导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"activeItem && activeItem.id\">\r\n          <el-descriptions-item label=\"所属部门\">{{ activeItem.deptName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域代码\">{{ activeItem.code }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域名称\">{{ activeItem.name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"所在区划\">{{ activeItem.regionName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"负责人\">{{ activeItem.userName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"联系电话\">{{ activeItem.userPhone }}</el-descriptions-item>\r\n          <el-descriptions-item :span=\"3\" label=\"区域范围\">{{ activeItem.scope }}</el-descriptions-item>\r\n        </template>\r\n      </el-descriptions>\r\n      <el-divider></el-divider>\r\n      <div class=\"location-head\">\r\n        <span class=\"location-title\">区域网点</span>\r\n        <div class=\"location-search\">\r\n          <el-input v-model=\"qform.keyword\" clearable size=\"mini\" placeholder=\"输入关键字\" autocomplete=\"off\">\r\n            <template slot=\"prepend\">检索:</template>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchLocation\"></el-button>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <page-table ref=\"grid\" size=\"mini\" path=\"/am/location/page\" :query=\"qform\" stripe border>\r\n        <el-table-column v-if=\"activeItem == null || activeItem.id == null\" label=\"所属区域\" prop=\"regionName\" width=\"100\"\r\n          align=\"center\" />\r\n        <el-table-column label=\"网点编码\" prop=\"code\" width=\"110\" align=\"center\" />\r\n        <el-table-column label=\"网点名称\" prop=\"name\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"负责人\" prop=\"contact\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"联系方式\" prop=\"phone\" width=\"150\" align=\"center\" />\r\n        <el-table-column label=\"地址\" prop=\"address\" width=\"800\" align=\"center\" />\r\n        <el-table-column label=\"网点时间\" prop=\"createTime\" align=\"center\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.createTime ? formatDateTime(scope.row.createTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"{ row }\">\r\n            <div v-if=\"row.id != 'admin'\">\r\n              <el-button type=\"primary\" size=\"mini\" @click.stop=\"editLocation(row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" @click.stop=\"removeLocation(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n    <el-dialog v-dialog-drag title=\"区域信息\" width=\"800px\" :visible.sync=\"regionVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"regionform\" :model=\"regionData\" :rules=\"regionRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\" prop=\"code\">\r\n              <el-input v-model=\"regionData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\" prop=\"name\">\r\n              <el-input v-model=\"regionData.name\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <tree-box v-model=\"regionData.dept\" :data=\"deptTree\" :expand-all=\"true\" :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所在区划：\" prop=\"region\">\r\n              <region v-model=\"regionData.region\" root=\"460000\" :start-level=\"1\" with-root any-node\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"user\">\r\n              <user-chosen v-model=\"regionData.user\" type=\"1\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域排序：\" prop=\"ord\">\r\n              <el-input-number v-model=\"regionData.ord\" autocomplete=\"off\" :min=\"1\" :max=\"999\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"区域范围：\" prop=\"scope\">\r\n              <el-input v-model=\"regionData.scope\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"regionVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveRegion\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog v-dialog-drag title=\"区域地点\" width=\"800px\" :visible.sync=\"locationVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"locationform\" :model=\"locationData\" :rules=\"locationRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\">\r\n              <el-input v-model=\"activeItem.code\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\">\r\n              <el-input v-model=\"activeItem.name\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点编码：\" prop=\"code\">\r\n              <el-input v-model=\"locationData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点名称：\" prop=\"name\">\r\n              <el-input v-model=\"locationData.name\" maxlength=\"64\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"contact\">\r\n              <el-input v-model=\"locationData.contact\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话：\" prop=\"phone\">\r\n              <el-input v-model=\"locationData.phone\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点地址：\" prop=\"address\">\r\n              <el-input v-model=\"locationData.address\" maxlength=\"128\" autocomplete=\"off\">\r\n                <template slot=\"append\">\r\n                  <el-button size=\"mini\" icon=\"el-icon-map-location\" @click=\"mapPin\">地图定位</el-button>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点备注：\" prop=\"memo\">\r\n              <el-input v-model=\"locationData.memo\" maxlength=\"128\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table ref=\"optionGrid\" :data=\"amLocationAsset\" size=\"mini\" :stripe=\"true\" :border=\"true\">\r\n        <el-table-column type=\"index\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"sn\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.sn\" size=\"mini\" autocomplete=\"off\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column width=\"70\" align=\"center\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"danger\" @click.stop=\"removeAsset(scope.$index)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"locationVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveLocation\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!--------网点导入------->\r\n    <el-dialog ref=\"uploadDlg\" title=\"批量导入\" fullscreen class=\"dialog-full\" :visible.sync=\"uploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"fileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"uploaded\" @removeFile=\"uploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th>结果提示</th>\r\n              <th>所属市县编号</th>\r\n              <th>销售终端编号</th>\r\n              <th>门店编号</th>\r\n              <th>业主姓名</th>\r\n              <th>负责人</th>\r\n              <th>联系方式</th>\r\n              <th>门店地址</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"item in uploadList\">\r\n              <tr v-if=\"showUploadAll || item.rowMsg != null\" :key=\"item.rowNum\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ item.rowNum }}</td>\r\n                <td class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.region }}</td>\r\n                <td>{{ item.sn }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>{{ item.contact }}</td>\r\n                <td>{{ item.phone }}</td>\r\n                <td>{{ item.address }}</td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <el-button size=\"small\" type=\"warning\" @click=\"toggleErr\">{{ showUploadAll ? '查看错误' : '查看全部' }}</el-button>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"uploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitUpload\">确定上传</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog ref=\"batchUploadDlg\" title=\"网点批量修改\" fullscreen class=\"dialog-full\" :visible.sync=\"batchUploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"batchFileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"batchUploaded\" @removeFile=\"batchUploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th v-if=\"getBatchErrorCount() > 0\">结果提示</th>\r\n              <th>网点编码</th>\r\n              <th>网点名称</th>\r\n              <th>网点备注</th>\r\n              <th>操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"(item, index) in batchUploadList\">\r\n              <tr :key=\"index\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ index + 1 }}</td>\r\n                <td v-if=\"getBatchErrorCount() > 0\" class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>\r\n                  <el-input\r\n                    v-model=\"item.memo\"\r\n                    size=\"mini\"\r\n                    placeholder=\"请输入网点备注\"\r\n                    maxlength=\"200\"\r\n                    show-word-limit\r\n                    clearable\r\n                    style=\"width: 100%;\"\r\n                  />\r\n                </td>\r\n                <td>\r\n                  <el-button type=\"danger\" size=\"mini\" @click=\"removeBatchRow(index)\">删除</el-button>\r\n                </td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <span style=\"color: #909399; font-size: 12px;\">\r\n            共 {{ batchUploadList.length }} 条数据，其中 {{ getBatchErrorCount() }} 条错误\r\n          </span>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"batchUploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBatchUpdate\">确定更新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <map-location ref=\"mapLocation\" @success=\"pined\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { Loading } from 'element-ui'\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport Region from '@/views/components/Region.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport MapLocation from '@/views/map/util/location.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { PageTable, Region, TreeBox, UserChosen, MapLocation, UploadFile },\r\n  data() {\r\n    return {\r\n      regionList: [],\r\n      deptTree: [], // 机构数据列表\r\n      activeItem: {},\r\n      tableHeight: 300,\r\n      regionVisible: false,\r\n      regionData: { region: '' },\r\n      regionRules: {\r\n        code: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],\r\n        dept: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],\r\n        region: [{ required: true, message: '请选择所在区划', trigger: 'blur' }]\r\n      },\r\n      qform: { keyword: '' },\r\n      locationVisible: false,\r\n      locationData: { region: '' },\r\n      locationRules: {\r\n        code: [{ required: true, message: '请输入地点编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入地点名称', trigger: 'blur' }]\r\n      },\r\n      fileList: [],\r\n      uploadList: [],\r\n      uploadVisible: false,\r\n      showUploadAll: true,\r\n      amLocationAsset: [{}],\r\n      // 批量修改相关\r\n      batchFileList: [],\r\n      batchUploadList: [],\r\n      batchUploadVisible: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDeptTree()\r\n    this.loadRegion()\r\n    this.searchLocation()\r\n    this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight - 380, 200))\r\n  },\r\n  methods: {\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegion() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionList = res || []\r\n        if (this.activeItem && this.activeItem.id) {\r\n          for (let i = 0; i < res.length; i++) {\r\n            if (res[i].id === this.activeItem.id) {\r\n              this.activeItem = res[i]\r\n              break\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    showRegion(item) {\r\n      this.activeItem = item\r\n      this.qform.region = item.id\r\n      this.qform.keyword = ''\r\n      this.searchLocation()\r\n    },\r\n    addRegion() {\r\n      this.regionVisible = true\r\n      this.regionData = { ord: 1 }\r\n    },\r\n    editRegion() {\r\n      if (!this.activeItem.id) return\r\n      this.regionVisible = true\r\n      const json = JSON.stringify(this.activeItem)\r\n      this.regionData = JSON.parse(json)\r\n    },\r\n    saveRegion() {\r\n      this.$refs.regionform.validate(valid => {\r\n        if (valid) {\r\n          this.$http({ url: '/am/region/save', data: this.regionData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.regionVisible = false\r\n              this.loadRegion()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeRegion() {\r\n      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/region/delete/' + this.activeItem.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.activeItem = {}\r\n            this.loadRegion()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    searchLocation() {\r\n      console.log('搜索参数:', JSON.stringify(this.qform))\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    addLocation() {\r\n      this.locationVisible = true\r\n      this.locationData = { region: this.activeItem.id }\r\n      this.amLocationAsset = []\r\n    },\r\n    // editLocation(item) {\r\n    //   this.locationVisible = true\r\n    //   const json = JSON.stringify(item)\r\n    //   this.locationData = JSON.parse(json)\r\n    // },\r\n    editLocation(item) {\r\n      this.$http('/am/location/get/' + item.id).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          this.locationVisible = true\r\n          this.locationData = res.data\r\n          this.amLocationAsset = res.data.amLocationAsset || []\r\n        }\r\n      })\r\n    },\r\n    saveLocation() {\r\n      this.$refs.locationform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.amLocationAsset.forEach(r => details.push({ sn: r.sn }))\r\n          if (!details.length) return this.$message.warning('请录入终端信息')\r\n          this.locationData.amLocationAsset = details\r\n          this.$http({ url: '/am/location/saveDevice', data: this.locationData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.locationVisible = false\r\n              this.searchLocation()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeLocation(item) {\r\n      this.$confirm('此操作将永久删除该地点, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/location/delete/' + item.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.searchLocation()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    mapPin() {\r\n      const ll = this.locationData.lat ? { lng: this.locationData.lng, lat: this.locationData.lat } : null\r\n      this.$refs.mapLocation.show(ll)\r\n    },\r\n    pined(r) {\r\n      this.$set(this.locationData, 'address', r.address)\r\n      this.$set(this.locationData, 'lng', r.lnglat ? r.lnglat.lng : null)\r\n      this.$set(this.locationData, 'lat', r.lnglat ? r.lnglat.lat : null)\r\n    },\r\n    upload() {\r\n      this.fileList = []\r\n      this.uploadList = []\r\n      this.uploadVisible = true\r\n    },\r\n    uploadRemove() {\r\n      this.uploadList = []\r\n    },\r\n    toggleErr() {\r\n      this.showUploadAll = !this.showUploadAll\r\n    },\r\n    uploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.showUploadAll = true\r\n            this.uploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitUpload() {\r\n      if (this.uploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/uploadData', data: this.uploadList }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success('上传成功')\r\n          this.$emit('success')\r\n          this.uploadVisible = false\r\n          this.search()\r\n        } else if (res.code === 2) {\r\n          this.uploadList = res.data\r\n          this.$message.error('存在错误的数据行')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n        // this.$message.error('网络超时')\r\n      })\r\n    },\r\n    addAsset() {\r\n      this.amLocationAsset.push({})\r\n    },\r\n    removeAsset(rowIndex) {\r\n      this.amLocationAsset.splice(rowIndex, 1)\r\n    },\r\n    // 批量修改相关方法\r\n    handleBatchCommand(command) {\r\n      if (command === 'export') {\r\n        this.exportBatch()\r\n      } else if (command === 'import') {\r\n        this.importBatch()\r\n      }\r\n    },\r\n    exportBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: `/am/location/exportBatch/${this.activeItem.id}`, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '网点批量修改模板.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    importBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      this.batchFileList = []\r\n      this.batchUploadList = []\r\n      this.batchUploadVisible = true\r\n    },\r\n    batchUploadRemove() {\r\n      this.batchUploadList = []\r\n    },\r\n    removeBatchRow(index) {\r\n      this.$confirm('确定要删除这条数据吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchUploadList.splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    getBatchErrorCount() {\r\n      return this.batchUploadList.filter(item => item.rowMsg != null).length\r\n    },\r\n    batchUploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadBatchFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.batchUploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitBatchUpdate() {\r\n      if (this.batchUploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n\r\n      // 过滤出没有错误的数据\r\n      const validData = this.batchUploadList.filter(item => !item.rowMsg)\r\n      if (validData.length === 0) {\r\n        return this.$message.warning('没有有效的数据可以提交，请先删除错误行或修正数据')\r\n      }\r\n\r\n      const errorCount = this.getBatchErrorCount()\r\n      if (errorCount > 0) {\r\n        this.$confirm(`当前有 ${errorCount} 条错误数据将被忽略，只提交 ${validData.length} 条有效数据，是否继续？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.checkDuplicatesAndUpdate(validData)\r\n        }).catch(() => {})\r\n      } else {\r\n        this.checkAndConfirmDuplicates(validData)\r\n      }\r\n    },\r\n    checkAndConfirmDuplicates(data) {\r\n      // 检查重复编码\r\n      this.$http({ url: '/am/location/checkBatchDuplicates', data: data }).then(res => {\r\n        if (res.code === 1 && res.data) {\r\n          const duplicateInfo = res.data\r\n          if (duplicateInfo.hasDuplicates) {\r\n            let message = ''\r\n            const importDuplicates = duplicateInfo.importDuplicates || []\r\n            const dbDuplicates = duplicateInfo.dbDuplicates || []\r\n\r\n            if (importDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在导入数据中重复出现：${importDuplicates.join(', ')}。\\n`\r\n            }\r\n\r\n            if (dbDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在数据库中存在多条记录：${dbDuplicates.join(', ')}。\\n`\r\n            }\r\n            message += `重复编码将以最后一条数据为准\\n`\r\n            message += `是否继续执行批量更新？`\r\n\r\n            this.$confirm(message, '发现重复编码', {\r\n              confirmButtonText: '确定更新',\r\n              cancelButtonText: '取消',\r\n              type: 'warning',\r\n              dangerouslyUseHTMLString: false\r\n            }).then(() => {\r\n              this.doBatchUpdate(data)\r\n            }).catch(() => {\r\n              // 用户取消，不执行更新\r\n            })\r\n          } else {\r\n            // 没有重复编码，直接更新\r\n            this.doBatchUpdate(data)\r\n          }\r\n        } else {\r\n          // 检查失败，直接更新\r\n          this.doBatchUpdate(data)\r\n        }\r\n      }).catch(() => {\r\n        // 检查失败，直接更新\r\n        this.doBatchUpdate(data)\r\n      })\r\n    },\r\n    doBatchUpdate(data) {\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/batchUpdate', data: data }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success(`批量更新成功`)\r\n          this.batchUploadVisible = false\r\n          this.searchLocation()\r\n        } else if (res.code === 2) {\r\n          this.batchUploadList = res.data\r\n          this.$message.error('部分数据更新失败，请查看错误信息')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang='scss' scope>\r\n.layout-lr {\r\n  height: calc(100vh - 52px);\r\n}\r\n\r\n.layout-lr .left {\r\n  min-width: 220px;\r\n}\r\n\r\n.layout-lr .center {\r\n  overflow: hidden;\r\n}\r\n\r\n.location-head {\r\n  height: 40px;\r\n}\r\n\r\n.location-title {\r\n  line-height: 32px;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n}\r\n\r\n.location-search {\r\n  width: 300px;\r\n  float: right;\r\n}\r\n\r\n.upload-block {\r\n  padding: 0 4px;\r\n  width: calc(100vw - 12px);\r\n  height: calc(100vh - 250px);\r\n  overflow: auto;\r\n}\r\n\r\n.upload-block table {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.upload-block table th,\r\n.upload-block table td {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n}\r\n\r\n.upload-block table tr.err {\r\n  background-color: #faee92;\r\n}\r\n</style>\r\n"]}]}