(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a7437a1"],{"07a7":function(e,t,n){"use strict";n("a105")},a105:function(e,t,n){},e8f3:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"dp-container"},[n("div",{ref:"map",staticClass:"map"})])},o=[],a=(n("d81d"),n("b0c0"),n("d3b7"),n("0643"),n("a573"),{name:"DpMonitor",data:function(){return{omap:null,map:{center:null,zoom:13,satellite:!1,markers:[]},markers:[],infoWindow:new window.AMap.InfoWindow({offset:new window.AMap.Pixel(6,-30)})}},beforeMount:function(){window.addEventListener("resize",this.resizeHandler)},beforeDestroy:function(){window.removeEventListener("resize",this.resizeHandler),this.omap&&this.omap.destroy(),this.omap=null},created:function(){this.resizeHandler()},mounted:function(){this.initMap()},methods:{resizeHandler:function(){this.containerHeight=document.documentElement.clientHeight-67,this.infoHeight=document.documentElement.clientHeight-280},initMap:function(){this.omap=new window.AMap.Map(this.$refs.map,this.map),this.omap.on("complete",this.mapComplete)},mapComplete:function(e){this.renderMapPoint()},renderMapPoint:function(){this.markers&&this.markers.length&&(this.omap.remove(this.markers),this.markers=[])},openInfoWindow:function(e){if(e.target){var t=e.target.getExtData(),n='<div style="padding:20px 15px;">';n+="<div>点位分组："+(t.groupName||"通用")+"</div>",n+="<div>点位编码："+t.no+"</div>",n+="<div>点位名称："+t.name+"</div>",n+="<div>地址："+(t.address||"")+"</div>",n+="</div>",this.infoWindow.setContent(n),this.infoWindow.open(this.omap,e.target.getPosition())}}}}),s=a,r=(n("07a7"),n("2877")),d=Object(r["a"])(s,i,o,!1,null,"452e7308",null);t["default"]=d.exports}}]);