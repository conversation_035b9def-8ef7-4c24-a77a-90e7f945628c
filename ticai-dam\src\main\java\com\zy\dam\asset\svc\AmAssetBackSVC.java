package com.zy.dam.asset.svc;

import com.zy.app.vo.User;
import com.zy.dam.asset.dao.AmAssetBackDAO;
import com.zy.dam.asset.dao.AmAssetSnDAO;
import com.zy.dam.asset.orm.AmAssetBack;
import com.zy.dam.asset.orm.AmAssetSn;
import com.zy.dam.asset.req.form.AssetBackApply;
import com.zy.dam.asset.req.form.AssetBackCheck;
import com.zy.dam.asset.req.form.AssetDetailForm;
import com.zy.dam.asset.vo.AssetBackVo;
import com.zy.dam.asset.vo.AssetDetailVo;
import com.zy.dam.report.vo.AssetBackExportVo;
import com.zy.model.Page;
import com.zy.model.Result;
import com.zy.model.VueFile;
import com.zy.sys.api.NoApi;
import com.zy.sys.dao.SysDeptDAO;
import com.zy.sys.dao.SysFileDAO;
import com.zy.sys.orm.SysDept;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>资管-资产退库业务服务接口</p>
 */
@Service
@Slf4j
public class AmAssetBackSVC {

    private static final String NO_KEY_BACK = "ASSET_BACK";
    /**
     * 资管-资产退库数据访问接口
     **/
    @Resource
    private AmAssetBackDAO dao;

    @Resource
    private SysDeptDAO deptDao;

    @Resource
    private NoApi noApi;
    @Resource
    private SysFileDAO fileDAO;
    @Resource
    private AmAssetTraceRecordSVC deviceTrackRecordSVC;
    @Resource
    private AmAssetSnDAO assetSnDAO;
    @Resource
    private com.zy.dam.asset.dao.AmAssetDAO assetDAO;

    @Transactional(rollbackFor = Exception.class)
    public Result apply(AssetBackApply form) {
        if (form.getAssetList() == null || form.getAssetList().isEmpty()) return Result.err("必须选择退库资产");
        AmAssetBack item = new AmAssetBack();
        BeanUtils.copyProperties(form, item);

//        if (item.getDept() != null) {
//            SysDept dept = deptDao.findOne(item.getDept());
//            if (dept != null) {
//                item.setRegion(dept.getNo());
//            }
//        }

        // 生成单号
        item.setNo(noApi.nextNo(NO_KEY_BACK));
        item.setStatus("1");
        dao.insert(item);

        if (form.getFileList() != null) {
            for (VueFile file : form.getFileList()) {
                fileDAO.updateRid(file.getId(), item.getId());
            }
        }

        int index = 1;
        for (AssetDetailForm detail : form.getAssetList()) {
            detail.setId(item.getId());
            detail.setOrd(index++);
            dao.insertDetail(detail);
            dao.updateSnStatus(detail.getAsset());
        }
        //直接审批通过
        AssetBackCheck check = new AssetBackCheck();
        check.setId(item.getId());
        check.setUser(item.getUser());
        check.setResult("1");
        return check(check);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result check(AssetBackCheck form) {
        AmAssetBack item = dao.findOne(form.getId());
        if (item == null) return Result.err("无法获取申请单");
        item.setStatus("1".equals(form.getResult()) ? "2" : "5");
        item.setCheckUser(form.getUser());
        item.setCheckMemo(form.getMemo());
        dao.updateCheck(item);
        //申请通过则修改资产状态，这里未考虑关联资产
        if ("2".equals(item.getStatus())) {
            //TODO 获取最后的申请单
            dao.updateBack(item.getId()); //退库

            // 记录设备退机操作
            List<AssetDetailVo> details = dao.findDetail(item.getId());
            if (details != null && !details.isEmpty()) {
                User currentUser = new User();
                currentUser.setId(form.getUser());

                for (AssetDetailVo detail : details) {
                    // 获取终端号
                    String terminalNo = null;
                    AmAssetSn assetSn = assetSnDAO.findOne(detail.getId());
                    if (assetSn != null) {
                        terminalNo = assetSn.getNowSn();
                    }

                    // 获取资产的网点ID
                    String locationId = null;
                    // 从资产表中获取网点ID
                    if (detail.getId() != null) {
                        locationId = assetDAO.findLocationById(detail.getId());
                    }

                    // 记录退机操作
                    deviceTrackRecordSVC.recordBack(
                        detail.getId(),
                        terminalNo,
                        new Date(), // 当前时间
                        item.getUser(),
                        locationId, // 使用从资产表中查询到的网点ID
                        item.getMemo(),
                        item.getId(),
                        currentUser
                    );

                    log.info("记录设备退机操作，资产ID：{}，终端号：{}，网点ID：{}", detail.getId(), terminalNo, locationId);
                }
            }
        }
        return Result.ok();
    }

    public List<AssetBackVo> page(Page page) {
        return dao.page(page);
    }

    public AmAssetBack findOne(String id) {
        return dao.findOne(id);
    }

    public AssetBackVo findVo(String id) {
        AssetBackVo vo = dao.findVo(id);
        if (vo == null) return null;
        vo.setDetails(dao.findDetail(id));
        vo.setFileList(fileDAO.findVueFileByRid("TK", id));
        return vo;
    }

    public List<AssetBackExportVo> findExport(List<String> ids) {
        if (ids == null || ids.isEmpty()) return new ArrayList<>();
        return dao.findExportByIds(ids);
    }

    public List<AssetBackExportVo> findExport(Page page) {
        page.setNotPage(true);
        return dao.findExportByQuery(page);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result recover(String id) {
        AmAssetBack item = dao.findOne(id);
        if (item == null) return Result.err("无法获取申请单");
        if ("2".equals(item.getStatus())) {
            dao.updateRecover(item.getId()); //还原资产信息
        }
        return Result.ok();
    }
}