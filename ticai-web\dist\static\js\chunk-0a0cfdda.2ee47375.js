(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a0cfdda"],{"01e7":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter"},[a("el-form",{attrs:{model:e.qform,size:"mini"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"检索：","label-width":e.formLabelWidth}},[a("el-input",{attrs:{clearable:"",placeholder:"输入名称、地址等关键字",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-button",{staticClass:"filter-item",attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{staticClass:"filter-item",attrs:{size:"mini",type:"success",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)],1)],1)],1),a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{path:"/sys/dept/page",size:"mini",query:e.qform,stripe:!0,border:!0}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"单位名称",prop:"name",width:"180"}}),a("el-table-column",{attrs:{label:"联系人",prop:"manager",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"联系方式",prop:"mobile",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"单位地址",prop:"address"}}),a("el-table-column",{attrs:{label:"操作",width:"270",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-user"},on:{click:function(a){return a.stopPropagation(),e.user(t.row)}}},[e._v("用户")]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(a){return a.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),e.remove(t.row)}}},[e._v("删除")])]}}])})],1),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"维保单位信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[a("el-form-item",{attrs:{label:"单位名称：",prop:"name"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"单位编码：",prop:"no"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.no,callback:function(t){e.$set(e.form,"no",t)},expression:"form.no"}})],1),a("el-form-item",{attrs:{label:"联系人：",prop:"manager"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.manager,callback:function(t){e.$set(e.form,"manager",t)},expression:"form.manager"}})],1),a("el-form-item",{attrs:{label:"联系方式：",prop:"mobile"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1),a("el-form-item",{attrs:{label:"单位地址",prop:"address"}},[a("el-input",{attrs:{maxlength:"128",autocomplete:"off"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),a("user",{ref:"user",on:{success:e.search}})],1)},o=[],r=(a("ac1f"),a("841c"),a("6ecd")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"【"+e.dept.name+"】维保单位用户",visible:e.visible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("div",{staticClass:"filter"},[a("el-form",{attrs:{model:e.qform,size:"mini",inline:""},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("div",{staticClass:"filter-item"},[a("label",[e._v("帐号：")]),a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.account,callback:function(t){e.$set(e.qform,"account",t)},expression:"qform.account"}})],1)]),a("div",{staticClass:"filter-item"},[a("label",[e._v("姓名：")]),a("div",[a("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.name,callback:function(t){e.$set(e.qform,"name",t)},expression:"qform.name"}})],1)]),a("div",{staticClass:"filter-item"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1)])],1),a("div",{staticStyle:{"min-height":"400px"}},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{path:"/sys/user/page",size:"mini",query:e.qform,stripe:"",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50"}}),a("el-table-column",{attrs:{label:"帐号",prop:"account",width:"100"}}),a("el-table-column",{attrs:{label:"姓名",prop:"name",width:"150"}}),a("el-table-column",{attrs:{label:"性别",prop:"gender",width:"50",align:"center",formatter:e.colGender}}),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"80",align:"center",formatter:e.fnStatus}}),a("el-table-column",{attrs:{label:"角色",prop:"roleNames"}}),a("el-table-column",{attrs:{label:"操作",width:"240",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(a){return a.stopPropagation(),e.detail(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-star-on"},on:{click:function(a){return a.stopPropagation(),e.assignRole(t.row)}}},[e._v("角色")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:function(a){return a.stopPropagation(),e.remove(t.row)}}},[e._v("删除")])]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"用户信息",visible:e.detailVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.detailVisible=t}}},[a("el-form",{ref:"dataform",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"所属机构："}},[a("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dept.name,callback:function(t){e.$set(e.dept,"name",t)},expression:"dept.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名：",prop:"name"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"性别：",prop:"gender"}},[a("el-radio-group",{model:{value:e.form.gender,callback:function(t){e.$set(e.form,"gender",t)},expression:"form.gender"}},[a("el-radio",{attrs:{label:"1"}},[e._v("男")]),a("el-radio",{attrs:{label:"2"}},[e._v("女")])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"帐号：",prop:"account"}},[a("el-input",{attrs:{readonly:"update"==e.opt,maxlength:"32",autocomplete:"off"},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1)],1),a("el-col",{attrs:{span:12}},[a("span",{staticClass:"form-memo"},[e._v(e._s("add"==e.opt?"初始化密码为：123456":"帐号不能修改"))])]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编码：",prop:"no"}},[a("el-input",{attrs:{maxlength:"12",autocomplete:"off"},model:{value:e.form.no,callback:function(t){e.$set(e.form,"no",t)},expression:"form.no"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系方式：",prop:"phone"}},[a("el-input",{attrs:{maxlength:"32",autocomplete:"off"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),a("role",{ref:"role",on:{success:e.search}})],1)},l=[],n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],ref:"dialog",attrs:{title:"角色分配",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[a("el-table",{ref:"grid",attrs:{data:e.list,stripe:!0,border:!0,"max-height":e.maxHeight},on:{"row-click":e.handleRowClick}},[a("el-table-column",{attrs:{type:"selection",width:"50"}}),a("el-table-column",{attrs:{label:"名称",prop:"name",width:"200"}}),a("el-table-column",{attrs:{label:"说明",prop:"memo"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)],1)],1)},c=[],u=(a("d3b7"),a("0643"),a("4e3e"),a("159b"),{data:function(){return{visible:!1,keyword:null,list:null,user:null}},computed:{maxHeight:function(){return document.documentElement.clientHeight-330}},created:function(){},mounted:function(){},methods:{show:function(e){var t=this;this.visible=!0,this.user=e,null!=this.data?this.selection():this.$http("/sys/role/list").then((function(e){t.list=e,t.selection()})).catch((function(){return t.$message.error("无法加载角色列表")}))},selection:function(){var e=this;this.$http("/sys/user/roleId/"+this.user).then((function(t){var a=t.data;if(a&&a.length){var i=e.$refs.grid;e.list.forEach((function(e){for(var t=0;t<a.length;t++)if(a[t]===e.id)return i.toggleRowSelection(e,!0)}))}})).catch((function(){return e.$message.error("无法加载角色列表")}))},handleRowClick:function(e){this.$refs.grid.toggleRowSelection(e)},close:function(){this.visible=!1},confirm:function(){var e=this,t={key:this.user,values:[]};this.$refs.grid.selection.forEach((function(e){t.values.push(e.id)})),this.$http({url:"/sys/user/assignRole",data:t}).then((function(t){e.visible=!1,e.$message.success("保存成功"),e.$emit("success")})).catch((function(){e.$message.error("无法分配角色")}))}}}),f=u,m=a("2877"),d=Object(m["a"])(f,n,c,!1,null,null,null),p=d.exports,h={1:"在职",5:"离职",8:"禁用"},g={components:{PageTable:r["a"],Role:p},data:function(){return{formLabelWidth:"110px",visible:!1,detailVisible:!1,opt:null,dept:{},qform:{account:null,name:null,dept:null},form:{gender:"1",dept:null},rules:{account:[{required:!0,message:"请输入帐号",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}]}}},methods:{fnStatus:function(e,t,a,i){return h[a]||""},search:function(){this.$refs.grid.search(this.qform)},show:function(e){var t=this;this.clearValidate(),this.visible=!0,this.dept=e,this.qform.dept=e.id,this.$refs.grid?this.$refs.grid.search(this.qform,!0):this.$nextTick((function(){return t.$refs.grid.search(t.qform,!0)}))},clearValidate:function(){this.$refs.dataform&&this.$refs.dataform.clearValidate()},add:function(){this.opt="add",this.form={gender:"1"},this.detailVisible=!0},detail:function(e){this.opt="update",this.detailVisible=!0,this.clearValidate(),this.form=Object.assign({},e)},assignRole:function(e){this.$refs.role.show(e.id)},remove:function(e){var t=this;this.$confirm("此操作将永久删除该用户, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/user/delete/"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&(e.form.dept=e.dept.id,e.form.type="2",e.$http({url:"/sys/user/"+e.opt,data:e.form}).then((function(t){t.code>0&&(e.$message.success("提交成功"),e.detailVisible=!1,e.search())})).catch((function(){})))}))}}},b=g,v=Object(m["a"])(b,s,l,!1,null,null,null),y=v.exports,w={components:{PageTable:r["a"],User:y},data:function(){return{formLabelWidth:"100px",detailVisible:!1,menuVisible:!1,qform:{keyword:null,type:"2",ignoreTypeRoot:"Y"},form:{},rules:{name:[{required:!0,message:"请输入单位名称",trigger:"blur"}]}}},mounted:function(){this.search()},methods:{search:function(){this.$refs.grid.search(this.qform)},user:function(e){this.$refs.user.show(e)},add:function(){this.detailVisible=!0,this.form={},this.clearValidate()},detail:function(e){this.detailVisible=!0,this.form=Object.assign({},e),this.clearValidate()},remove:function(e){var t=this;this.$confirm("此操作将永久删除该维保单人, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$http({url:"/sys/dept/delete/"+e.id}).then((function(e){e.code>0&&(t.$message.success("删除成功"),t.detailVisible=!1,t.search())}))})).catch((function(){}))},clearValidate:function(){this.$refs.dataform&&this.$refs.dataform.clearValidate()},save:function(){var e=this;this.$refs.dataform.validate((function(t){t&&(e.form.pid="A80",e.form.type="2",e.$http({url:"/sys/dept/save",data:e.form}).then((function(t){t.code>0&&(e.detailVisible=!1,e.$message.success("提交成功"),e.search())})).catch((function(){})))}))}}},x=w,$=Object(m["a"])(x,i,o,!1,null,null,null);t["default"]=$.exports},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"6ecd":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticClass:"page-table-ctn"},[a("el-table",e._g(e._b({directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"grid",attrs:{"max-height":e.maxHeight,data:e.rows}},"el-table",e.$attrs,!1),e.$listeners),[e._t("default")],2),e.paging?a("el-footer",{staticClass:"footer"},[a("div",{staticClass:"size-info"},[e.total>1?a("span",[e._v("显示第 "+e._s(e.from)+" 条到第 "+e._s(e.to)+" 条的数据，")]):e._e(),e._v(" 共"+e._s(e.total)+" 条数据 ")]),a("el-pagination",e._b({staticStyle:{float:"right"},attrs:{layout:e.layout,"page-sizes":e.pageSizes,"current-page":e.pi,"page-size":e.pz,total:e.total},on:{"current-change":e.handleNumberChange,"size-change":e.handleSizeChange}},"el-pagination",e.$attrs,!1))],1):e._e()],1)},o=[],r=a("53ca"),s=(a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("b775")),l={name:"PageTable",props:{path:{type:String,require:!0,default:null},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:function(){return[10,20,30,50,100]}},layout:{type:String,default:"sizes, prev, pager, next, jumper"},paging:{type:Boolean,default:!0},query:{type:Object,default:function(){}},auto:{type:Boolean,default:!1},checkField:{type:String,default:null}},data:function(){return{pi:1,pz:this.pageSize,params:this.query||{},rows:[],total:0,from:0,to:0,maxHeight:null,loading:!1}},watch:{},mounted:function(){this.auto&&this.search()},methods:{setParams:function(e){this.params=e||{}},setMaxHeight:function(e){this.maxHeight=e,this.$refs.grid.doLayout()},handleSizeChange:function(e){this.pz=e,this.search(1)},handleNumberChange:function(e){this.search(e)},search:function(e,t){var a=this;if(this.path){var i={pageNumber:1},o=Object(r["a"])(e);"undefined"===o?i.pageNumber=1:"number"===o?i.pageNumber=e:"object"===o?(this.params=e,"number"===typeof t&&(i.pageNumber=t),"boolean"===typeof t&&this.empty()):i.pageNumber=e.pageNumber,this.pi=i.pageNumber,this.paging&&(this.params.pageNumber=i.pageNumber,this.params.pageSize=this.pz),this.loading=!0,Object(s["a"])({url:this.path,data:this.params}).then((function(e){a.loading=!1,a.paging?a.renderPage(e):a.renderList(e.rows?e.rows:e),a.$emit("loaded",e)})).catch((function(e){a.loading=!1,console.log(e)}))}},empty:function(){this.pi=1,this.rows=[],this.total=0,this.from=0,this.to=0},renderList:function(e){this.rows=e},renderPage:function(e){var t=this;this.checkField&&e.rows.forEach((function(e){e[t.checkField]=!1})),this.rows=e.rows,this.total=e.total,this.total>0?(this.from=(this.pi-1)*this.pz+1,this.to=this.from+(this.rows&&this.rows.length?this.rows.length-1:0)):(this.from=0,this.to=0)},getData:function(){return this.rows},getSelection:function(){return this.$refs.grid.selection},getSelectionId:function(e){var t=this.$refs.grid.selection;e||(e="id");for(var a=[],i=0;i<t.length;i++)t[i][e]&&a.push(t[i][e]);return a}}},n=l,c=(a("b2d4"),a("2877")),u=Object(c["a"])(n,i,o,!1,null,"bdcc19d8",null);t["a"]=u.exports},"841c":function(e,t,a){"use strict";var i=a("d784"),o=a("825a"),r=a("1d80"),s=a("129f"),l=a("14c3");i("search",1,(function(e,t,a){return[function(t){var a=r(this),i=void 0==t?void 0:t[e];return void 0!==i?i.call(t,a):new RegExp(t)[e](String(a))},function(e){var i=a(t,e,this);if(i.done)return i.value;var r=o(e),n=String(this),c=r.lastIndex;s(c,0)||(r.lastIndex=0);var u=l(r,n);return s(r.lastIndex,c)||(r.lastIndex=c),null===u?-1:u.index}]}))},ac65:function(e,t,a){},b2d4:function(e,t,a){"use strict";a("ac65")}}]);