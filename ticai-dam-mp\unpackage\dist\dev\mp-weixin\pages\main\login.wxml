<view class="container"><view><view class="wechatapp"><view class="header-avatar"><open-data type="userAvatarUrl"></open-data></view></view><view class="auth-title">申请获取以下权限</view><view class="auth-subtitle">获得你的公开信息（昵称、头像等）</view><block wx:if="{{canIUseGetUserProfile}}"><button class="login-btn" openType="getUserProfile" lang="zh_CN" data-event-opts="{{[['tap',[['getUserProfile',['$event']]]]]}}" bindtap="__e">{{info+"授权登录"}}</button></block><block wx:else><button class="login-btn" openType="getUserInfo" lang="zh_CN" data-event-opts="{{[['tap',[['bindGetUserInfo',['$event']]]]]}}" bindtap="__e">{{info+"授权登录"}}</button></block><button class="login-close" lang="zh_CN" data-event-opts="{{[['tap',[['loginClose',['$event']]]]]}}" bindtap="__e">取消登录</button></view></view>