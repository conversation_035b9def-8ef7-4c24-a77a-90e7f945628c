(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-980753a2"],{a315:function(e,t,a){},e645:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-tollbar"},[a("div",{staticClass:"opt"},[a("el-button-group",[a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010201",expression:"'010201'"}],attrs:{type:"success",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("资产入库")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010202",expression:"'010202'"}],attrs:{type:"primary",size:"mini",icon:"el-icon-upload"},on:{click:e.upload}},[e._v("批量导入")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010203",expression:"'010203'"}],attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.download}},[e._v("选中导出")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010204",expression:"'010204'"}],attrs:{disabled:0==e.items.length,type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:e.batchDel}},[e._v("批量删除")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010205",expression:"'010205'"},{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length,type:"warning",size:"mini",icon:"el-icon-edit"},on:{click:e.edit}},[e._v("信息修改")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010206",expression:"'010206'"},{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length,type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:e.revise}},[e._v("编辑")]),a("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length,size:"mini",icon:"el-icon-document"},on:{click:e.preview}},[e._v("查看详情")])],1)],1),a("div",{staticClass:"search",staticStyle:{"min-width":"350px"}},[a("el-button-group",[a("el-button",{attrs:{size:"mini",icon:"el-icon-star-on"},on:{click:function(t){e.more=!e.more}}},[e._v(e._s(e.more?"隐藏更多":"更多筛选"))]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),a("el-button",{directives:[{name:"priv",rawName:"v-priv",value:"010206",expression:"'010206'"}],attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.exportQuery}},[e._v("根据条件导出")])],1)],1)]),a("div",{staticClass:"page-filter"},[a("el-form",{attrs:{model:e.qform,inline:"",size:"mini","label-width":"150px"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[a("el-form-item",{attrs:{label:"快捷检索："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入资产编码、名称",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}})],1),a("el-form-item",{attrs:{label:"资产类型："}},[a("asset-type-chosen",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请选择资产类型"},model:{value:e.qform.typeLike,callback:function(t){e.$set(e.qform,"typeLike",t)},expression:"qform.typeLike"}})],1),a("el-form-item",{attrs:{label:"所在区域："}},[a("chosen",{staticStyle:{width:"100%"},attrs:{clearable:"",path:"/am/region/list","value-field":"id","label-field":"name",placeholder:"请选择区域"},model:{value:e.qform.region,callback:function(t){e.$set(e.qform,"region",t)},expression:"qform.region"}})],1),a("el-form-item",{attrs:{label:"网点名称："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入网点名称",autocomplete:"off"},model:{value:e.qform.name,callback:function(t){e.$set(e.qform,"name",t)},expression:"qform.name"}})],1),e.more?[a("el-form-item",{directives:[{name:"root-dept",rawName:"v-root-dept"}],attrs:{label:"所属部门："}},[a("dept-tree-box",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.qform.dept,callback:function(t){e.$set(e.qform,"dept",t)},expression:"qform.dept"}})],1),a("el-form-item",{attrs:{label:"使用部门："}},[a("dept-tree-box",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.qform.useDept,callback:function(t){e.$set(e.qform,"useDept",t)},expression:"qform.useDept"}})],1),a("el-form-item",{attrs:{label:"使用人："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"使用姓名，编码",autocomplete:"off"},model:{value:e.qform.userName,callback:function(t){e.$set(e.qform,"userName",t)},expression:"qform.userName"}})],1),a("el-form-item",{attrs:{label:"规则型号："}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入规则型号",autocomplete:"off"},model:{value:e.qform.spec,callback:function(t){e.$set(e.qform,"spec",t)},expression:"qform.spec"}})],1),a("el-form-item",{attrs:{label:"销售终端编号:"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入销售终端编号",autocomplete:"off"},model:{value:e.qform.sn,callback:function(t){e.$set(e.qform,"sn",t)},expression:"qform.sn"}})],1),a("el-form-item",{attrs:{label:"当前状态："}},[a("el-select",{staticStyle:{width:"180px"},attrs:{multiple:"","collapse-tags":"",clearable:"",placeholder:"请选择状态"},model:{value:e.qform.statusList,callback:function(t){e.$set(e.qform,"statusList",t)},expression:"qform.statusList"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"定位地址:"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",placeholder:"请输入定位地址",autocomplete:"off"},model:{value:e.qform.locAddr,callback:function(t){e.$set(e.qform,"locAddr",t)},expression:"qform.locAddr"}})],1),a("el-form-item",{attrs:{label:"取得日期："}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.inDate,callback:function(t){e.inDate=t},expression:"inDate"}})],1),a("el-form-item",{attrs:{label:"质保期限："}},[a("radio-box",{staticStyle:{"min-width":"180px"},attrs:{button:"",all:"全部",options:e.exDateOptions},on:{selected:e.changeExDate},model:{value:e.qform.exDateIndex,callback:function(t){e.$set(e.qform,"exDateIndex",t)},expression:"qform.exDateIndex"}}),e.exDateVisible?[a("el-date-picker",{staticStyle:{width:"300px","margin-left":"20px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.exDate,callback:function(t){e.exDate=t},expression:"exDate"}})]:e._e()],2)]:e._e()],2)],1)]),a("div",{staticClass:"page-body"},[a("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/am/asset/page",stripe:"",border:"","highlight-current-row":""},on:{"selection-change":e.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),a("el-table-column",{attrs:{label:"资产类型",prop:"typeName",width:"130","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"130","header-align":"center",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.viewItem(t.row)}}},[e._v(e._s(t.row.no))])]}}])}),a("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"130",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.viewItem(t.row)}}},[e._v(e._s(t.row.name))])]}}])}),a("el-table-column",{attrs:{label:"规则型号",prop:"spec",width:"120","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"取得日期",prop:"takeDate",width:"120","header-align":"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"使用部门",prop:"useDeptName",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"使用人",prop:"useUserName",width:"90",align:"center"}}),a("el-table-column",{attrs:{label:"销售终端编号",prop:"sn",width:"110","header-align":"center"}}),a("el-table-column",{attrs:{label:"使用网点",prop:"locationName","min-width":"150","header-align":"center"}}),a("el-table-column",{attrs:{label:"定位地址",prop:"locAddr","min-width":"250","header-align":"center"}}),a("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{size:"small",hit:"",type:e.getAssetStatusType(t.row),"disable-transitions":""}},[e._v(e._s(e.getAssetStatusText(t.row)))])]}}])})],1)],1),a("el-dialog",{ref:"uploadDlg",staticClass:"dialog-full",attrs:{title:"批量导入",fullscreen:"",visible:e.uploadVisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.uploadVisible=t}}},[a("div",{staticStyle:{margin:"10px 20px"}},[a("upload-file",{attrs:{type:"ASSET_UPLOAD",simple:"",multiple:!1,limit:1,accept:".xls,.xlsx"},on:{success:e.uploaded,removeFile:e.uploadRemove},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}})],1),a("div",{staticClass:"upload-block"},[a("table",[a("thead",[a("tr",[a("th",[e._v("行号")]),a("th",[e._v("结果提示")]),a("th",[e._v("资产编码")]),a("th",[e._v("资产分类")]),a("th",[e._v("资产名称")]),a("th",[e._v("资产原值")]),a("th",[e._v("净值")]),a("th",[e._v("取得日期")]),a("th",[e._v("投入使用日期")]),a("th",[e._v("使用部门")]),a("th",[e._v("管理部门")]),a("th",[e._v("规格型号")]),a("th",[e._v("管理人姓名")]),a("th",[e._v("折旧/摊销年限")]),a("th",[e._v("发票号")])])]),a("tbody",[e._l(e.uploadList,(function(t){return[e.showUploadAll||null!=t.rowMsg?a("tr",{key:t.rowNum,class:{err:null!=t.rowMsg}},[a("td",[e._v(e._s(t.rowNum))]),a("td",{staticClass:"upload-msg"},[e._v(e._s(t.rowMsg))]),a("td",[e._v(e._s(t.no))]),a("td",[e._v(e._s(t.type1))]),a("td",[e._v(e._s(t.name))]),a("td",[e._v(e._s(t.selfValue))]),a("td",[e._v(e._s(t.value))]),a("td",[e._v(e._s(t.takeDateText))]),a("td",[e._v(e._s(t.productDateText))]),a("td",[e._v(e._s(t.useDeptName))]),a("td",[e._v(e._s(t.deptName))]),a("td",[e._v(e._s(t.spec))]),a("td",[e._v(e._s(t.custodianName))]),a("td",[e._v(e._s(t.expiryMonth))]),a("td",[e._v(e._s(t.invoice))])]):e._e()]}))],2)])]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{left:"30px",float:"left"}},[a("el-button",{attrs:{size:"small",type:"warning"},on:{click:e.toggleErr}},[e._v(e._s(e.showUploadAll?"查看错误":"查看全部"))])],1),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.uploadVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submitUpload}},[e._v("确定上传")])],1)]),a("detail-new",{ref:"detailNew",on:{success:e.search}}),a("detail-edit",{ref:"detailEdit",on:{success:e.search}}),a("detail-view",{ref:"detailView"}),a("revise",{ref:"revise",on:{success:e.search}})],1)},i=[],s=(a("b64b"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("4e3e"),a("159b"),a("5c96")),o=a("576f"),n=a("6ecd"),r=a("d2ed"),c=a("73a0"),d=a("5edd"),u=a("04bd"),p=a("39d0"),m=a("c21a"),f=a("660a"),h=a("d8a3"),v=a("69db"),b={1:"90天",2:"30天",3:"七天",8:"已过保",9:"指定日期范围"},g={components:{PageTable:n["a"],DetailNew:u["a"],AssetTypeChosen:r["a"],RadioBox:c["a"],DeptTreeBox:d["a"],DetailEdit:p["a"],DetailView:m["a"],UploadFile:f["a"],Revise:h["a"],Chosen:v["a"]},data:function(){return{fullscreenLoading:!1,qform:{keyword:null,dept:null,region:null,statusList:[]},more:!1,statusOptions:Object(o["a"])(),exDateOptions:[],exDateVisible:!1,exDate:[],items:[],regionLocation:[],fileList:[],uploadList:[],uploadVisible:!1,showUploadAll:!0,inDate:[]}},watch:{exDate:function(e){this.qform.exBegin=2===e.length?e[0]:null,this.qform.exEnd=2===e.length?e[1]:null},inDate:function(e){this.qform.begin=e&&2===e.length?e[0]:null,this.qform.end=e&&2===e.length?e[1]:null}},mounted:function(){var e=this;Object.keys(b).forEach((function(t){e.exDateOptions.push({value:t,text:b[t]})})),this.search(),this.$route.query&&"create"===this.$route.query.module&&this.$nextTick((function(){e.add()}))},methods:{getAssetStatusType:function(e){return Object(o["c"])(e.status)},getAssetStatusText:function(e){return Object(o["b"])(e.status)},search:function(){this.$refs.grid.search(this.qform)},add:function(){this.$refs.detailNew.show()},changeExDate:function(e){this.exDateVisible="9"===e,this.exDateVisible||(this.exDate=[])},selectionChange:function(e){this.items=e||[]},upload:function(){this.fileList=[],this.uploadList=[],this.uploadVisible=!0},uploaded:function(e){var t=this;if(e&&e.length){var a=s["Loading"].service({fullscreen:!0,text:"解析文件中..."});this.$http({url:"/am/asset/uploadFile",data:e[0]}).then((function(e){e.code>0&&(t.showUploadAll=!0,t.uploadList=e.data),a.close()}))}},uploadRemove:function(e){this.uploadList=[]},toggleErr:function(){this.showUploadAll=!this.showUploadAll},batchDel:function(){var e=this;if(0===this.items.length)return this.$message.warning("至少要选择一条资产记录");this.$confirm("您确定要永久批量删除这些资产吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=[];e.items.forEach((function(e){t.push(e.id)})),e.$http({url:"/am/asset/deletes",data:t}).then((function(t){t.code>0&&(e.$message.success("批量删除成功"),e.search())}))})).catch((function(){}))},edit:function(){var e=this;1===this.items.length&&(this.fullscreenLoading=!0,this.$http("/am/asset/get/"+this.items[0].id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&e.$refs.detailEdit.show(t.data)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")})))},revise:function(){var e=this;1===this.items.length&&(this.fullscreenLoading=!0,this.$http("/am/asset/get/"+this.items[0].id).then((function(t){e.fullscreenLoading=!1,t.code>0&&t.data&&e.$refs.revise.show(t.data)})).catch((function(){e.fullscreenLoading=!1,e.$message.error("网络超时")})))},preview:function(){1===this.items.length&&this.viewItem(this.items[0])},viewItem:function(e){var t=this;console.log(e),this.fullscreenLoading=!0,this.$http("/am/asset/get/"+e.id).then((function(e){t.fullscreenLoading=!1,e.code>0&&e.data&&t.$refs.detailView.show(e.data)})).catch((function(){t.fullscreenLoading=!1,t.$message.error("网络超时")}))},submitUpload:function(){var e=this;if(0===this.uploadList.length)return this.$message.warning("没有可提交的数据");var t=s["Loading"].service({fullscreen:!0,text:"数据上传中..."});this.$http({url:"/am/asset/uploadData",data:this.uploadList}).then((function(a){1===a.code?(e.$message.success("上传成功"),e.uploadVisible=!1,e.search()):2===a.code&&(e.uploadList=a.data,e.$message.error("存在错误的数据行")),t.close()})).catch((function(){t.close(),e.$message.error("网络超时")}))},download:function(){var e=this;if(0===this.items.length)return this.$message.warning("至少要选择一条资产记录");var t=[];this.items.forEach((function(e){return t.push(e.id)}));var a=s["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$http({url:"/am/asset/exportLedgerBatch",data:t,responseType:"blob"}).then((function(t){a.close(),e.$saveAs(t,"资产台账明细.xlsx")})).catch((function(t){a.close(),e.$message.error("导出生成出错:"+t)}))},exportQuery:function(){var e=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=s["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});e.$http({url:"/am/asset/exportLedgerAll",data:e.qform,responseType:"blob"}).then((function(a){t.close(),e.$saveAs(a,"资产台账明细.xlsx")})).catch((function(a){t.close(),e.$message.error("导出生成出错:"+a)}))}))}}},x=g,w=(a("ff20"),a("2877")),y=Object(w["a"])(x,l,i,!1,null,null,null);t["default"]=y.exports},ff20:function(e,t,a){"use strict";a("a315")}}]);