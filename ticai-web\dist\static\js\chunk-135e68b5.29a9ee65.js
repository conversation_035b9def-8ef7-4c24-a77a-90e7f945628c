(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-135e68b5"],{"12f27":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",[l("div",{staticClass:"page-header"},[l("div",{staticClass:"page-tollbar"},[l("div",{staticClass:"opt"},[l("el-button-group",[l("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.createWo}},[e._v("发起工单")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length||/2|3/.test(e.items[0].status),type:"success",size:"mini",icon:"el-icon-edit"},on:{click:e.solveWo}},[e._v("完成工单")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length||"2"!=e.items[0].status,type:"warning",size:"mini",icon:"el-icon-check"},on:{click:e.closeWo}},[e._v("关闭工单")]),l("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.download}},[e._v("选中导出")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:0==e.items.length,type:"danger",size:"mini",icon:"el-icon-remove"},on:{click:e.batchDel}},[e._v("批量删除")]),l("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{disabled:1!=e.items.length,size:"mini",icon:"el-icon-document"},on:{click:e.preview}},[e._v("查看详情")])],1)],1),l("div",{staticClass:"search"},[l("el-button-group",[l("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),l("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-download"},on:{click:e.exportQuery}},[e._v("根据条件导出")])],1)],1)]),l("div",{staticClass:"page-filter"},[l("el-form",{attrs:{model:e.qform,inline:"",size:"mini","label-width":"110px"},nativeOn:{submit:function(t){return t.preventDefault(),e.search(t)}}},[l("el-form-item",{attrs:{label:"单号："}},[l("el-input",{staticStyle:{width:"100px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.no,callback:function(t){e.$set(e.qform,"no",t)},expression:"qform.no"}})],1),l("el-form-item",{attrs:{label:"网点名称："}},[l("el-input",{staticStyle:{width:"180px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.locationName,callback:function(t){e.$set(e.qform,"locationName",t)},expression:"qform.locationName"}})],1),l("el-form-item",{attrs:{label:"联系人："}},[l("el-input",{staticStyle:{width:"90px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.contact,callback:function(t){e.$set(e.qform,"contact",t)},expression:"qform.contact"}})],1),l("el-form-item",{attrs:{label:"设备型号："}},[l("el-input",{staticStyle:{width:"120px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.spec,callback:function(t){e.$set(e.qform,"spec",t)},expression:"qform.spec"}})],1),l("el-form-item",{attrs:{label:"保障时间："}},[l("el-date-picker",{staticStyle:{width:"230px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.qdate,callback:function(t){e.qdate=t},expression:"qdate"}})],1),l("el-form-item",{attrs:{label:"当前状态："}},[l("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:e.qform.status,callback:function(t){e.$set(e.qform,"status",t)},expression:"qform.status"}},e._l(e.statusOptions,(function(e){return l("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1),l("el-form-item",{directives:[{name:"root-dept",rawName:"v-root-dept"}],attrs:{label:"所属部门："}},[l("dept-tree-box",{staticStyle:{width:"50px"},attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.qform.dept,callback:function(t){e.$set(e.qform,"dept",t)},expression:"qform.dept"}})],1),l("el-form-item",{attrs:{label:"服务人员："}},[l("el-input",{staticStyle:{width:"90px"},attrs:{clearable:"",autocomplete:"off"},model:{value:e.qform.serviceStaff,callback:function(t){e.$set(e.qform,"serviceStaff",t)},expression:"qform.serviceStaff"}})],1)],1)],1)]),l("div",{staticClass:"page-body"},[l("page-table",{directives:[{name:"table-height",rawName:"v-table-height"}],ref:"grid",attrs:{size:"mini",path:"/wo/bill/page",query:e.qform,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":e.selectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}),l("el-table-column",{attrs:{label:"单号",prop:"no",width:"140",align:"center",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-link",{attrs:{type:"primary",size:"mini"},on:{click:function(l){return l.stopPropagation(),e.viewItem(t.row)}}},[e._v(e._s(t.row.no))])]}}])}),l("el-table-column",{attrs:{label:"网点名称",prop:"locationName",width:"160","header-align":"center"}}),l("el-table-column",{attrs:{label:"网点地址",prop:"locationAddress","min-width":"250","header-align":"center"}}),l("el-table-column",{attrs:{label:"联系人",prop:"contact",width:"80",align:"center"}}),l("el-table-column",{attrs:{label:"联系电话",prop:"phone",width:"110",align:"center"}}),l("el-table-column",{attrs:{label:"所属部门",prop:"deptName",width:"100",align:"center"}}),l("el-table-column",{attrs:{label:"服务人员",prop:"serviceStaff",width:"100",align:"center"}}),l("el-table-column",{attrs:{label:"报障时间",prop:"reportTime",width:"140",align:"center"}}),l("el-table-column",{attrs:{label:"排障时间",prop:"solveTime",width:"140",align:"center"}}),l("el-table-column",{attrs:{label:"当前状态",prop:"status",width:"80",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-tag",{attrs:{size:"small",hit:"",type:e.getStatusType(t.row),"disable-transitions":""}},[e._v(e._s(e.getStatusText(t.row)))])]}}])})],1)],1),l("CreateWo",{ref:"createWo",on:{success:e.search}}),l("SolveWo",{ref:"solveWo",on:{success:e.search}}),l("CloseWo",{ref:"closeWo",on:{success:e.search}}),l("ViewWo",{ref:"viewWo"})],1)},i=[],o=(l("b64b"),l("d3b7"),l("ac1f"),l("841c"),l("0643"),l("4e3e"),l("159b"),l("5c96")),s=l("6ecd"),n=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",[l("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"工单信息",width:"1200px",top:"50px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[l("el-form",{ref:"dataform",attrs:{model:e.dataModel,rules:e.rules,size:"mini","label-suffix":":","label-width":"120px"}},[l("el-row",[l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"申请单号"}},[l("el-input",{staticClass:"readonly",attrs:{value:"单号由系统生成",readonly:""}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"服务类型",prop:"type"}},[l("el-radio-group",{model:{value:e.dataModel.type,callback:function(t){e.$set(e.dataModel,"type",t)},expression:"dataModel.type"}},[l("el-radio",{attrs:{label:"1"}},[e._v("上门服务")]),l("el-radio",{attrs:{label:"2"}},[e._v("远程指导")])],1)],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"维保单位",prop:"maintain"}},[e.isSelf?l("el-input",{staticClass:"readonly",attrs:{value:"本单位",readonly:""}}):l("el-select",{staticStyle:{width:"100%"},model:{value:e.dataModel.maintain,callback:function(t){e.$set(e.dataModel,"maintain",t)},expression:"dataModel.maintain"}},e._l(e.deptOptions,(function(e){return l("el-option",{key:e.value,attrs:{value:e.value,label:e.text}})})),1)],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"登记日期",prop:"registerTime"}},[l("el-date-picker",{staticStyle:{width:"140px"},attrs:{type:"date",placeholder:"请选择","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",editable:""},model:{value:e.dataModel.registerTime,callback:function(t){e.$set(e.dataModel,"registerTime",t)},expression:"dataModel.registerTime"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"网点用户",prop:"locationName"}},[l("el-input",{staticClass:"readonly",attrs:{readonly:""},model:{value:e.dataModel.locationName,callback:function(t){e.$set(e.dataModel,"locationName",t)},expression:"dataModel.locationName"}},[l("el-button",{attrs:{slot:"append"},on:{click:e.selectLocation},slot:"append"},[e._v("选择")])],1)],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"网点地址",prop:"locationAddress"}},[l("el-input",{model:{value:e.dataModel.locationAddress,callback:function(t){e.$set(e.dataModel,"locationAddress",t)},expression:"dataModel.locationAddress"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[l("el-input",{model:{value:e.dataModel.contact,callback:function(t){e.$set(e.dataModel,"contact",t)},expression:"dataModel.contact"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[l("el-input",{model:{value:e.dataModel.phone,callback:function(t){e.$set(e.dataModel,"phone",t)},expression:"dataModel.phone"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"备用电话",prop:"phoneRes"}},[l("el-input",{model:{value:e.dataModel.phoneRes,callback:function(t){e.$set(e.dataModel,"phoneRes",t)},expression:"dataModel.phoneRes"}})],1)],1),l("el-col",{attrs:{span:24}},[l("el-form-item",{attrs:{label:"用户反映故障",prop:"faultReport"}},[l("el-input",{model:{value:e.dataModel.faultReport,callback:function(t){e.$set(e.dataModel,"faultReport",t)},expression:"dataModel.faultReport"}})],1)],1),l("el-col",{attrs:{span:24}},[l("el-form-item",{attrs:{label:"现场检查故障",prop:"faultCheck"}},[l("el-input",{model:{value:e.dataModel.faultCheck,callback:function(t){e.$set(e.dataModel,"faultCheck",t)},expression:"dataModel.faultCheck"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"报障时间",prop:"reportTime"}},[l("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.reportTime,callback:function(t){e.$set(e.dataModel,"reportTime",t)},expression:"dataModel.reportTime"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"维修时长",prop:"takeMinute"}},[l("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.takeMinute,callback:function(t){e.$set(e.dataModel,"takeMinute",t)},expression:"dataModel.takeMinute"}},[l("span",{attrs:{slot:"append"},slot:"append"},[e._v("分钟")])])],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"收费项目",prop:"feeFlag"}},[l("el-radio-group",{model:{value:e.dataModel.feeFlag,callback:function(t){e.$set(e.dataModel,"feeFlag",t)},expression:"dataModel.feeFlag"}},[l("el-radio",{attrs:{label:"1"}},[e._v("是")]),l("el-radio",{attrs:{label:"2"}},[e._v("否")])],1)],1)],1)],1),"1"==e.dataModel.feeFlag?[l("el-row",[l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"材料费",prop:"matCost"}},[l("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.matCost,callback:function(t){e.$set(e.dataModel,"matCost",t)},expression:"dataModel.matCost"}},[l("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"维修费",prop:"maiCost"}},[l("el-input",{staticStyle:{width:"140px"},attrs:{type:"number",autocomplete:"off",min:0,max:999999},model:{value:e.dataModel.maiCost,callback:function(t){e.$set(e.dataModel,"maiCost",t)},expression:"dataModel.maiCost"}},[l("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"费用合计",prop:"amount"}},[l("el-input",{staticClass:"readonly",staticStyle:{width:"140px"},attrs:{value:parseInt(e.dataModel.matCost||0)+parseInt(e.dataModel.maiCost||0),type:"number",autocomplete:"off",readonly:""}},[l("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1)],1)]:e._e(),l("el-row",[l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"排障时间",prop:"solveTime"}},[l("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.solveTime,callback:function(t){e.$set(e.dataModel,"solveTime",t)},expression:"dataModel.solveTime"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"到达时间",prop:"arriveTime"}},[l("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.arriveTime,callback:function(t){e.$set(e.dataModel,"arriveTime",t)},expression:"dataModel.arriveTime"}})],1)],1),l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"离开时间",prop:"leaveTime"}},[l("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"请选择","value-format":"yyyy-MM-dd HH:mm",format:"yyyy-MM-dd HH:mm",clearable:"",editable:""},model:{value:e.dataModel.leaveTime,callback:function(t){e.$set(e.dataModel,"leaveTime",t)},expression:"dataModel.leaveTime"}})],1)],1)],1),l("el-row",[l("el-col",{attrs:{span:8}},[l("el-form-item",{attrs:{label:"所属部门",prop:"dept"}},[l("dept-tree-box",{attrs:{clearable:"",placeholder:"请选择所属部门"},model:{value:e.dataModel.dept,callback:function(t){e.$set(e.dataModel,"dept",t)},expression:"dataModel.dept"}})],1)],1)],1)],2),l("el-divider"),l("div",{staticStyle:{margin:"10px 0"}},[e._v("【服务项目】")]),l("el-table",{attrs:{data:e.detailList,size:"mini",border:""}},[l("el-table-column",{attrs:{type:"index",width:"50"}}),l("el-table-column",{attrs:{label:"序列号",prop:"sn","min-width":"100px"}}),l("el-table-column",{attrs:{label:"设备名称",prop:"name",width:"200px"}}),l("el-table-column",{attrs:{label:"故障现象",prop:"fault","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?l("el-select",{staticStyle:{width:"100%"},attrs:{size:"mini","allow-create":"",filterable:"",clearable:""},model:{value:a.fault,callback:function(t){e.$set(a,"fault",t)},expression:"row.fault"}},e._l(e.faultTypeOptions,(function(e){return l("el-option",{key:e,attrs:{value:e,label:e}})})),1):l("span",[e._v(e._s(a.fault))])]}}])}),l("el-table-column",{attrs:{label:"是否保修",prop:"flag",width:"85px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?l("el-select",{attrs:{size:"mini",placeholder:""},model:{value:a.flag,callback:function(t){e.$set(a,"flag",t)},expression:"row.flag"}},[l("el-option",{attrs:{value:"1",label:"是"}}),l("el-option",{attrs:{value:"2",label:"否"}})],1):l("span",[e._v(e._s("1"==a.flag?"是":"否"))])]}}])}),l("el-table-column",{attrs:{label:"维修情况",prop:"solve","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?l("el-select",{staticStyle:{width:"100%"},attrs:{size:"mini","allow-create":"",filterable:"","default-first-option":"",clearable:""},model:{value:a.solve,callback:function(t){e.$set(a,"solve",t)},expression:"row.solve"}},e._l(e.faultDealOptions,(function(e){return l("el-option",{key:e,attrs:{value:e,label:e}})})),1):l("span",[e._v(e._s(a.solve))])]}}])}),l("el-table-column",{attrs:{width:"105",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.$index;return[a.edit?l("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-circle-check"},on:{click:function(t){return e.handleRowEdit(a)}}}):l("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){a.edit=!0}}}),l("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(t){return e.handleRowDelete(a,i)}}})]}}])},[l("template",{slot:"header"},[l("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addRow}},[e._v("新增项目")])],1)],2)],1),l("div",{staticStyle:{margin:"10px 0"}},[e._v("【附件】")]),l("upload-file",{attrs:{simple:"",multiple:"","btn-size":"mini",type:"GD"},model:{value:e.fileList,callback:function(t){e.fileList=t},expression:"fileList"}}),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),l("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1),l("LocationSelect",{ref:"location",on:{selected:e.selectedLocation}}),l("AssetLocationChosen",{ref:"asset",attrs:{multiple:""},on:{selected:e.selectedAsset}})],1)},r=[],c=(l("c740"),l("a434"),l("b0c0"),l("4360")),d=l("dd8a"),u=l("660a"),p=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"选择资产",visible:e.visible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[l("el-table",{attrs:{size:"mini",data:e.list,stripe:"",border:"","highlight-current-row":""},on:{"selection-change":e.selectionChange,"row-click":e.clickRow}},[e.multiple?l("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}):l("el-table-column",{attrs:{label:"",width:"45",fixed:"left",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.selectedItem&&e.selectedItem.id===t.row.id?l("el-link",{attrs:{underline:!1,type:"primary"}},[l("svg-icon",{attrs:{"icon-class":"ic_radio"}})],1):l("el-link",{attrs:{underline:!1}},[l("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1)]}}])}),l("el-table-column",{attrs:{label:"资产名称",prop:"name","min-width":"150",fixed:"left"}}),l("el-table-column",{attrs:{label:"资产类型",prop:"typeName","min-width":"180","header-align":"center"}}),l("el-table-column",{attrs:{label:"资产编码",prop:"no",width:"120","header-align":"center"}}),l("el-table-column",{attrs:{label:"资产序列号",prop:"sn","min-width":"150","header-align":"center"}}),l("el-table-column",{attrs:{label:"所属部门",prop:"deptName","min-width":"150","header-align":"center"}})],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),l("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)],1)},m=[],f={name:"AssetLocationChosen",props:{multiple:{type:Boolean,default:!1}},data:function(){return{visible:!1,qform:{keyword:null,ignoreId:null},list:[],selectedItem:null,items:[]}},methods:{search:function(){this.$refs.grid.search(this.qform)},show:function(e){var t=this;e&&(this.list=[],this.$http("/am/asset/location/"+e).then((function(e){e.code>0&&(t.list=e.data||[])})),this.visible=!0)},clickRow:function(e){this.multiple||(this.selectedItem&&this.selectedItem.id===e.id?this.selectedItem=null:this.selectedItem=e)},selectionChange:function(e){this.multiple&&(this.items=e||[])},confirm:function(){this.$emit("selected",this.multiple?this.items:this.selectedItem,this.tag),this.visible=!1}}},h=f,b=l("2877"),v=Object(b["a"])(h,p,m,!1,null,null,null),g=v.exports,y=l("ed08"),w=l("5edd"),k={name:"CreateWo",components:{LocationSelect:d["a"],UploadFile:u["a"],AssetLocationChosen:g,DeptTreeBox:w["a"]},data:function(){return{fullscreenLoading:!1,visible:!1,dataModel:{},isSelf:"2"===c["a"].getters.user.deptType,deptOptions:[],faultTypeOptions:[],faultDealOptions:[],rules:{type:[{required:!0,message:"请选择服务类型",trigger:"blur"}],maintain:[{required:!0,message:"请选择维保单位",trigger:"blur"}],registerTime:[{required:!0,message:"请选择登记日期",trigger:"blur"}],locationName:[{required:!0,message:"请选择网点",trigger:"blur"}],reportTime:[{required:!0,message:"请选择报障时间",trigger:"blur"}],faultReport:[{required:!0,message:"请输入用户反映故障",trigger:"blur"}],dept:[{required:!0,message:"请选择所属部门",trigger:"blur"}]},detailList:[],fileList:[]}},mounted:function(){var e=this;this.isSelf||this.$http("/sys/dept/children/A80").then((function(t){t&&t.length&&t.forEach((function(t){"0"!==t.pid&&e.deptOptions.push(t)}))})),this.$http("/sys/dict/code/WO_FAULT_TYPE").then((function(t){t&&t.length&&t.forEach((function(t){t&&e.faultTypeOptions.push(t.name)}))})),this.$http("/sys/dict/code/WO_FAULT_DEAL").then((function(t){t&&t.length&&t.forEach((function(t){t&&e.faultDealOptions.push(t.name)}))}))},methods:{show:function(){this.dataModel={type:"1",feeFlag:"1",registerTime:Object(y["b"])()},this.isSelf&&(this.dataModel.maintain=c["a"].getters.user.dept),this.detailList=[],this.fileList=[],this.visible=!0},selectLocation:function(){this.$refs.location.show()},selectedLocation:function(e){this.dataModel.locationId=e.id,this.dataModel.locationAddress=e.address,this.dataModel.contact=e.contact,this.dataModel.phone=e.phone,this.$set(this.dataModel,"locationName",e.name)},addRow:function(){if(!this.dataModel.locationId)return this.$message.warning("请选择网点");this.$refs.asset.show(this.dataModel.locationId)},selectedAsset:function(e){var t=this;e.forEach((function(e){var l=t.detailList.findIndex((function(t){return t.asset===e.id}));-1===l&&t.detailList.push({asset:e.id,name:e.name,spec:e.spec,sn:e.sn,flag:"1",edit:!0})}))},handleRowEdit:function(e){e.edit=!1},handleRowDelete:function(e,t){this.detailList.splice(t,1)},save:function(){var e=this;this.$refs.dataform.validate((function(t){if(t){if(0===e.detailList.length)return e.$message.warning("至少提供一个服务项目");e.dataModel.detailList=e.detailList,e.dataModel.attachList=e.fileList,e.$http({url:"/wo/bill/create",data:e.dataModel}).then((function(t){t.code>0&&(e.visible=!1,e.$message.success("创建工单成功"),e.$emit("success"))}))}}))}}},x=k,M=Object(b["a"])(x,n,r,!1,null,null,null),$=M.exports,_=l("f71b"),q=l("f5d2"),T=l("c9ab"),S={1:"待解决",2:"已完成",3:"已关闭"},C={components:{PageTable:s["a"],CreateWo:$,SolveWo:_["a"],CloseWo:q["a"],ViewWo:T["a"],DeptTreeBox:w["a"]},data:function(){return{fullscreenLoading:!1,statusOptions:[],qform:{keyword:null},qdate:[],items:[]}},watch:{qdate:function(e){console.log(e),this.qform.reportBegin=e&&2===e.length?e[0]:null,this.qform.reportEnd=e&&2===e.length?e[1]:null}},mounted:function(){var e=this;Object.keys(S).forEach((function(t){e.statusOptions.push({value:t,text:S[t]})})),this.search()},methods:{getStatusType:function(e){switch(e.status){case"1":return"";case"2":return"success";case"3":return"info"}return""},getStatusText:function(e){return S[e.status]||""},search:function(){this.$refs.grid.search(this.qform)},selectionChange:function(e){this.items=e||[]},showOne:function(e,t){var l=this;t&&t.id||(t=1===this.items.length?this.items[0]:null),t&&e&&this.$http("/wo/bill/get/"+t.id).then((function(t){l.fullscreenLoading=!1,t.code>0&&t.data&&e.show(t.data)})).catch((function(){l.fullscreenLoading=!1,l.$message.error("网络超时")}))},createWo:function(){this.$refs.createWo.show()},solveWo:function(){this.showOne(this.$refs.solveWo)},closeWo:function(){this.showOne(this.$refs.closeWo)},download:function(){var e=this;if(0===this.items.length)return this.$message.warning("至少要选择一条资产记录");var t=[];this.items.forEach((function(e){return t.push(e.id)}));var l=o["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});this.$jasper({url:"/wo/bill/exportBatch",data:t,responseType:"blob"}).then((function(t){l.close(),e.$saveAs(t,"工单统计报表明细.xlsx")})).catch((function(t){l.close(),e.$message.error("导出生成出错:"+t)}))},exportQuery:function(){var e=this;this.$confirm("您确定要根据这些条件导出吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=o["Loading"].service({fullscreen:!0,text:"正在导出文件，请耐心等待..."});e.$jasper({url:"/wo/bill/exportAll",data:e.qform,responseType:"blob"}).then((function(l){t.close(),e.$saveAs(l,"工单统计报表明细.xlsx")})).catch((function(l){t.close(),e.$message.error("导出生成出错:"+l)}))}))},preview:function(){this.showOne(this.$refs.viewWo)},viewItem:function(e){this.showOne(this.$refs.viewWo,e)},batchDel:function(){var e=this;if(0===this.items.length)return this.$message.warning("请选择要删除的记录");var t=[];this.items.forEach((function(e){return t.push(e.id)})),this.$confirm("此操作将永久删除该这些工单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$http({url:"/wo/bill/deletes",data:t}).then((function(t){t.code>0&&(e.$message.success("删除成功"),e.search())}))})).catch((function(){}))}}},L=C,I=Object(b["a"])(L,a,i,!1,null,null,null);t["default"]=I.exports},"7b46":function(e,t,l){"use strict";l("9efb")},"9efb":function(e,t,l){},c740:function(e,t,l){"use strict";var a=l("23e7"),i=l("b727").findIndex,o=l("44d2"),s=l("ae40"),n="findIndex",r=!0,c=s(n);n in[]&&Array(1)[n]((function(){r=!1})),a({target:"Array",proto:!0,forced:r||!c},{findIndex:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),o(n)},dd8a:function(e,t,l){"use strict";var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],attrs:{title:"选择网点",width:"1100px",visible:e.visible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[l("div",{staticClass:"center"},[l("div",{staticClass:"location-head"},[l("span",{staticClass:"location-title"},[e._v("区域网点")]),l("div",{staticClass:"location-search"},[l("el-input",{attrs:{size:"mini",clearable:"",placeholder:"输入关键字",autocomplete:"off"},model:{value:e.qform.keyword,callback:function(t){e.$set(e.qform,"keyword",t)},expression:"qform.keyword"}},[l("template",{slot:"prepend"},[e._v("检索:")]),l("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.searchLocation},slot:"append"})],2)],1)]),l("page-table",{ref:"grid",attrs:{size:"mini",path:"/am/location/page",query:e.qform,stripe:"",border:""},on:{"selection-change":e.selectionChange,"row-click":e.clickRow}},[e.multiple?l("el-table-column",{attrs:{type:"selection",width:"45",fixed:"left",align:"center"}}):l("el-table-column",{attrs:{label:"",width:"45",fixed:"left",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.selectedItem&&e.selectedItem.id===t.row.id?l("el-link",{attrs:{underline:!1,type:"primary"}},[l("svg-icon",{attrs:{"icon-class":"ic_radio"}})],1):l("el-link",{attrs:{underline:!1}},[l("svg-icon",{attrs:{"icon-class":"ic_radio_un"}})],1)]}}])}),null==e.activeItem||null==e.activeItem.id?l("el-table-column",{attrs:{label:"所属区域",prop:"regionName",width:"80"}}):e._e(),l("el-table-column",{attrs:{label:"网点编码",prop:"code",width:"110",align:"center"}}),l("el-table-column",{attrs:{label:"销售终端编号",prop:"sn",width:"110",align:"center"}}),l("el-table-column",{attrs:{label:"网点名称",prop:"name",width:"220"}}),l("el-table-column",{attrs:{label:"负责人",prop:"contact",width:"80",align:"center"}}),l("el-table-column",{attrs:{label:"联系方式",prop:"phone",width:"110",align:"center"}}),l("el-table-column",{attrs:{label:"地址",prop:"address"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")]),l("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)])},i=[],o=(l("ac1f"),l("841c"),l("6ecd")),s={components:{PageTable:o["a"]},props:{multiple:{type:Boolean,default:!1}},data:function(){return{activeItem:{},visible:!1,tableHeight:300,selectedItem:null,items:[],qform:{keyword:""}}},mounted:function(){},methods:{show:function(){var e=this;this.visible=!0,this.selectedItem=null,this.items=[],this.$refs.grid?this.$refs.grid.search(this.qform,!0):this.$nextTick((function(){return e.$refs.grid.search(e.qform,!0)}))},searchLocation:function(){this.$refs.grid.search(this.qform)},selectionChange:function(e){this.multiple&&(this.items=e||[])},clickRow:function(e){this.multiple||(this.selectedItem&&this.selectedItem.id===e.id?this.selectedItem=null:this.selectedItem=e)},confirm:function(){this.$emit("selected",this.multiple?this.items:this.selectedItem,this.tag),this.visible=!1}}},n=s,r=(l("7b46"),l("2877")),c=Object(r["a"])(n,a,i,!1,null,null,null);t["a"]=c.exports}}]);